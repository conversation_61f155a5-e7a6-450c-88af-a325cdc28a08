import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import RoleStore from '../../stores/roleStore';
import { RoleTable } from './components/roleTable';
import {mergeStyleSets} from "@fluentui/react";
import {L} from "../../lib/abpUtility";
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
});

export interface IProps {
  searchStore: SearchStore;
  roleStore: RoleStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.RoleStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  public render() {
    let items = this.props.roleStore.dataSet ? this.props.roleStore.dataSet.items : [];

    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Role List')}</h2>
        </div>
        <RoleTable
          searchText={this.props.searchStore.searchText}
          items={items}
          store={this.props.roleStore}
          history={this.props.history}
        />
      </>
    );
  }
}

export default Index;