import { Pivot, PivotItem } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { RatingDto } from '../../../services/rating/ratingDto';
import { defaultRating } from '../../../stores/ratingStore';

export class RatingContentView extends GenericContentView {
    private rating: RatingDto = defaultRating;

    componentDidMount() {
        this.checkIfDataIsLoaded("rating");
    }

    renderContent() {
        this.rating = this.props.payload.model ? this.props.payload.model : this.props.payload;

        const pivotStyles = {
            root: {
              marginLeft: '-8px'
            },
            linkIsSelected: {
              color: myTheme.palette.red,
              selectors: {
                ':before': {
                  height: '5px',
                  backgroundColor: additionalTheme.darkerRed
                }
              }
            }
          };

        return <Pivot theme={myTheme} styles={pivotStyles}>
            <PivotItem headerText={L('General')} key={'General'}>
                {this.renderElement(new ContentViewModelProperty('title', L("Title"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'title': this.rating.title})}
            </PivotItem>
        </Pivot>
    }
}