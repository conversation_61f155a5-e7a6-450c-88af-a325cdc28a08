import { IColumn } from '@fluentui/react';

export interface ITableState<TDto> {
  column: IColumn;
  allItems: TDto[];
  isModalSelection: boolean;
  isCompactMode: boolean;
  showModal: boolean;
  isDraggable: boolean;
  showDialog: boolean;
  showFiltersDialog: boolean;
  dialogEntities: TDto[];
  dialogEntityId: string;
  modalVisible: boolean;
  maxResultCount: number;
  skipCount: number;
  filter: string;
  prevFilter: string;
  prevCustomRequest: string;
  selectionDetails: string;
  isShimmerEnabled: boolean;
  dialogAction: (input: TDto, values?: any) => Promise<void>;
  dialogTitle: string;
  isBulkOperation: boolean;
  refresh: boolean;
  dialogYesButtonDisabled: boolean;
  isBusy: boolean;
  pagedAllItems: TDto[];
  asyncActionInProgress?: boolean;
  overrideItemsTriggerCounter?: number;
  currentOrderBy?: string;
  currentSorting?: string;
  currentFilters?: any;
  currentFiltersOptions?: any;
  currentFiltersSearchValues?: any[];
  prevFiltersSearchValues?: string;
  toggleColumnFilterDialogForIds?: any;
}
