import { Pivot, PivotItem, Stack, Text, mergeStyleSets, Spinner, SpinnerSize, PrimaryButton, Icon, IDropdownOption, Dialog, DialogFooter, DefaultButton, DialogType, MessageBar, MessageBarType, IconButton, IChoiceGroupOption } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import {additionalTheme, myTheme} from '../../../styles/theme';
import insurerAttachedFilesService from '../../../services/attachedFiles/insurerAttachedFilesService';
import { dateFormat, enumToChoiceGroupOptions, enumToDropdownOptions } from '../../../utils/utils';
import { uploadFileToAzure } from '../../../services/azureService';
import React, { FormEvent } from 'react';
import { ChoiceGroupBase } from '../../BaseComponents/ChoiceGroupBase';
import { InsuranceCompanyPdfType } from '../../../services/insuranceCompany/insuranceCompanyPdfTypeEnums';
import { CheckBoxBase } from '../../BaseComponents/CheckBoxBase';
import { InsurerAttachedFilesDto } from '../../../services/attachedFiles/insurerAttachedFilesDto';
import { InsurerDto, MobilePaymentSettingsDto } from '../../../services/insurer/dto/insurerDto';
import { defaultInsurer } from '../../../stores/insurerStore';
import { DropdownBase } from '../../BaseComponents';
import paymentService from '../../../services/payment/paymentService';
import policyDictionaryService from '../../../services/policyDictionary/policyDictionaryService';
import { spinnerClassNames } from '../../../styles/spinnerStyles';
import insurerAvailabilityService from '../../../services/insurerAvailability/insurerAvailabilityService';
import { validateLocalStorageKeyAndTimestamp } from '../../../utils/localStorageUtils';
import { ProductDto } from '../../../services/product/productDto';
import productService from '../../../services/product/productService';
import { InsurerAvailabilityDto } from '../../../services/insurerAvailability/dto/insurerAvailabilityDto';
import { isConfigForAG, isConfigForProduction } from '../../../utils/authUtils';
import { InsurerAttachedFilePolicyType } from '../../../services/attachedFiles/enums/insurerAttachedFilePolicyTypeEnums';
import { LabeledTextField } from '../../../components/LabeledTextField';
import { UserDto } from '../../../services/user/dto/userDto';
import { defaultUser } from '../../../stores/userCrudStore';
import { inject, observer } from 'mobx-react';
import Stores from '../../../stores/storeIdentifier';
import userService from '../../../services/user/userService';
import { UserAgencyLoginDto } from '../../../services/userAgencyLogin/dto/userAgencyLoginDto';
import userAgencyLoginService from '../../../services/userAgencyLogin/userAgencyLoginService';
import { defaultUserAgencyLogin } from '../../../stores/userAgencyLoginStore';
import { UserAgencyLoginInputType } from '../../../services/userAgencyLogin/dto/userAgencyLoginInputTypeEnums';

const classNames = mergeStyleSets({
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinnerAbsolute: {
        position: 'absolute',
        top: 0,
        left: 300,
    },
    attachedFilesLabel: {
        fontWeight: 'bold',
        marginTop: '20px',
        marginBottom: '20px',
    },
    attachedFilesListItem: {
        listStyleType: 'decimal',
        marginBottom: '15px',
        padding: '10px',
        border: `1px solid ${myTheme.palette.neutralQuaternary}`,
        selectors: {
            // ':nth-child(even)': {
            //     background: myTheme.palette.neutralLight,
            // }
        }
    },
    messageBar: {
        width: 'fit-content',
        marginTop: '10px',
        marginBottom: '10px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
    uploadButton: {
        width: 'fit-content',
        padding: '25px 50px',
        marginTop: '20px',
    },
    inputIcon: {
        cursor: 'pointer',
        marginLeft: '15px !important',
        marginRight: '10px',
        fontSize: '20px',
        marginTop: '20px',
        transition: 'all 120ms',
        selectors: {
            '&:hover': {
                transform: 'scale(1.2)',
            }
        }
    },
    inputIcon2: {
        cursor: 'pointer',
        marginLeft: '15px !important',
        fontSize: '20px',
        marginTop: '48px',
        transition: 'all 120ms',
        selectors: {
            '&:hover': {
                transform: 'scale(1.2)',
            }
        }
    },
    paymentMethodSettingsBoxDropdown: {
        width: '100%',
    }, 
    fileContainer: {
        boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
        borderRadius: '12px',
        padding: '0 20px 20px 20px',
        marginTop: '20px',
    },
    insurerAccessSettingPasswordInput: {
        letterSpacing: '3px',
    },
    insurerAccessSettingsBoxInput: {
        width: '100%',
    },
});

declare var abp: any;

@inject(Stores.UserAgencyLoginStore)
@observer
export class InsuranceCompanyContentView extends GenericContentView {
    private insuranceCompany: InsurerDto = defaultInsurer;
    private attachedFiles: any = {
        totalCount: 0,
        items: []
    };
    private fileUploadInputRef: any;
    private selectedFileForUpload: any = {
        name: "" as string,
        src: "" as string,
        type: "" as string,
        includeInCalculationsEmail: false as boolean,
    };
    private selectedFileData: any = {};
    private attachmentFileAsyncActionStatus: any = {};
    private mobilePaymentSettingsOptions: any = {
        paymentTypes: [] as IDropdownOption[],
        paymentMethods: [] as IDropdownOption[],
        paymentPlans: [] as IDropdownOption[],
        policyTypes: [] as IDropdownOption[],
    };
    private mobilePaymentSettingsTilesData: any = {
        tileIndexToRemove: -1 as number,
        showDialog: false as boolean,
    };
    private disableConfirmButton: boolean = false;
    private hideConfirmButton: boolean = false;
    private formError: string = '';
    private allProducts: ProductDto[] = [];
    private allInsurerAvailabilities: InsurerAvailabilityDto[] = [];
    private showDeleteFileDialog: boolean = false;
    private attachedFileToDelete: InsurerAttachedFilesDto | null = null;
    private pdfTypeChoiceGroupOptions: IChoiceGroupOption[] = enumToChoiceGroupOptions(InsuranceCompanyPdfType, true, true, "string");
    private originInsurerAccessSettings: UserAgencyLoginDto[] = [];
    private user: UserDto = defaultUser;
    private allInsurerAccessSettings: UserAgencyLoginDto[] = [];
    private showInsurerAccessSettingPassword: boolean[] = [];
    private showMessageBar: boolean = false;
    private messageBarType: MessageBarType = MessageBarType.success;
    private messageBarText: string = '';
    private userRoles: string[] = [];

    constructor(props: any) {
        super(props);
        this.fileUploadInputRef = React.createRef();
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} 
                    onClick={() => {
                        this.disableConfirmButton = false;
                        this.hideConfirmButton = false;
                        if(this.props.toggleHideConfirm) this.props.toggleHideConfirm(false);
                        if(this.props.toggleConfirm) this.props.toggleConfirm(true);
                        this._onConfirm();
                    }} 
                    text={L('Save')} iconProps={{ iconName: 'none' }}
                    style={{display: this.hideConfirmButton ? 'none' : 'unset'}}
                    disabled={this.asyncActionInProgress || this.disableConfirmButton}
                />
    };

    renderBack = () => {
        return <DefaultButton text={L('Back')} iconProps={{ iconName: 'Back' }} allowDisabledFocus
                    onClick={() => {
                        this.disableConfirmButton = false;
                        this.hideConfirmButton = false;
                        if(this.props.toggleHideConfirm) this.props.toggleHideConfirm(false);
                        if(this.props.toggleConfirm) this.props.toggleConfirm(true);
                        this._onBack();
                    }}
                />;
    };

    async componentDidMount() {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        this.checkIfDataIsLoaded("insuranceCompany");

        this.attachedFiles = await insurerAttachedFilesService.getAllFiles();
        if(this.attachedFiles && this.attachedFiles.items) {
            this.attachedFiles.items.forEach((file: InsurerAttachedFilesDto) => {
                if(file.insurerName === this.insuranceCompany.name) {
                    this.selectedFileData[file.id] = {includeInCalculationsEmail: file.includeInCalculationsEmail};
                }
            });
        }

        await paymentService.getPaymentTypes().then((response: any) => {
            if(response && Array.isArray(response) && response.length > 0) {
                response.forEach((element: any) => {
                    this.mobilePaymentSettingsOptions.paymentTypes.push({key: element.value, text: element.displayName});
                });
            }
        }).catch((error: any) => {
            console.error(error);
        });

        await paymentService.getPaymentMethods().then((response: any) => {
            if(response && Array.isArray(response) && response.length > 0) {
                response.forEach((element: any) => {
                    this.mobilePaymentSettingsOptions.paymentMethods.push({key: element.value, text: element.displayName});
                });
            }
        }).catch((error: any) => {
            console.error(error);
        });

        await paymentService.getPaymentPlans().then((response: any) => {
            if(response && Array.isArray(response) && response.length > 0) {
                response.forEach((element: any) => {
                    this.mobilePaymentSettingsOptions.paymentPlans.push({key: element.value, text: element.displayName});
                });
            }
        }).catch((error: any) => {
            console.error(error);
        });

        await policyDictionaryService.getTypes().then((response: any) => {
            if(response && Array.isArray(response) && response.length > 0) {
                response.forEach((element: any) => {
                    this.mobilePaymentSettingsOptions.policyTypes.push(
                        {key: element, text: element === 'Home' ? L('Home2') : (element === 'Vehicle' ? L('Vehicle2') : L(element))}
                    );
                });
            }
        }).catch((error: any) => {
            console.error(error);
        });

        if(!!this.insuranceCompany.id) {
            await insurerAvailabilityService.getByInsurer(this.insuranceCompany.id).then((response: any) => {
                if(response && Array.isArray(response.items) && response.items.length > 0) {
                    this.allInsurerAvailabilities = response.items;
                }
            }).catch((error: any) => {
                console.error(error);
            });
        }

        if(validateLocalStorageKeyAndTimestamp('policyCalculationAllProducts', 3600000)) { // 1h
            const tempAllProducts: ProductDto[] = JSON.parse(localStorage.getItem('policyCalculationAllProducts')!).data;
            tempAllProducts.forEach((product: ProductDto) => {
                if(product.Published) {
                    this.allProducts.push(product);
                }
            });
        } else {
            await productService.getAll(productService.defaultRequest).then((response: any) => {
                if(response && response.items && response.items.length > 0) {
                    this.allProducts = [];
                    response.items.forEach((product: ProductDto) => {
                        if(product.Published) {
                            this.allProducts.push(product);
                        }
                    });

                    localStorage.setItem('policyCalculationAllProducts', JSON.stringify({data: this.allProducts, timestamp: new Date().getTime()}));
                }
            }).catch((error: any) => {
                console.error(error);
            });
        }

        if(this.pdfTypeChoiceGroupOptions && Array.isArray(this.pdfTypeChoiceGroupOptions)) {
            this.pdfTypeChoiceGroupOptions.some((option: IChoiceGroupOption) => {
                if(option.key === InsuranceCompanyPdfType.INFO) {
                    option.disabled = true;
                    return true;
                }
                return false;
            });
        }

        if(abp && abp.session && abp.session.userId && abp.session.userId > 0) {
            await userService.get({id: abp.session.userId}).then((user: any) => {
                if(user && user.roleNames) {
                    this.userRoles = [...user.roleNames];
                }
            }).catch((error: any) => {
                console.error(error);
            });
        }

        this.refreshInsurerAccessSettings(true);

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    private triggerUpload = () => {
        this.fileUploadInputRef.current.click();
    };

    private async onUpload() {
        this.asyncActionInProgress = true;

        this.insuranceCompany = this.props.payload.model ? this.props.payload.model : this.props.payload;
        const selectedFile = !!this.fileUploadInputRef.current.files && this.fileUploadInputRef.current.files.length ? this.fileUploadInputRef.current.files[0] : null;
        
        if(!!selectedFile) {
            this.selectedFileForUpload.name = selectedFile.name;
            this.selectedFileForUpload.src = URL.createObjectURL(selectedFile);
        }

        this.forceUpdate();

        let result = await uploadFileToAzure(selectedFile);

        await insurerAttachedFilesService.createNew({
            "fileUrl": result.url,
            "originalFileName": selectedFile.name,
            "blobFileName": result.name,
            "id": '0',
            "status": 'Uploaded',
            "insurerName": this.insuranceCompany.name,
            "type": this.selectedFileForUpload.type,
            "includeInCalculationsEmail": this.selectedFileForUpload.includeInCalculationsEmail,
            "policyType": InsurerAttachedFilePolicyType.Vehicle,
        }).then(async (response: any) => {
            if(response && !!response.id && response.id > 0) {
                this.attachedFiles = await insurerAttachedFilesService.getAllFiles();
            }

            this.asyncActionInProgress = false;
            this.forceUpdate();
        }).catch((error: any) => {
            console.error(error);

            this.asyncActionInProgress = false;
            this.forceUpdate();
        });
    }

    private async updateAttachment(file: InsurerAttachedFilesDto) {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        await insurerAttachedFilesService.update(file).then(async (response: any) => {
            if(response && !!response.id && response.id > 0) {
                this.attachmentFileAsyncActionStatus[response.id] = true;
                this.forceUpdate();

                this.attachedFiles = await insurerAttachedFilesService.getAllFiles();
                
                setTimeout(() => {
                    this.attachmentFileAsyncActionStatus[response.id] = false;
                    this.forceUpdate();
                }, 3000);
            } else {
                console.error(response);
            }
        }).catch((error: any) => {
            console.error(error);
        });

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    async deleteAttachedFile() {
        this.showDeleteFileDialog = false;

        if(this.attachedFileToDelete) {
            this.asyncActionInProgress = true;
            this.forceUpdate();
            
            await insurerAttachedFilesService.delete(this.attachedFileToDelete).then(async (deleteResponse: any) => {
                this.attachedFileToDelete = null;

                if(deleteResponse && deleteResponse.success) {
                    this.attachedFiles = await insurerAttachedFilesService.getAllFiles();
                }

                this.asyncActionInProgress = false;
                this.forceUpdate();
            }).catch((error: any) => {
                console.error(error);
                this.attachedFileToDelete = null;
                this.asyncActionInProgress = false;
                this.forceUpdate();
            });
        }
    }

    closeDeleteFileDialog() {
        this.attachedFileToDelete = null;
        this.showDeleteFileDialog = false;
        this.forceUpdate();
    }

    private async refreshInsurerAccessSettings(forceUpdate?: boolean) {
        if(this.props.userAgencyLoginStore) {
            await this.props.userAgencyLoginStore?.getInsurerLoginsByInsurerId(parseInt(this.insuranceCompany.id)).then((response: any) => {
                if(response && response.items) {
                    this.originInsurerAccessSettings = [...response.items];
                    this.allInsurerAccessSettings = [...response.items];

                    if(forceUpdate) {
                        this.forceUpdate();
                    }
                }
            }).catch((error: any) => {
                console.error(error);
            });
        }
    }

    private toggleShowFilePassword(index: number, bool: boolean) {
        this.showInsurerAccessSettingPassword[index] = bool; 
        this.forceUpdate();
    }

    private async saveInsurerAccessSettings() {
        if(this.allInsurerAccessSettings && Array.isArray(this.allInsurerAccessSettings)) {
            this.asyncActionInProgress = true;
            this.forceUpdate();

            for (const insurerAccessSetting of this.allInsurerAccessSettings) {
                const filteredOriginInsurerAccessSettings: UserAgencyLoginDto[] = this.originInsurerAccessSettings.filter(
                    (originInsurerAccessSetting: UserAgencyLoginDto) => originInsurerAccessSetting.insurerId === insurerAccessSetting.insurerId
                                                                            && originInsurerAccessSetting.segment === insurerAccessSetting.segment
                );

                if(insurerAccessSetting && (!Array.isArray(filteredOriginInsurerAccessSettings) || !filteredOriginInsurerAccessSettings[0] ||
                    JSON.stringify(filteredOriginInsurerAccessSettings[0].datas) !== JSON.stringify(insurerAccessSetting.datas)))
                {
                    await userAgencyLoginService.saveInsurerLogins(this.allInsurerAccessSettings).then((response: any) => {
                        if(response && response.length > 0) {
                            this.messageBarType = MessageBarType.success;
                            this.messageBarText = L('Saved sucessfully.');
                        } else {
                            this.messageBarType = MessageBarType.error;
                            this.messageBarText = L('Something went wrong. Try again later or contact with administrator.');
                        }
                    }).catch((error: any) => {
                        console.error(error);
                        this.messageBarType = MessageBarType.error;
                        this.messageBarText = L('Something went wrong. Try again later or contact with administrator.');
                    });

                    this.showMessageBar = true;
                }
            };

            this.asyncActionInProgress = false;
            this.refreshInsurerAccessSettings(true);
        }
    }

    renderContent() {
        this.insuranceCompany = this.props.payload.model ? this.props.payload.model : this.props.payload;

        const hasInsurerAccessSettingsChanged: boolean = JSON.stringify(this.allInsurerAccessSettings) !== JSON.stringify(this.originInsurerAccessSettings);

        let hideInsurerAccessSettingsTab: boolean = true;
        this.allInsurerAccessSettings.some((userAgencyLogin: UserAgencyLoginDto) => {
            if(userAgencyLogin.datas && userAgencyLogin.datas.length > 0) {
                hideInsurerAccessSettingsTab = false;
                return true;
            }
            return false;
        });

        if(!Array.isArray(this.insuranceCompany.mobilePaymentSettings)) {
            const cloneMobilePaymentSettings: MobilePaymentSettingsDto[] = [];
            for(let key in Object.keys(this.insuranceCompany.mobilePaymentSettings)) {
                cloneMobilePaymentSettings.push(this.insuranceCompany.mobilePaymentSettings[key]);
            }
            this.insuranceCompany.mobilePaymentSettings = cloneMobilePaymentSettings;
        }
        
        let attachedFilesList: JSX.Element[] = [];
        if(!!this.attachedFiles && !!this.attachedFiles.items && this.attachedFiles.totalCount > 0) {
            this.attachedFiles.items.forEach((file: any) => {
                if(file.insurerName === this.insuranceCompany.name) {
                    attachedFilesList.push(
                        <li key={file.id} className={classNames.attachedFilesListItem}>
                            <IconButton
                                styles={{
                                    root: {
                                        color: myTheme.palette.red,
                                        marginRight: '2px',
                                    },
                                    rootHovered: {
                                        color: myTheme.palette.redDark,
                                    },
                                }}
                                iconProps={{ iconName: 'Delete' }}
                                ariaLabel={L("Close popup modal")}
                                onClick={() => { 
                                    this.attachedFileToDelete = file;
                                    this.showDeleteFileDialog = true; 
                                    this.forceUpdate();
                                }}
                            />

                            {dateFormat(file.creationTime, "DD.MM.YYYY HH:mm", true)}{` - `} 
                            <a href={file.fileUrl} title={L("Download file")}>{file.originalFileName}</a>

                            <Stack horizontal={true}>
                                <CheckBoxBase label={L("Include in calculations email")} 
                                    value={this.selectedFileData[file.id] && typeof this.selectedFileData[file.id].includeInCalculationsEmail !== 'undefined' ? this.selectedFileData[file.id].includeInCalculationsEmail : file.includeInCalculationsEmail} 
                                    disabled={this.asyncActionInProgress} containerCustomStyles={{display: 'flex', alignItems: 'center'}}
                                    onChange={(e: boolean | undefined) => {
                                        let shouldTakeAction: boolean = false;

                                        if(typeof e === 'boolean' && !this.selectedFileData[file.id]) {
                                            shouldTakeAction = true;
                                        } else if(typeof e === 'boolean' && this.selectedFileData[file.id] && e !== this.selectedFileData[file.id].includeInCalculationsEmail) {
                                            shouldTakeAction = true;
                                        }

                                        if(shouldTakeAction === true) {
                                            if(this.selectedFileData[file.id]) {
                                                this.selectedFileData[file.id].includeInCalculationsEmail = e;
                                            } else {
                                                this.selectedFileData[file.id] = {
                                                    includeInCalculationsEmail: e
                                                };
                                            }

                                            this.updateAttachment({...file, includeInCalculationsEmail: e});
                                            this.forceUpdate();
                                        }
                                    }}
                                />

                                <DropdownBase label={L('Policy type')} options={enumToDropdownOptions(InsurerAttachedFilePolicyType, true, false, "string")}
                                    value={file.policyType && typeof file.policyType !== 'undefined' ? file.policyType : undefined} 
                                    disabled={false} isDataLoaded={true} required={false}
                                    customLabelStyles={{minWidth: '100px', width: '100px', marginTop: 0, marginLeft: 50, background: 'none', color: additionalTheme.grey, fontSize: '14px', fontWeight: 'normal', border: 'none', paddingLeft: 0}}
                                    customClassName={classNames.paymentMethodSettingsBoxDropdown}
                                    // labelContainerCustomStyles={{flexDirection: 'column', marginTop: '10px'}}
                                    onChange={(e: string | number | undefined) => {
                                        if(!!e && e !== file.policyType) {
                                            this.updateAttachment({...file, policyType: e});
                                        }
                                    }} 
                                />

                                { this.attachmentFileAsyncActionStatus[file.id] === true &&
                                    <Icon iconName='CheckMark' style={{color: 'green', cursor: 'default'}} className={classNames.inputIcon} title={L('Attachment data changed successfully.')} />
                                }
                            </Stack>
                        </li>
                    );
                }
            });
        }

        const fileUploadButton = <PrimaryButton 
                                    className={classNames.uploadButton}
                                    theme={myTheme}
                                    text={L('Upload file')}
                                    type={'file'}
                                    onClick={this.triggerUpload}
                                    disabled={this.asyncActionInProgress || (!this.selectedFileForUpload.type || this.selectedFileForUpload.type.length === 0)}
                                    iconProps={{ iconName: 'none' }}
                                    style={{marginTop: '20px'}}
                                />;

        let anyDuplicateSettingFound: boolean = false;
        const mobilePaymentTiles = this.mobilePaymentSettingsOptions.policyTypes.length > 0 ? this.insuranceCompany.mobilePaymentSettings.map((setting: any, settingIndex: number) => {
            let foundDuplicatedSettingIndex: boolean = false;
            this.insuranceCompany.mobilePaymentSettings.forEach((otherSetting: any, otherSettingIndex: number) => {
                if(settingIndex !== otherSettingIndex) {
                    for(let p in setting){
                        if(setting.hasOwnProperty(p)){
                            if(setting[p] !== otherSetting[p]){
                                return false;
                            }
                        }
                    }
                    for(let p in otherSetting){
                        if(otherSetting.hasOwnProperty(p)){
                            if(setting[p] !== otherSetting[p]){
                                return false;
                            }
                        }
                    }

                    foundDuplicatedSettingIndex = true;
                    anyDuplicateSettingFound = true;

                    return true;
                }
                return true;
            });

            return <Stack tokens={{childrenGap: '20%', padding: '0px 33px 23px 66px'}} styles={{
                    root: {
                        width: 'fit-content',
                        marginTop: 15,
                        marginRight: 15,
                        position: 'relative',
                        background: myTheme.palette.white,
                        boxShadow: foundDuplicatedSettingIndex !== false ? '0px 2px 4px red' : '0px 2px 4px rgba(0, 0, 0, 0.25)',
                        borderRadius: '12px',
                    },
                }}>
                    <span style={{position: 'absolute', top: 12, left: 24, fontSize: 30, fontWeight: 600, color: myTheme.palette.neutralPrimaryAlt}}>
                        {this.insuranceCompany.mobilePaymentSettings.length - settingIndex}
                    </span>

                    <DropdownBase label={L('Policy type')} options={this.mobilePaymentSettingsOptions.policyTypes}
                        value={setting.policyType} disabled={false} isDataLoaded={true} required={true}
                        customLabelStyles={{minWidth: '150px', width: '150px', marginTop: 0, background: 'none', color: additionalTheme.grey, fontSize: '14px', fontWeight: 'normal', border: 'none', paddingLeft: 0}}
                        customClassName={classNames.paymentMethodSettingsBoxDropdown}
                        labelContainerCustomStyles={{flexDirection: 'column', marginTop: '10px'}}
                        onChange={(e: string | number | undefined) => {
                            if(e) {
                                this.insuranceCompany.mobilePaymentSettings[settingIndex].policyType = e.toString();
                                this.forceUpdate();
                            }
                        }} 
                    />

                    <DropdownBase label={L('Payment method')} options={this.mobilePaymentSettingsOptions.paymentMethods}
                        value={setting.paymentMethod} disabled={false} isDataLoaded={true} required={true}
                        customLabelStyles={{minWidth: '150px', width: '150px', marginTop: 0, background: 'none', color: additionalTheme.grey, fontSize: '14px', fontWeight: 'normal', border: 'none', paddingLeft: 0}}
                        customClassName={classNames.paymentMethodSettingsBoxDropdown}
                        labelContainerCustomStyles={{flexDirection: 'column', marginTop: '10px'}}
                        onChange={(e: string | number | undefined) => {
                            if(e) {
                                this.insuranceCompany.mobilePaymentSettings[settingIndex].paymentMethod = e.toString();
                                this.forceUpdate();
                            }
                        }} 
                    />

                    <DropdownBase label={L('Payment plan')} options={this.mobilePaymentSettingsOptions.paymentPlans}
                        value={setting.paymentPlan} disabled={false} isDataLoaded={true} required={true}
                        customLabelStyles={{minWidth: '150px', width: '150px', marginTop: 0, background: 'none', color: additionalTheme.grey, fontSize: '14px', fontWeight: 'normal', border: 'none', paddingLeft: 0}}
                        customClassName={classNames.paymentMethodSettingsBoxDropdown}
                        labelContainerCustomStyles={{flexDirection: 'column', marginTop: '10px'}}
                        onChange={(e: string | number | undefined) => {
                            if(e) {
                                this.insuranceCompany.mobilePaymentSettings[settingIndex].paymentPlan = e.toString();
                                this.forceUpdate();
                            }
                        }} 
                    />

                    <DropdownBase label={L('Payment type')} options={this.mobilePaymentSettingsOptions.paymentTypes}
                        value={setting.paymentType} disabled={false} isDataLoaded={true} required={true}
                        customLabelStyles={{minWidth: '150px', width: '150px', marginTop: 0, background: 'none', color: additionalTheme.grey, fontSize: '14px', fontWeight: 'normal', border: 'none', paddingLeft: 0}}
                        customClassName={classNames.paymentMethodSettingsBoxDropdown}
                        labelContainerCustomStyles={{flexDirection: 'column', marginTop: '10px'}}
                        onChange={(e: string | number | undefined) => {
                            if(e) {
                                this.insuranceCompany.mobilePaymentSettings[settingIndex].paymentType = e.toString();
                                this.forceUpdate();
                            }
                        }} 
                    />

                    <PrimaryButton theme={myTheme} text={L('Delete')} disabled={this.asyncActionInProgress} /*iconProps={{ iconName: 'Delete' }} */
                        style={{marginTop: '20px', width: 'fit-content', backgroundColor: 'white', color: additionalTheme.grey, border: `1px solid ${additionalTheme.grey}`}}
                        onClick={() => {
                            this.mobilePaymentSettingsTilesData.tileIndexToRemove = settingIndex;
                            this.mobilePaymentSettingsTilesData.showDialog = true;
                            this.forceUpdate();
                        }}
                    />
                </Stack>
        }) : <></>;

        let errorFound: boolean = false;

        this.insuranceCompany.mobilePaymentSettings.some((setting: any) => {
            for(let key in setting) {
                if(setting.hasOwnProperty(key) && (!setting[key] || setting[key].length === 0)) {
                    errorFound = true;
                    return true;
                }
            }
            return false;
        });

        if((errorFound !== false || anyDuplicateSettingFound !== false) && this.disableConfirmButton === false) {
            this.formError = errorFound !== false ? L('Complete missing data in the form') : L('Duplicate settings detected');
            this.disableConfirmButton = true;
            if(this.props.toggleConfirm) this.props.toggleConfirm(false);
        } else if((errorFound === false && anyDuplicateSettingFound === false) && this.disableConfirmButton === true) {
            this.formError = '';
            this.disableConfirmButton = false;
            if(this.props.toggleConfirm) this.props.toggleConfirm(true);
        }
        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed
                    }
                }
            }
        };

        return <>
            {this.formError &&
                <MessageBar messageBarType={MessageBarType.warning} isMultiline={false} className={`${classNames.messageBar}`}
                    onDismiss={() => { this.formError = ''; this.forceUpdate(); }}
                >
                    {this.formError}
                </MessageBar>
            }

            <Dialog
                hidden={!this.showDeleteFileDialog}
                onDismiss={() => this.closeDeleteFileDialog()}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L("Are you sure you want to delete this file?"),
                    closeButtonAriaLabel: L('Close'),
                }}
                modalProps={{isBlocking: true, styles: {main: { maxWidth: 450 }},}}
                theme={myTheme}
            >
                <DialogFooter theme={myTheme}>
                    <DefaultButton theme={myTheme} onClick={() => this.closeDeleteFileDialog()} text={L('No')} />
                    <PrimaryButton text={L('Yes')} theme={myTheme} disabled={this.asyncActionInProgress}
                        onClick={() => {
                            this.deleteAttachedFile();
                        }}
                    />
                </DialogFooter>
            </Dialog>

            <Pivot styles={pivotStyles} style={{marginTop: '20px'}} theme={myTheme} onLinkClick={(item: PivotItem | undefined) => {
                if(item?.props && item.props.itemKey && item.props.itemKey === `productAvailabilitySettings` && !this.hideConfirmButton) {
                    this.hideConfirmButton = true;
                    if(this.props.toggleHideConfirm) this.props.toggleHideConfirm(true);
                } else if(this.hideConfirmButton) {
                    this.hideConfirmButton = false;
                    if(this.props.toggleHideConfirm) this.props.toggleHideConfirm(false);
                }

                this._onCloseMessage();
            }}>
                <PivotItem headerText={L('General')} key={'General'}>
                    {this.asyncActionInProgress && (
                        <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                    )}

                    {this.renderElement(new ContentViewModelProperty('name', "Name", Controls.Text, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded}), [], {'name': this.insuranceCompany.name})}
                    {this.renderElement(new ContentViewModelProperty('fullName', "Full name", Controls.Text, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'fullName': this.insuranceCompany.fullName})}
                    {this.renderElement(new ContentViewModelProperty('emailAddress', L('E-mail'), Controls.Text, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded}), [], {'emailAddress': this.insuranceCompany.emailAddress})}
                    {this.renderElement(new ContentViewModelProperty('phoneNumber', "Phone number", Controls.Text, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded}), [], {'phoneNumber': this.insuranceCompany.phoneNumber})}
                    {this.renderElement(new ContentViewModelProperty('city', L('City'), Controls.Text, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded}), [], {'city': this.insuranceCompany.city})}
                    {this.renderElement(new ContentViewModelProperty('address', "Address", Controls.Text, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'address': this.insuranceCompany.address})}
                    {this.renderElement(new ContentViewModelProperty('zipPostalCode', L('Zip postal code'), Controls.Text, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded}), [], {'zipPostalCode': this.insuranceCompany.zipPostalCode})}
                    {this.renderElement(new ContentViewModelProperty('logoLink', "Logo link", Controls.Text, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'logoLink': this.insuranceCompany.logoLink})}
                    {this.renderElement(new ContentViewModelProperty('isDirectSales', "Is direct sales", Controls.CheckBox, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded}), [], {'isDirectSales': this.insuranceCompany.isDirectSales})}
                    {this.renderElement(new ContentViewModelProperty('isSendProposal', "Is send proposal", Controls.CheckBox, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded}), [], {'isSendProposal': this.insuranceCompany.isSendProposal})}
                    {this.renderElement(new ContentViewModelProperty('isSendPdfFromCalculation', "Is send from calculation", Controls.CheckBox, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded}), [], {'isSendPdfFromCalculation': this.insuranceCompany.isSendPdfFromCalculation})}
                    {this.renderElement(new ContentViewModelProperty('isAgencySales', "Is agency sales", Controls.CheckBox, false, [], this.asyncActionInProgress, {isDataLoaded: this.isDataLoaded}), [], {'isAgencySales': this.insuranceCompany.isAgencySales})}
                    <ChoiceGroupBase 
                        label={L('Sended proposal')} 
                        disabled={!this.isDataLoaded || this.asyncActionInProgress}
                        value={!!this.insuranceCompany.isSendProposal ? (this.insuranceCompany.isSendProposal ? 'true' : 'false') : 'false'}
                        options={[
                            {key: 'true', text: L('Yes')}, 
                            {key: 'false', text: L('No')}
                        ]}
                        onChange={(e: any) => {
                            this.insuranceCompany.isSendProposal = e.key === 'true';
                            this.forceUpdate();
                        }}
                    />
                </PivotItem>

                <PivotItem headerText={L('IPID / OWU')} key={'attachedFiles'}>
                    <Stack className={classNames.fileContainer}>
                        <ChoiceGroupBase label={L("File type")} value={this.selectedFileForUpload.type} disabled={this.asyncActionInProgress}
                            options={this.pdfTypeChoiceGroupOptions}
                            onChange={(e: any) => {
                                this.selectedFileForUpload.type = e.key;
                                this.forceUpdate();
                            }}
                        />

                        <CheckBoxBase label={L("Include in calculations email")} 
                            value={this.selectedFileForUpload.includeInCalculationsEmail} disabled={this.asyncActionInProgress}
                            onChange={(e: any) => {
                                this.selectedFileForUpload.includeInCalculationsEmail = e;
                                this.forceUpdate();
                            }}
                        />

                        <Stack horizontal={true}>
                            <input ref={this.fileUploadInputRef} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUpload()} />
                            { fileUploadButton }

                            {this.asyncActionInProgress && (
                                <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                            )}
                        </Stack>
                    </Stack>

                    { attachedFilesList.length > 0 && <Stack>
                        <Text variant="large" className={classNames.attachedFilesLabel}>
                            { L('Attached files:') }
                        </Text>

                        <div>
                            { attachedFilesList }
                        </div>
                    </Stack> }
                </PivotItem>

                {(!isConfigForAG() && !isConfigForProduction()) &&
                    <PivotItem headerText={L('Mobile payment settings')} key={'mobilePaymentSettings'}>
                        <Dialog
                            hidden={!this.mobilePaymentSettingsTilesData.showDialog}
                            onDismiss={() => {this.mobilePaymentSettingsTilesData.showDialog = false; this.mobilePaymentSettingsTilesData.tileIndexToRemove = -1; this.forceUpdate();}}
                            dialogContentProps={{
                                type: DialogType.normal,
                                title: L("Are you sure you want to delete this settings?"),
                                closeButtonAriaLabel: L('Close'),
                            }}
                            modalProps={{isBlocking: true, styles: {main: { maxWidth: 450 }},}}
                            theme={myTheme}
                        >
                            <DialogFooter theme={myTheme}>
                                <DefaultButton theme={myTheme} onClick={() => {this.mobilePaymentSettingsTilesData.showDialog = false; this.forceUpdate();}} text={L('No')} />
                                <PrimaryButton text={L('Yes')} theme={myTheme} disabled={this.asyncActionInProgress}
                                    onClick={() => {
                                        if(this.mobilePaymentSettingsTilesData.tileIndexToRemove >= 0) {
                                            const cloneMobilePaymentSettings: MobilePaymentSettingsDto[] = [];
                                            this.insuranceCompany.mobilePaymentSettings.forEach((settingTile: any, tileIndex: number) => {
                                                if(tileIndex !== this.mobilePaymentSettingsTilesData.tileIndexToRemove) cloneMobilePaymentSettings.push(settingTile);
                                            });
                                            this.insuranceCompany.mobilePaymentSettings = cloneMobilePaymentSettings;
                                        }
                                        this.mobilePaymentSettingsTilesData.tileIndexToRemove = -1;
                                        this.mobilePaymentSettingsTilesData.showDialog = false;
                                        this.forceUpdate();
                                    }}
                                />
                            </DialogFooter>
                        </Dialog>

                        <PrimaryButton theme={myTheme} text={L('Add')} disabled={this.asyncActionInProgress} iconProps={{ iconName: 'Add' }} style={{marginTop: '20px'}}
                            onClick={() => {
                                this.insuranceCompany.mobilePaymentSettings.unshift({
                                    policyType: "",
                                    paymentMethod: "",
                                    paymentPlan: "",
                                    paymentType: "",
                                });
                                this.forceUpdate();
                            }}
                        />

                        {this.asyncActionInProgress &&
                            <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" style={{marginLeft: 25}} />
                        }
                        
                        <div style={{display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', flexWrap: 'wrap'}}>
                            {mobilePaymentTiles}
                        </div>
                    </PivotItem>
                }

                <PivotItem headerText={L('Product availability settings')} key={'productAvailabilitySettings'} style={{position: 'relative', minHeight: 80}} itemKey={'productAvailabilitySettings'}>
                    {this.asyncActionInProgress && (
                        <Spinner label={L('Please wait...')} className={`${classNames.loadSpinner} ${classNames.loadSpinnerAbsolute}`} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
                    )}

                    <Stack>
                        {(!this.allProducts || this.allProducts.length === 0) && !this.asyncActionInProgress ?
                            <Text variant="large" className={classNames.attachedFilesLabel}>
                                { L('No data') }
                            </Text>
                        :
                            this.allProducts.map((product: ProductDto) => {
                                let foundInsurerAvailability: InsurerAvailabilityDto | undefined = undefined;
                                let foundInsurerAvailabilityIndex: number = -1;

                                this.allInsurerAvailabilities.some((insurerAvailability: InsurerAvailabilityDto, insurerAvailabilityIndex: number) => {
                                    if(insurerAvailability.productId === product.id) {
                                        foundInsurerAvailability = insurerAvailability;
                                        foundInsurerAvailabilityIndex = insurerAvailabilityIndex;
                                        return true;
                                    }
                                    return false;
                                });

                                return <ChoiceGroupBase label={L(product.Name)} disabled={this.asyncActionInProgress}
                                    value={!!foundInsurerAvailability ? (foundInsurerAvailability['availability'] ? 'true' : 'false') : 'false'}
                                    options={[{key: 'true', text: L('Yes')}, {key: 'false', text: L('No')}]}
                                    onChange={async (e: any) => {
                                        this.asyncActionInProgress = true;
                                        this.forceUpdate();

                                        if(!!foundInsurerAvailability) {
                                            await insurerAvailabilityService.update({
                                                insurerId: this.insuranceCompany.id,
                                                // insurer: this.insuranceCompany,
                                                productId: product.id,
                                                availability: e.key === 'true' ? true : false,
                                                id: foundInsurerAvailability.id,
                                            }).then((response: any) => {
                                                if(response && (!!response.id || response.success) && foundInsurerAvailability) {
                                                    foundInsurerAvailability['availability'] = e.key === 'true' ? true : false;
                                                    this.allInsurerAvailabilities[foundInsurerAvailabilityIndex] = foundInsurerAvailability;
                                                    this.forceUpdate();
                                                }
                                            }).catch((error: any) => {
                                                console.error(error);
                                            });
                                        } else {
                                            await insurerAvailabilityService.create({
                                                insurerId: this.insuranceCompany.id,
                                                // insurer: this.insuranceCompany,
                                                productId: product.id,
                                                availability: e.key === 'true' ? true : false,
                                                id: '',
                                            }).then((response: any) => {
                                                if(response && (!!response.id || response.success) && foundInsurerAvailability) {
                                                    foundInsurerAvailability['availability'] = e.key === 'true' ? true : false;
                                                    this.allInsurerAvailabilities[foundInsurerAvailabilityIndex] = foundInsurerAvailability;
                                                    this.forceUpdate();
                                                }
                                            }).catch((error: any) => {
                                                console.error(error);
                                            });
                                        }

                                        this.asyncActionInProgress = false;
                                        this.forceUpdate();
                                    }}
                                />;
                            })
                        }
                    </Stack>
                </PivotItem>

                {((this.userRoles.includes("GN-ADMIN") || this.userRoles.includes("ADMIN")) && !hideInsurerAccessSettingsTab) &&
                    <PivotItem headerText={L('Insurer access settings')} key={'InsurerAccessSettings'} itemKey={'insurerAccessSettings'}>
                        {this.asyncActionInProgress &&
                            <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" style={{marginLeft: 25}} />
                        }
                        
                        <div style={{display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', flexWrap: 'wrap'}}>
                            {
                                this.allInsurerAccessSettings.map((userAgencyLogin: UserAgencyLoginDto, userAgencyLoginIndex: number) => {
                                    const filteredInsurerAccessSettings: UserAgencyLoginDto[] = this.allInsurerAccessSettings.filter(
                                        (insurerAccessSetting: UserAgencyLoginDto) => insurerAccessSetting.insurerId === userAgencyLogin.insurerId 
                                                                                        && insurerAccessSetting.segment === userAgencyLogin.segment
                                    );

                                    if(!userAgencyLogin.datas || userAgencyLogin.datas.length === 0) {
                                        return <></>;
                                    }
                                    
                                    return <Stack tokens={{childrenGap: '20%', padding: '15px 33px 23px 33px'}} styles={{
                                        root: {
                                            width: 'fit-content',
                                            marginTop: 15,
                                            marginRight: 15,
                                            position: 'relative',
                                            alignItems:'center',
                                            justifyContent:'flex-start',
                                            background: myTheme.palette.white,
                                            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.25)',
                                            borderRadius: '12px',
                                        },
                                    }}>
                                        <div style={{width: 280, display: 'flex', height: '90px', justifyContent: 'center', alignItems: 'center', flexDirection: 'column'}}>
                                            { userAgencyLogin.logoLink && !!userAgencyLogin.logoLink ?
                                                <img style={{ marginTop:'15px', maxHeight:'70px'}} src={userAgencyLogin.logoLink} alt={`${userAgencyLogin.insurerName} logo`} />
                                                :
                                                <p style={{ width: '100%', textAlign: 'center', fontWeight: 'bold', justifyContent:'center', fontSize: '1.1rem', marginBottom: 0}}>{L(userAgencyLogin.insurerName)}</p>
                                            }

                                            <p style={{ width: '100%', textAlign: 'center', fontWeight: 'bold', justifyContent:'center', marginBottom: '10px'}}>{L(userAgencyLogin.segment)}</p>
                                        </div>

                                        {userAgencyLogin.datas.map((data: any, dataIndex: number) => {
                                            if(data && data.type === UserAgencyLoginInputType.Login) {
                                                return (
                                                <LabeledTextField key={`accessSettingLoginForInsurer${userAgencyLogin.insurerId}-${userAgencyLoginIndex}`} label={L(data.name)} isDataLoaded={true} disabled={this.asyncActionInProgress}
                                                    value={filteredInsurerAccessSettings && Array.isArray(filteredInsurerAccessSettings) && filteredInsurerAccessSettings[0] ? filteredInsurerAccessSettings[0].datas[dataIndex].value : ''}
                                                    customLabelStyles={{minWidth: '150px', width: '150px', marginTop: 0, background: 'none', color: additionalTheme.grey, fontSize: '14px', fontWeight: 'normal', border: 'none', paddingLeft: 0}}
                                                    customClassNames={`${classNames.insurerAccessSettingsBoxInput}`} required={true}
                                                    labelContainerCustomStyles={{flexDirection: 'column', marginTop: '10px', marginLeft: '35px', marginRight: '35px'}}
                                                    onChange={(event: FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string | undefined) => {
                                                        if(typeof newValue === 'string') {
                                                            const clonedData: any[] = [...userAgencyLogin.datas];
                                                            clonedData[dataIndex] = {...data, value: newValue};

                                                            if(filteredInsurerAccessSettings.length > 0) {
                                                                const cloneAllInsurerAccessSettings: UserAgencyLoginDto[] = [];
                                                                this.allInsurerAccessSettings.forEach((insurerAccessSetting: UserAgencyLoginDto) => {
                                                                    if(insurerAccessSetting.insurerId !== userAgencyLogin.insurerId || insurerAccessSetting.segment !== userAgencyLogin.segment) {
                                                                        cloneAllInsurerAccessSettings.push(insurerAccessSetting);
                                                                    } else {
                                                                        cloneAllInsurerAccessSettings.push({
                                                                            ...insurerAccessSetting,
                                                                            datas: clonedData,
                                                                        });
                                                                    }
                                                                });
                                                                this.allInsurerAccessSettings = cloneAllInsurerAccessSettings;
                                                            } else {
                                                                this.allInsurerAccessSettings = [...this.allInsurerAccessSettings, {
                                                                    ...defaultUserAgencyLogin,
                                                                    insurerId: userAgencyLogin.insurerId,
                                                                    datas: clonedData,
                                                                }];
                                                            }
                                                            this.forceUpdate();
                                                        }
                                                    }} 
                                                />)
                                            } else if(data && data.type === UserAgencyLoginInputType.Password) {
                                                return (
                                                <div style={{display: 'flex', flexDirection: 'row', flexWrap: 'nowrap', marginTop: 0, 'position': 'relative'}}>
                                                    <LabeledTextField key={`accessSettingPasswordForInsurer${userAgencyLogin.insurerId}-${userAgencyLoginIndex}`} type={this.showInsurerAccessSettingPassword[userAgencyLoginIndex] === true ? `text` : `password`}
                                                        label={L(data.name)} theme={myTheme} isDataLoaded={true} disabled={false} required={true}
                                                        customLabelStyles={{minWidth: '150px', width: '150px', marginTop: 0, background: 'none', color: additionalTheme.grey, fontSize: '14px', fontWeight: 'normal', border: 'none', paddingLeft: 0}}
                                                        customClassNames={`${classNames.insurerAccessSettingsBoxInput}`}
                                                        labelContainerCustomStyles={{flexDirection: 'column', marginTop: '10px', marginRight: '0px', marginLeft: '35px'}}
                                                        value={filteredInsurerAccessSettings && Array.isArray(filteredInsurerAccessSettings) && filteredInsurerAccessSettings[0] ? filteredInsurerAccessSettings[0].datas[dataIndex].value : ''} 
                                                        className={classNames.insurerAccessSettingPasswordInput} 
                                                        onChange={(event: FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string | undefined) => {
                                                            if(typeof newValue === 'string') {
                                                                const clonedData: any[] = [...userAgencyLogin.datas];
                                                                clonedData[dataIndex] = {...data, value: newValue};

                                                                if(filteredInsurerAccessSettings.length > 0) {
                                                                    const cloneAllInsurerAccessSettings: UserAgencyLoginDto[] = [];
                                                                    this.allInsurerAccessSettings.forEach((insurerAccessSetting: UserAgencyLoginDto) => {
                                                                        if(insurerAccessSetting.insurerId !== userAgencyLogin.insurerId || insurerAccessSetting.segment !== userAgencyLogin.segment) {
                                                                            cloneAllInsurerAccessSettings.push(insurerAccessSetting);
                                                                        } else {
                                                                            cloneAllInsurerAccessSettings.push({
                                                                                ...insurerAccessSetting,
                                                                                datas: clonedData,
                                                                            });
                                                                        }
                                                                    });
                                                                    this.allInsurerAccessSettings = cloneAllInsurerAccessSettings;
                                                                } else {
                                                                    this.allInsurerAccessSettings = [...this.allInsurerAccessSettings, {
                                                                        ...defaultUserAgencyLogin,
                                                                        insurerId: userAgencyLogin.insurerId,
                                                                        datas: clonedData,
                                                                    }];
                                                                }
                                                                this.forceUpdate();
                                                            }
                                                        }}
                                                    />
        
                                                    <Icon iconName='RedEye' className={classNames.inputIcon2} title={L('Hold to reveal password')} 
                                                        onMouseDown={() => this.toggleShowFilePassword(userAgencyLoginIndex, true)} onMouseUp={() => this.toggleShowFilePassword(userAgencyLoginIndex, false)}
                                                        style={{color: this.showInsurerAccessSettingPassword[userAgencyLoginIndex] ? 'green' : 'black'}}
                                                    />
                                                </div>)
                                            } else if(data && data.type === UserAgencyLoginInputType.Token) {
                                                return (
                                                <LabeledTextField key={`accessSettingTokenForInsurer${userAgencyLogin.insurerId}-${userAgencyLoginIndex}`} label={L(data.name)} isDataLoaded={true} disabled={this.asyncActionInProgress}
                                                    value={filteredInsurerAccessSettings && Array.isArray(filteredInsurerAccessSettings) && filteredInsurerAccessSettings[0] ? filteredInsurerAccessSettings[0].datas[dataIndex].value : ''}
                                                    customLabelStyles={{minWidth: '150px', width: '150px', marginTop: 0, background: 'none', color: additionalTheme.grey, fontSize: '14px', fontWeight: 'normal', border: 'none', paddingLeft: 0}}
                                                    customClassNames={`${classNames.insurerAccessSettingsBoxInput}`}
                                                    labelContainerCustomStyles={{flexDirection: 'column', marginTop: '10px'}}
                                                    onChange={(event: FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string | undefined) => {
                                                        if(typeof newValue === 'string') {
                                                            const clonedData: any[] = [...userAgencyLogin.datas];
                                                            clonedData[dataIndex] = {...data, value: newValue};

                                                            if(filteredInsurerAccessSettings.length > 0) {
                                                                const cloneAllInsurerAccessSettings: UserAgencyLoginDto[] = [];
                                                                this.allInsurerAccessSettings.forEach((insurerAccessSetting: UserAgencyLoginDto) => {
                                                                    if(insurerAccessSetting.insurerId !== userAgencyLogin.insurerId || insurerAccessSetting.segment !== userAgencyLogin.segment) {
                                                                        cloneAllInsurerAccessSettings.push(insurerAccessSetting);
                                                                    } else {
                                                                        cloneAllInsurerAccessSettings.push({
                                                                            ...insurerAccessSetting,
                                                                            datas: clonedData,
                                                                        });
                                                                    }
                                                                });
                                                                this.allInsurerAccessSettings = cloneAllInsurerAccessSettings;
                                                            } else {
                                                                this.allInsurerAccessSettings = [...this.allInsurerAccessSettings, {
                                                                    ...defaultUserAgencyLogin,
                                                                    insurerId: userAgencyLogin.insurerId,
                                                                    datas: clonedData,
                                                                }];
                                                            }
                                                            
                                                            this.forceUpdate();
                                                        }
                                                    }} 
                                                />)
                                            } else {
                                                return <></>;
                                            }
                                        })}
                                    </Stack>
                                })
                            }
                        </div>

                        <PrimaryButton theme={myTheme} style={{marginTop: 25}}
                            onClick={() => this.saveInsurerAccessSettings()} 
                            text={L('Save changes')} iconProps={{ iconName: 'none' }}
                            disabled={this.asyncActionInProgress || !hasInsurerAccessSettingsChanged}
                        />

                        {this.showMessageBar &&
                            <MessageBar messageBarType={this.messageBarType} isMultiline={false} className={classNames.messageBar} onDismiss={() => {
                                this.showMessageBar = false;
                                this.forceUpdate();
                            }}>
                                {`${this.messageBarText}`}
                            </MessageBar>
                        }
                    </PivotItem>
                }
            </Pivot>
        </>
    }
}