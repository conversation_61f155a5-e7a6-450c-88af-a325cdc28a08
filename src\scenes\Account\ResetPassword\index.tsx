import * as React from 'react';
import { inject, observer } from 'mobx-react';
import Stores from '../../../stores/storeIdentifier';
import AccountStore from '../../../stores/accountStore';
import { L } from '../../../lib/abpUtility';
import { Link, PrimaryButton, mergeStyleSets } from '@fluentui/react';
import Loading from '../../../components/Loading';
import { TextFieldBase } from '../../BaseComponents';
import { myTheme } from '../../../styles/theme';
import { createOrUpdateClassNames } from '../../BaseComponents/createOrUpdate';

enum Status {
  Normal,
  Error,
  Success,
  Loading,
}

export interface IProps {
  accountStore: AccountStore;
}

export interface IState {
  status: Status;
  password?: string | undefined;
  confirmPassword?: string | undefined;
  localMessage?: string | undefined;
}

const classNames = mergeStyleSets({
  verificate: {
    height: '80vh',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
  },
  success: {
    color: myTheme.palette.greenLight,
  },
  error: {
    color: myTheme.palette.red,
  },
  info: {
    color: myTheme.palette.themePrimary,
  },
  verificateTitle: {
    fontWeight: 'bold',
    paddingBottom: '8px',
    width: '200px',
    textAlign: 'center',
  },
  top5: {
    marginTop: '5px',
  },
});

@inject(Stores.AccountStore)
@observer
export class ResetPassword extends React.Component<IProps, IState> {
  state = {
    status: Status.Normal,
    password: undefined,
    confirmPassword: undefined,
    localMessage: undefined,
  };

  send = async () => {
    if (this.state.password === this.state.confirmPassword) {
      this.setState({ localMessage: undefined });
      this.setState({ status: Status.Loading });
      try {
        let parameters = new URLSearchParams(window.location.search);
        await this.props.accountStore.resetPassword({
          password: this.state.password,
          email: parameters.get('Email'),
          token: parameters.get('Token'),
        });
        this.setState({ status: Status.Success });
      } catch {
        this.setState({ status: Status.Error });
      }
    } else {
      this.setState({ localMessage: 'Passwords must be the same!' });
    }
  };

  render() {
    return (
      <div className={classNames.verificate}>
        {this.state.status === Status.Loading && <Loading />}
        {this.state.status === Status.Error && (
          <>
            <div className={`${classNames.error} ${classNames.verificateTitle}`}>{L('Error.')}</div>
            <div>{L('Some error occured.')}</div>
            <div>
              {L('Go to home page ')} <Link theme={myTheme} href="/user/login">{L('here')}</Link>.
            </div>
          </>
        )}
        {this.state.status === Status.Success && (
          <>
            <div className={`${classNames.success} ${classNames.verificateTitle}`}>{L('Success.')}</div>
            <div>{L('Contgratulations, your account has been confirmed.')}</div>
            <div>
              {L('Now you can login ')} <Link theme={myTheme} href="/user/login">{L('here')}</Link>.
            </div>
          </>
        )}
        {this.state.status === Status.Normal && (
          <>
            <div className={`${classNames.info} ${classNames.verificateTitle}`}>{L('Submit password.')}</div>

            <TextFieldBase
              type="password"
              label={L('Password')}
              isDataLoaded={true}
              onChange={(e, value) => {
                this.setState({ password: value });
              }}
              theme={myTheme}
            />
            <TextFieldBase
              type="password"
              label={L('Confirm Password')}
              isDataLoaded={true}
              onChange={(e, value) => {
                this.setState({ confirmPassword: value });
              }}
              theme={myTheme}
            />
            <div className={createOrUpdateClassNames.panelActions}>
              <PrimaryButton theme={myTheme} text={L('Send')} type={'submit'} onClick={ () => this.send() } className="form-btn" />
            </div>
          </>
        )}
        {this.state.localMessage && <div className={`${classNames.error} ${classNames.top5}`}>{this.state.localMessage}</div>}
      </div>
    );
  }
}

export default ResetPassword;