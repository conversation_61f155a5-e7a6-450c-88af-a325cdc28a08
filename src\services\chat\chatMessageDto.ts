export interface ChatMessageDto {
    authToken: string;
    user: string;
    message: string;
    threadId?: string;
}

export interface OutputChatMessageDto {
    id: string;
    type: Metadata;
    sequenceId: string;
    version: string;
    content: Content;
    senderDisplayName: string | null;
    createdOn: Date;
    sender: { id: string } | null;
    deletedOn: string | null;
    editedOn: string | null;
    metadata: Metadata;
    userId: number;
}

export interface OutputChatMessageResultDto {
    items: OutputChatMessageDto[];
    totalCount: number;
}

export interface Content {
    message: string | null;
    topic: string | null;
    participants: Participant[];
    initiator: Initiator;
}

export interface Initiator {
    id: string;
}

export interface Participant {
    user: Initiator;
    displayName: string;
    shareHistoryTime: Date;
}

export interface Metadata {
}