import 'famfamfam-flags/dist/sprite/famfamfam-flags.css';
import * as React from 'react';
import Stores from '../../stores/storeIdentifier';
import UserStore from '../../stores/userStore';
import { inject } from 'mobx-react';
import {DefaultButton, IContextualMenuItem, IContextualMenuProps, mergeStyleSets} from '@fluentui/react';
import { myTheme } from '../../styles/theme';


declare var abp: any;

export interface ILanguageSelectProps {
  userStore?: UserStore;
}

const classNames = mergeStyleSets({
  languageMenu: {
    minWidth: '20px',
    color: myTheme.palette.neutralDark,

  },
  languageWrapper: {
    color: myTheme.palette.neutralDark,
    border: 0,
    textTransform: 'uppercase',
    fontSize:'14px',
    fontWeight: 700,
    fontFamily: 'Segoe UI',
    minWidth: '40px',
    maxWidth: '40px',
    minHeight: '40px',
    maxHeight: '40px',
    selectors: {
      '& svg': {
        fontSize: 20
      }
    },
  },
  activeLanguage: {
    selectors: {
      '& .ms-ContextualMenu-itemText': {
        color: myTheme.palette.themePrimary,
      }
    }
  }
});

@inject(Stores.UserStore)
class LanguageSelect extends React.Component<ILanguageSelectProps> {
  get languages() {
    return abp.localization.languages.filter((val: any) => {
      return !val.isDisabled;
    });
  }

  async changeLanguage(languageName: string) {
    if(languageName !== abp.utils.getCookieValue('Abp.Localization.CultureName')) {
      await this.props.userStore!.changeLanguage(languageName).then((response: any) => {
        abp.utils.setCookieValue(
          'Abp.Localization.CultureName',
          languageName,
          new Date(new Date().getTime() + 5 * 365 * 86400000), //5 year
          abp.appPath
        );
    
        window.location.reload();
      }).catch((error: any) => {
        console.error(error);
        alert('Error');
      });
    }
  }

  get currentLanguage() {
    return abp.localization.currentLanguage.name;
  }

  getMenuItems = () => {
    let arr: any[] = []
    const selectedLanguage = abp.utils.getCookieValue('Abp.Localization.CultureName');
    this.languages.map((item: any) => (
      arr.push({
        key: item.name,
        text: item.displayName,
        onClick: () => this.changeLanguage(item.name),
        className: (selectedLanguage === item.name) ? `${classNames.activeLanguage}` : "",
        theme: myTheme,
      })
    ));
    return arr;
  }

  render() {
    let languagesItems = this.getMenuItems();
    let currentLanguage = abp.utils.getCookieValue('Abp.Localization.CultureName');
    const menuItems: IContextualMenuItem[] = languagesItems;
    const userDropdownMenu: IContextualMenuProps = {
      shouldFocusOnMount: true,
      className: `${classNames.languageMenu}`,
      items: menuItems,
    };

    return (
        <DefaultButton theme={myTheme} className={classNames.languageWrapper} menuProps={userDropdownMenu} text={currentLanguage}/>
    );
  }
}

export default LanguageSelect;
