import { isUserLoggedIn } from '../../utils/authUtils';
import { CrudServiceBase } from '../base/crudServiceBase';
import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { AgreementDto } from './dto/agreementDto';

export class AgreementsService extends CrudServiceBase<AgreementDto> {
    constructor() {
        super(Endpoint.Agreements);
        this.internalHttp = httpApi;
    }

    public async getAgreements(insurerName: string, calculationId: number, policyType: string, calculationType: string, calculationData: any) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetAgreements/?InsurerName=${insurerName}&CalculationId=${calculationId}&PolicyType=${policyType}&CalculationType=${calculationType}`), { params: calculationData });
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportAgreementsService: AgreementsService = new AgreementsService();
export default exportAgreementsService;