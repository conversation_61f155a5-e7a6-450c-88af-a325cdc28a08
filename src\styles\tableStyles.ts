import { mergeStyleSets} from '@fluentui/merge-styles';
import {additionalTheme, myTheme} from './theme';

export const classNames = mergeStyleSets({
  headerWrapper: {
    display: 'flex',
    justifyContent: 'space-between',
    padding: '20px 3% 0',
  },
  headerPivot: {
    display: 'flex',
    alignItems: 'center',
  },
  noMargin: {
    margin: 0,
    marginBottom: '5px',
    paddingRight: '10px',
  },
  mb2: {
    marginBottom: '0.5rem',
  },
  selectionDetails: {
    minHeight: '30px',
    height: 'auto',
    padding: '5px 3% 5px 60px',
    selectors: {
      '& > div': {
        height: 'auto',
        width: '100%',
      }
    }
  },
  tableWrapper: {
    selectors: {
      '& .ms.List-Cell': {
        backgroundColor: additionalTheme.white
      },
      '& .ms-DetailsHeader': {
        paddingTop: 0,
      },
      '& .ms-DetailsHeader-cell.ms-DetailsHeader-cellIsCheck .is-checked.ms-Check:before': {
        background: myTheme.palette.themePrimary
      }
    }
  },
  checkboxCell: {
    selectors: {
      '& .is-checked.ms-Check:before': {
        background: myTheme.palette.themePrimary
      }
    }
  }
});