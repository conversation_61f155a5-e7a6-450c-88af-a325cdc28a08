import * as React from 'react';
import { DefaultButton, mergeStyleSets, PrimaryButton } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import {additionalTheme, myTheme} from '../../styles/theme';
import Header from '../../components/Header';

export enum TextType {
    PrivacyPolicy,
    TermsOfUse
}

export interface IProps {
    type: TextType,
    text: string
}

export interface IState {
}

const classNames = mergeStyleSets({
    loginHeader: {
        marginBottom: '60px',
        backgroundColor: myTheme.palette.themePrimary,
        width: '100%',
        padding: '0px',
    },
    container: {
        paddingBottom: 90,
        width: '100vw',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
    },
    card: {
        display: 'flex',
        justifyContent: 'flex-start',
        flexDirection: "column",
        alignItems: 'center',
        height: '70vh',
        background: additionalTheme.white,
        maxWidth: 730,
        width: "50vw",
    },
    buttons: {
        height: '30vh',
    },
    cardPhone: {
        display: 'flex',
        justifyContent: 'center',
        flexDirection: "column",
        alignItems: 'center',
        background: additionalTheme.white,
    },
    cardBody: {
        overflow: "auto",
        paddingLeft: 15,
        paddingRight: 15,
    },
    footer: {
        width: "100%",
        display: "flex",
        justifyContent: 'flex-end',
        padding: '15px',
    },
    margin: {
        marginLeft: 5,
        marginRight: 5
    }
});

export class TextPage extends React.Component<IProps, IState> {
    render() {
        let navigation = "/account/privacy-policy";
        let navigationLabel = "Privacy policy";

        if (this.props.type === TextType.PrivacyPolicy) {
            navigation = "/account/terms-of-use";
            navigationLabel = "Terms of use";
        }

        let inner = <>
            <div className={classNames.cardBody} dangerouslySetInnerHTML={{ __html: this.props.text }} />
        </>;

        let buttons = <>
            <div className={classNames.footer}>
                <a href={navigation} className={classNames.margin}>
                    <DefaultButton theme={myTheme} text={L(navigationLabel)} type={'submit'} />
                </a>
                <a href="/user/login" className={classNames.margin}>
                    <PrimaryButton theme={myTheme} text={L('Back to Log In')} type={'submit'} />
                </a>
            </div>
        </>;

        if (window.innerWidth < 800) {
            return <div className={classNames.cardPhone}>{inner}</div>
        }

        return (
            <>
                <div className={classNames.loginHeader}>
                    <Header />
                </div>
                <div className={classNames.container}>
                    <div className={classNames.card}>
                        {inner}
                    </div>
                    <div className={classNames.buttons}>
                        {buttons}
                    </div>
                </div>
            </>
        );
    }
}

export default TextPage;
