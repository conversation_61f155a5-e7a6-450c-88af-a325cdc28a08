import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import { InsuranceCompanyTable } from './components/insuranceCompanyTable';
import InsurerStore from '../../stores/insurerStore';
import {L} from "../../lib/abpUtility";
import {mergeStyleSets} from "@fluentui/react";
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
        display: "flex",
        alignItems: "center",
  }
});

export interface IProps {
  searchStore: SearchStore;
  insurerStore: InsurerStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.InsurerStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  public render() {
    let items = this.props.insurerStore.dataSet ? this.props.insurerStore.dataSet.items : [];

    return (
      <>
        <div className={classNames.titleContainer}>
            <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Insurance Company List')}</h2>
        </div>
        <InsuranceCompanyTable
          searchText={this.props.searchStore.searchText}
          items={items}
          store={this.props.insurerStore}
          history={this.props.history}
        />
      </>
    );
  }
}

export default Index;