import { Checkbox, FontWeights, IChoiceGroupOption, IStackTokens, mergeStyleSets, MessageBar, MessageBarType, PrimaryButton, Spinner, SpinnerSize, Stack, Text } from "@fluentui/react";
import { inject } from "mobx-react";
import React from "react";
import { LabeledTextField } from "../../components/LabeledTextField";
import { L } from "../../lib/abpUtility";
import productAttributeService from "../../services/productAttribute/productAttributeService";
import ProductAttributeStore from "../../stores/productAttributeStore";
import Stores from "../../stores/storeIdentifier";
import { myTheme } from "../../styles/theme";
import { jsonViewer } from "../../utils/jsonViewerUtils";
import { enumToDropdownOptions, isJsonString, isStringNumeric, modifyFirstLetter } from "../../utils/utils";
import { Controls } from "./controls";
import { DropdownBase } from "./dropdownBase";
import { TextFieldBase } from "./textFieldBase";
import { ChoiceGroupBase } from "./ChoiceGroupBase";

const contentStyles = mergeStyleSets({
    container: {
        display: 'flex',
        flexFlow: 'column nowrap',
        alignItems: 'stretch',
    },
    header: [
        myTheme.fonts.large,
        {
            flex: '1 1 auto',
            color: myTheme.palette.neutralPrimary,
            display: 'flex',
            alignItems: 'center',
            fontWeight: FontWeights.semibold,
            padding: '12px 12px 14px 24px',
        },
    ],
    bodyLabel: {
        display: 'block',
        margin: '25px 0 5px 15px',
    },
    body: {
        flex: '4 4 auto',
        padding: '0px 15px 10px',
        overflowY: 'hidden',
        maxWidth: '100%',
        background: myTheme.palette.neutralQuaternaryAlt,
        selectors: {
            // ':last-child': {
            //     marginTop: '25px',
            // },
            'p': { 
                margin: '14px 0' 
            },
            'p:first-child': { 
                marginTop: 0 
            },
            'p:last-child': { 
                marginBottom: 0 
            },
            '.json': {
                fontSize: '16px',
            },
            '.json > .json__item': {
                display: 'block',
            },
            '.json__item': {
                display: 'none',
                marginTop: '10px',
                paddingLeft: '20px',
                // -webkit-user-select: 'none',
                //     -moz-user-select: 'none',
                //     -ms-user-select: 'none',
                        userSelect: 'none',
            },
            '.json__item--collapsible': {
                cursor: 'pointer',
                overflow: 'hidden',
                position: 'relative',
                selectors: {
                    '::before': {
                        content: "'+'",
                        position: 'absolute',
                        left: '5px',
                    },
                    '::after': {
                        backgroundColor: 'lightgrey',
                        content: "''",
                        height: '100%',
                        left: '9px',
                        position: 'absolute',
                        top: '26px',
                        width: '1px',
                    }
                }
            },
            '.json__item--collapsible:hover > .json__key, .json__item--collapsible:hover > .json__value': {
                textDecoration: 'underline',
            },
            '.json__toggle': {
                display: 'none',
            },
            '.json__toggle:checked ~ .json__item': {
                display: 'block',
            },
            '.json__key': {
                color: 'darkblue',
                display: 'inline',
            },
            '.json__key::after': {
                content: "': '",
            },
            '.json__value': {
                display: 'inline',
            },
            '.json__value--string': {
                color: 'green',
            },
            '.json__value--number': {
                color: 'blue',
            },
            '.json__value--boolean': {
                color: 'red',
            },
        },
    }
});

const classNames = mergeStyleSets({
    stackContainer: {
        width: 'auto',
        maxWidth: '90%',
        height: 'auto',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'flex-start',
        padding: '0 15px 20px',
        margin: '0 auto',
    },
    inputsWrapper: {
        background: myTheme.palette.neutralQuaternaryAlt,
        wdith: '100%',
        minWidth: '600px',
        maxWidth: '675px',
        marginRight: '20px',
        padding: '10px 15px',
        overflow: 'hidden',
    },
    conditionsWrapper: {
        borderBottom: `1px solid ${myTheme.palette.neutralPrimary}`,
    },
    conditionsConditionsWrapper: {
        minWidth: '500px',
        overflow: 'auto',
        background: myTheme.palette.neutralQuaternary,
        borderRadius: '3px',
        selectors: {
            '> div': {
                selectors: {
                    ':first-child': {
                        marginTop: '0 !important',
                    }
                }
            }
        }
    },
    jsonViewerWrapper: {
        wdith: '100%',
        minWidth: '500px',
    },
    jsonStringInput: {
        width: '100%',
        padding: '5px 2px',
        selectors: {
            '& .ms-TextField-field': {
                width: '100%',
            }
        }
    },
    loadSpinner: {
        position: 'absolute',
        top: -10,
        left: 20,
        display: 'inline-flex',
        marginTop: '30px',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    customChoiceGroup: {
        display: 'block',
    }
});

const verticalGapStackTokens: IStackTokens = {
    childrenGap: 10,
    padding: 10,
};

export interface IAdminJsonBuilderToolProps {
    activeAttribute?: any;
    productAttributeStore?: ProductAttributeStore;
}

export interface IAdminJsonBuilderToolState {
    resultJsonOutput: any;
    resultJsonString: string;
    jsonStringError: boolean;
    inputsValues: any;
    refreshAfterDelete: boolean;
    refreshJsonOutput: boolean;
    refreshInputsValues: boolean;
    conditionsCount: number;
    conditionsInputsCount: any;
    asyncActionInProgress: boolean;
    asyncActionMessage: string;
    messageBarType: MessageBarType;
    resultJsonStringSetFromUserFields: boolean;
    activeAttributeKey: string;
}

@inject(Stores.ProductAttributeStore)
export class AdminJsonBuilderTool extends React.Component<IAdminJsonBuilderToolProps, IAdminJsonBuilderToolState> {
    constructor(props: IAdminJsonBuilderToolProps) {
        super(props);

        this.state = {
            resultJsonOutput: undefined,
            resultJsonString: '',
            jsonStringError: false,
            inputsValues: {
                "defaultSection": true,
                "defaultType": null,
                "validationSection": false,
                "conditionsSection": true,
            },
            refreshAfterDelete: false,
            refreshJsonOutput: true,
            refreshInputsValues: false,
            conditionsCount: 0,
            conditionsInputsCount: {},
            asyncActionInProgress: false,
            asyncActionMessage: "",
            messageBarType: MessageBarType.success,
            resultJsonStringSetFromUserFields: false,
            activeAttributeKey: '',
        };
    }

    private validateJsonString(jsonString: string | undefined) {
        if(jsonString?.length === 0 || (!!jsonString && !isJsonString(jsonString))) {
            this.setState((prevState) => ({ ...prevState, resultJsonString: !!jsonString ? jsonString : "", jsonStringError: true }));
        } else {
            this.setState((prevState) => ({ ...prevState, resultJsonString: !!jsonString ? jsonString : "",
                                            jsonStringError: false, resultJsonOutput: JSON.parse(!!jsonString ? jsonString : ""),
                                            refreshInputsValues: true }));
        }   
    }

    private getDeeperLevel(obj: any): any[] {
        let result: any[] = [];

        for(let key in obj) {
            if(obj.hasOwnProperty(key)) {
                result.push({
                    key: key,
                    value: obj[key],
                    type: typeof obj[key]
                });
            }
        }
        return result;
    }

    private generateValuesFromJson() {
        let inputKeys: string[] = [];
        let inputValues: any[] = [];
        let inputTypes: string[] = [];
        let conditionsCount: number = 0;
        let conditionsInputsCount: any = {};

        function loop(obj: any, keys: any[], that: any, withoutKey?: boolean) {
            let nextLevel: any[] = that.getDeeperLevel(withoutKey === true ? obj : obj[keys[keys.length-1]]);

            nextLevel.forEach((element: any) => {
                if(Array.isArray(element.value)) {
                    conditionsInputsCount[conditionsCount-1] = 0;
                    element.value.forEach((el: any, index: number) => {
                        conditionsInputsCount[conditionsCount-1]++;
                        keys[2] = index;
                        loop(el, keys, that, true);
                    });
                } else if(element.type === 'object') {
                    loop(element.value, keys, that, keys[keys.length-1] ? true : false);
                } else {
                    const allowedKeysForDeeperInputKey: string[] = ['Key', 'Value', 'IsValueExactlyTheSame', 'HasAnyValue', 'IsValueADate', 'DateComparsionType', 'OtherDateForComparsion', 'KeyOfInputWithDate', 'KeyOfInputWithOtherDate'];
                    let inputKeyString: string = "";
                    keys.forEach((key: any, iterator: number) => {
                        if(iterator === 0) {
                            inputKeyString += modifyFirstLetter(key, 'toLowerCase');
                        } else if(iterator === 1 || (iterator > 1 && allowedKeysForDeeperInputKey.includes(element.key))) {
                            inputKeyString += `${key};`;
                        }
                    });
                    inputKeys.push(`${inputKeyString}${element.key}`);
                    inputValues.push(element.value);
                    inputTypes.push(element.type);
                }
            });
        }

        let setJsonStringFromUserFields: boolean = false;
        const { resultJsonStringSetFromUserFields, resultJsonString } = this.state;
        const { activeAttribute } = this.props;
        let jsonString: string = resultJsonString;

        if(resultJsonStringSetFromUserFields === false) {
            if(activeAttribute && activeAttribute.userFields) {
                activeAttribute.userFields.some((UserField: any) => {
                    if(UserField.Key === 'attr_json_props') {
                        jsonString = UserField.Value;
                        return true;
                    }
                    return false;
                });

                activeAttribute.userFields.some((UserField: any) => {
                    if(UserField.Key === 'key') {
                        this.setState((prevState) => ({ ...prevState, activeAttributeKey: UserField.Value }));
                        return true;
                    }
                    return false;
                });
            }

            setJsonStringFromUserFields = true;
        }

        let parsedJsonString = jsonString.length > 0 && isJsonString(jsonString) ? JSON.parse(jsonString) : {};
        
        for(let key in parsedJsonString) {
            if(parsedJsonString.hasOwnProperty(key)) {
                if(Array.isArray(parsedJsonString[key])) {
                    let tempKeyArr: any[] = [key];
                    // eslint-disable-next-line
                    parsedJsonString[key].forEach((el: any, index: number) => {
                        conditionsCount++;
                        tempKeyArr[1] = index;
                        loop(el, tempKeyArr, this, true);
                    });
                } else {
                    loop(parsedJsonString, [key], this);
                }
            }
        }
        
        if(setJsonStringFromUserFields === true) {
            this.setInputsValue(inputKeys, inputValues, inputTypes, 
                {refreshJsonOutput: false, refreshInputsValues: false, conditionsCount, conditionsInputsCount, resultJsonStringSetFromUserFields: true }
            );
        } else {
            this.setInputsValue(inputKeys, inputValues, inputTypes, 
                {refreshJsonOutput: false, refreshInputsValues: false, conditionsCount, conditionsInputsCount }
            );
        }
    }

    private deleteInputsValue(inputKeys: string[], refreshJsonOutput?: boolean) {
        const inputsValuesClone: any = {...this.state.inputsValues};
        inputKeys.forEach((inputKey: string) => {
            delete inputsValuesClone[inputKey];
        });

        this.setState((prevState) => ({ ...prevState, inputsValues: inputsValuesClone, refreshAfterDelete: true, refreshJsonOutput: refreshJsonOutput ? refreshJsonOutput : false }));
    }

    private setInputsValue(inputKey: string | string[], inputValue: any | any[], valueType?: string | string[], additionalPropsToSetInState?: any) {
        const inputsValuesClone: any = {...this.state.inputsValues};
        
        if(Array.isArray(inputKey) && Array.isArray(inputValue) && (Array.isArray(valueType) || !valueType)) {
            inputKey.forEach((key: string, index: number) => {
                if(valueType && !!valueType[index] && valueType[index] === 'number') {
                    inputsValuesClone[key] = Number(inputValue[index]);
                } else {
                    inputsValuesClone[key] = inputValue[index];
                }
            });
        } else if(!Array.isArray(inputKey)) {
            if(!!valueType && valueType === 'number') {
                inputsValuesClone[inputKey] = Number(inputValue);
            } else {
                inputsValuesClone[inputKey] = inputValue;
            }
        }

        this.setState((prevState) => ({
            ...prevState, 
            inputsValues: inputsValuesClone,
            ...additionalPropsToSetInState,
            refreshJsonOutput: additionalPropsToSetInState && additionalPropsToSetInState.refreshJsonOutput ? additionalPropsToSetInState.refreshJsonOutput : true,
        }));
    }

    private loopInputsValuesForSection(section: string, tempResultJsonOutput: any): any {
        const { inputsValues } = this.state;
        let keysToDelete: string[] = [];

        for(let key in inputsValues) {
            if(inputsValues.hasOwnProperty(key)) {
                let splittedKey = key.split(modifyFirstLetter(section, 'toLowerCase'));
                if(splittedKey.length > 1 && splittedKey[1] !== 'Section') {
                    splittedKey.shift();

                    splittedKey = splittedKey[0].split(';');
                    if(splittedKey.length > 1 && isStringNumeric(splittedKey[0])) {
                        let tempIndex = Number(splittedKey[0]);

                        splittedKey.shift();

                        if(!tempResultJsonOutput[section][tempIndex]) {
                            tempResultJsonOutput[section][tempIndex] = {};
                        }

                        if(splittedKey.length > 1 && isStringNumeric(splittedKey[0])) {
                            let tempIndex2 = Number(splittedKey[0]);
                            
                            splittedKey.shift();

                            if(!tempResultJsonOutput[section][tempIndex]["ConditionDetails"]) {
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"] = [];
                            }
                            
                            if(splittedKey[0] === "Key") {
                                if(!!inputsValues[key]) {
                                    tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2] = { "Key": inputsValues[key] };
                                } else {
                                    delete tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2];
                                }
                            }
                            
                            if(splittedKey[0] === "Value") {
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"].some((condition: any, conditionIndex: number) => {
                                    if(!!condition && !!condition["Key"] && tempIndex2 === conditionIndex) {
                                        if(!!tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2]) {
                                            tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2] = {...tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2], "Value": inputsValues[key] };
                                        }
                                        return true;
                                    }
                                    return false;
                                });
                            }

                            if(splittedKey[0] === "IsValueExactlyTheSame" && !!tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2]) {
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2] = {...tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2], "IsValueExactlyTheSame": inputsValues[key] };
                            }
                            
                            if(splittedKey[0] === "HasAnyValue" && !!tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2]) {
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2] = {...tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2], "HasAnyValue": inputsValues[key] };
                            }

                            if(splittedKey[0] === "HasAnyValue" && !!tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2]) {
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2] = {...tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2], "HasAnyValue": inputsValues[key] };
                            }

                            if(splittedKey[0] === "IsValueADate" && !!tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2]) {
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2] = {...tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2], "IsValueADate": inputsValues[key] };
                            }

                            if(splittedKey[0] === "DateComparsionType" && !!tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2]) {
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2] = {...tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2], "DateComparsionType": inputsValues[key] };
                            }

                            if(splittedKey[0] === "OtherDateForComparsion" && !!tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2]) {
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2] = {...tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2], "OtherDateForComparsion": inputsValues[key] };
                            }

                            if(splittedKey[0] === "KeyOfInputWithDate" && !!tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2]) {
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2] = {...tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2], "KeyOfInputWithDate": inputsValues[key] };
                            }

                            if(splittedKey[0] === "KeyOfInputWithOtherDate" && !!tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2]) {
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2] = {...tempResultJsonOutput[section][tempIndex]["ConditionDetails"][tempIndex2], "KeyOfInputWithOtherDate": inputsValues[key] };
                            }

                            if(tempResultJsonOutput[section][tempIndex]["ConditionDetails"].length > this.state.conditionsInputsCount[tempIndex]) {
                                keysToDelete.push(`conditions${tempIndex};${tempIndex2};Key`);
                                keysToDelete.push(`conditions${tempIndex};${tempIndex2};Value`);
                                keysToDelete.push(`conditions${tempIndex};${tempIndex2};IsValueExactlyTheSame`);
                                keysToDelete.push(`conditions${tempIndex};${tempIndex2};HasAnyValue`);
                                keysToDelete.push(`conditions${tempIndex};${tempIndex2};IsValueADate`);
                                keysToDelete.push(`conditions${tempIndex};${tempIndex2};DateComparsionType`);
                                keysToDelete.push(`conditions${tempIndex};${tempIndex2};OtherDateForComparsion`);
                                keysToDelete.push(`conditions${tempIndex};${tempIndex2};KeyOfInputWithDate`);
                                keysToDelete.push(`conditions${tempIndex};${tempIndex2};KeyOfInputWithOtherDate`);
                                tempResultJsonOutput[section][tempIndex]["ConditionDetails"].splice(this.state.conditionsInputsCount[tempIndex], 1);

                                if(tempResultJsonOutput[section][tempIndex]["ConditionDetails"].length === 0) {
                                    delete tempResultJsonOutput[section][tempIndex]["ConditionDetails"];
                                }
                            } else {
                                if(inputsValues[`conditions${tempIndex};${tempIndex2};IsValueExactlyTheSame`] === true && inputsValues[`conditions${tempIndex};${tempIndex2};DateComparsionType`]) {
                                    keysToDelete.push(`conditions${tempIndex};${tempIndex2};DateComparsionType`);
                                }

                                if(inputsValues[`conditions${tempIndex};${tempIndex2};DateComparsionType`] !== 'betweenValueAndDate') {
                                    keysToDelete.push(`conditions${tempIndex};${tempIndex2};OtherDateForComparsion`);
                                }

                                if(!inputsValues[`conditions${tempIndex};${tempIndex2};IsValueADate`] || 
                                    (inputsValues[`conditions${tempIndex};${tempIndex2};Value`] && inputsValues[`conditions${tempIndex};${tempIndex2};Value`].split('_')[0] !== 'KEY')
                                ) {
                                    keysToDelete.push(`conditions${tempIndex};${tempIndex2};KeyOfInputWithDate`);
                                }

                                if(!inputsValues[`conditions${tempIndex};${tempIndex2};IsValueADate`] || 
                                    (inputsValues[`conditions${tempIndex};${tempIndex2};OtherDateForComparsion`] && inputsValues[`conditions${tempIndex};${tempIndex2};OtherDateForComparsion`].split('_')[0] !== 'KEY')
                                ) {
                                    keysToDelete.push(`conditions${tempIndex};${tempIndex2};KeyOfInputWithOtherDate`);
                                }
                            }
                        } else {
                            tempResultJsonOutput[section][tempIndex][modifyFirstLetter(splittedKey.join(), 'toUpperCase')] = inputsValues[key];
                        }
                    } else {
                        if(!!inputsValues[key]) {
                            tempResultJsonOutput[section][modifyFirstLetter(splittedKey.join(), 'toUpperCase')] = inputsValues[key];
                        } else {
                            delete tempResultJsonOutput[section][modifyFirstLetter(splittedKey.join(), 'toUpperCase')];
                        }
                    }
                }
            }
        }

        if(keysToDelete.length > 0) {
            this.deleteInputsValue(keysToDelete);
        }

        return tempResultJsonOutput;
    }

    private generateJsonFromValues() {
        const { inputsValues, refreshAfterDelete, resultJsonStringSetFromUserFields } = this.state;
        let tempResultJsonOutput: any = {};

        if(inputsValues['defaultSection'] === true) {
            tempResultJsonOutput["Default"] = {};
            tempResultJsonOutput = this.loopInputsValuesForSection("Default", tempResultJsonOutput);
        }
        if(inputsValues['validationSection'] === true) {
            tempResultJsonOutput["Validation"] = {};
            tempResultJsonOutput = this.loopInputsValuesForSection("Validation", tempResultJsonOutput);
        }
        if(inputsValues['conditionsSection'] === true) {
            tempResultJsonOutput["Conditions"] = [];
            tempResultJsonOutput = this.loopInputsValuesForSection("Conditions", tempResultJsonOutput);
        }

        if(resultJsonStringSetFromUserFields === true) {
            this.setState((prevState) => ({ ...prevState, resultJsonOutput: tempResultJsonOutput, 
                resultJsonString: JSON.stringify(tempResultJsonOutput, (key, value) => { if(typeof value !== 'undefined') return value; }), 
                refreshJsonOutput: refreshAfterDelete ? true : false,
                refreshAfterDelete: false, jsonStringError: false }));
        } else {
            this.setState((prevState) => ({ ...prevState, resultJsonOutput: tempResultJsonOutput, 
                refreshJsonOutput: refreshAfterDelete ? true : false,
                refreshAfterDelete: false, jsonStringError: false }));
        }
    }

    private async saveJsonStringAsUserField(attrProperty: any) {
        if(attrProperty && attrProperty.userFields) {
            this.setState((prevState) => ({ ...prevState, asyncActionInProgress: true }));
    
            let indexOfJsonProps: number = -1;
            attrProperty.userFields.some((userField: any, index: number) => {
                if(userField.Key === 'attr_json_props') {
                    indexOfJsonProps = index;
                    return true;
                }
                return false;
            });
    
            let requestBody: any = [
                {
                    "operationType": indexOfJsonProps >= 0 ? "Replace" : "Add",
                    "value": indexOfJsonProps >= 0 ? this.state.resultJsonString : {
                        "Key": "attr_json_props",
                        "Value": this.state.resultJsonString
                    },
                    "path": `/UserFields/${indexOfJsonProps >= 0 ? indexOfJsonProps + '/Value' : '-'}`
                }
            ];
    
            let response = await productAttributeService.saveUserField(attrProperty.ProductAttributeId, requestBody);
            await this.props.productAttributeStore?.getAll(this.props.productAttributeStore.defaultRequest);
    
            this.setState((prevState) => ({ 
                ...prevState,
                asyncActionInProgress: false, 
                asyncActionMessage: response.status === 200 ? L('Success') : L('Something went wrong, check response in dev console.'),
                messageBarType: response.status === 200 ? MessageBarType.success : MessageBarType.error,
            }));
        }
    }
    
    private resetEverything() {
        if(window.confirm(L("Are you sure?"))) {
            this.setState((prevState) => ({
                resultJsonOutput: undefined,
                resultJsonString: '',
                jsonStringError: false,
                inputsValues: {
                    "defaultSection": true,
                    "defaultType": null,
                    "validationSection": false,
                    "conditionsSection": true,
                },
                refreshJsonOutput: true,
                refreshInputsValues: true,
                conditionsCount: 0,
                conditionsInputsCount: {},
            }));
        }
    }

    render() {
        const { activeAttribute } = this.props;
        const { resultJsonOutput, resultJsonString, jsonStringError, inputsValues, refreshJsonOutput, refreshInputsValues,
            conditionsCount, conditionsInputsCount, asyncActionInProgress, asyncActionMessage, messageBarType,
            resultJsonStringSetFromUserFields } = this.state;

        if(refreshJsonOutput) {
            this.generateJsonFromValues();
        }

        if(refreshInputsValues || resultJsonStringSetFromUserFields === false) {
            this.generateValuesFromJson();
        }

        let conditionsInputs: JSX.Element[] = [];
        let conditionsConditionsInputs: any = {};

        for(let i = 0; i < conditionsCount; i++) {
            if(!conditionsConditionsInputs[i]) {
                conditionsConditionsInputs[i] = [] as JSX.Element[];
            }

            for(let j = 0; j < conditionsInputsCount[i]; j++) {
                conditionsConditionsInputs[i].push(
                    <Stack className={`${classNames.inputsWrapper} ${classNames.conditionsConditionsWrapper}`} tokens={verticalGapStackTokens} style={{paddingLeft: '25px'}}>
                        <LabeledTextField theme={myTheme} label={L('Key')} value={inputsValues[`conditions${i};${j};Key`]} customWidth={300}
                            isDataLoaded={true} onChange={(e, value) => { 
                                if(!value || value.length === 0) {
                                    this.deleteInputsValue([
                                        `conditions${i};${j};Value`, `conditions${i};${j};IsValueExactlyTheSame`, `conditions${i};${j};HasAnyValue`,
                                        `conditions${i};${j};IsValueADate`, `conditions${i};${j};DateComparsionType`, `conditions${i};${j};OtherDateForComparsion`
                                    ]);
                                }
                                this.setInputsValue(`conditions${i};${j};Key`, value); 
                            }} 
                            customLabelStyles={{width: 150, minWidth: 150}} />
                        
                        <LabeledTextField theme={myTheme} label={L('Value')} value={inputsValues[`conditions${i};${j};Value`]} customWidth={300} 
                            disabled={!inputsValues[`conditions${i};${j};Key`] || inputsValues[`conditions${i};${j};Key`].length === 0}
                            isDataLoaded={true} onChange={(e, value) => { this.setInputsValue(`conditions${i};${j};Value`, value); }}
                            customLabelStyles={{width: 150, minWidth: 150}} />

                        <Checkbox disabled={!inputsValues[`conditions${i};${j};Key`] || inputsValues[`conditions${i};${j};Key`].length === 0}
                            theme={myTheme} checked={inputsValues[`conditions${i};${j};IsValueExactlyTheSame`]} label={L('Value must be exactly the same')} 
                            onChange={(e: any, value: any) => { this.setInputsValue(`conditions${i};${j};IsValueExactlyTheSame`, value); }} />

                        <Checkbox disabled={!inputsValues[`conditions${i};${j};Key`] || inputsValues[`conditions${i};${j};Key`].length === 0}
                            theme={myTheme} checked={inputsValues[`conditions${i};${j};HasAnyValue`]} label={L('Has any value')} 
                            onChange={(e: any, value: any) => { this.setInputsValue(`conditions${i};${j};HasAnyValue`, value); }} />
                            
                        <Checkbox disabled={!inputsValues[`conditions${i};${j};Key`] || inputsValues[`conditions${i};${j};Key`].length === 0 || 
                                !inputsValues[`conditions${i};${j};Value`] || !isNaN(inputsValues[`conditions${i};${j};Value`])
                            }
                            theme={myTheme} checked={inputsValues[`conditions${i};${j};IsValueADate`]} label={L('Value is a date')} 
                            onChange={(e: any, value: any) => { this.setInputsValue(`conditions${i};${j};IsValueADate`, value); }} />

                        {(inputsValues[`conditions${i};${j};IsValueADate`] && inputsValues[`conditions${i};${j};Value`] && inputsValues[`conditions${i};${j};Value`].split('_')[0] === 'KEY') &&
                            <LabeledTextField theme={myTheme} label={L('Key of input with date')} value={inputsValues[`conditions${i};${j};KeyOfInputWithDate`]} customWidth={300} 
                                disabled={!inputsValues[`conditions${i};${j};Key`] || inputsValues[`conditions${i};${j};Key`].length === 0}
                                isDataLoaded={true} onChange={(e, value) => { this.setInputsValue(`conditions${i};${j};KeyOfInputWithDate`, value); }}
                                customLabelStyles={{width: 150, minWidth: 150}} />
                        }

                        {(inputsValues[`conditions${i};${j};IsValueADate`] && !inputsValues[`conditions${i};${j};IsValueExactlyTheSame`]) &&
                            <ChoiceGroupBase label={L("Date comparsion type")} value={inputsValues[`conditions${i};${j};DateComparsionType`]}
                                disabled={!inputsValues[`conditions${i};${j};Key`] || inputsValues[`conditions${i};${j};Key`].length === 0} 
                                options={[
                                    { key: 'beforeValue', text: L('Date before value') }, 
                                    { key: 'afterValue', text: L('Date after value') },
                                    { key: 'betweenValueAndDate', text: L('Date between value and other date') },
                                ]}
                                onChange={(option: IChoiceGroupOption | undefined) => { 
                                    if(option) {
                                        this.setInputsValue(`conditions${i};${j};DateComparsionType`, option?.key);
                                    }
                                }}
                                customContainerClassNames={classNames.customChoiceGroup}
                            />
                        }

                        {inputsValues[`conditions${i};${j};DateComparsionType`] === 'betweenValueAndDate' &&
                            <LabeledTextField theme={myTheme} label={L('Other date for comparsion')} value={inputsValues[`conditions${i};${j};OtherDateForComparsion`]} customWidth={300} 
                                disabled={!inputsValues[`conditions${i};${j};Key`] || inputsValues[`conditions${i};${j};Key`].length === 0}
                                isDataLoaded={true} onChange={(e, value) => { this.setInputsValue(`conditions${i};${j};OtherDateForComparsion`, value); }}
                                customLabelStyles={{width: 150, minWidth: 150}} />
                        }

                        {(inputsValues[`conditions${i};${j};OtherDateForComparsion`] && inputsValues[`conditions${i};${j};OtherDateForComparsion`].split('_')[0] === 'KEY') &&
                            <LabeledTextField theme={myTheme} label={L('Key of input with other date')} value={inputsValues[`conditions${i};${j};KeyOfInputWithOtherDate`]} customWidth={300} 
                                disabled={!inputsValues[`conditions${i};${j};Key`] || inputsValues[`conditions${i};${j};Key`].length === 0}
                                isDataLoaded={true} onChange={(e, value) => { this.setInputsValue(`conditions${i};${j};KeyOfInputWithOtherDate`, value); }}
                                customLabelStyles={{width: 150, minWidth: 150}} />
                        }
                    </Stack>
                );
            }
            
            conditionsInputs.push(
                <Stack className={`${classNames.inputsWrapper} ${classNames.conditionsWrapper}`} tokens={verticalGapStackTokens} style={{paddingLeft: '25px'}}>
                    <Stack horizontal>
                        <PrimaryButton key="button1" theme={myTheme} text={'+'} type={'button'} className="form-btn" style={{marginRight: '20px'}}
                                        onClick={() => this.setState((prevState) => {
                                            const conditionsInputsCountClone = {...prevState.conditionsInputsCount};
                                            if(conditionsInputsCountClone[i]) {
                                                conditionsInputsCountClone[i]++;
                                            } else {
                                                conditionsInputsCountClone[i] = 1;
                                            }

                                            return { ...prevState, conditionsInputsCount: conditionsInputsCountClone }; 
                                        })} />
                        <PrimaryButton key="button2" theme={myTheme} text={'-'} type={'button'} className="form-btn" style={{marginRight: '20px'}}
                                        onClick={() => this.setState((prevState) => {
                                            const conditionsInputsCountClone = {...prevState.conditionsInputsCount};
                                            if(conditionsInputsCountClone[i]) {
                                                conditionsInputsCountClone[i] = conditionsInputsCountClone[i] - 1 >= 0 ? conditionsInputsCountClone[i] - 1 : 0;
                                            } else {
                                                conditionsInputsCountClone[i] = 0;
                                            }

                                            return { ...prevState, conditionsInputsCount: conditionsInputsCountClone, refreshJsonOutput: true }; 
                                        } )} />
                    </Stack>

                    {conditionsConditionsInputs[i]}
                    
                    <LabeledTextField theme={myTheme} label={L('Priority')} value={inputsValues[`conditions${i};Priority`]} type="number" customWidth={300}
                                            isDataLoaded={true} onChange={(e, value) => { this.setInputsValue(`conditions${i};Priority`, value, "number"); }}
                                            customLabelStyles={{width: 150, minWidth: 150}} />

                    <LabeledTextField theme={myTheme} label={L('Default value')} value={inputsValues[`conditions${i};DefaultValue`]} customWidth={300}
                                            isDataLoaded={true} onChange={(e, value) => { this.setInputsValue(`conditions${i};DefaultValue`, value); }}
                                            customLabelStyles={{width: 150, minWidth: 150}} />

                    <Checkbox disabled={false} theme={myTheme} checked={inputsValues[`conditions${i};IsHidden`]} label={L('Is hidden')} 
                        onChange={(e: any, value: any) => { this.setInputsValue(`conditions${i};IsHidden`, value); }} />

                    <Checkbox disabled={false} theme={myTheme} checked={inputsValues[`conditions${i};IsDisabled`]} label={L('Is disabled')} 
                        onChange={(e: any, value: any) => { this.setInputsValue(`conditions${i};IsDisabled`, value); }} />

                    <Checkbox disabled={false} theme={myTheme} checked={inputsValues[`conditions${i};ExcludeInMobileApp`]} label={L('Exclude in the mobile application')} 
                        onChange={(e: any, value: any) => { this.setInputsValue(`conditions${i};ExcludeInMobileApp`, value); }} />

                    <Checkbox disabled={false} theme={myTheme} checked={inputsValues[`conditions${i};ForceOmitCheckIfInputsChagnedManually`]} label={L('Force "omit check if inputs chagned manually"')} 
                        onChange={(e: any, value: any) => { this.setInputsValue(`conditions${i};ForceOmitCheckIfInputsChagnedManually`, value); }} />
                </Stack>
            );
        }

        return <Stack horizontal className={classNames.stackContainer}>
                {asyncActionInProgress && 
                    <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
                }

                <Stack className={classNames.inputsWrapper} tokens={verticalGapStackTokens}>
                    {activeAttribute && <>
                            <Text variant="mediumPlus" style={{marginBottom: 10, textAlign: 'center'}}>
                                {L(`You are working in context of attribute labeled as:`)}
                                <br />
                                <strong>{activeAttribute.attrName}</strong>
                                <br />
                                <p>{L('Current Key')}: <strong><i>{this.state.activeAttributeKey}</i></strong></p>
                            </Text>

                            <PrimaryButton theme={myTheme} text={L('Save JSON string as User Field')} style={{marginBottom: 10}} disabled={asyncActionInProgress || !activeAttribute}
                                type={'button'} className="form-btn" onClick={() => this.saveJsonStringAsUserField(activeAttribute) } />
                        
                            {(asyncActionMessage && asyncActionMessage.length > 0) && 
                                <MessageBar
                                    messageBarType={messageBarType}
                                    onDismiss={ () => this.setState((prevState) => ({ ...prevState, asyncActionMessage: "" })) }
                                    dismissButtonAriaLabel={L("Close")}
                                    style={{whiteSpace: 'pre-line'}}
                                    styles={{ root: { width: 'fit-content', marginBottom: 10 } }}
                                >
                                    {asyncActionMessage}
                                </MessageBar>
                            }
                        </>
                    }

                    <PrimaryButton theme={myTheme} text={L('Reset everything')} style={{marginBottom: '15px', background: myTheme.palette.red}}
                        type={'button'} className="form-btn" onClick={() => this.resetEverything() } />

                    <Checkbox disabled={false} theme={myTheme} checked={!!inputsValues['defaultSection'] ? inputsValues['defaultSection'] : false} label={L('"Default" section')} 
                        onChange={(e: any, value: any) => { this.setInputsValue('defaultSection', value); }} />

                    { inputsValues['defaultSection'] && 
                        <Stack className={classNames.inputsWrapper} tokens={verticalGapStackTokens} style={{paddingLeft: '25px', minWidth: 550}}>
                            <Checkbox disabled={false} theme={myTheme} checked={!!inputsValues['defaultExcludeInMobileApp'] ? inputsValues['defaultExcludeInMobileApp'] : false} 
                                label={L('Exclude in the mobile application')} onChange={(e: any, value: any) => { this.setInputsValue('defaultExcludeInMobileApp', value); }} />

                            <Checkbox disabled={false} theme={myTheme} checked={!!inputsValues['defaultIsHidden'] ? inputsValues['defaultIsHidden'] : false} label={L('Is hidden')} 
                                onChange={(e: any, value: any) => { this.setInputsValue('defaultIsHidden', value); }} />

                            <Checkbox disabled={false} theme={myTheme} checked={!!inputsValues['defaultIsDisabled'] ? inputsValues['defaultIsDisabled'] : false} label={L('Is disabled')} 
                                onChange={(e: any, value: any) => { this.setInputsValue('defaultIsDisabled', value); }} />

                            <Checkbox disabled={false} theme={myTheme} checked={inputsValues['defaultForceOmitCheckIfInputsChagnedManually']} label={L('Force "omit check if inputs chagned manually"')} 
                                onChange={(e: any, value: any) => { this.setInputsValue(`defaultForceOmitCheckIfInputsChagnedManually`, value); }} />

                            <DropdownBase label={L('Type')} options={ enumToDropdownOptions(Controls, true, false, "string") }
                                    value={inputsValues['defaultType']} disabled={false} isDataLoaded={true} /* customDropdownWidth="150px" */
                                    customLabelStyles={{minWidth: '150px', width: '150px'}}
                                    customDropdownWidth="150px"
                                    onChange={(e: string | number | undefined) => {
                                        if(inputsValues['defaultType'] !== e) {
                                            this.setInputsValue('defaultType', e);
                                        }
                                    }} />

                            <LabeledTextField theme={myTheme} label={L('Default value')} value={!!inputsValues['defaultDefaultValue'] ? inputsValues['defaultDefaultValue'] : ''} 
                                            isDataLoaded={true} onChange={(e, value) => { this.setInputsValue('defaultDefaultValue', value); }}
                                            customLabelStyles={{width: 150, minWidth: 150}} />
                        </Stack>
                    }

                    <Checkbox disabled={true} theme={myTheme} checked={!!inputsValues['validationSection'] ? inputsValues['validationSection'] : false} 
                        label={L('"Validation" section')} onChange={(e: any, value: any) => { this.setInputsValue('validationSection', value); }} />

                    { inputsValues['validationSection'] && 
                        <Stack className={classNames.inputsWrapper} tokens={verticalGapStackTokens} style={{paddingLeft: '25px'}}>
                            <Checkbox disabled={false} theme={myTheme} checked={!!inputsValues['validationNumber'] ? inputsValues['validationNumber'] : false} label={L('Type number')} 
                                onChange={(e: any, value: any) => { this.setInputsValue('validationNumber', value); }} />
                            
                            <Checkbox disabled={false} theme={myTheme} checked={!!inputsValues['validationMandatory'] ? inputsValues['validationMandatory'] : false} label={L('Mandatory')} 
                                onChange={(e: any, value: any) => { this.setInputsValue('validationMandatory', value); }} />

                            <LabeledTextField theme={myTheme} label={L('Min length')} value={!!inputsValues['validationMinlength'] ? inputsValues['validationMinlength'] : ''} type="number"
                                            customLabelStyles={{width: "150px", minWidth: "150px"}} isDataLoaded={true} onChange={(e, value) => { this.setInputsValue('validationMinlength', value, "number"); }} />
                                            
                            <LabeledTextField theme={myTheme} label={L('Max length')} value={!!inputsValues['validationMaxlength'] ? inputsValues['validationMaxlength'] : ''} type="number" 
                                            customLabelStyles={{width: "150px", minWidth: "150px"}} isDataLoaded={true} onChange={(e, value) => { this.setInputsValue('validationMaxlength', value, "number"); }} />
                        </Stack>
                    }

                    <Checkbox disabled={false} theme={myTheme} checked={!!inputsValues['conditionsSection'] ? inputsValues['conditionsSection'] : false} label={L('"Conditions" section')} 
                        onChange={(e: any, value: any) => { this.setInputsValue('conditionsSection', value); }} />

                    { inputsValues['conditionsSection'] && 
                        <Stack className={classNames.inputsWrapper} tokens={verticalGapStackTokens} style={{paddingLeft: '25px'}}>
                            <Stack horizontal horizontalAlign="center" style={{borderBottom: `1px solid ${myTheme.palette.neutralDark}`, paddingBottom: '10px'}}>
                                <PrimaryButton theme={myTheme} text={'+'} type={'button'} className="form-btn" style={{marginRight: '20px'}}
                                                onClick={() => this.setState((prevState) => ({ ...prevState, conditionsCount: prevState.conditionsCount + 1 })) } />
                                <PrimaryButton theme={myTheme} text={'-'} type={'button'} className="form-btn" style={{marginRight: '20px'}}
                                                onClick={() => this.setState((prevState) => {
                                                    const newConditionCount: number = prevState.conditionsCount - 1 >= 0 ? prevState.conditionsCount - 1 : 0;
                                                    const newInputsValues: any = {};

                                                    for(let key in prevState.inputsValues) {
                                                        if(prevState.inputsValues.hasOwnProperty(key)) {
                                                            if(!key.includes(`conditions${prevState.conditionsCount - 1}`)) {
                                                                newInputsValues[key] = prevState.inputsValues[key];
                                                            }
                                                        }
                                                    }

                                                    return { ...prevState, conditionsCount: newConditionCount, inputsValues: newInputsValues, refreshJsonOutput: true };
                                                } )} />
                            </Stack>
                            
                            {conditionsInputs}
                        </Stack>
                    }
                </Stack>

                <Stack.Item className={classNames.jsonViewerWrapper}>
                    <div className={contentStyles.body}>
                        <TextFieldBase type="text" label={L('JSON string:')} theme={myTheme} value={resultJsonString} multiline={true} rows={5}
                            isDataLoaded={true} className={classNames.jsonStringInput} errorMessage={jsonStringError ? L('JSON string is not valid.') : ''}
                            onChange={(e, value) => { this.validateJsonString(value); }}
                        />
                    </div>

                    <Text className={contentStyles.bodyLabel}>{L('JSON preivew:')}</Text>
                    <div className={contentStyles.body} 
                        dangerouslySetInnerHTML={{__html: jsonViewer(resultJsonOutput, true)}}>
                    </div>
                </Stack.Item>
            </Stack>
    }
}