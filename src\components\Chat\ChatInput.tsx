import React, { useState } from 'react';
import IconSend from './img/icon_send.svg'
import { HubMessageTypeEnum } from '../../services/chat/hubMessageTypeEnums';

interface IChatInput {
    sendMessage: (message: string, time: string) => void;
    sendActionNotification: (actionType: HubMessageTypeEnum) => void;
}
const ChatInput: React.FC<IChatInput> = (props) => {
    const [message, setMessage] = useState<string>('');
    const [prevNotificationType, setPrevNotificationType] = useState<HubMessageTypeEnum>(HubMessageTypeEnum.EndTyping);

    const onSubmit = (e: any) => {
        e.preventDefault();
        let time = new Date().toString();

        const isMessageProvided = message && message !== '';

        if (isMessageProvided) {
            props.sendMessage(message, time);
            setMessage('');
        } else {
            console.warn('Please insert a message.');
        }
    }

    const onMessageUpdate = (e: any) => {
        setMessage(e.target.value);

        if(!!e.target.value && e.target.value !== message && prevNotificationType !== HubMessageTypeEnum.StartTyping) {
            setPrevNotificationType(HubMessageTypeEnum.StartTyping);
        } else if((!e.target.value || e.target.value === '') && prevNotificationType === HubMessageTypeEnum.StartTyping) {
            setPrevNotificationType(HubMessageTypeEnum.EndTyping);
        }
    }

    return (
        <div className='chat__input'>
            <form className='chat__input--form' onSubmit={onSubmit}>
                <br />
                <input type="text" id="message" name="message" className='chat__input--message' value={message}
                    onChange={onMessageUpdate} 
                />
                <div className="chat__button--custom-icon--container" onClick={onSubmit}>
                    <img className='chat__button--custom-icon' src={IconSend} alt="" />
                </div>
            </form>
        </div>
    )
};

export default ChatInput;