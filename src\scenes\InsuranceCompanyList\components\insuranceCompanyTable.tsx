import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { InsuranceCompanyPanel } from './insuranceCompanyPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { RouterPath } from "../../../components/Router/router.config";
import { Link } from '@fluentui/react';
import { InsurerDto } from "../../../services/insurer/dto/insurerDto";
import { myTheme } from "../../../styles/theme";

export class InsuranceCompanyTable extends FluentTableBase<InsurerDto> {
  getItemDisplayNameOf(item: InsurerDto): string {
    return item.name
  }

  getColumns(): ITableColumn[] {
    return InsuranceCompanyTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Name'),
        fieldName: 'name',
        onRender: (item: any): any => {
          // if (!isGranted("Management.User")) {
          //   return item.Name;
          // }
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.InsuranceCompany}/${item.id}`);
                      }} 
                        href={`/${RouterPath.InsuranceCompany}/${item.id}`}>
                  {item.name}
                </Link>
        }
      },
      {
        name: L('Full name'),
        fieldName: 'fullName',
      },
      {
        name: L('Phone number'),
        fieldName: 'phoneNumber',
      },
      {
        name: L('Address'),
        fieldName: 'address',
        // onRender: (item: InsurerDto) => {
        //   return item.published ? (
        //     <span style={{ color: '#fff', backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
        //   ) : (
        //     <span>{L('No')}</span>
        //   );
        // }
      },
      {
        name: L('Agent'),
        fieldName: 'agent',
        minWidth: 100,
        maxWidth: 100,
        onRender: (item: InsurerDto) => {
          return item.isAgentSales ? (
            <span style={{ color: '#fff', backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
          ) : (
            <span>{L('No')}</span>
          );
        }
      },
      {
        name: L('Direct'),
        fieldName: 'direct',
        minWidth: 100,
        maxWidth: 100,
        onRender: (item: InsurerDto) => {
          return item.isDirectSales ? (
            <span style={{ color: '#fff', backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
          ) : (
            <span>{L('No')}</span>
          );
        }
      }
    ];
  }

  getTitle(): string {
    return L('Insurance company list');
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <InsuranceCompanyPanel
      {...props}
    />
  }
}