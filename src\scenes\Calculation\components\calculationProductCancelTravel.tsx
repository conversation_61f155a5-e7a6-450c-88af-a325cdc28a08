import { mergeStyleSets, Pivot, PivotItem } from "@fluentui/react";
import React from "react";
import { ContentViewModelProperty } from "../../BaseComponents/contentViewBase";
import { Controls } from "../../BaseComponents/controls";
import { L } from "../../../lib/abpUtility";
import { additionalTheme, myTheme } from "../../../styles/theme";
import { dateFormat } from "../../../utils/utils";
import { LabelComponent } from "../../BaseComponents/labelComponent";
import { checkUsedKeysForDataSet } from "./calculationProductProcessData";

const pivotStyles = {
	root: {
		marginLeft: '-8px'
	},
	linkIsSelected: {
		color: myTheme.palette.red,
		selectors: {
			':before': {
				height: '5px',
				backgroundColor: additionalTheme.darkerRed
			}
		}
	}
};

const classNames = mergeStyleSets({
	tableCell: {
		padding: '3px 10px', 
		border: '1px solid #000',
		textAlign: 'center'
	}
});

export interface ICalculationProductCancelTravelProps {
	calculation: any;
	calculationData: any;
	apkData: any;
	isEditMode: boolean;
	isDataLoaded: boolean;
	activitiesPivotItem: JSX.Element;
	productAttributeResultItems: any[];
	gnLanguage: any;
	renderElement: (element: ContentViewModelProperty, error: any, value: any, tableInputData?: any) => JSX.Element;
}

export class CalculationProductCancelTravel extends React.Component<ICalculationProductCancelTravelProps> {
	private usedAPKInputsKeys: string[] = ['CancelTravel.APK.ResignationFromTrip', 'CancelTravel.APK.TypeOfTrip', 'CancelTravel.APK.TripExceed30000PerPerson', 'CancelTravel.APK.TravelExpenses', 'CancelTravel.APK.ChronicDiseases'];
	private usedInputsKeys: string[] = ['CancelTravelInsurance.PaymentMethod', 'CancelTravelInsurance.TypePolicy', 'CancelTravelInsurance.PurchaseDate', 'CancelTravelInsurance.StartDate', 'CancelTravelInsurance.EndDate', 'CancelTravelInsurance.NumberOfPeopleTraveling', 'CancelTravelInsurance.TravelInsuranceSum', 'CancelTravelInsurance.InsuranceProtectionExtension', 'CancelTravelInsurance.InsurerIsTravelParticipant', 'CancelTravelInsurance.Participants'];
	private usedInputsKeysChecked: boolean = false;

	render() {
		const { calculation, calculationData, apkData, isDataLoaded, renderElement } = this.props;

		// some saved calculations doesn't have full data causing errors
		if(Object.keys(calculationData).length > 0 && !this.usedInputsKeysChecked) {
			checkUsedKeysForDataSet(apkData, this.usedAPKInputsKeys);
			checkUsedKeysForDataSet(calculationData, this.usedInputsKeys, this.props.productAttributeResultItems, this.props.gnLanguage);

			this.usedInputsKeysChecked = true;
			this.forceUpdate();
		}

		const parsedTravelersDataParticipants: any = calculationData['CancelTravelInsurance.Participants'] ? JSON.parse(calculationData['CancelTravelInsurance.Participants'].valueLocale) : {};
		let participantsTableRows: JSX.Element[] = [];
		for(let i = 0; i < Object.keys(parsedTravelersDataParticipants).length; i++) {
			participantsTableRows.push(<tr>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerNumber}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerFirstName}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerSurname}</td>
				<td className={classNames.tableCell}>{dateFormat(parsedTravelersDataParticipants[i].date, "DD.MM.YYYY")}</td>
			</tr>);
		}
		const participantsTable: JSX.Element = <table style={{marginTop: 20}}>
			<tr>
				<th className={classNames.tableCell}>{L('Traveler number')}</th>
				<th className={classNames.tableCell}>{L('Traveler first name')}</th>
				<th className={classNames.tableCell}>{L('Traveler surname')}</th>
				<th className={classNames.tableCell}>{L('Traveler birth date')}</th>
			</tr>
			{participantsTableRows.map((row) => {
				return row;
			})}
		</table>;

		return (
			<>
				<Pivot theme={myTheme} styles={pivotStyles}>		
					<PivotItem headerText={L('General')} key={'General'}>
						{renderElement(new ContentViewModelProperty('calculationId', L("Calculation ID"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'calculationId': calculation.id})}
						{renderElement(new ContentViewModelProperty('translatedSegment', L("Product"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'translatedSegment': L(calculation.segment)})}
						{renderElement(new ContentViewModelProperty('status', L("Status"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'status': L(calculation.lastCreatedPolicyStatus)})}
						{renderElement(new ContentViewModelProperty('creationTime', L("Creation date"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'creationTime': calculation.creationTime})}
						{renderElement(new ContentViewModelProperty('startDate', L("Policy start date"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'startDate': calculation.startDate})}
						{renderElement(new ContentViewModelProperty('customerName', L("Customer name"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerName': calculation.client.user.name})}
						{renderElement(new ContentViewModelProperty('customerSurname', L("Customer surname"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerSurname': calculation.client.user.surname})}
						{renderElement(new ContentViewModelProperty('pesel', L("Pesel"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'pesel': calculation.client.pesel})}
						{renderElement(new ContentViewModelProperty('customerEmail', L("Customer email"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerEmail': calculation.client.user.emailAddress})}
						{renderElement(new ContentViewModelProperty('phoneNumber', L("Customer phone number"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'phoneNumber': calculation.client.phone})}
					</PivotItem>
					
					{Object.keys(apkData).length > 0 &&
						<PivotItem headerText={L('APK')} key={'APK'}>
							{renderElement(new ContentViewModelProperty('CancelTravel.APK.ResignationFromTrip', apkData['CancelTravel.APK.ResignationFromTrip'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'CancelTravel.APK.ResignationFromTrip': apkData['CancelTravel.APK.ResignationFromTrip'].value})}
							{renderElement(new ContentViewModelProperty('CancelTravel.APK.TypeOfTrip', apkData['CancelTravel.APK.TypeOfTrip'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'CancelTravel.APK.TypeOfTrip': apkData['CancelTravel.APK.TypeOfTrip'].valueLocale})}
							{renderElement(new ContentViewModelProperty('CancelTravel.APK.TripExceed30000PerPerson', apkData['CancelTravel.APK.TripExceed30000PerPerson'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'CancelTravel.APK.TripExceed30000PerPerson': apkData['CancelTravel.APK.TripExceed30000PerPerson'].value})}
							{renderElement(new ContentViewModelProperty('CancelTravel.APK.TravelExpenses', apkData['CancelTravel.APK.TravelExpenses'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded, rows: 5}), [], {'CancelTravel.APK.TravelExpenses': apkData['CancelTravel.APK.TravelExpenses'].valueLocale})}
							{renderElement(new ContentViewModelProperty('CancelTravel.APK.ChronicDiseases', apkData['CancelTravel.APK.ChronicDiseases'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'CancelTravel.APK.ChronicDiseases': apkData['CancelTravel.APK.ChronicDiseases'].value})}
						</PivotItem>
					}

					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('Complete the data')} key={'Complete the data'}>
							{renderElement(new ContentViewModelProperty('CancelTravelInsurance.TypePolicy', calculationData['CancelTravelInsurance.TypePolicy'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'CancelTravelInsurance.TypePolicy': calculationData['CancelTravelInsurance.TypePolicy'].valueLocale})}
							{renderElement(new ContentViewModelProperty('CancelTravelInsurance.PurchaseDate', calculationData['CancelTravelInsurance.PurchaseDate'].label, Controls.Date, false, [], true, {isDataLoaded: isDataLoaded}), [], {'CancelTravelInsurance.PurchaseDate': calculationData['CancelTravelInsurance.PurchaseDate'].value})}
							{renderElement(new ContentViewModelProperty('CancelTravelInsurance.StartDate', calculationData['CancelTravelInsurance.StartDate'].label, Controls.Date, false, [], true, {isDataLoaded: isDataLoaded}), [], {'CancelTravelInsurance.StartDate': calculationData['CancelTravelInsurance.StartDate'].value})}
							{renderElement(new ContentViewModelProperty('CancelTravelInsurance.EndDate', calculationData['CancelTravelInsurance.EndDate'].label, Controls.Date, false, [], true, {isDataLoaded: isDataLoaded}), [], {'CancelTravelInsurance.EndDate': calculationData['CancelTravelInsurance.EndDate'].value})}
							{renderElement(new ContentViewModelProperty('CancelTravelInsurance.NumberOfPeopleTraveling', calculationData['CancelTravelInsurance.NumberOfPeopleTraveling'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'CancelTravelInsurance.NumberOfPeopleTraveling': calculationData['CancelTravelInsurance.NumberOfPeopleTraveling'].valueLocale})}
							{renderElement(new ContentViewModelProperty('CancelTravelInsurance.TravelInsuranceSum', calculationData['CancelTravelInsurance.TravelInsuranceSum'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'CancelTravelInsurance.TravelInsuranceSum': calculationData['CancelTravelInsurance.TravelInsuranceSum'].valueLocale})}
							{renderElement(new ContentViewModelProperty('CancelTravelInsurance.PaymentMethod', calculationData['CancelTravelInsurance.PaymentMethod'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'CancelTravelInsurance.PaymentMethod': calculationData['CancelTravelInsurance.PaymentMethod'].valueLocale})}
							{renderElement(new ContentViewModelProperty('CancelTravelInsurance.InsuranceProtectionExtension', calculationData['CancelTravelInsurance.InsuranceProtectionExtension'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded, rows: 5}), [], {'CancelTravelInsurance.InsuranceProtectionExtension': calculationData['CancelTravelInsurance.InsuranceProtectionExtension'].valueLocale})}
						</PivotItem>
					}

					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('Travelers data')} key={'Travelers data'}>
							{renderElement(new ContentViewModelProperty('CancelTravelInsurance.InsurerIsTravelParticipant', calculationData['CancelTravelInsurance.InsurerIsTravelParticipant'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'CancelTravelInsurance.InsurerIsTravelParticipant': calculationData['CancelTravelInsurance.InsurerIsTravelParticipant'].valueLocale})}
							<LabelComponent label={calculationData['CancelTravelInsurance.Participants'].label} customStyles={{marginTop: '20px', marginBottom: '-20px'}} />
							{participantsTable}
						</PivotItem>
					}

					{this.props.activitiesPivotItem}
				</Pivot>
			</>
		);
	}
}