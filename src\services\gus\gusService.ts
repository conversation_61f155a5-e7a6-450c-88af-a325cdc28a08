import { CrudServiceBase } from '../base/crudServiceBase';
import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { GusDto } from './gusDto';

export class GusService extends CrudServiceBase<GusDto> {
    constructor() {
        super(Endpoint.Gus);
        this.internalHttp = httpApi;
    }

    public async getCityInfoByPostCode(postcode: string, country: string): Promise<GusDto> {
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetCityInfoByPostCode?postcode=${postcode}&country=${country}`, true));

        if(result.data.value && result.data.value.length > 0) {
            if(result.data.value[0].Id) {
                result.data.value[0].id = result.data.value[0].Id;
            }
            return result.data.value[0];
        } else {
            if(result.data.Id) {
                result.data.id = result.data.Id;
            }
            return result.data;
        }
    }

    public async getCities(keyword: string): Promise<GusDto> {
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetCities?keyword=${keyword}`, true));

        if(result.data.value && result.data.value.length > 0) {
            if(result.data.value[0].Id) {
                result.data.value[0].id = result.data.value[0].Id;
            }
            return result.data.value[0];
        } else {
            if(result.data.Id) {
                result.data.id = result.data.Id;
            }
            return result.data;
        }
    }

    public async getCompanyInfoByNip(nip: string): Promise<GusDto> {
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetCompanyInfoByNip?nip=${nip}`, true));

        if(result.data.value && result.data.value.length > 0) {
            if(result.data.value[0].Id) {
                result.data.value[0].id = result.data.value[0].Id;
            }
            return result.data.value[0];
        } else {
            if(result.data.Id) {
                result.data.id = result.data.Id;
            }
            return result.data;
        }
    }

    public async getCompanyInfoByRegon(regon: string): Promise<GusDto> {
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetCompanyInfoByRegon?regon=${regon}`, true));

        if(result.data.value && result.data.value.length > 0) {
            if(result.data.value[0].Id) {
                result.data.value[0].id = result.data.value[0].Id;
            }
            return result.data.value[0];
        } else {
            if(result.data.Id) {
                result.data.id = result.data.Id;
            }
            return result.data;
        }
    }
}

const exportGusService: GusService = new GusService();
export default exportGusService;