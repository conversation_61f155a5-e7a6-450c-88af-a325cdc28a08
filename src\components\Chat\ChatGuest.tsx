import React from 'react';
import { L } from '../../lib/abpUtility';

interface IChatGuestProps {
    enterWaitingRoom: () => void;
}

const ChatGuest: React.FC<IChatGuestProps> = (props) => {
    const { enterWaitingRoom } = props;

    return (
        <div className="chat__window--guest">
            <p className='chat__window--waiting-message'>{L('You will be conected with our consultants as soon as possible.')}</p>
            <div className="chat__button" onClick={() => enterWaitingRoom()}>
                {L('Join waiting room')}
            </div>
        </div>
    );
};

export default ChatGuest;