import {FluentTableBase} from "../Fluent/base/fluentTableBase";
import {ITableColumn} from "./ITableColumn";
import {myTheme} from "../../styles/theme";
import {L} from "../../lib/abpUtility";
import {ICrudPermissons} from "./commandBarBase";
import {ThemeProvider} from "@fluentui/react";
import {ClientContestDto} from "../../services/clientContest/dto/clientContestDto";
import { dateFormat } from "../../utils/utils";

export class CustomerContestFluentListBaseWithCommandBar extends FluentTableBase<ClientContestDto> {
    disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;

    getItemDisplayNameOf(item: ClientContestDto): any {
        return item.contest?.name
    }

    getColumns(): ITableColumn[] {
        return this.getTableColumns(this.props);
    }

    private getTableColumns(props: any): ITableColumn[] {
        return [
            {
                name: L('Contest name'),
                fieldName: 'contestName',
                onRender: (item: ClientContestDto) => {
                    return item.contest?.name;
                }
            },
            {
                name: L('Conditions meet'),
                fieldName: 'isConditionsMeet',
                onRender: (item: ClientContestDto) => {
                    return item.isConditionsMeet === true ? (
                        <span style={{ color: '#fff', backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
                    ) : (
                        <span>{L('No')}</span>
                    );
                }
            },
            {
                name: L('Is received'),
                fieldName: 'isReceived',
                onRender: (item: ClientContestDto) => {
                    return item.isReceived === true ? (
                        <span style={{ color: '#fff', backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
                    ) : (
                        <span>{L('No')}</span>
                    );
                }
            },
            {
                name: L('Is prize send to client'),
                fieldName: 'isPrizeSentToClient',
                onRender: (item: ClientContestDto) => {
                    return item.isPrizeSentToClient === true ? (
                        <span style={{ color: '#fff', backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
                    ) : (
                        <span>{L('No')}</span>
                    );
                }
            },
            {
                name: L('Selected'),
                fieldName: 'receivedPrizeId',
                onRender: (item: ClientContestDto) => {
                    return item.receivedPrizeId;
                }
            },
            {
                name: L('Post date'),
                fieldName: 'postDate',
                onRender: (item: ClientContestDto) => {
                    return (new Date(item.postDate).getFullYear() < 1900 || item.isPrizeSentToClient !== true) ? '-' : dateFormat(item.postDate.toString(), "DD.MM.YYYY");
                },
            },
            {
                name: L('Prize number'),
                fieldName: 'prizeNumber',
                onRender: (item: ClientContestDto) => {
                    return item.prizeNumber;
                }
            },
        ];
    }

    getCrudPermission(): ICrudPermissons {
        return {
            create: false,
            update: false,
            delete: false,
            customActions: (this.props.customData && this.props.customData.disableCustomActions && this.props.customData.disableCustomActions === true) ? false : true,
        };
    }

    getCommandBarBaseProps() {
        let props = super.getCommandBarBaseProps();

        let customActionsPropsWithoutButton = [{
            displayFor: 'single',
            buttonText: L("Details"),
            buttonIcon: "none",
        },
        ]
        return {
            ...props,
            customActionsProps: customActionsPropsWithoutButton,
            customActions: [
                this.props.customData.fetchClientContestData
            ]
        }
    }

    renderAll(pageInfo: string, values: any, columns: any) {
        return <>
            <ThemeProvider theme={myTheme}>
                { this.renderAnnounced(pageInfo) }
                { this.renderCommandBarBase() }

                { this.renderListScrollablePane(values, columns) }
            </ThemeProvider>

            { this.renderPanel() }
        </>;
    }

}