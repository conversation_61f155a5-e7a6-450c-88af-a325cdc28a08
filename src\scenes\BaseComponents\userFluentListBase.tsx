import { Icon, SearchBox, ThemeProvider } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import AppConsts from '../../lib/appconst';
import { UserDto } from '../../services/user/dto/userDto';
import { fluentTableClassNames } from '../../styles/fluentTableStyles';
import {additionalTheme, myTheme} from '../../styles/theme';
import { FluentTableBase } from '../Fluent/base/fluentTableBase';
import { ICrudPermissons } from './commandBarBase';
import { ITableColumn } from './ITableColumn';
import { LabelComponent } from './labelComponent';
import { LabelContainerComponent } from './labelContainerComponent';

var _ = require('lodash');

export class UserFluentListBase extends FluentTableBase<UserDto> {
  private debouncedOnSearchboxChange: any = _.debounce((e: any, newValue: string | undefined, customPayload?: any) => {
    newValue = typeof newValue === 'undefined' || newValue.length === 0 ? " " : newValue;
    if(this.props.customOnSearchTextChanged) {
      this.props.customOnSearchTextChanged(newValue);
    } else {
      this.overrideFilter(newValue);
    }
  }, AppConsts.defaultSerachBarDelay, []);
  
  getItemDisplayNameOf(item: UserDto): string {
    if(item) {
      if(!!item.userName) {
        return item.userName;
      } else {
        return item.emailAddress;
      }
    }
    return "";
  }

  getColumns(): ITableColumn[] {
    return this.getTableColumns(this.props);
  }

  private getTableColumns(props: any): ITableColumn[] {
    return [          
      {
        name: L('CustomerName'),
        fieldName: 'name',
      },
      {
        name: L('Surname'),
        fieldName: 'surname',
      },
      {
        name: L('E-mail'),
        fieldName: 'emailAddress',
      },
      {
        name: L('UserName'),
        fieldName: 'userName',
      },
      {
        name: L('Active'),
        fieldName: 'isActive',
        onRender: (item: UserDto) => {
          return item.isActive ? (
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.green, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
          ) : (
            <span style={{ color: additionalTheme.white, backgroundColor: additionalTheme.lighterRed, padding: '2px 5px', borderRadius: '2px' }}>{L('No')}</span>
          );
        }
      }
    ];
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: false,
      delete: false,
      customActions: false,
    };
  }

  renderAll(pageInfo: string, values: any, columns: any) {
    return <>
      <LabelContainerComponent marginTop={'0'}>
        <LabelComponent label={this.props.customData.customLabel ? this.props.customData.customLabel : L('Select user')}/>
        <SearchBox
          theme={myTheme}
          styles={{
            root: {
              flex: 1,
              maxWidth: '350px',
              height: '32px', 
              backgroundColor: myTheme.palette.white,
              border: `1px solid ${myTheme.palette.black}`,
              boxSizing: 'border-box',
            },
            field: { borderRadius: '2px' },
          }}
          placeholder={ L('Search') }
          onChange={ (e: any, newValue: string | undefined) => {
            this.debouncedOnSearchboxChange(e, newValue);
          }}
        />

        {this.props.customData.selectedUser &&
          <p className={fluentTableClassNames.summaryAttribute}>
              <span>{this.props.customData.selectedUser}</span>
              <div style={{backgroundColor: additionalTheme.white, borderRadius: '50%', width: '20px', height: '20px', display: 'flex', justifyContent: 'center', alignItems: 'center', marginLeft: '10px'}}>
                <Icon iconName="Cancel" style={{marginRight: '0', cursor: 'pointer', color: additionalTheme.grey, fontWeight: 'bold'}} title={L("Delete")}
                  onClick={() => { if(this.props.customOnSelectionChanged) this.props.customOnSelectionChanged('deleteUser') }} />
              </div>
          </p>
        }
      </LabelContainerComponent>

      <ThemeProvider theme={myTheme}>
        {this.renderAnnounced(pageInfo)}

        {this.renderListScrollablePane(values, columns)}
      </ThemeProvider>
    </>;
  }
}