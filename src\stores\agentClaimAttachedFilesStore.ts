import { CrudStoreBase } from './crudStoreBase';
import apkAttachedFilesService from '../services/apkAttachedFiles/apkAttachedFilesService';
import { AgentClaimAttachedFilesDto } from '../services/agentClaim/dto/agentClaimAttachedFilesDto';
import agentClaimAttachedFilesService from '../services/agentClaim/agentClaimAttachedFilesService';
import { defaultAgentClaim } from './agentClaimStore';

class AgentClaimAttachedFilesStore extends CrudStoreBase<AgentClaimAttachedFilesDto>{
  constructor() {
    super(agentClaimAttachedFilesService, defaultAgentClaimAttachedFile)
  }

  public async getAllFiles() {
		return apkAttachedFilesService.getAllFiles();
	}

  public async getOpinionByClaimId(claimId: number) {
		await agentClaimAttachedFilesService.getOpinionsByClaimId(claimId).then((result: any) => {
			this.dataSet = result;
		}).catch((error: any) => {
			console.error(error);
		});
	}

  public async getQuotationByClaimId(claimId: number) {
		await agentClaimAttachedFilesService.getQuotationByClaimId(claimId).then((result: any) => {
			this.dataSet = result;
		}).catch((error: any) => {
			console.error(error);
		});
	}

  public async getFvByClaimId(claimId: number) {
		await agentClaimAttachedFilesService.getFvByClaimId(claimId).then((result: any) => {
			this.dataSet = result;
		}).catch((error: any) => {
			console.error(error);
		});
	}
  
  public async getDocumentByClaimId(claimId: number) {
		await agentClaimAttachedFilesService.getDocumentByClaimId(claimId).then((result: any) => {
			this.dataSet = result;
		}).catch((error: any) => {
			console.error(error);
		});
	}

  public async getPhotoByClaimId(claimId: number) {
		await agentClaimAttachedFilesService.getPhotoByClaimId(claimId).then((result: any) => {
			this.dataSet = result;
		}).catch((error: any) => {
			console.error(error);
		});
	}
}

export const defaultAgentClaimAttachedFile: AgentClaimAttachedFilesDto = {
  id: "",
  claimId: 0,
  claim: defaultAgentClaim,
  agentClaimFileType: '',
  fileUrl: '',
  originalFileName: '',
  blobFileName: '',
  description: '',
  documentType: ''
}

export default AgentClaimAttachedFilesStore;