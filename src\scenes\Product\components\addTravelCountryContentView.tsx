import { mergeStyleSets, Stack, SelectionMode, Selection, Spinner, SpinnerSize, MessageBar, MessageBarType } from '@fluentui/react';
import { myTheme } from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import Stores from '../../../stores/storeIdentifier';
import { inject, observer } from 'mobx-react';
import TravelCountryStore from '../../../stores/travelCountryStore';
import { InsurerAddTravelCountryFluentListBaseWithCommandBar } from '../../BaseComponents/insurerAddTravelCountryFluentListBaseWithCommandBar';
import travelCountryCoverageService from '../../../services/travelCountryCoverage/travelCountryCoverageService';
import { L } from '../../../lib/abpUtility';

const classNames = mergeStyleSets({
    contentContainer: {
        display: 'block',
        width: '100%',
        height: '500px',
        maxHeight: '500px',
        position: 'relative',
        marginTop: '20px',
    },
    smallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '10px !important',
        marginTop: '22px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    messageBar: {
		width: "fit-content",
        marginTop: "25px",
		selectors: {
			"& .ms-MessageBar-innerText": {
				selectors: {
					"& span": {
						whiteSpace: "pre-line",
					},
				},
			},
		},
	},
});

export interface IProps {
	travelCountryStore: TravelCountryStore;
}

@inject(Stores.LanguageStore)
@inject(Stores.ProductAttributeStore)
@inject(Stores.TravelCountryStore)
@observer
export class AddTravelCountryContentView extends GenericContentView {
    private selectedTravelCountry: any;
    private showMessageBar: boolean = false;
    private messageBarType: MessageBarType = MessageBarType.success;
    private messageBarText: string = '';
    private _travelCountryListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedTravelCountry: any = this._travelCountryListSelection.getSelection();
            if(Array.isArray(selectedTravelCountry) && selectedTravelCountry.length > 0 && !!selectedTravelCountry[0].id) {
                this.selectedTravelCountry = selectedTravelCountry[0];
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private overrideAllItemsTrigger: number = 0;

    private async reloadItems() {
        if(this.props.customData && this.props.customData.fetchTravelCountryByInsurerId) {
            await this.props.customData.fetchTravelCountryByInsurerId();
            this.overrideAllItemsTrigger = this.overrideAllItemsTrigger + 1;
            this.forceUpdate();
        }
    }

    private handleAddCountryFromGeneralList = async () => {
        this.asyncActionInProgress = true;
        this.forceUpdate();
        await travelCountryCoverageService.createTravelCountryCoverageListForInsurerId(this.props.customData.insurerId).then((result) => {
            if(result) {
                this.showMessageBar = true;
                this.asyncActionInProgress = false;
                this.messageBarType = MessageBarType.success;
                this.messageBarText = L('Successfully added countries from the general list');
                this.forceUpdate();
            }
        })
    }

    renderContent() {
        return <>
            <Stack>
                <div className={classNames.contentContainer}>
                    <InsurerAddTravelCountryFluentListBaseWithCommandBar 
                        store={this.props.travelCountryStore!}
                        items={
                            this.props.travelCountryStore?.dataSet && this.props.travelCountryStore?.dataSet.items
                                ? this.props.travelCountryStore?.dataSet.items
                                : []
                        }
                        customSelection={this._travelCountryListSelection}
                        searchText={''}
                        history={this.props.history}
                        customData={{
                            selectedTravelCountry: this.selectedTravelCountry,
                            overrideAllItemsTrigger: this.overrideAllItemsTrigger,
                            insurerId: this.props.customData.insurerId,
                            disableGetAllOnMount: true,
                            handleAddCountryFromGeneralList: this.handleAddCountryFromGeneralList
                        }}
                        scrollablePanelMarginTop={120}
                        refreshItems={() => this.reloadItems()}
                    />
                    <Stack horizontal={true}>
                        {this.asyncActionInProgress &&
                            <Spinner className={classNames.smallLoadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                        }
                    </Stack>
                </div>
                {this.showMessageBar &&
                    <MessageBar messageBarType={this.messageBarType} isMultiline={false} className={classNames.messageBar} onDismiss={() => {
                        this.showMessageBar = false;
                        this.forceUpdate();
                    }}>
                        {`${this.messageBarText}`}
                    </MessageBar>
                }
            </Stack>
        </>
    }
}