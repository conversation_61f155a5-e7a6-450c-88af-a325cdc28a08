import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Details<PERSON>ist, IChoiceGroupOption, Icon, IDropdownOption, mergeStyleSets, MessageBar, MessageBarType, Pivot, PivotItem, PrimaryButton, Selection, SelectionMode, Spinner, SpinnerSize, Stack } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { UserDto } from '../../../services/user/dto/userDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView, IGenericContentViewProps } from '../../BaseComponents/genericContentView';
import { defaultUser } from '../../../stores/userCrudStore';
import userService from '../../../services/user/userService';
import organizationUnitService from '../../../services/organizationUnit/organizationUnitService';
import { IContentViewState } from '../../BaseComponents/IContentViewState';
import Stores from '../../../stores/storeIdentifier';
import { inject, observer } from 'mobx-react';
import { spinnerClassNames } from '../../../styles/spinnerStyles';
import { LabeledTextField } from '../../../components/LabeledTextField';
import { FormEvent } from 'react';
import { fluentTableClassNames } from '../../../styles/fluentTableStyles';
import { UserFluentListBase } from '../../BaseComponents/userFluentListBase';
import { isConfigForAG } from '../../../utils/authUtils';
import { UserAgencyLoginDto } from '../../../services/userAgencyLogin/dto/userAgencyLoginDto';
import { defaultUserAgencyLogin } from '../../../stores/userAgencyLoginStore';
import userAgencyLoginService from '../../../services/userAgencyLogin/userAgencyLoginService';
import { UserAgencyLoginInputType } from '../../../services/userAgencyLogin/dto/userAgencyLoginInputTypeEnums';

declare var abp: any;

const classNames = mergeStyleSets({
	messageBar: {
		width: "fit-content",
        marginTop: "25px",
		selectors: {
			"& .ms-MessageBar-innerText": {
				selectors: {
					"& span": {
						whiteSpace: "pre-line",
					},
				},
			},
		},
	},
    insurerAccessSettingPasswordInput: {
        letterSpacing: '3px',
    },
    inputIcon: {
        cursor: 'pointer',
        marginLeft: '15px !important',
        fontSize: '20px',
        marginTop: '48px',
        transition: 'all 120ms',
        selectors: {
            '&:hover': {
                transform: 'scale(1.2)',
            }
        }
    },
    insurerAccessSettingsBoxInput: {
        width: '100%',
    },
    dataContainer: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap',
        borderRadius: '12px',
        boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
        padding: '20px 25px',
        marginTop: '30px',
        width: 'fit-content',
    }
});

@inject(Stores.UserCrudStore)
@inject(Stores.UserAgencyLoginStore)
@observer
export class UserContentView extends GenericContentView {
    private user: UserDto = defaultUser;
    private originInsurerAccessSettings: UserAgencyLoginDto[] = [];
    private allInsurerAccessSettings: UserAgencyLoginDto[] = [];
    private allRoles: IDropdownOption[] = [];
    private allOrganizationUnits: any = {};
    private initSelection: boolean = true;
    private _selection: Selection;
    private initSelectionIndexesSet: boolean = false;
    private showMessageBar: boolean = false;
    private messageBarType: MessageBarType = MessageBarType.success;
    private messageBarText: string = '';
    private hideConfirmButton: boolean = false;
    private selectedManagerFullName: string = "";
    private showInsurerAccessSettingPassword: boolean[] = [];
    private _userListManagerSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedUser: any = this._userListManagerSelection.getSelection();
            if(Array.isArray(selectedUser) && selectedUser.length > 0 && !!selectedUser[0].id) {
                this.user.managerId = selectedUser[0].id.toString();
                this.selectedManagerFullName = `[${selectedUser[0].id}] ${selectedUser[0].name} ${selectedUser[0].surname}`;
                this._userListManagerSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private selectedDirectorFullName: string = "";
    private _userListDirectorSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedUser: any = this._userListDirectorSelection.getSelection();
            if(Array.isArray(selectedUser) && selectedUser.length > 0 && !!selectedUser[0].id) {
                this.user.directorId = selectedUser[0].id.toString();
                this.selectedDirectorFullName = `[${selectedUser[0].id}] ${selectedUser[0].name} ${selectedUser[0].surname}`;
                this._userListDirectorSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private userWithAdminRole: boolean = false;

    constructor(props: IGenericContentViewProps) {
        super(props);

        this._selection = new Selection({
            onSelectionChanged: () => {
                if(!this.initSelection) {
                    const selectedOrganizationUnitIds: number[] = [];
                    this._selection.getSelection().forEach((element: any) => {
                        selectedOrganizationUnitIds.push(element.id);
                    });

                    this.user.organizationUnitIds = selectedOrganizationUnitIds;
                } else {
                    this.initSelection = false;
                }
                this.forceUpdate();
            },
        });
    }

    async componentDidMount() {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        if(!this.props.userCrudStore || !this.props.userCrudStore.dataSet || this.props.userCrudStore.dataSet.totalCount === 0) {
            await this.props.userCrudStore?.getAll(this.props.userCrudStore?.defaultRequest);
        }
        
        this.checkIfDataIsLoaded("user");
        
        if(this.user && !!this.user.id && parseInt(this.user.id) > 0) {
            // if(!this.props.userAgencyLoginStore || !this.props.userAgencyLoginStore.dataSet || this.props.userAgencyLoginStore.dataSet.totalCount === 0) {
            //     await this.props.userAgencyLoginStore?.getAgentLoginsByUserId(parseInt(this.user.id));
            // }

            if(!!this.user.directorId) {
                this.selectedDirectorFullName = `[${this.user.directorId.toString()}] ${this.user.directorFullName}`;
            }

            if(!!this.user.managerId) {
                this.selectedManagerFullName = `[${this.user.managerId.toString()}] ${this.user.managerFullName}`;
            }
        }

        this.getAllRoles().then((result) => {
            result.forEach((role) => {
                this.allRoles.push({ key: role.normalizedName, text: L(role.displayName) });
            });
        });

        this.refreshInsurerAccessSettings();
        this.getOrganizationUnits();

        this.checkIsUserWithAdminRole();

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    componentDidUpdate(prevProps: Readonly<IGenericContentViewProps>, prevState: Readonly<IContentViewState>, snapshot?: any): void {
        if(this.allOrganizationUnits && this.allOrganizationUnits.items && this.allOrganizationUnits.items.length > 0 && this.initSelectionIndexesSet === false) {
            if(this.user && this.user.organizationUnitIds && Array.isArray(this.user.organizationUnitIds)) {
                this.user.organizationUnitIds.forEach((ouId: number) => {
                    this._selection.setIndexSelected(this.getIndexByItemId(ouId), true, false);
                });
            }
            this.initSelectionIndexesSet = true;

            this.forceUpdate();
        }
    }

    private async refreshInsurerAccessSettings(forceUpdate?: boolean) {
        if(this.props.userAgencyLoginStore) {
            await this.props.userAgencyLoginStore?.getAgentLoginsByUserId(parseInt(this.user.id)).then((response: any) => {
                if(response && response.items) {
                    this.originInsurerAccessSettings = [...response.items];
                    this.allInsurerAccessSettings = [...response.items];

                    if(forceUpdate) {
                        this.forceUpdate();
                    }
                }
            }).catch((error: any) => {
                console.error(error);
            });
        }
    }

    private async getAllRoles() {
        let allRoles: any[] = [];
        await userService.getRoles().then((value) => {
            allRoles = value;
        });        
        return allRoles;
    }

    private getIndexByItemId(itemId: number): number {
        let indexToReturn = -1;
        
        if(this.allOrganizationUnits.items.length > 0) {
            this.allOrganizationUnits.items.some((item: any, index: number) => {
                if(item.id === itemId || item.id === itemId) {
                    indexToReturn = index;
                    return true;
                }
                return false;
            })
        }
    
        return indexToReturn;
    }

    private async getOrganizationUnits() {
        let allOrganizationUnits: any[] = [];
        let maxLevelPerGroup: any = {};

        const getElementObj = (element: any, level: number) => {
            return {key: element.id, id: element.id, level: level, code: element.code, displayName: element.displayName};
        }

        const getNextLevelElement = (element: any, level: number, groupIterator: number) => {
            allOrganizationUnits[groupIterator].push(getElementObj(element, level));
            
            if(element.children && element.children.length > 0) {
                element.children.forEach((nextLevelElement: any) => {
                    getNextLevelElement(nextLevelElement, level + 1, groupIterator);
                });
            } else {
                maxLevelPerGroup[groupIterator] = level;
            }
        };

        await organizationUnitService.getOrganizationUnitTree().then((response: any) => {
            if(response && response.roots) {
                this.allOrganizationUnits['items'] = [];
                this.allOrganizationUnits['groups'] = [];

                response.roots.forEach((element: any, iterator: number) => {
                    if(!allOrganizationUnits[iterator]) {
                        allOrganizationUnits[iterator] = [];
                    }

                    getNextLevelElement(element, 0, iterator);
                    
                    allOrganizationUnits[iterator].sort((a: any, b: any) => (a.level > b.level) ? 1 : ((b.level > a.level) ? -1 : 0));
                    this.allOrganizationUnits['items'] = [...this.allOrganizationUnits['items'], ...allOrganizationUnits[iterator]];

                    let alreadySetGroupsForIteratorAndLevel: any = {};

                    allOrganizationUnits[iterator].forEach((element: any) => {
                        if(element.level !== alreadySetGroupsForIteratorAndLevel[iterator]) {
                            alreadySetGroupsForIteratorAndLevel[iterator] = element.level;

                            let startIndex = 0;
                            if(iterator > 0) {
                                for(let i = 0; i < iterator; i++) {
                                    startIndex += allOrganizationUnits[i].length;
                                }
                            }
                            for(let levelIterator = 0; levelIterator < element.level; levelIterator++) {
                                startIndex += allOrganizationUnits[iterator].filter((x: any) => x.level === levelIterator).length;
                            }

                            this.allOrganizationUnits['groups'].push({
                                key: `groupedLevel${iterator}${element.level}`, 
                                name: `${element.code.split('.')[0]} - ${L("level")} ${element.level}`, 
                                startIndex: startIndex, 
                                count: allOrganizationUnits[iterator].filter((x: any) => x.level === element.level).length, 
                                level: element.level
                            });
                        }
                    });
                });

                this.forceUpdate();
            }
        }).catch((error: any) => {
            console.error(error);
        });
    }

    private async saveInsurerAccessSettings() {
        if(this.allInsurerAccessSettings && Array.isArray(this.allInsurerAccessSettings)) {
            this.asyncActionInProgress = true;
            this.forceUpdate();

            for (const insurerAccessSetting of this.allInsurerAccessSettings) {
                const filteredOriginInsurerAccessSettings: UserAgencyLoginDto[] = this.originInsurerAccessSettings.filter(
                    (originInsurerAccessSetting: UserAgencyLoginDto) => originInsurerAccessSetting.insurerId === insurerAccessSetting.insurerId
                                                                            && originInsurerAccessSetting.segment === insurerAccessSetting.segment
                );

                if(insurerAccessSetting && (!Array.isArray(filteredOriginInsurerAccessSettings) || !filteredOriginInsurerAccessSettings[0] ||
                    JSON.stringify(filteredOriginInsurerAccessSettings[0].datas) !== JSON.stringify(insurerAccessSetting.datas))
                ) {
                    await userAgencyLoginService.saveAgentLogins(this.allInsurerAccessSettings, parseInt(this.user.id)).then((response: any) => {
                        if(response && response.length > 0) {
                            this.messageBarType = MessageBarType.success;
                            this.messageBarText = L('Saved sucessfully.');
                        } else {
                            this.messageBarType = MessageBarType.error;
                            this.messageBarText = L('Something went wrong. Try again later or contact with administrator.');
                        }
                    }).catch((error: any) => {
                        console.error(error);
                        this.messageBarType = MessageBarType.error;
                        this.messageBarText = L('Something went wrong. Try again later or contact with administrator.');
                    });

                    this.showMessageBar = true;
                }
            };

            this.asyncActionInProgress = false;
            this.refreshInsurerAccessSettings(true);
        }
    }

    private toggleShowFilePassword(index: number, bool: boolean) {
        this.showInsurerAccessSettingPassword[index] = bool; 
        this.forceUpdate();
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} 
                    onClick={() => {
                        this.hideConfirmButton = false;
                        if(this.props.toggleHideConfirm) this.props.toggleHideConfirm(false);
                        this._onConfirm();
                    }} 
                    text={L('Save')}
                    style={{display: this.hideConfirmButton ? 'none' : 'unset'}}
                    disabled={this.asyncActionInProgress}
                />
    };

    renderBack = () => {
        return <DefaultButton text={L('Back')} iconProps={{ iconName: 'Back' }} allowDisabledFocus
                    onClick={() => {
                        this.hideConfirmButton = false;
                        if(this.props.toggleHideConfirm) this.props.toggleHideConfirm(false);
                        this._onBack();
                    }}
                />;
    };

    private async checkIsUserWithAdminRole() {
        await userService.CheckUserAdminRole().then((response: any) => {
            this.userWithAdminRole = response;
            this.forceUpdate();
        });
    }

    renderContent() {
        this.user = this.props.payload.model ? this.props.payload.model : this.props.payload;
        let preSelectedRoles: string[] = [];

        this.allRoles.forEach((role) => {
            if(this.user && this.user.roleNames && Array.isArray(this.user.roleNames) && this.user.roleNames.includes(role.key as string)) {
                role.selected = true;
                preSelectedRoles.push(role.key as string);
            }
        });
        
        const rolesOptions: any = {
            dropdown: this.allRoles as IDropdownOption[],
            choicegroup: [] as IChoiceGroupOption[]
        };

        const hasInsurerAccessSettingsChanged: boolean = JSON.stringify(this.allInsurerAccessSettings) !== JSON.stringify(this.originInsurerAccessSettings);

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed,
                    }
                }
            }
        };

        return <Pivot theme={myTheme} styles={pivotStyles} onLinkClick={(item: PivotItem | undefined) => { 
            if(item?.props && item.props.itemKey && item.props.itemKey === `insurerAccessSettings` && !this.hideConfirmButton) {
                this.hideConfirmButton = true;
                if(this.props.toggleHideConfirm) this.props.toggleHideConfirm(true);
            } else if(this.hideConfirmButton) {
                this.hideConfirmButton = false;
                if(this.props.toggleHideConfirm) this.props.toggleHideConfirm(false);
            }
        }}>
            <PivotItem headerText={L('General')} key={'General'}>
                <Stack className={classNames.dataContainer}>
                    <Stack.Item style={{marginRight: '70px'}}>
                        {this.renderElement(new ContentViewModelProperty('userName', L('UserName'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'userName': this.user.userName})}
                        {this.renderElement(new ContentViewModelProperty('name', L('Name'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'name': this.user.name})}
                        {this.renderElement(new ContentViewModelProperty('surname', L('Surname'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'surname': this.user.surname})}
                    </Stack.Item>
                    <Stack.Item>
                        {this.renderElement(new ContentViewModelProperty('emailAddress', L('E-mail'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'emailAddress': this.user.emailAddress})}
                        {this.renderElement(new ContentViewModelProperty('phoneNumber', L('Phone'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'phoneNumber': this.user.phoneNumber})}
                        {this.renderElement(new ContentViewModelProperty('roleNames', L('Roles'), Controls.MultiPicker, false, rolesOptions, false, {isDataLoaded: this.isDataLoaded}), [], {'roleNames': preSelectedRoles})}
                    </Stack.Item>
                </Stack>
        
                <div className={fluentTableClassNames.contentContainer}>
                    <UserFluentListBase
                        // searchText={this.selectClientSearchText}
                        searchText={undefined}
                        items={this.props.userCrudStore?.dataSet && this.props.userCrudStore?.dataSet.items ? this.props.userCrudStore?.dataSet.items : []}
                        store={this.props.userCrudStore!}
                        history={this.props.history}
                        scrollablePanelMarginTop={70}
                        customData={{
                            customLabel: L('Select a manager'),
                            selectedUser: !!this.selectedManagerFullName ? `${this.selectedManagerFullName}` : undefined,
                        }}
                        // customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                        customSelection={this._userListManagerSelection}
                        customOnSelectionChanged={(selection: any) => {
                            if(typeof selection === 'string' && selection === 'deleteUser') {
                                this.user.id = defaultUser.id;
                                this.selectedManagerFullName = "";
                                this.forceUpdate();
                            }
                        }}
                    />
                </div>

                <div className={fluentTableClassNames.contentContainer}>
                    <UserFluentListBase
                        // searchText={this.selectClientSearchText}
                        searchText={undefined}
                        items={this.props.userCrudStore?.dataSet && this.props.userCrudStore?.dataSet.items ? this.props.userCrudStore?.dataSet.items : []}
                        store={this.props.userCrudStore!}
                        history={this.props.history}
                        scrollablePanelMarginTop={70}
                        customData={{
                            customLabel: L('Select a director'),
                            selectedUser: !!this.selectedDirectorFullName ? `${this.selectedDirectorFullName}` : undefined,
                        }}
                        // customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                        customSelection={this._userListDirectorSelection}
                        customOnSelectionChanged={(selection: any) => {
                            if(typeof selection === 'string' && selection === 'deleteUser') {
                                this.user.id = defaultUser.id;
                                this.selectedDirectorFullName = "";
                                this.forceUpdate();
                            }
                        }}
                    />
                </div>

                {!isConfigForAG() &&
                    <Stack style={{marginTop: '30px'}}>
                        <DetailsList
                            // componentRef={this._root}
                            items={this.allOrganizationUnits && this.allOrganizationUnits.items ? this.allOrganizationUnits.items : []}
                            groups={this.allOrganizationUnits && this.allOrganizationUnits.groups ? this.allOrganizationUnits.groups : []}
                            columns={[
                                {key: 'name', name: L('Name'), fieldName: 'displayName', minWidth: 150, maxWidth: 300},
                                {key: 'code', name: L('Code'), fieldName: 'code', minWidth: 250},
                            ]}
                            // onRenderDetailsHeader={this._onRenderDetailsHeader}
                            groupProps={{ showEmptyGroups: true }}
                            // onRenderItemColumn={this._onRenderColumn}
                            compact={true}
                            selectionMode={SelectionMode.multiple}
                            selection={this._selection}
                        />
                    </Stack>
                }
            </PivotItem>

            {(this.user.id === abp.session.userId || this.userWithAdminRole) &&
                <PivotItem headerText={L('Insurer access settings')} key={'InsurerAccessSettings'} itemKey={'insurerAccessSettings'}>
                    {this.asyncActionInProgress &&
                        <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" style={{marginLeft: 25}} />
                    }
                    
                    <div style={{display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', flexWrap: 'wrap'}}>
                        {
                            this.allInsurerAccessSettings.map((userAgencyLogin: UserAgencyLoginDto, userAgencyLoginIndex: number) => {
                                const filteredInsurerAccessSettings: UserAgencyLoginDto[] = this.allInsurerAccessSettings.filter(
                                    (insurerAccessSetting: UserAgencyLoginDto) => insurerAccessSetting.insurerId === userAgencyLogin.insurerId 
                                                                                    && insurerAccessSetting.segment === userAgencyLogin.segment
                                );

                                if(!userAgencyLogin.datas || userAgencyLogin.datas.length === 0) {
                                    return <></>;
                                }
                                
                                return <Stack tokens={{childrenGap: '20%', padding: '15px 33px 23px 33px'}} styles={{
                                    root: {
                                        width: 'fit-content',
                                        marginTop: 15,
                                        marginRight: 15,
                                        position: 'relative',
                                        alignItems:'center',
                                        justifyContent:'flex-start',
                                        background: myTheme.palette.white,
                                        boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.25)',
                                        borderRadius: '12px',
                                    },
                                }}>
                                    <div style={{width:280, display: 'flex',height: '70px', justifyContent: 'center', alignItems: 'center', flexDirection: 'column'}}>
                                        { userAgencyLogin.logoLink && !!userAgencyLogin.logoLink ?
                                            <img style={{ marginTop:'15px', maxHeight:'70px'}} src={userAgencyLogin.logoLink} alt={`${userAgencyLogin.insurerName} logo`} />
                                            :
                                            <p style={{ width: '100%', textAlign: 'center', fontWeight: 'bold', justifyContent:'center', fontSize: '1.1rem', marginBottom: 0}}>{L(userAgencyLogin.insurerName)}</p>
                                        }
                                    </div>

                                    {userAgencyLogin.datas.map((data: any, dataIndex: number) => {
                                        if(data && data.type === UserAgencyLoginInputType.Login) {
                                            return (
                                            <LabeledTextField key={`accessSettingLoginForInsurer${userAgencyLogin.insurerId}-${userAgencyLoginIndex}`} label={data.name === 'AgentRef' ? L('Login') : L(data.name)} isDataLoaded={true} disabled={this.asyncActionInProgress}
                                                value={filteredInsurerAccessSettings && Array.isArray(filteredInsurerAccessSettings) && filteredInsurerAccessSettings[0] ? filteredInsurerAccessSettings[0].datas[dataIndex].value : ''}
                                                customLabelStyles={{minWidth: '150px', width: '150px', marginTop: 0, background: 'none', color: additionalTheme.grey, fontSize: '14px', fontWeight: 'normal', border: 'none', paddingLeft: 0}}
                                                customClassNames={`${classNames.insurerAccessSettingsBoxInput}`} required={true}
                                                labelContainerCustomStyles={{flexDirection: 'column', marginTop: '10px', marginLeft: '35px', marginRight: '35px'}}
                                                onChange={(event: FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string | undefined) => {
                                                    if(typeof newValue === 'string') {
                                                        const clonedData: any[] = [...userAgencyLogin.datas];
                                                        clonedData[dataIndex] = {...data, value: newValue};

                                                        if(filteredInsurerAccessSettings.length > 0) {
                                                            const cloneAllInsurerAccessSettings: UserAgencyLoginDto[] = [];
                                                            this.allInsurerAccessSettings.forEach((insurerAccessSetting: UserAgencyLoginDto) => {
                                                                if(insurerAccessSetting.insurerId !== userAgencyLogin.insurerId || insurerAccessSetting.segment !== userAgencyLogin.segment) {
                                                                    cloneAllInsurerAccessSettings.push(insurerAccessSetting);
                                                                } else {
                                                                    cloneAllInsurerAccessSettings.push({
                                                                        ...insurerAccessSetting,
                                                                        datas: clonedData,
                                                                    });
                                                                }
                                                            });
                                                            this.allInsurerAccessSettings = cloneAllInsurerAccessSettings;
                                                        } else {
                                                            this.allInsurerAccessSettings = [...this.allInsurerAccessSettings, {
                                                                ...defaultUserAgencyLogin,
                                                                insurerId: userAgencyLogin.insurerId,
                                                                datas: clonedData,
                                                            }];
                                                        }
                                                        this.forceUpdate();
                                                    }
                                                }} 
                                            />)
                                        } else if(data && data.type === UserAgencyLoginInputType.Password) {
                                            return (
                                            <div style={{display: 'flex', flexDirection: 'row', flexWrap: 'nowrap', marginTop: 0, 'position': 'relative'}}>
                                                <LabeledTextField key={`accessSettingPasswordForInsurer${userAgencyLogin.insurerId}-${userAgencyLoginIndex}`} type={this.showInsurerAccessSettingPassword[userAgencyLoginIndex] === true ? `text` : `password`}
                                                    label={L(data.name)} theme={myTheme} isDataLoaded={true} disabled={false} required={true}
                                                    customLabelStyles={{minWidth: '150px', width: '150px', marginTop: 0, background: 'none', color: additionalTheme.grey, fontSize: '14px', fontWeight: 'normal', border: 'none', paddingLeft: 0}}
                                                    customClassNames={`${classNames.insurerAccessSettingsBoxInput}`}
                                                    labelContainerCustomStyles={{flexDirection: 'column', marginTop: '10px', marginRight: '0px', marginLeft: '35px'}}
                                                    value={filteredInsurerAccessSettings && Array.isArray(filteredInsurerAccessSettings) && filteredInsurerAccessSettings[0] ? filteredInsurerAccessSettings[0].datas[dataIndex].value : ''} 
                                                    className={classNames.insurerAccessSettingPasswordInput} 
                                                    onChange={(event: FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string | undefined) => {
                                                        if(typeof newValue === 'string') {
                                                            const clonedData: any[] = [...userAgencyLogin.datas];
                                                            clonedData[dataIndex] = {...data, value: newValue};

                                                            if(filteredInsurerAccessSettings.length > 0) {
                                                                const cloneAllInsurerAccessSettings: UserAgencyLoginDto[] = [];
                                                                this.allInsurerAccessSettings.forEach((insurerAccessSetting: UserAgencyLoginDto) => {
                                                                    if(insurerAccessSetting.insurerId !== userAgencyLogin.insurerId || insurerAccessSetting.segment !== userAgencyLogin.segment) {
                                                                        cloneAllInsurerAccessSettings.push(insurerAccessSetting);
                                                                    } else {
                                                                        cloneAllInsurerAccessSettings.push({
                                                                            ...insurerAccessSetting,
                                                                            datas: clonedData,
                                                                        });
                                                                    }
                                                                });
                                                                this.allInsurerAccessSettings = cloneAllInsurerAccessSettings;
                                                            } else {
                                                                this.allInsurerAccessSettings = [...this.allInsurerAccessSettings, {
                                                                    ...defaultUserAgencyLogin,
                                                                    insurerId: userAgencyLogin.insurerId,
                                                                    datas: clonedData,
                                                                }];
                                                            }
                                                            this.forceUpdate();
                                                        }
                                                    }}
                                                />

                                                <Icon iconName='RedEye' className={classNames.inputIcon} title={L('Hold to reveal password')} 
                                                    onMouseDown={() => this.toggleShowFilePassword(userAgencyLoginIndex, true)} onMouseUp={() => this.toggleShowFilePassword(userAgencyLoginIndex, false)}
                                                    style={{color: this.showInsurerAccessSettingPassword[userAgencyLoginIndex] ? 'green' : 'black'}}
                                                />
                                            </div>)
                                        } else {
                                            return <></>;
                                        }
                                    })}
                                </Stack>
                            })
                        }
                    </div>

                    <PrimaryButton theme={myTheme} style={{marginTop: 25}}
                        onClick={() => this.saveInsurerAccessSettings()} 
                        text={L('Save changes')} iconProps={{ iconName: 'none' }}
                        disabled={this.asyncActionInProgress || !hasInsurerAccessSettingsChanged}
                    />

                    {this.showMessageBar &&
                        <MessageBar messageBarType={this.messageBarType} isMultiline={false} className={classNames.messageBar} onDismiss={() => {
                            this.showMessageBar = false;
                            this.forceUpdate();
                        }}>
                            {`${this.messageBarText}`}
                        </MessageBar>
                    }
                </PivotItem>
            }
        </Pivot>
    }
}