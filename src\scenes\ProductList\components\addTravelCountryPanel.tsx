import { ProductDto } from '../../../services/product/productDto';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { AddTravelCountryContentView } from '../../Product/components/addTravelCountryContentView';

export class AddTravelCountryPanel extends GenericPanel {
    getPanelTitle(): string {
        return L("Travel");
    }

    renderConfirm = () => {
        return <></>;
    };

    renderContent() {
        return <AddTravelCountryContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ProductDto } customData={this.props.customData} />;
    }
}