import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";
import { CustomAttributeDto } from "./customAttributeDto";
import { OrderItemStatus } from "../enums/orderItemStatusEnums";

export interface OrderItemDto extends BaseApiEntityModel {
    Id: string;
    OrderItemGuid: string;
    ProductId: string;
    Sku: string;
    VendorId: string;
    WarehouseId: string;
    SeId: string;
    Quantity: number;
    IsShipEnabled: boolean;
    OpenQty: number;
    ShipQty: number;
    CancelQty: number;
    CancelAmount: number;
    ReturnQty: number;
    Status: OrderItemStatus;
    TaxRate: number;
    UnitPriceWithoutDiscInclTax: number;
    UnitPriceWithoutDiscExclTax: number;
    UnitPriceInclTax: number;
    UnitPriceExclTax: number;
    PriceInclTax: number;
    PriceExclTax: number;
    DiscountAmountInclTax: number;
    DiscountAmountExclTax: number;
    OriginalProductCost: number;
    AttributeDescription: string;
    Attributes: CustomAttributeDto[];
    DownloadCount: number;
    IsDownloadActivated: boolean;
    LicenseDownloadId: string;
    ItemWeight: number | null;
    RentalStartDateUtc: string | null;
    RentalEndDateUtc: string | null;
    CreatedOnUtc: string;
    Commission: number;
    CId: string;
}