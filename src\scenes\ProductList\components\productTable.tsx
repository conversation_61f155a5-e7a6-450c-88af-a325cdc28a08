import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { isGranted, L } from '../../../lib/abpUtility';
import { ProductDto } from '../../../services/product/productDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { ProductPanel } from './productPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { RouterPath } from "../../../components/Router/router.config";
import { Dialog, DialogType, Link } from '@fluentui/react';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { findHiddenAttributes } from "../../../utils/storeUtils";

export class ProductTable extends FluentTableBase<ProductDto> {
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";

  getItemDisplayNameOf(item: ProductDto): string {
    return item.Name
  }

  getColumns(): ITableColumn[] {
    return ProductTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Name'),
        fieldName: 'Name',
        onRender: (item: any): any => {
          // if (!isGranted("Management.User")) {
          //   return item.Name;
          // }
          return <Link style={{color:myTheme.palette.neutralDark}} onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Product}/${item.Id}`);
                      }} 
                        href={`/${RouterPath.Product}/${item.Id}`}>
                  {item.Name}
                </Link>
        }
      },
      {
        name: L('Short description'),
        fieldName: 'ShortDescription',
      },
      {
        name: L('Type'),
        fieldName: 'ProductTypeId',
      },
      {
        name: L('Has attributes'),
        fieldName: 'ProductAttributeMappings',
        onRender: (item: ProductDto) => {
          return findHiddenAttributes(item.ProductAttributeMappings).itemsCountWithoutHidden > 0 ? (
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.green, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
          ) : (
            <span style={{color: additionalTheme.white, backgroundColor: additionalTheme.lighterRed, padding: '2px 5px', borderRadius: '2px'}}>{L('No')}</span>
          );
        }
      },
      {
        name: L('Published'),
        fieldName: 'Published',
        onRender: (item: ProductDto) => {
          return item.Published ? (
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.green, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
          ) : (
            <span style={{color: additionalTheme.white, backgroundColor: additionalTheme.lighterRed, padding: '2px 5px', borderRadius: '2px'}}>{L('No')}</span>
          );
        }
      }
    ];
  }

  getTitle(): string {
    return L('Products');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: isGranted('Product.Get.O') || isGranted('Product.Get.S') || isGranted('Product.Get.A') ? true : false,
      delete: false,
      customActions: false,
    };
  }

  // getCommandBarBaseProps() {
  //   let props = super.getCommandBarBaseProps();
  //   return {
  //     ...props,
  //     customActionsProps: [
  //       {
  //         displayFor: 'single',
  //         buttonText: L("Create calculation"),
  //         buttonIcon: "AddToShoppingList",
  //       }
  //     ],
  //     customActions: [
  //       (item: ProductDto) => {
  //         if(!item.Published) {
  //           this.togglePopUpDialog("Action unavailable", "You cannot perform this action on an unpublished product.");
  //         } else if(item.Published && findHiddenAttributes(item.ProductAttributeMappings).itemsCountWithoutHidden > 0) {
  //           this.props.history.push(`/${RouterPath.PolicyCalculation}/?entityType=product&entityId=${item.Id}`);
  //         } else {
  //           this.togglePopUpDialog("Action unavailable", "This product does not have any attributes.");
  //         }
  //       }
  //     ]
  //   }
  // }

  private togglePopUpDialog(title: string, text: string, visibility?: boolean) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.forceUpdate();
  }

  private dialogClose() {
    this.showPopUpDialog = false;
    this.forceUpdate();
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <Dialog
        hidden={!this.showPopUpDialog}
        onDismiss={() => this.dialogClose()}
        dialogContentProps={{
            type: DialogType.normal,
            title: L(this.popUpDialogTitle),
            subText: L(this.popUpDialogText),
        }}
        modalProps={{
            isBlocking: true
        }}
      >
      </Dialog>

      <ProductPanel
        {...props}
      />
    </>
  }
}