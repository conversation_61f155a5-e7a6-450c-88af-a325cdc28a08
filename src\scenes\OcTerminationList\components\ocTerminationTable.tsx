import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { isGranted, L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { OcTerminationPanel } from './ocTerminationPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { OcTerminationDto } from "../../../services/ocTermination/dto/ocTerminationDto";
import { additionalTheme, myTheme } from "../../../styles/theme";
import { Dialog, DialogType, Icon } from "@fluentui/react";
import { dateFormat } from "../../../utils/utils";
import ocTerminationService from "../../../services/ocTermination/ocTerminationService";

export class OcTerminationTable extends FluentTableBase<OcTerminationDto> {
  private shouldReloadItems: boolean = false;
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";

  getItemDisplayNameOf(item: OcTerminationDto): string {
    if(!!item.id) {
      return item.id;
    } else {
      return item.id;
    }
  }

  getColumns(): ITableColumn[] {
    return OcTerminationTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('ID'),
        fieldName: 'id',
        minWidth: 80,
        maxWidth: 80,
      },
      {
        name: L('Agent'),
        fieldName: 'agent.fullName',
        minWidth: 150,
        maxWidth: 150,
        onRender: (item: any): any => {
          return item.agent ? (item.agent.fullName ? item.agent.fullName : `${item.agent.name} ${item.agent.surname}`) : '';
        }
      },
      {
        name: L('Client name'),
        fieldName: 'clientFullName',
        minWidth: 150,
        maxWidth: 150,
      },
      {
        name: L('Registration number'),
        fieldName: 'registrationNumber',
        minWidth: 150,
        maxWidth: 150,
      },
      {
        name: L('Status'),
        fieldName: 'status',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any): any => {
          let textColor = additionalTheme.white;
          let background = myTheme.palette.orange;

          if (item.status === 'Prepared') {
            textColor = additionalTheme.white;
            background = myTheme.palette.green;
          }  if (item.status === 'Expired') {
            textColor = additionalTheme.white;
            background = additionalTheme.lighterRed;
          } if (item.status === 'Sended') {
            textColor = additionalTheme.white;
            background = myTheme.palette.green;
          } if (item.status === 'Signed') {
            textColor = additionalTheme.white;
            background = myTheme.palette.green;
          }
          return (
              <span style={{ color:textColor, backgroundColor: background, padding: '2px 5px', borderRadius: '2px' }}>
            {L(item.status)}
          </span>
        );

        }
      },
      {
        name: L('Creation way'),
        fieldName: 'creationWay',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any): any => {
          return <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.orange, padding: '2px 5px', borderRadius: '2px' }}>
            {L(item.creationWay)}
          </span>;
        }
      },
      {
        name: L('Creation date'),
        fieldName: 'creationTime',
        minWidth: 150,
        maxWidth: 150,
        onRender: (item: any): any => {
          return dateFormat(item.creationTime);
        }
      },
      {
        name: L('Download file'),
        fieldName: 'linkToFile',
        minWidth: 150,
        maxWidth: 150,
        onRender: (item: any): any => {
          return !!item.linkToFile ? <a style={{color: myTheme.palette.neutralDark}} href={item.linkToFile} download={item.displayedFileName} title={`${L("Download file")} ${item.displayedFileName}`}>
            {L("Download")} <Icon iconName="Download" style={{marginLeft: '5px'}} />
          </a>
          :
          <Icon iconName="StatusCircleErrorX" />;
        }
      },
    ];
  }

  private reloadListOnDialogClose() {
    this.showPopUpDialog = false;

    if(this.shouldReloadItems) {
      this.reloadItems();
    }

    this.forceUpdate();
  }

  private async reloadItems() {
    this.selectionSetAllSelected(false);
    if(typeof this.props.refreshItems !== 'undefined') {
      await this.props.refreshItems!();
    } else {
      this.togglePopUpDialog("Error", "The list could not be refreshed automatically, please refresh the page manually.");
    }
  }

  private togglePopUpDialog(title: string, text: string, visibility?: boolean) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.forceUpdate();
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: false,
      customActions: true,
    };
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    return {
      ...props,
      setButtons: () => {
        return {
          'newItem': {text: 'New', icon: 'Add'},
          'delete': {text: 'Delete', icon: 'Delete'},
          'edit': {text: 'Edit'},
          'newMsg': {text: 'Msg', icon: ''},
        }
      },
      customActionsProps: [
        isGranted('TerminationOCContract.Duplicate.O') || isGranted('TerminationOCContract.Duplicate.S') || isGranted('TerminationOCContract.Duplicate.A') ? {
          displayFor: 'single',
          buttonText: L("Duplicate"),
          buttonIcon: "none",
          buttonColor: myTheme.palette.black,
          buttonIconColor: myTheme.palette.black,
        } : undefined,
      ],
      customActions: [
        isGranted('TerminationOCContract.Duplicate.O') || isGranted('TerminationOCContract.Duplicate.S') || isGranted('TerminationOCContract.Duplicate.A') ? 
          async (item: OcTerminationDto) => {
            let copyResult = await ocTerminationService.copyOcTermination(parseInt(item.id));

            if(copyResult && copyResult.hasOwnProperty('id')) {
              this.reloadItems();
              this.createOrUpdateModalOpen(copyResult);
            } else {
              this.togglePopUpDialog("Error", "Error occured during attempt of oc termination duplicate.");
            }
          } : undefined,
      ]
    }
  }

  getTitle(): string {
    return L('OC termination list');
  }
  
  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
        <Dialog
          hidden={!this.showPopUpDialog}
          onDismiss={() => this.reloadListOnDialogClose()}
          dialogContentProps={{
              type: DialogType.normal,
              title: L(this.popUpDialogTitle),
              subText: L(this.popUpDialogText),
          }}
          modalProps={{
              isBlocking: true
          }}
        >
      </Dialog>

      <OcTerminationPanel
        {...props}
      />;
    </>; 
  }
}