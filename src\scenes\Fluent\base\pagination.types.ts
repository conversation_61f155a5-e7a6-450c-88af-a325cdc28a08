import { RouteComponentProps } from "react-router-dom";
import { IColumn } from '@fluentui/react';

export interface IRenderProps {
  data: Array<any>;
  total?: number,
  loading?:  boolean
}

export interface IPaginationProps extends RouteComponentProps<any> {
  render: (renderProps: IRenderProps) => JSX.Element;
  reload: boolean;
  searchPhrase: string;
  selectedClients: Array<string>;
  filter: boolean,
  add: boolean;
  footerRef: React.RefObject<HTMLDivElement>;
  dispatch: React.Dispatch<any>;
  column: IColumn;
}
  
export  interface IPaginationState {
  data: Array<any>;
  limit: number;
  page: number;
  loading: boolean;
  total: number;
}