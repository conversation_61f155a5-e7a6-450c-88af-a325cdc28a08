import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { RatingDto } from "../../../services/rating/ratingDto";
import { RatingPanel } from "./ratingPanel";
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import {additionalTheme, myTheme} from "../../../styles/theme";
import { RatingCategory } from "../../../services/rating/ratingCategoryEnums";

export class RatingTable extends FluentTableBase<RatingDto> {
  getItemDisplayNameOf(item: RatingDto): string {
    return item.title
  }

  getColumns(): ITableColumn[] {
    return RatingTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Title'),
        fieldName: 'title',
        // onRender: (item: any): any => {
        //   return <Link onClick={(e) => {
        //                 e.preventDefault();
        //                 props.history.push(`/${RouterPath.InsuranceCompany}/${item.id}`);
        //               }} 
        //                 href={`/${RouterPath.InsuranceCompany}/${item.id}`}>
        //           {item.Name}
        //         </Link>
        // }
      },
      {
        name: L('Text'),
        fieldName: 'text',
      },
      {
        name: L('Category ID'),
        fieldName: 'categoryId',
        onRender: (item: RatingDto) => {
          return <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>
            { L(RatingCategory[item.categoryId]) }
          </span>
        }
      }
    ];
  }

  getTitle(): string {
    return L('Rating list');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: false,
      delete: false,
      customActions: false,
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <RatingPanel
      {...props}
    />
  }
}