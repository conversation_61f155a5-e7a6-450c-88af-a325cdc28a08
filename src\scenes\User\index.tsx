import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import UserStore from '../../stores/userCrudStore';
import { UserDto } from '../../services/user/dto/userDto';
import { UserContentView } from './components/userContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import userService from '../../services/user/userCrudService';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	userStore: UserStore;
	match: any
}

@inject(Stores.UserStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private userId: string = this.props.match.params.id;
	private response: any = {};
	
	async componentDidMount() {
		this.response = await userService.get({ id: this.userId } as UserDto);
		this.props.userStore.model = this.response;
		this.forceUpdate();
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<UserContentView store={this.props.userStore} payload={ this.props.userStore.model as UserDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;