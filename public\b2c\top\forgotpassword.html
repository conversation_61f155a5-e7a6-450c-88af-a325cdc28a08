
<!DOCTYPE html>
<html>
<head>
    <title>User details</title>

    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!-- <link rel="stylesheet" href="https://top.a-soft.pl/b2c/top/register.css" /> -->

    <style type="text/css">
        *{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body{
            margin: 0;
            padding: 0;
        }

        #panel{
            display: flex;
            margin: auto;
            width: 463px;
            height: 100vh;
            justify-content: center;
            align-items: center;
        }

        .intro h2{
            margin: 21px 0 21px 0;
            text-align: center;
            font-style: normal;
            font-weight: normal;
            font-size: 48px;
            line-height: 64px;
        }

        input{
            width: 323px;
            height: 48px;
            border: 1px solid #928E8E;
            box-sizing: border-box;
            border-radius: 5px;
            margin: 0 0 10px 0;
            text-align: left;
            padding: 0 0 0 16.95px;
            font-size: 18px;
        }

        button{
            width: 323px;
            height: 42px;
            background: #0078d4;
            border-radius: 5px;
            border: none;
            color: white;
            font-style: normal;
            font-weight: normal;
            font-size: 18px;
            line-height: 24px;
            margin: 10px 0 0 0;
        }

        #emailVerificationControl_but_send_code{
            margin: 10px 0 10px 0;
            background: #0078d4;
            border-radius: 5px;
            border: none;
            color: white;
            font-style: normal;
            font-weight: normal;
        }
        #emailVerificationControl_but_verify_code {
            background: #0078d4;
            border-radius: 5px;
            border: none;
            color: white;
            font-style: normal;
            font-weight: normal;
        }
        #emailVerificationControl_but_send_new_code {
            background: #0078d4;
            border-radius: 5px;
            border: none;
            color: white;
            font-style: normal;
            font-weight: normal;
        }
        #emailVerificationControl_but_change_claims {
            background: #0078d4;
            border-radius: 5px;
            border: none;
            color: white;
            font-style: normal;
            font-weight: normal;
        }

        li{
            list-style: none;
        }

        ul{
            padding: 0;
        }

        #forgotPassword{
            text-decoration: none;
            font-size: 14px;
        }

        #email, #emailVerificationCode{
            width: 323px;
            height: 48px;
            border: 1px solid #928E8E;
            box-sizing: border-box;
            border-radius: 5px;
            margin: 0 0 10px 0;
            text-align: left;
            padding: 0 0 0 16.95px;
            font-size: 18px;
        }

        .create{
            font-size: 14px;
        }

        .create a{
            text-decoration: none;
            margin: 0 0 0 28px;
        }
        #cancel {
            background: #0078d4;
            border-radius: 5px;
            border: none;
            color: white;
            font-style: normal;
            font-weight: normal;
        }
        #continue {
            background: #0078d4;
            border-radius: 5px;
            border: none;
            color: white;
            font-style: normal;
            font-weight: normal;
        }

        #brand{
            display: flex;
            justify-content: center;
            margin: 0 0 21px 0;
        }

        #panel_center{
            display: flex;
            justify-content: center;
            flex-direction: column;
        }

        #panel_layout{
            height: 870px;
        }

        #api{
            display: flex;
            align-items: center;
            flex-direction: column;
        }

        .buttons{
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .attrEntry{
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
    </style>
</head>
<body>
    <div class="panel" id="panel">
        <table class="panel_layout" role="presentation">
            <tbody>
                <tr class="panel_layout_row">
                    <td id="panel_left" />
                    <td id="panel_center">
                        <div id="brand" style="width: 300px;">
                            <img src="https://toptmp.blob.core.windows.net/public-files/images/logoTopHorizontalBlue.png" style="width: 100%; margin-bottom: 30px;" alt="">       
                        </div>
                        <div class="inner_container">
                            <div class="api_container normaltext">
                                <img alt="Company Logo" class="companyLogo" style="display: none;"
                                    data-tenant-branding-logo="true" />
                                <div id="api">
                                    
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <script>
        document.querySelectorAll("label").forEach(element => {
            element.remove();
        });

        document.querySelectorAll(".helpLink").forEach(element => {
            element.remove();
        });
    </script>
</body>
</html>
