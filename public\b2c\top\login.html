<!DOCTYPE html>
<html lang="pl">

<head>

    <title>Sign up or sign in</title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <meta name="locale" content="en-US">
    <meta name="ROBOTS" content="NONE, NOARCHIVE">
    <meta name="GOOGLEBOT" content="NOARCHIVE">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes">
    <!-- <link rel="stylesheet" href="https://top.a-soft.pl/b2c/top/login.css" /> -->
</head>

<body>
    <div class="panel" id="panel">
        <table class="panel_layout" role="presentation">
            <tbody>
                <tr class="panel_layout_row">
                    <td id="panel_left" ></td>
                    <td id="panel_center">
                        <div id="brand" style="width: 300px;">
                            <img src="https://toptmp.blob.core.windows.net/public-files/images/logoTopHorizontalBlue.png" style="width: 100%; margin-bottom: 30px;" alt="">
                            <!-- <svg width="121" height="121" viewBox="0 0 121 121" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="60.5" cy="60.5" r="60.5" fill="#233762"/>
                            </svg>             -->
                        </div>
                        <div class="inner_container">
                            <div class="api_container normaltext">
                                <img alt="Company Logo" class="companyLogo" style="display: none;"
                                    data-tenant-branding-logo="true" />
                                <div id="api" data-name="Unified" role="main">
                                    <form id="localAccountForm" action="JavaScript:void(0);" class="localAccount"
                                        aria-label="Zaloguj się za pomocą: adres e-mail">
                                        <div class="create">
                                            <p>
                                                Nie masz konta?<a id="createAccount"
                                                    href="/toptest3.onmicrosoft.com/B2C_1_Wojtek_Test_CustomLayout/api/CombinedSigninAndSignup/unified?local=signup&amp;csrf_token=dEtwRUIxdDhlanROY01INVRPVUNJcm5WS0U4bVFJbHh0MTMvakUzMlB0RVRGUE94UUxnUjRaQ2F3VGhiRTZLWXM1NzVZeG1MTVplRUtKSW9jbDRXdUE9PTsyMDIyLTAyLTAyVDE2OjUzOjI5LjcyNTQ0NDVaO1dwWXMrMGdQV3JFNUtvdHd3WHVPNUE9PTt7Ik9yY2hlc3RyYXRpb25TdGVwIjoxfQ==&amp;tx=StateProperties=eyJUSUQiOiJiY2I1YTRhYi03M2RkLTQ0Y2UtODk0My1jNzkyYWRkN2UyYTIifQ&amp;p=B2C_1_Wojtek_Test_CustomLayout">Zarejestruj
                                                    się teraz</a>
                                            </p>
                                        </div>
                                        <div class="error pageLevel" aria-hidden="true" role="alert"
                                            style="display: none;">
                                            <p></p>
                                        </div>
                                        <div class="entry">
                                            <div class="entry-item">
                                                <label for="email">
                                                    Adres e-mail
                                                </label>
                                                <div class="error itemLevel" aria-hidden="true" role="alert"
                                                    style="display: none;">
                                                    <p></p>
                                                </div>
                                                <input type="email" id="email" name="Adres e-mail"
                                                    title="Wprowadź prawidłowy element Adres e-mail"
                                                    pattern="^[a-zA-Z0-9!#$%&amp;'+^_`{}~-]+(?:\.[a-zA-Z0-9!#$%&amp;'+^_`{}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$"
                                                    autofocus="" aria-label="Adres e-mail">
                                            </div>
                                            <div class="entry-item">
                                                <div class="password-label">
                                                    <label for="password">Hasło</label>
                                                    <a id="forgotPassword"
                                                        href="/toptest3.onmicrosoft.com/B2C_1_Wojtek_Test_CustomLayout/api/CombinedSigninAndSignup/forgotPassword?csrf_token=dEtwRUIxdDhlanROY01INVRPVUNJcm5WS0U4bVFJbHh0MTMvakUzMlB0RVRGUE94UUxnUjRaQ2F3VGhiRTZLWXM1NzVZeG1MTVplRUtKSW9jbDRXdUE9PTsyMDIyLTAyLTAyVDE2OjUzOjI5LjcyNTQ0NDVaO1dwWXMrMGdQV3JFNUtvdHd3WHVPNUE9PTt7Ik9yY2hlc3RyYXRpb25TdGVwIjoxfQ==&amp;tx=StateProperties=eyJUSUQiOiJiY2I1YTRhYi03M2RkLTQ0Y2UtODk0My1jNzkyYWRkN2UyYTIifQ&amp;p=B2C_1_Wojtek_Test_CustomLayout">Nie
                                                        pamiętasz hasła?</a>
                                                </div>
                                                <div class="error itemLevel" aria-hidden="true" style="display: none;">
                                                    <p role="alert"></p>
                                                </div>
                                                <input type="password" id="password" name="Hasło" aria-label="Hasło"
                                                    autocomplete="current-password" aria-required="true">
                                            </div>
                                            <div class="working"></div>


                                            <div class="buttons">
                                                <button id="next" type="submit" form="localAccountForm">Zaloguj
                                                    się</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <script>"use strict"; $(document).ready(function () { if (navigator.userAgent.match(/IEMobile\/10\.0/)) { let t = document.createElement("style"); t.appendChild(document.createTextNode("@-ms-viewport{width:auto!important}")), t.appendChild(document.createTextNode("@-ms-viewport{height:auto!important}")), document.getElementsByTagName("head")[0].appendChild(t) } if (navigator.userAgent.match(/MSIE 10/i)) { let e = $("#footer_links_container"); $(e).css("padding-top", "100px") } let o, i = $("#background_background_image"), n = function () { document.body.style.overflow = "hidden", ($(window).width() - 500) / $(window).height() < o ? (i.height($(window).height()), i.width("auto")) : (i.width($(window).width() - 500), i.height("auto")), document.body.style.overflow = "" }; $("<img>").attr("src", i.attr("src")).on("load", function () { o = this.width / this.height, n() }), $(window).resize(function () { n() }), "undefined" != typeof $("#MicrosoftAccountExchange") && $("#MicrosoftAccountExchange").text("Microsoft"), $("*").removeAttr("placeholder") });</script>
    <script>
        const forgotPasswordClone = document.querySelector("#forgotPassword")?.cloneNode(true);
        const signInButtonClone = document.querySelectorAll(".buttons")[0]?.cloneNode(true);
        const googleSignInButtonClone = document.querySelector("#GoogleExchange")?.cloneNode(true);
        const signUpClone = document.querySelectorAll(".create")[0]?.cloneNode(true);
        const loginLabel = document.querySelector("label[for='email']")?.cloneNode(true);
        const passwordLabel = document.querySelector("label[for='password']")?.cloneNode(true);
        const brand = document.querySelector("#brand")?.cloneNode(true);

        document.querySelector("#forgotPassword")?.remove();
        document.querySelector("#brand")?.remove();
        document.querySelector("#GoogleExchange")?.remove();
        document.querySelector("#GoogleWorkspace")?.remove();
        document.querySelectorAll(".create")[0]?.remove();
        document.querySelectorAll(".buttons")[0]?.remove();
        document.querySelectorAll(".intro")[0]?.remove();

        document.querySelectorAll(".divider").forEach(element => {
            element.remove();
        });

        document.querySelectorAll("label").forEach(element => {
            element.remove();
        });

        document.querySelector("#email").placeholder = loginLabel.innerText.trim();
        document.querySelector("#password").placeholder = passwordLabel.innerText.trim();
        
        document.querySelectorAll(".entry")[0].appendChild(forgotPasswordClone);
        document.querySelectorAll(".entry")[0].appendChild(signInButtonClone);
        document.querySelectorAll(".entry")[0].appendChild(signUpClone);
        document.querySelector("#panel_center").insertBefore(brand, document.querySelector("#panel_center").firstChild);
        document.querySelectorAll(".entry")[0].appendChild(signUpClone);
        // document.querySelectorAll(".buttons")[0].appendChild(googleSignInButtonClone);
    </script>

    <style>
        *{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body{
            margin: 0;
            padding: 0;
        }

        #panel{
            display: flex;
            margin: auto;
            width: 463px;
            height: 570px;
            justify-content: center;
        }

        .intro h2{
            margin: 21px 0 21px 0;
            text-align: center;
            font-style: normal;
            font-weight: normal;
            font-size: 48px;
            line-height: 64px;
        }

        #email, #password{
            width: 323px;
            height: 48px;
            border: 1px solid #928E8E;
            box-sizing: border-box;
            border-radius: 5px;
            margin: 0 0 10px 0;
            text-align: left;
            padding: 0 0 0 16.95px;
            font-size: 18px;
        }

        .buttons{
            height: 120px;
            display: flex;
            justify-content: center;
        }

        #next{
            width: 188px;
            height: 42px;
            background: #0078d4;
            border-radius: 5px;
            border: none;
            color: white;
            font-style: normal;
            font-weight: normal;
            font-size: 18px;
            line-height: 24px;
            margin: 28px 0 0 0;
        }

        #GoogleExchange{
            width: 188px;
            height: 42px;
            background: #233762;
            border-radius: 5px;
            border: none;
            color: white;
            font-style: normal;
            font-weight: normal;
            font-size: 18px;
            line-height: 24px;
            margin: 4px 0 0 0;
        }

        #forgotPassword{
            text-decoration: none;
            font-size: 14px;
        }

        .create{
            font-size: 14px;
        }

        .create a{
            text-decoration: none;
            margin: 0 0 0 28px;
        }

        .intro{
            display: none;
        }

        #brand{
            display: flex;
            justify-content: center;
        }

        #panel_center{
            display: flex;
            justify-content: center;
            flex-direction: column;
            height: 100vh;
        }
    </style>
</body>

</html>