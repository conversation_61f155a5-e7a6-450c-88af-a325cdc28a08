import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";

export interface InsurancePolicyDto extends BaseApiEntityModel {
  comment: string;
  conclusionDate: string;
  creationTime: string;
  creatorUserId: number;
  customerEmail: string;
  customerName: string;
  customerSurname: string;
  endDate: string;
  frequency: number;
  insurer: string;
  lastModificationTime: string;
  lastModifierUserId: number;
  offerNumber: number;
  orderDate: string;
  orderGuid: string;
  orderItemGuid: string;
  orderNumber: number;
  paymentMethod: string;
  paymentStatus: string;
  policyDate: string;
  policyNumber: string;
  policyTotal: number;
  productId: string;
  reason: string;
  segment: string;
  startDate: string;
  status: string;
  fileUrl: string;
  customerId: string;
  clientId: number;
  cancellationDate: string;
}