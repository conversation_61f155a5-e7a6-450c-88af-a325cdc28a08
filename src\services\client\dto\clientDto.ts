import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";
import { UserDto } from "../../user/dto/userDto";

export interface ClientDto extends BaseApiEntityModel {
    customerId: string;
    creatorUserId?: number | null;
    userId: number;
    user: UserDto;
    agreementsPayload: string;
    agreementsStatus: string | null;
    emailAdditional: string;
    dateOfBirth: string | null;
    company: string;
    streetAddress: string;
    streetAddress2: string;
    zipPostalCode: string;
    city: string;
    country: string;
    county: string;
    countryId: string;
    stateProvinceId: string;
    phone: string;
    pesel: string;
    nip: string;
    regon: string;
    pkDs: any[];
    mainPKD?: string;
    note: string;
    clientType: string;
    nationality: string;
    superAgentId: number | null;
    maritalStatus: string;
    drivingLicenceIssueYear?: number;
    yearOfBirthOldestChild?: number;
    lastModifierUserId?: number | null;
    lastModifierUser?: UserDto;
    creatorUser?: UserDto;
}