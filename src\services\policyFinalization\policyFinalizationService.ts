import { CrudServiceBase } from '../base/crudServiceBase';
import Endpoint from '../endpoint';
import { PolicyFinalizationDto } from './policyFinalizationDto';
import { httpApi } from '../httpService';

export class PolicyFinalizationService extends CrudServiceBase<PolicyFinalizationDto> {
    constructor() {
        super(Endpoint.PolicyFinalization);
        this.internalHttp = httpApi;
    }

    async finalize(requestBody: any) {
        return await this.internalHttp.post(this.endpoint.Custom("Finalize", true), requestBody);
    }

    async finalizeWithAcceptanceUw(requestBody: any) {
        return await this.internalHttp.post(this.endpoint.Custom("FinalizeWithAcceptanceUW", true), requestBody);
    }
}

const exportPolicyFinalizationService: PolicyFinalizationService = new PolicyFinalizationService();
export default exportPolicyFinalizationService;