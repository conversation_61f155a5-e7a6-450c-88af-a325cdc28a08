import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import SearchStore from '../../stores/searchStore';
import OrderStore from '../../stores/orderStore';
import { OrderTable } from './components/orderTable';
import { FocusZone, FocusZoneDirection, FocusZoneTabbableElements, IDropdownOption, mergeStyleSets } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import { DropdownBase } from '../BaseComponents';
import { OrderStatus } from '../../services/order/enums/orderStatusEnums';
import { enumToDropdownOptions } from '../../utils/utils';
import { CrudConsts } from '../../stores/crudStoreBase';
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  },
});

export interface IProps {
  searchStore: SearchStore;
  orderStore: OrderStore;
  history: any;
}

export interface IState {
  filterByStatus: number;
  reloadingItems: boolean;
  gotNewItems: boolean;
  items: any[];
  customRequest: any;
} 

@inject(Stores.SearchStore)
@inject(Stores.OrderStore)
@observer
class Index extends AppComponentBase<IProps, IState> {
  constructor(props: IProps) {
    super(props);

    this.state = {
      ...this.state,
      items: [],
      filterByStatus: 0,
      reloadingItems: false,
      gotNewItems: true,
      customRequest: {...this.props.orderStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '', maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE},
    };
  }

  private async setCustomRequest(newFilterByStatus: number | undefined) {
    const requestPayload: any = {...this.props.orderStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '',
                                  maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE};

    if(typeof newFilterByStatus === 'number' && newFilterByStatus > 0) {
      requestPayload['status'] = OrderStatus[newFilterByStatus];
    }

    this.setState((prevState) => ({...prevState, customRequest: requestPayload, 
                                    filterByStatus: typeof newFilterByStatus === 'number' ? newFilterByStatus : this.state.filterByStatus,
                                  }));
  }

  private async refreshItems() {
    this.setState((prevState) => ({...prevState, items: [], reloadingItems: true }));

    await this.props.orderStore.getAllLazy().then(() => {
      this.setState((prevState) => ({...prevState, reloadingItems: false, gotNewItems: true }));
    });
  }

  public render() {
    if(!this.state.reloadingItems && this.state.gotNewItems) {
      const items = this.props.orderStore.dataSet ? this.props.orderStore.dataSet.items : [];
      
      if(items.length > 0) {
        this.setState((prevState) => ({...prevState, items: items, gotNewItems: false }));
      }
    }
    
    let filterByStatusDropdownOptions: IDropdownOption[] = [
      { key: 0, text: L('All2') },
      ...enumToDropdownOptions(OrderStatus, true, true)
    ];

    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Order List')}</h2>
        </div>
        <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all}>
          <DropdownBase key={'filterByStatusDropdown'} required={false} label={L('Filter by status')} options={filterByStatusDropdownOptions}
            value={this.state.filterByStatus} disabled={false} isDataLoaded={true} customLabelStyles={{maxWidth: 200, whiteSpace: 'nowrap', marginRight:'30px'}}
            onChange={(e: string | number | undefined) => {
              if(typeof e === 'number' && e !== this.state.filterByStatus) {
                this.setCustomRequest(e);
              }
            }} />
        </FocusZone>
        
        <OrderTable
          searchText={this.props.searchStore.searchText}
          items={this.state.items}
          store={this.props.orderStore}
          history={this.props.history}
          scrollablePanelMarginTop={220}
          refreshItems={() => this.refreshItems()}
          customData={{
            customRequest: this.state.customRequest,
          }}
        />
      </>
    );
  }
}

export default Index;