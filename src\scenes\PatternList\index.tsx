import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import { PatternTable } from './components/patternTable';
import PatternStore from '../../stores/patternStore';
import {L} from "../../lib/abpUtility";
import {mergeStyleSets} from "@fluentui/react";
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
});

export interface IProps {
  searchStore: SearchStore;
  patternStore: PatternStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.PatternStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  public render() {
    let items = this.props.patternStore.dataSet ? this.props.patternStore.dataSet.items : [];
    
    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Pattern List')}</h2>
        </div>
        <PatternTable
          searchText={this.props.searchStore.searchText}
          items={items}
          store={this.props.patternStore}
          history={this.props.history}
        />
      </>
    );
  }
}

export default Index;