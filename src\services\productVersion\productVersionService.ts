import { isUserLoggedIn } from '../../utils/authUtils';
import { CrudServiceBase } from '../base/crudServiceBase';
import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { ProductVersionDto } from './dto/productVersionDto';

export class ProductVersionService extends CrudServiceBase<ProductVersionDto> {
    constructor() {
        super(Endpoint.ProductVersion);
        this.internalHttp = httpApi;
    }

    public async UploadProductsVersion(payload: any) {
        isUserLoggedIn();
        let result = await httpApi.put(this.endpoint.Custom(`UploadProductsVersion`, false), payload);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async AddCurrentProductsVersion() {
        isUserLoggedIn();
        let result = await httpApi.put(this.endpoint.Custom(`AddCurrentProductsVersion`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async Delete(productVersionId: number) {
        isUserLoggedIn();
        let result = await this.internalHttp.delete(this.endpoint.Custom(`Delete?Id=${productVersionId}`, false));
        return result.data;
    }

    public async SetActive(productVersionId: number) {
        isUserLoggedIn();
        let result = await this.internalHttp.put(this.endpoint.Custom(`SetActive?id=${productVersionId}`, false));
        return result.data;
    }
}

const exportRatingService: ProductVersionService = new ProductVersionService();
export default exportRatingService;