import { inject, observer } from 'mobx-react';

import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { PatternDto } from '../../services/pattern/dto/patternDto';
import { PatternContentView } from './components/patternContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import PatternStore from '../../stores/patternStore';
import patternService from '../../services/pattern/patternService';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	patternStore: PatternStore;
	match: any
}

@inject(Stores.PatternStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private patternId: string = this.props.match.params.id;

	async componentDidMount() {
		this.props.patternStore.model = await patternService.get({ id: this.patternId } as PatternDto);;
		this.forceUpdate();
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<PatternContentView store={this.props.patternStore} payload={ this.props.patternStore.model as PatternDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;