import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import { ClaimTable } from './components/claimTable';
import ClaimStore from '../../stores/claimStore';
import {L} from "../../lib/abpUtility";
import {mergeStyleSets} from "@fluentui/react";
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
});

export interface IProps {
  searchStore: SearchStore;
  claimStore: ClaimStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.ClaimStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  public render() {
    let items = this.props.claimStore.dataSet ? this.props.claimStore.dataSet.items : [];
    
    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Claim List')}</h2>
        </div>
        <ClaimTable
          searchText={this.props.searchStore.searchText}
          items={items}
          store={this.props.claimStore}
          history={this.props.history}
        />
      </>
    );
  }
}

export default Index;