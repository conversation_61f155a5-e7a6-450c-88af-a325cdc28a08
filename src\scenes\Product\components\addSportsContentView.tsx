import { mergeStyleSets, Stack, SelectionMode, Selection, MessageBarType, MessageBar } from '@fluentui/react';
import { myTheme } from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import Stores from '../../../stores/storeIdentifier';
import { inject, observer } from 'mobx-react';
import SportDisciplineStore from '../../../stores/sportDisciplineStore';
import { InsurerAddSportsFluentListBaseWithCommandBar } from '../../BaseComponents/insurerAddSportsFluentListBaseWithCommandBar';
import sportInsuranceCoverageService from '../../../services/sportInsuranceCoverage/sportInsuranceCoverageService';
import { L } from '../../../lib/abpUtility';

const classNames = mergeStyleSets({
    contentContainer: {
        display: 'block',
        width: '100%',
        height: '500px',
        maxHeight: '500px',
        position: 'relative',
        marginTop: '20px',
    },
    smallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '10px !important',
        marginTop: '22px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    messageBar: {
		width: "fit-content",
        marginTop: "25px",
		selectors: {
			"& .ms-MessageBar-innerText": {
				selectors: {
					"& span": {
						whiteSpace: "pre-line",
					},
				},
			},
		},
	},
});

export interface IProps {
	sportDisciplineStore: SportDisciplineStore;
}

@inject(Stores.LanguageStore)
@inject(Stores.ProductAttributeStore)
@inject(Stores.SportDisciplineStore)
@observer
export class AddSportsContentView extends GenericContentView {
    private selectedSport: any;
    private showMessageBar: boolean = false;
    private messageBarType: MessageBarType = MessageBarType.success;
    private messageBarText: string = '';
    private _sportListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedSportContest: any = this._sportListSelection.getSelection();
            if(Array.isArray(selectedSportContest) && selectedSportContest.length > 0 && !!selectedSportContest[0].id) {
                this.selectedSport = selectedSportContest[0];
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private overrideAllItemsTrigger: number = 0;

    private async reloadItems() {
        if(this.props.customData && this.props.customData.fetchSportsByInsurerId) {
            await this.props.customData.fetchSportsByInsurerId();
            this.overrideAllItemsTrigger = this.overrideAllItemsTrigger + 1;
            this.forceUpdate();
        }
    }

    private handleAddSportsFromGeneralList = async () => {
        this.asyncActionInProgress = true;
        this.forceUpdate();
        await sportInsuranceCoverageService.createSportCoverageListForInsurerId(this.props.customData.insurerId).then((result) => {
            if(result) {
                this.showMessageBar = true;
                this.asyncActionInProgress = false;
                this.messageBarType = MessageBarType.success;
                this.messageBarText = L('Successfully added sport disciplines from the general list');
                this.forceUpdate();
            }
        })
    }

    renderContent() {
        return <>
            <Stack>
                <div className={classNames.contentContainer}>
                    <InsurerAddSportsFluentListBaseWithCommandBar 
                        store={this.props.sportDisciplineStore!}
                        items={
                            this.props.sportDisciplineStore?.dataSet && this.props.sportDisciplineStore?.dataSet.items
                                ? this.props.sportDisciplineStore?.dataSet.items
                                : []
                        }
                        customSelection={this._sportListSelection}
                        searchText={''}
                        history={this.props.history}
                        customData={{
                            selectedSport: this.selectedSport,
                            overrideAllItemsTrigger: this.overrideAllItemsTrigger,
                            insurerId: this.props.customData.insurerId,
                            disableGetAllOnMount: true,
                            handleAddSportsFromGeneralList: this.handleAddSportsFromGeneralList
                        }}
                        scrollablePanelMarginTop={120}
                        refreshItems={() => this.reloadItems()}
                    />
                </div>
                {this.showMessageBar &&
                    <MessageBar messageBarType={this.messageBarType} isMultiline={false} className={classNames.messageBar} onDismiss={() => {
                        this.showMessageBar = false;
                        this.forceUpdate();
                    }}>
                        {`${this.messageBarText}`}
                    </MessageBar>
                }
            </Stack>
        </>
    }
}