import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { RatingContentView } from './components/ratingContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import RatingStore from '../../stores/ratingStore';
import { RatingDto } from '../../services/rating/ratingDto';
import { L } from '../../lib/abpUtility';
import { isConfigForAG, isConfigForProduction } from '../../utils/authUtils';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	ratingStore: RatingStore;
	match: any
}

@inject(Stores.RatingStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private ratingId = this.props.match.params.id;

	async componentDidMount() {
		if(isConfigForAG() || isConfigForProduction()) {
			window.location.href = '/exception';
		}
		
		await this.props.ratingStore.get({ id: this.ratingId } as RatingDto);
	}

	public render() {
		if(isConfigForAG() || isConfigForProduction()) {
			return <>
				<p style={{marginTop: 50, fontWeight: 'bold', fontSize: '1.5rem'}}>{L('You do not have access to this page.')}</p>
			</>;
		} else {
			return (
				<FocusZone as="div" className={classNames.subpageContentWrapper}>
					<RatingContentView store={this.props.ratingStore} payload={ this.props.ratingStore.model as RatingDto } renderFooter={{show: true}} />
				</FocusZone>
			);
		}
	}
}

export default Index;