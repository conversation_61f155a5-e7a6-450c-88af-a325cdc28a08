import { Spinner, SpinnerSize } from '@fluentui/react';
import * as React from 'react';
import { L } from '../../../lib/abpUtility';
import { PatternTypeEnum } from '../../../services/pattern/patternTypeEnums';
import { getPatternText } from '../../../utils/utils';
import createOrUpdateClassNames from '../../BaseComponents/createOrUpdate';

export interface IState {
  text: string;
  asyncActionInProgress: boolean;
}

export class TermsAndConditionsPage<TProps> extends React.Component<TProps, IState>{
  constructor(props: any){
    super(props);
    
    this.state = {
      text : "",
      asyncActionInProgress: false,
    }
  };

  async componentDidMount() {
    this.setState({ asyncActionInProgress: true });
    
    let textFromPattern = await getPatternText(PatternTypeEnum.TermsAndConditions);

    this.setState({ text: textFromPattern, asyncActionInProgress: false });
  };

  render() {
    return <div style={{marginLeft: 60, marginRight: 500,}}>
      {this.state.asyncActionInProgress &&
        <Spinner label={L('Please wait...')} className={createOrUpdateClassNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
      }

      <div dangerouslySetInnerHTML={{ __html: this.state.text }} />
    </div>
  };
}