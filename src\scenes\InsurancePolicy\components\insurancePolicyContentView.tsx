import { Default<PERSON>utt<PERSON>, <PERSON>alog, <PERSON>alog<PERSON>ontent, DialogFooter, DialogType, mergeStyleSets, MessageBar, MessageBarType, Pivot, PivotItem, PrimaryButton, Spinner, SpinnerSize, Stack, Text } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { InsurancePolicyDto } from '../../../services/insurancePolicy/insurancePolicyDto';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { defaultInsurancePolicy } from '../../../stores/insurancePolicyStore';
import React from 'react';
import { uploadFileToAzure, uploadSignedAPKFileToAzure } from '../../../services/azureService';
import policyAttachedFilesService from '../../../services/attachedFiles/policyAttachedFilesService';
import { catchErrorMessage, dateFormat, enumToDropdownOptions } from '../../../utils/utils';
import apkAttachedFilesService from '../../../services/apkAttachedFiles/apkAttachedFilesService';
import { InsurancePolicyCancelReason } from '../../../services/insurancePolicy/insurancePolicyCancelReasonEnums';
import { DropdownBase } from '../../BaseComponents/dropdownBase';
import { DatePickerBase } from '../../BaseComponents/datePickerBase';
import { CheckBoxBase } from '../../BaseComponents/CheckBoxBase';
import cancelledPolicyAttachedFilesService from '../../../services/attachedFiles/cancelledPolicyAttachedFilesService';
import insurancePolicyService from '../../../services/insurancePolicy/insurancePolicyService';
import { LabeledTextField } from '../../../components/LabeledTextField';
import { InsurancePolicyStatus } from '../../../services/insurancePolicy/insurancePolicyStatusEnums';
import moment from 'moment';

const classNames = mergeStyleSets({
    downloadButton: {
        width: 'fit-content',
        padding: '25px 50px',
        marginTop: '20px',
        marginRight: '20px',
    },
    uploadButton: {
        width: 'fit-content',
        padding: '25px 50px',
        marginTop: '25px',
    },
    filePreviewWrapper: {
        display: 'flex',
        marginTop: '10px',
        flexWrap: 'nowrap',
        flexDirection: 'row',
        alignItems: 'center',
        selectors: {
            '& p': {
                marginRight: '10px',
            }
        }
    },
    attachedFilesLabel: {
        fontWeight: 'bold',
        marginTop: '20px',
        marginBottom: '10px',
    },
    attachedFilesListItem: {
        marginBottom: '5px',
    },
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '25px',
        marginTop: '35px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    messageBar: {
        width: 'fit-content',
        marginLeft: '25px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
    fontBold: {
        fontWeight: '800',
    },
    sectionContainterTitle: {
        position: 'absolute',
        top: '0',
        left: '21px',
        padding: '2px 4px',
        transform: 'translateY(-60%)',
        boxSizing: 'border-box',
        background: myTheme.palette.white,
    },
    sectionContainter: {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        width: 'fit-content',
        minWidth: '30%',
        maxWidth: '95%',
        minHeight: '150px',
        border: `1px solid ${myTheme.palette.neutralPrimary}`,
        padding: '10px 25px 25px',
        selectors: {
            ':first-child': {
                marginTop: '25px',
            }
        }
    },
});

export class InsurancePolicyContentView extends GenericContentView {
    private insurancePolicy: InsurancePolicyDto = defaultInsurancePolicy;
    private insurancePolicyExtendedData: any = undefined;
    private fileUploadInputRef: any;
    private apkFileUploadInputRef: any;
    private cancelPolicyFileUploadInputRef: any;
    private selectedFileForUpload: any = {
        name: "" as string,
        src: "" as string,
    };
    asyncActionInProgress: boolean = false;
    private attachedFiles: any;
    private apkAttachedFiles: any;
    private cancelPolicyAttachedFiles: any;
    private isEditMode: boolean = false;
    private showCancelPolicyDialog: boolean = false;
    private cancelPolicyData: any = {
        'reason': '',
        'date': '',
        'gotConfirmation': false,
        'otherReason': '',
    };
    private cancelPolicyAttachedFileError: string = '';

    constructor(props: any) {
        super(props);
        this.fileUploadInputRef = React.createRef();
        this.apkFileUploadInputRef = React.createRef();
        this.cancelPolicyFileUploadInputRef = React.createRef();
    }

    async componentDidMount() {
        this.asyncActionInProgress = true;
        this.attachedFiles = await policyAttachedFilesService.getByPolicyId(typeof this.insurancePolicy.id === 'string' ? parseInt(this.insurancePolicy.id) : this.insurancePolicy.id);
        this.apkAttachedFiles = await apkAttachedFilesService.getByPolicyId(typeof this.insurancePolicy.id === 'string' ? parseInt(this.insurancePolicy.id) : this.insurancePolicy.id);
        this.cancelPolicyAttachedFiles = await cancelledPolicyAttachedFilesService.getByPolicyId(typeof this.insurancePolicy.id === 'string' ? parseInt(this.insurancePolicy.id) : this.insurancePolicy.id);
        
        this.checkIfDataIsLoaded("insurancePolicy");

        if(this.insurancePolicy.status === InsurancePolicyStatus.Annulled) {
            await insurancePolicyService.getExtended(typeof this.insurancePolicy.id === 'string' ? parseInt(this.insurancePolicy.id) : this.insurancePolicy.id).then((response: any) => {
                if(response && response.id) {
                    this.insurancePolicyExtendedData = response;
                }
            }).catch((error: any) => {
                console.error(error);
            });
        }

        this.asyncActionInProgress = false;

        this.forceUpdate();
    }

    async componentDidUpdate() {
        if(!this.asyncActionInProgress && !this.apkAttachedFiles && !!this.insurancePolicy.id) {
            this.asyncActionInProgress = true;
            this.apkAttachedFiles = await apkAttachedFilesService.getByPolicyId(parseInt(this.insurancePolicy.id));
            this.asyncActionInProgress = false;
        }
    }

    renderConfirm = () => {
        if(this.isEditMode === true) {
            return <PrimaryButton theme={myTheme} onClick={this._onConfirm} text={L('Save')} iconProps={{ iconName: 'Save' }} disabled={this.asyncActionInProgress} />;
        } else {
            return <></>;
        }
    };

    triggerUpload = () => {
        this.fileUploadInputRef.current.click();
    };

    triggerUploadApk = () => {
        this.apkFileUploadInputRef.current.click();
    }

    triggerUploadCancelPolicy = () => {
        this.cancelPolicyFileUploadInputRef.current.click();
    }

    async cancelPolicy() {
        if(this.insurancePolicy) {
            this.asyncActionInProgress = true;
            this.forceUpdate();

            const cancelPolicyReason: string = this.cancelPolicyData.reason === InsurancePolicyCancelReason.other && !!this.cancelPolicyData.otherReason ? 
                                                `${this.cancelPolicyData.reason} - ${this.cancelPolicyData.otherReason}` : this.cancelPolicyData.reason;

            await insurancePolicyService.ManualCancelPolicy(parseInt(this.insurancePolicy.id), cancelPolicyReason, this.cancelPolicyData.date, this.cancelPolicyData.gotConfirmation).then(async (response: any) => {
                await insurancePolicyService.getSingle(parseInt(this.insurancePolicy.id)).then((response: any) => {
                    if(response && response.data && response.data.result && response.data.result.id) {
                        this.insurancePolicy = response.data.result;
                    }
                }).catch((error: any) => {
                    console.error(error);
                });

                await insurancePolicyService.getExtended(parseInt(this.insurancePolicy.id)).then((response: any) => {
                    if(response && response.id) {
                        this.insurancePolicyExtendedData = response;
                    }
                }).catch((error: any) => {
                    console.error(error);
                });

                this.showCancelPolicyDialog = false;
                this.asyncActionInProgress = false;
                this.forceUpdate();
            }).catch((error: any) => {
                console.error(error);
                this.showCancelPolicyDialog = false;    
                this.asyncActionInProgress = false;
                this.forceUpdate();
            });
        }
    }

    async onUpload() {
        this.asyncActionInProgress = true;

        this.insurancePolicy = this.props.payload.model ? this.props.payload.model : this.props.payload;
        const selectedFile = !!this.fileUploadInputRef.current.files && this.fileUploadInputRef.current.files.length ? this.fileUploadInputRef.current.files[0] : null;
        
        if(!!selectedFile) {
            this.selectedFileForUpload.name = selectedFile.name;
            this.selectedFileForUpload.src = URL.createObjectURL(selectedFile);
        }

        this.forceUpdate();

        let result = await uploadFileToAzure(selectedFile);

        await policyAttachedFilesService.createNew({
            "customerId": !!this.insurancePolicy['customerId'] ? this.insurancePolicy['customerId'] : '',
            "fileUrl": result.url,
            "originalFileName": selectedFile.name,
            "blobFileName": result.name,
            "policyId": this.insurancePolicy.id,
            "orderId": !!this.insurancePolicy['orderId'] ? this.insurancePolicy['orderId'] : '',
            "id": 0
        }).then(async (response: any) => {
            this.attachedFiles = await policyAttachedFilesService.getByPolicyId(typeof this.insurancePolicy.id === 'string' ? parseInt(this.insurancePolicy.id) : this.insurancePolicy.id);

            this.asyncActionInProgress = false;
            this.forceUpdate();
        }).catch((error: any) => {
            console.error(error);

            this.asyncActionInProgress = false;
            this.forceUpdate();
        });
    }

    async onUploadApk() {
        this.asyncActionInProgress = true;

        this.insurancePolicy = this.props.payload.model ? this.props.payload.model : this.props.payload;
        const selectedFile = !!this.apkFileUploadInputRef.current.files && this.apkFileUploadInputRef.current.files.length ? this.apkFileUploadInputRef.current.files[0] : null;
        
        if(!!selectedFile) {
            this.selectedFileForUpload.name = selectedFile.name;
            this.selectedFileForUpload.src = URL.createObjectURL(selectedFile);
        }

        this.forceUpdate();

        await uploadSignedAPKFileToAzure(selectedFile, parseInt(this.insurancePolicy.id)).then(async (result: any) => {
            await apkAttachedFilesService.createNew({
                "customerId": !!this.insurancePolicy['customerId'] ? this.insurancePolicy['customerId'] : '',
                "fileUrl": result.url,
                "originalFileName": selectedFile.name,
                "blobFileName": result.name,
                "policyId": parseInt(this.insurancePolicy.id),
                "policyNumber": !!this.insurancePolicy.policyNumber ? this.insurancePolicy.policyNumber : "0",
                "clientId": this.insurancePolicy.clientId,
                "id": "0",
                "creationWay": "manual",
                "displayedFileName": selectedFile.name,
                "description": "",
                "segment": !!this.insurancePolicy.segment ? this.insurancePolicy.segment : "",
                "productName": !!this.insurancePolicy.segment ? this.insurancePolicy.segment : "",
                "policyIssued": true,
            }).then(async (response: any) => {
                this.apkAttachedFiles = await apkAttachedFilesService.getByPolicyId(typeof this.insurancePolicy.id === 'string' ? parseInt(this.insurancePolicy.id) : this.insurancePolicy.id);
    
                this.asyncActionInProgress = false;
                this.forceUpdate();
            }).catch((error: any) => {
                console.error(error);
    
                this.asyncActionInProgress = false;
                this.forceUpdate();
            });
        }).catch((error: any) => {
            console.error(error);
    
            this.asyncActionInProgress = false;
            this.forceUpdate();
        });
    }

    async onUploadCancelPolicy() {
        this.asyncActionInProgress = true;

        const selectedFile = !!this.cancelPolicyFileUploadInputRef.current.files && this.cancelPolicyFileUploadInputRef.current.files.length ? this.cancelPolicyFileUploadInputRef.current.files[0] : null;
        
        if(!!selectedFile) {
            this.selectedFileForUpload.name = selectedFile.name;
            this.selectedFileForUpload.src = URL.createObjectURL(selectedFile);
        }

        this.forceUpdate();

        let result = await uploadFileToAzure(selectedFile);

        await cancelledPolicyAttachedFilesService.createNew({
            'policyId': parseInt(this.insurancePolicy.id),
            'fileUrl': result.url,
            'originalFileName': selectedFile.name,
            'blobFileName': result.name,
            'description': '',
            'id': '0',
        }).then(async (response: any) => {
            this.cancelPolicyAttachedFiles = await cancelledPolicyAttachedFilesService.getByPolicyId(parseInt(this.insurancePolicy.id));

            this.asyncActionInProgress = false;
            this.forceUpdate();
        }).catch((error: any) => {
            console.error(error);
            this.cancelPolicyAttachedFileError = catchErrorMessage(error);
            this.asyncActionInProgress = false;
            this.forceUpdate();
        });
    }
    
    renderContent() {
        if(!this.insurancePolicy.id || this.insurancePolicy.id.length === 0 || parseInt(this.insurancePolicy.id) === 0) {
            this.insurancePolicy = this.props.payload.model ? this.props.payload.model : this.props.payload;
        }

        let attachedFilesList: JSX.Element[] = [];
        if(!!this.attachedFiles && !!this.attachedFiles.items && this.attachedFiles.items.length > 0) {
            this.attachedFiles.items.forEach((file: any) => {
                attachedFilesList.push(
                    <li key={file.id} className={classNames.attachedFilesListItem}>
                        {dateFormat(file.creationTime, "DD.MM.YYYY HH:mm", true)}{` - `} 
                        <a href={file.fileUrl} title={L("Download file")}>{file.originalFileName}</a>
                    </li>
                );
            });
        }

        let apkAttachedFilesList: JSX.Element[] = [];
        if(!!this.apkAttachedFiles && this.apkAttachedFiles.length > 0) {
            this.apkAttachedFiles.forEach((file: any, fileIndex: number) => {
                apkAttachedFilesList.push(
                    <li key={!!file.attachedFileId ? file.attachedFileId : fileIndex} className={classNames.attachedFilesListItem}>
                        {dateFormat(file.creationTime, "DD.MM.YYYY HH:mm", true)}{` - `} 
                        <a href={file.url} title={L("Download file")}>
                            {!!file.displayedFileName ? file.displayedFileName :
                                (!!file.originalFileName ? file.originalFileName : `${L('File name placeholder')} ${fileIndex+1}`)}
                        </a>
                    </li>
                );
            });
        }

        let cancelPolicyAttachedFilesList: JSX.Element[] = [];
        if(this.cancelPolicyAttachedFiles && this.cancelPolicyAttachedFiles.items && this.cancelPolicyAttachedFiles.items.length > 0) {
            this.cancelPolicyAttachedFiles.items.forEach((file: any, fileIndex: number) => {
                cancelPolicyAttachedFilesList.push(
                    <li key={!!file.id ? file.id : fileIndex} className={classNames.attachedFilesListItem}>
                        {dateFormat(file.creationTime, "DD.MM.YYYY HH:mm", true)}{` - `} 
                        <a href={file.fileUrl} title={L("Download file")}>
                            {!!file.displayedFileName ? file.displayedFileName :
                                (!!file.originalFileName ? file.originalFileName : `${L('File name placeholder')} ${fileIndex+1}`)}
                        </a>
                    </li>
                );
            });
        }
        
        const fileUploadButton = <PrimaryButton 
                                    className={classNames.uploadButton}
                                    theme={myTheme}
                                    text={L('Upload file')}
                                    type={'file'}
                                    onClick={this.triggerUpload}
                                    disabled={this.asyncActionInProgress === true}
                                    iconProps={{ iconName: 'Upload' }}
                                />;

        const apkFileUploadButton = <PrimaryButton 
                                        className={classNames.uploadButton}
                                        theme={myTheme}
                                        text={L('Upload signed APK file')}
                                        type={'file'}
                                        onClick={this.triggerUploadApk}
                                        disabled={this.asyncActionInProgress === true}
                                        iconProps={{ iconName: 'Upload' }}
                                        style={{marginLeft: 25}}
                                    />;

        const cancelPolicyUploadButton = <PrimaryButton 
                                            className={classNames.uploadButton}
                                            theme={myTheme}
                                            text={L('Upload cancel policy files')}
                                            type={'file'}
                                            onClick={this.triggerUploadCancelPolicy}
                                            disabled={this.asyncActionInProgress === true}
                                            iconProps={{ iconName: 'Upload' }}
                                            style={{margin: '40px auto 0'}}
                                        />;

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed,
                    }
                }
            }
        };
        
        return <>
            <Dialog
                hidden={!this.showCancelPolicyDialog}
                onDismiss={() => { this.showCancelPolicyDialog = false; this.forceUpdate(); }}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L('Insurance policy canceling'),
                }}
                modalProps={{
                    isBlocking: true,
                }}
                minWidth={625}
            >
                <DialogContent>
                    <DropdownBase key={'cancelPolicyReason'} label={L('Cancelling reason')} options={ enumToDropdownOptions(InsurancePolicyCancelReason, true, true, "string") }
                        value={this.cancelPolicyData.reason} disabled={false} isDataLoaded={true} required={true} customDropdownWidth={'300px'} customLabelStyles={{ width: "180px", minWidth: "180px" }}
                        onChange={(e: string | number | undefined) => {
                            if(this.cancelPolicyData.reason !== e) {
                                this.cancelPolicyData.reason = e;
                                this.forceUpdate();
                            }
                        }}
                    />

                    {this.cancelPolicyData.reason === InsurancePolicyCancelReason.other &&
                        <LabeledTextField key={'cancelPolicyReasonOther'} required={false} label={L('Other cancelling reason')} 
                            rows={5} multiline={true} value={this.cancelPolicyData.otherReason} disabled={false} isDataLoaded={true}
                            customLabelStyles={{ width: "180px", minWidth: "180px" }}
                            onChange={(e: any, newValue: string | undefined) => {
                                this.cancelPolicyData.otherReason = !!newValue ? newValue : '';
                                this.forceUpdate();
                            }}
                        />
                    }

                    <CheckBoxBase key={'cancelPolicyGotConfirmation'} label={L('Got confirmation')} value={this.cancelPolicyData.gotConfirmation}
                        disabled={false} required={true} containerCustomStyles={{width: '350px'}}
                        onChange={(e) => {
                            if(this.cancelPolicyData.gotConfirmation !== e) {
                                this.cancelPolicyData.gotConfirmation = e;
                                this.forceUpdate();
                            }
                        }} 
                    />

                    <DatePickerBase key={'cancelPolicyDate'} required={true} label={L('Cancelling date')} 
                        value={!!this.cancelPolicyData.date ? moment(this.cancelPolicyData.date) : this.cancelPolicyData.date}
                        disabled={false} isDataLoaded={true} validationData={{'minDate': 'SUBTRACT_30_DAYS', 'maxDate': 'TODAY'}} customLabelStyles={{ width: "180px", minWidth: "180px" }}
                        onChange={(value: string | undefined) => {
                            if(this.cancelPolicyData.date !== value) {
                                this.cancelPolicyData.date = moment(value).utc().format('YYYY-MM-DDTHH:mm:ss[Z]');
                                this.forceUpdate();
                            }
                        }} 
                    />
                </DialogContent>

                <DialogFooter>
                    {this.asyncActionInProgress && (
                        <div style={{position: 'absolute', top: '-32px'}}>
                            <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
                        </div>
                    )}

                    <PrimaryButton theme={myTheme} text={L('Cancel policy')} 
                        disabled={this.asyncActionInProgress === true || this.cancelPolicyData.gotConfirmation === false || !this.cancelPolicyData.reason || !this.cancelPolicyData.date}
                        style={{ backgroundColor: this.cancelPolicyData.gotConfirmation === false || !this.cancelPolicyData.reason || !this.cancelPolicyData.date ? myTheme.palette.whiteTranslucent40 : myTheme.palette.red }}
                        onClick={() => { this.cancelPolicy(); }}
                    />
                </DialogFooter>
            </Dialog>
            
            <Pivot theme={myTheme} styles={pivotStyles}>
                <PivotItem headerText={L('General')} key={'General'}>
                    <DefaultButton theme={myTheme} text={L('Toggle edit mode')} disabled={this.asyncActionInProgress === true} iconProps={{ iconName: 'Edit' }}
                        style={{marginTop: 15}} onClick={() => {
                            if(typeof this.props.toggleConfirm === 'function') {
                                this.props.toggleConfirm(!this.isEditMode);
                            }
                            this.isEditMode = !this.isEditMode;
                            this.forceUpdate();
                        }} 
                    />

                    {this.renderElement(new ContentViewModelProperty('offerNumber', L("Offer number"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'offerNumber': this.insurancePolicy.offerNumber})}
                    {this.renderElement(new ContentViewModelProperty('status', L("Status"), Controls.Picker, false, {dropdown: enumToDropdownOptions(InsurancePolicyStatus, false, true, "string")}, !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'status': this.insurancePolicy.status})}
                    {this.renderElement(new ContentViewModelProperty('segment', L("Segment"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'segment': this.insurancePolicy.segment})}
                    {this.renderElement(new ContentViewModelProperty('orderNumber', L("Order number"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'orderNumber': this.insurancePolicy.orderNumber})}
                    {this.renderElement(new ContentViewModelProperty('orderDate', L("Order date"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'orderDate': this.insurancePolicy.orderDate})}
                    {this.renderElement(new ContentViewModelProperty('customerName', L("Customer name"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'customerName': this.insurancePolicy.customerName})}
                    {this.renderElement(new ContentViewModelProperty('customerSurname', L("Customer surname"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'customerSurname': this.insurancePolicy.customerSurname})}
                    {this.renderElement(new ContentViewModelProperty('customerEmail', L("Customer email"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'customerEmail': this.insurancePolicy.customerEmail})}
                    {this.renderElement(new ContentViewModelProperty('creationTime', L("Creation date"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'creationTime': this.insurancePolicy.creationTime})}
                    {this.renderElement(new ContentViewModelProperty('startDate', L("Policy start date"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'startDate': this.insurancePolicy.startDate})}
                    {this.renderElement(new ContentViewModelProperty('endDate', L("Policy end date"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'endDate': this.insurancePolicy.endDate})}
                    {this.renderElement(new ContentViewModelProperty('insurer', L("Insurer"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'insurer': this.insurancePolicy.insurer})}
                    {this.renderElement(new ContentViewModelProperty('comment', L("Comment"), Controls.Text, false, [], !this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'comment': this.insurancePolicy.comment})}
                    
                    { attachedFilesList.length > 0 && <Stack>
                        <Text variant="large" className={classNames.attachedFilesLabel}>
                            { L('Related Policies:') }
                        </Text>

                        <ul>
                            { attachedFilesList }
                        </ul>
                    </Stack> }

                    { apkAttachedFilesList.length > 0 && <Stack>
                        <Text variant="large" className={classNames.attachedFilesLabel}>
                            { L('Related APK:') }
                        </Text>

                        <ul>
                            { apkAttachedFilesList }
                        </ul>
                    </Stack> }
                    
                    <div style={{display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'}}>
                        <input ref={this.fileUploadInputRef} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUpload()} />
                        {fileUploadButton}

                        <input ref={this.apkFileUploadInputRef} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUploadApk()} />
                        {apkFileUploadButton}

                        {this.asyncActionInProgress && (
                            <div style={{marginTop: '-11px'}}>
                                <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                            </div>
                        )}
                    </div>
                </PivotItem>

                <PivotItem headerText={L('Activities')} key={'activities'}>
                    <div style={{display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center'}}>
                        {(this.insurancePolicy.status !== InsurancePolicyStatus.Annulled && this.insurancePolicy.status !== InsurancePolicyStatus.Cancelled) &&
                            <DefaultButton theme={myTheme} text={L('Cancel policy')} disabled={this.asyncActionInProgress === true} iconProps={{ iconName: 'Cancel' }}
                                className={classNames.uploadButton} style={{marginLeft: 0}}
                                onClick={() => {
                                    this.showCancelPolicyDialog = true; 
                                    this.forceUpdate();
                                }} 
                            />
                        }

                        {this.asyncActionInProgress && (
                            <div style={{marginTop: '-11px'}}>
                                <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                            </div>
                        )}
                    </div>

                    <Stack horizontal style={{marginTop: 20}}>
                        {this.insurancePolicy.status === InsurancePolicyStatus.Annulled &&
                            <div className={`${classNames.sectionContainter}`}>
                                <Text className={`${classNames.fontBold} ${classNames.sectionContainterTitle}`}>{ L("Policy cancelled") }</Text>
                                <Stack horizontal={false}>
                                    {this.renderElement(new ContentViewModelProperty('cancelledReason', L("Reason"), Controls.Text, true, [], true, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'cancelledReason': this.insurancePolicyExtendedData ? this.insurancePolicyExtendedData.cancelledReason : ''})}
                                    {this.renderElement(new ContentViewModelProperty('cancelledConfirmation', L("Confirmed"), Controls.CheckBox, true, [], true, {isDataLoaded: this.isDataLoaded}), [], {'cancelledConfirmation': this.insurancePolicyExtendedData ? this.insurancePolicyExtendedData.cancelledConfirmation : false})}
                                    {this.renderElement(new ContentViewModelProperty('cancelledDate', L("Date"), Controls.Date, true, [], true, {isDataLoaded: this.isDataLoaded}), [], {'cancelledDate': this.insurancePolicyExtendedData ? this.insurancePolicyExtendedData.cancelledDate : ''})}
                                    {this.renderElement(new ContentViewModelProperty('agentFullName', L("Cancelled by"), Controls.Text, true, [], true, {isDataLoaded: this.isDataLoaded}), [], {'agentFullName': this.insurancePolicyExtendedData ? this.insurancePolicyExtendedData.agentFullName : ''})}

                                    <input ref={this.cancelPolicyFileUploadInputRef} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUploadCancelPolicy()} />
                                    { cancelPolicyUploadButton }
                                </Stack>
                            </div>
                        }

                        {!!this.cancelPolicyAttachedFileError &&
                            <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`}
                                onDismiss={() => { this.cancelPolicyAttachedFileError = ''; this.forceUpdate(); }}
                            >
                                {this.cancelPolicyAttachedFileError}
                            </MessageBar>
                        }
                    </Stack>

                    { cancelPolicyAttachedFilesList.length > 0 && <Stack>
                        <Text variant="large" className={classNames.attachedFilesLabel}>
                            { L('Cancelled policy attached files:') }
                        </Text>

                        <ul>
                            { cancelPolicyAttachedFilesList }
                        </ul>
                    </Stack> }
                </PivotItem>
            </Pivot>
        </>
    }
}