import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";
import { ProductAttributeMappingDto } from "./productAttributeMappingDto";
import { ProductType } from "./productTypeEnums";

export interface ProductDto extends BaseApiEntityModel {
  Id: string;
  ProductTypeId: ProductType;
  // parentGroupedProductId: string;
  // visibleIndividually: boolean;
  Name: string;
  SeName: string;
  ShortDescription: string;
  FullDescription: string;
  // AdminComment: string;
  ProductLayoutId: string;
  // BrandId: string;
  VendorId: string;
  // ShowOnHomePage: boolean;
  // MetaKeywords: string;
  // MetaDescription: string;
  // MetaTitle: string;
  // AllowCustomerReviews: boolean;
  // ApprovedRatingSum: number;
  // NotApprovedRatingSum: number;
  // ApprovedTotalReviews: number;
  // NotApprovedTotalReviews: number;
  // ExternalId: string;
  // Sku: string;
  // Mpn: string;
  // Gtin: string;
  // IsGiftVoucher: boolean;
  // GiftVoucherTypeId: GiftVoucherType;
  // OverGiftAmount: number | null;
  // RequireOtherProducts: boolean;
  // RequiredProductIds: string;
  // AutoAddRequiredProducts: boolean;
  // IsDownload: boolean;
  // DownloadId: string;
  // UnlimitedDownloads: boolean;
  // DownloadActivationTypeId: DownloadActivationType;
  // MaxNumberOfDownloads: number;
  // DownloadExpirationDays: number | null;
  // HasSampleDownload: boolean;
  // SampleDownloadId: string;
  // HasUserAgreement: boolean;
  // UserAgreementText: string;
  // IsRecurring: boolean;
  // RecurringCycleLength: number;
  // RecurringTotalCycles: number;
  // RecurringCyclePeriodId: RecurringCyclePeriod;
  // IncBothDate: boolean;
  // Interval: number;
  // IntervalUnitId: IntervalUnit;
  // IsShipEnabled: boolean;
  // IsFreeShipping: boolean;
  // ShipSeparately: boolean;
  // AdditionalShippingCharge: number;
  // DeliveryDateId: string;
  // IsTaxExempt: boolean;
  // TaxCategoryId: string;
  // IsTele: boolean;
  // UseMultipleWarehouses: boolean;
  // WarehouseId: string;
  // StockQuantity: number;
  // ReservedQuantity: number;
  // ManageInventoryMethodId: ManageInventoryMethod;
  // StockAvailability: boolean;
  // DisplayStockQuantity: boolean;
  // MinStockQuantity: number;
  // LowStock: boolean;
  // LowStockActivityId: LowStockActivity;
  // NotifyAdminForQuantityBelow: number;
  // BackorderModeId: BackorderMode;
  // AllowOutOfStockSubscriptions: boolean;
  // OrderMinimumQuantity: number;
  // OrderMaximumQuantity: number;
  // AllowedQuantities: string;
  // NotReturnable: boolean;
  // DisableBuyButton: boolean;
  // DisableWishlistButton: boolean;
  // AvailableForPreOrder: boolean;
  // PreOrderDateTimeUtc: string | null;
  // CallForPrice: boolean;
  // Price: number;
  // OldPrice: number;
  // CatalogPrice: number;
  // ProductCost: number;
  // EnteredPrice: boolean;
  // MinEnteredPrice: number;
  // MaxEnteredPrice: number;
  // BasepriceEnabled: boolean;
  // BasepriceAmount: number;
  // BasepriceUnitId: string;
  // BasepriceBaseAmount: number;
  // BasepriceBaseUnitId: string;
  // UnitId: string;
  // MarkAsNew: boolean;
  // MarkAsNewStartDateTimeUtc: string | null;
  // MarkAsNewEndDateTimeUtc: string | null;
  // Weight: number;
  // Length: number;
  // Width: number;
  // Height: number;
  // AvailableStartDateTimeUtc: string | null;
  // AvailableEndDateTimeUtc: string | null;
  // StartPrice: number;
  // AuctionEnded: boolean;
  // DisplayOrder: number;
  // DisplayOrderCategory: number;
  // DisplayOrderBrand: number;
  // DisplayOrderCollection: number;
  Published: boolean;
  CreatedOnUtc: string;
  UpdatedOnUtc: string;
  // Sold: number;
  // Viewed: number;
  // OnSale: number;
  // Flag: string;
  // ProductCategories: ProductCategoryDto[];
  // ProductCollections: ProductCollectionDto[];
  // ProductPictures: ProductPictureDto[];
  // ProductSpecificationAttributes: ProductSpecificationAttributeDto[];
  // TierPrices: ProductTierPriceDto[];
  // ProductWarehouseInventory: ProductWarehouseInventoryDto[];
  ProductAttributeMappings: ProductAttributeMappingDto[];
  // ProductAttributeCombinations: ProductAttributeCombinationDto[];
  // ProductTags: string[];
  // AppliedDiscounts: string[];
}

export interface KeyWithId {
  key: string;
  id: string;
}

export interface KeyWithIdAndProductId extends KeyWithId {
  productId: string;
}

export interface KeysToIdMapper {
  attributeNameToIdList: KeyWithIdAndProductId[];
  attributeValueToOptionIdList: KeyWithId[];
  productKeyToProductIdList: KeyWithId[];
  productIdToTypeList: KeyWithId[];
  productIdToMapNameList: KeyWithId[];
}