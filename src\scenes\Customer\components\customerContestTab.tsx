import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, DialogFooter, <PERSON>alogType, Spinner, SpinnerSize, Stack, mergeStyleSets, Checkbox, SelectionMode, Selection } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import { myTheme } from "../../../styles/theme";
import {CustomerContestFluentListBaseWithCommandBar} from "../../BaseComponents/customerContestFluentListBaseWithCommandBar";
import {inject} from "mobx-react";
import Stores from "../../../stores/storeIdentifier";
import ClientContestStore from "../../../stores/clientContestStore";
import ClientContestService from "../../../services/clientContest/clientContestService";
import { LabeledTextField } from "../../../components/LabeledTextField";

const classNames = mergeStyleSets({
    contentContainer: {
        display: 'block',
        width: '100%',
        height: '500px',
        maxHeight: '500px',
        position: 'relative',
        border: `1px solid ${myTheme.palette.themeLighter}`,
        marginTop: '20px',
    },
    fontBold: {
        fontWeight: '800',
    },
    summaryAttributeWrapper: {
        display: 'flex',
        flexWrap: 'wrap',
        maxWidth: '100%',
        width: 'auto',
    },
    summaryAttribute: {
        padding: '5px 10px',
        margin: '3px 0 0 10px',
        background: myTheme.palette.themeLighter,
        fontSize: '0.8rem',
        whiteSpace: 'pre-line',
        selectors: {
            '&:last-child': {
                borderRight: 'none',
            }
        }
    },
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    customDialogTitle: {
        display: 'none',
    },
    checkboxWrapper: {
        marginTop: '20px',
    },
    checkbox: {
        marginBottom: '10px',
    }
});

export interface ICustomerContestTabProps {
    customerId: number;
    asyncActionInProgress: boolean;
    history?: any,
    clientContestStore?: ClientContestStore;
}

@inject(Stores.ClientContestStore)
export class CustomerContestTab extends React.Component<ICustomerContestTabProps> {
    private showDialog: boolean = false;
    private singleClientContest: any = [];
    private selectContestSearchText: string = "";
    private clientContestId: number = 0;
    private _clientContestListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedClientContest: any = this._clientContestListSelection.getSelection();
            if(Array.isArray(selectedClientContest) && selectedClientContest.length > 0 && !!selectedClientContest[0].id) {
                this.clientContestId = selectedClientContest[0].id;
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });

    componentWillUnmount(): void {
        this.props.clientContestStore?.resetDataSet();
    }

    getByClientId = async () => {
        await this.props.clientContestStore?.getByClientId(this.props.customerId);
    }

    fetchClientContestData = async () => {
        await ClientContestService.getDetails(this.clientContestId).then((result: any) => {
            this.singleClientContest = result;
        });
        this.showDialog = true; 
        this.forceUpdate();
    }

    render() {
        return <>
            <Dialog
                hidden={!this.showDialog}
                onDismiss={() => { this.showDialog = false; this.forceUpdate(); }}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L('Detailed information'),
                }}
                modalProps={{
                    isBlocking: true,
                }}
                minWidth={550}
            >
                <LabeledTextField
                    isDataLoaded={true}
                    label={L("Is conditions meet")}
                    disabled={true}
                    value={this.singleClientContest.isConditionsMeet ? L('Yes') : L('No')}
                />
                <LabeledTextField
                    isDataLoaded={true}
                    label={L("Is received")}
                    disabled={true}
                    value={this.singleClientContest.isReceived ? L('Yes') : L('No')}
                />
                <LabeledTextField
                    isDataLoaded={true}
                    label={L("Is prize send to client")}
                    disabled={true}
                    value={this.singleClientContest.isPrizeSentToClient ? L('Yes') : L('No')}
                />
                <LabeledTextField
                    isDataLoaded={true}
                    label={L("Is participates")}
                    disabled={true}
                    value={this.singleClientContest.isParticipates ? L('Yes') : L('No')}
                />
                <LabeledTextField
                    isDataLoaded={true}
                    label={L("OloPoints")}
                    disabled={true}
                    value={this.singleClientContest.oloPoints}
                />
                <LabeledTextField
                    isDataLoaded={true}
                    label={L("Prize number")}
                    disabled={true}
                    value={this.singleClientContest.prizeNumber}
                />
                <LabeledTextField
                    isDataLoaded={true}
                    label={L("Received prize")}
                    disabled={true}
                    value={this.singleClientContest.receivedPrize}
                />
                <Stack className={classNames.checkboxWrapper}>
                    <Checkbox className={classNames.checkbox} disabled={false} theme={myTheme} checked={this.singleClientContest.agreement} label={L("Agreement")} />
                </Stack>

                <DialogFooter>
                    {this.props.asyncActionInProgress && (
                        <div style={{position: 'absolute', bottom: '0', left: '0'}}>
                            <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
                        </div>
                    )}
                    
                    <DefaultButton theme={myTheme} text={L('Cancel')} disabled={false}
                        onClick={() => { this.showDialog = false; this.forceUpdate(); }} 
                    />
                </DialogFooter>
            </Dialog>

            <Stack>
                <div className={classNames.contentContainer} >
                    <CustomerContestFluentListBaseWithCommandBar
                        store={this.props.clientContestStore!}
                        items={
                            this.props.clientContestStore?.dataSet && this.props.clientContestStore?.dataSet.items
                                ? this.props.clientContestStore?.dataSet.items
                                : []
                        }
                        customSelection={this._clientContestListSelection}
                        searchText={this.selectContestSearchText}
                        history={this.props.history}
                        customData={{
                            fetchClientContestData: this.fetchClientContestData,
                            customRequest: {customerId: this.props.customerId}
                        }}
                        scrollablePanelMarginTop={120}
                    />
                </div>
            </Stack>
        </>;
    }
}