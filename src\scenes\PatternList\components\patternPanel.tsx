import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { PatternContentView } from '../../Pattern/components/patternContentView';
import { PatternDto } from '../../../services/pattern/dto/patternDto';
import patternService from '../../../services/pattern/patternService';

export class PatternPanel extends GenericPanel {
    private prevEntityId: any = 0;
    private dataDownloaded: boolean = false;

    getPanelTitle(): string {
        return L("Pattern");
    }

    private async getEntityModel(entityId: string) {
        await patternService.get({ id: entityId } as PatternDto).then(async (pattern: PatternDto) => {
            this.props.payload.model = await patternService.get({ id: entityId } as PatternDto);
            this.dataDownloaded = true;
            this.forceUpdate();
        });
    }

    renderContent() {
        if(typeof this.props.payload.entityId === 'number' && this.props.payload.entityId > 0 && this.props.payload.entityId !== this.prevEntityId) {
            this.prevEntityId = this.props.payload.entityId;
            this.getEntityModel(this.prevEntityId.toString());
        } else if(this.dataDownloaded && (this.state.model.value.id === 0 || this.state.model.value.id === "")) {
            this.prevEntityId = 0;
            this.dataDownloaded = false;
        }

        return <PatternContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as PatternDto } />;
    }
}