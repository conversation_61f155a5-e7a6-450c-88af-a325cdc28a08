import { CrudServiceBase } from "../base/crudServiceBase";
import Endpoint from "../endpoint";
import { httpApi } from "../httpService";
import { SmsDto } from "./dto/smsDto";

class SmsService extends CrudServiceBase<SmsDto> {
  constructor() {
    super(Endpoint.Sms);
    this.internalHttp = httpApi;
  }

  public async sendSms(number: string, message: string): Promise<SmsDto> {
    let result = await this.internalHttp.post(this.endpoint.Custom(`SendSms?to=${number}&message=${message}`, true));
    return !!result.data && !!result.data.result ? result.data.result : result.data;
  }
}

const exportSmsService: SmsService = new SmsService();
export default exportSmsService;
