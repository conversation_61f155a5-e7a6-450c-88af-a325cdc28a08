import { ProductDto } from '../../../services/product/productDto';
import { ProductContentView } from '../../Product/components/productContentView';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';

export class ProductPanel extends GenericPanel {
    getPanelTitle(): string {
        return L("Product");
    }

    // getModel() {
    //     return getPartialModel(this.state.model.value, ["Name", "ShortDescription", "FullDescription", "ProductTypeId", "Published"]);
    // }

    renderContent() {
        return <ProductContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ProductDto } />;
    }
}