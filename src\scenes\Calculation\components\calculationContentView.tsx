import { Default<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogFooter, DialogType, Icon, mergeStyleSets, PivotItem, PrimaryButton, Spinner, SpinnerSize, Stack, Text } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { myTheme } from '../../../styles/theme';
import { inject } from 'mobx-react';
import Stores from '../../../stores/storeIdentifier';
import { catchErrorMessage, dateFormat, enumToDropdownOptions } from '../../../utils/utils';
import React from 'react';
import { uploadFileToAzure } from '../../../services/azureService';
import policyCalculationAttachedFilesService from '../../../services/attachedFiles/policyCalculationAttachedFilesService';
import { AzureB2CStorageKey } from '../../Login/AzureB2C/signInButton';
import { CalculationDto } from '../../../services/calculation/dto/calculationDto';
import { defaultCalculation } from '../../../stores/calculationStore';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { DropdownBase } from '../../BaseComponents/dropdownBase';
import { InsuranceCompanyNewOffer } from '../../../services/calculation/dto/insuranceCompanyNewOfferEnum';
import { CheckBoxBase } from '../../BaseComponents';
import { NumberOfInstallments } from '../../../services/calculation/dto/numberOfInstallmentsEnum';
import { LabeledTextField } from '../../../components/LabeledTextField';
import { DatePickerBase } from '../../BaseComponents/datePickerBase';
import individualOfferAttachedFileService from '../../../services/individualOfferAttachedFile/individualOfferAttachedFileService';
import { defaultIndividualOffer } from '../../../stores/individualOfferStore';
import { IndividualOfferDto } from '../../../services/individualOffer/individualOfferDto';
import individualOfferService from '../../../services/individualOffer/individualOfferService';
import { IndividualOfferAttachedFileDto } from '../../../services/individualOfferAttachedFile/individualOfferAttachedFileDto';
import { defaultIndividualOfferAttachedFile } from '../../../stores/individualOfferAttachedFileStore';
import { CalculationProductVehicle } from './calculationProductVehicle';
import { InsurerAttachedFilePolicyType } from '../../../services/attachedFiles/enums/insurerAttachedFilePolicyTypeEnums';
import { CalculationProductChildren } from './calculationProductChildren';
import { CalculationProductTravel } from './calculationProductTravel';
import { CalculationProductCancelTravel } from './calculationProductCancelTravel';
import productAttributeService from '../../../services/productAttribute/productAttributeService';
import { getGrandNodeLanguage } from '../../../utils/languageUtils';
import { calculationProductProcessData } from './calculationProductProcessData';
import policyCalculationService from '../../../services/policyCalculation/policyCalculationService';
import { NewOfferStatus } from '../../../services/calculation/dto/newOfferStatusEnums';

const classNames = mergeStyleSets({
  makeIndividualOfferButton: {
    width: 'fit-content',
    padding: '25px 50px',
    marginTop: '25px',
  },
  sectionContainter: {
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    width: 'fit-content',
    minWidth: '30%',
    maxWidth: '95%',
    minHeight: '150px',
    border: `1px solid ${myTheme.palette.neutralPrimary}`,
    padding: '10px 25px 25px',
    selectors: {
      ':first-child': {
          marginTop: '25px',
      }
    }
  },
  inputIcon: {
    cursor: 'pointer',
    marginLeft: '15px !important',
    marginRight: '10px',
    fontSize: '20px',
    marginTop: '26px',
    transition: 'all 120ms',
    selectors: {
      '&:hover': {
          transform: 'scale(1.2)',
      }
    }
  },
  smallLoadSpinner: {
    display: 'inline-flex',
    marginLeft: '10px !important',
    marginTop: '22px',
    selectors: {
      '& .ms-Spinner-label': {
          color: myTheme.palette.themePrimary,
      }
    }
  },
  uploadButton: {
    width: 'fit-content',
    padding: '25px 50px',
    marginTop: '20px',
  },
  attachedFilesLabel: {
    fontWeight: 'bold',
    marginTop: '20px',
    marginBottom: '10px',
  },
  attachedFilesListItem: {
    listStyleType: 'decimal',
    marginBottom: '15px',
    padding: '10px',
    background: myTheme.palette.themeLighterAlt,
    selectors: {
      // ':nth-child(even)': {
      //     background: myTheme.palette.neutralLight,
      // }
    }
  },
  fontBold: {
    fontWeight: '800',
  },
  sectionContainterTitle: {
    position: 'absolute',
    top: '0',
    left: '21px',
    padding: '2px 4px',
    transform: 'translateY(-60%)',
    boxSizing: 'border-box',
    background: myTheme.palette.white,
  },
  loadSpinner: {
    display: 'inline-flex',
    marginLeft: '45px !important',
    marginTop: '20px',
    selectors: {
      '& .ms-Spinner-label': {
          color: myTheme.palette.themePrimary,
      }
    }
  },
  fileStatusDropdown: {
  },
  messageBar: {
    width: 'fit-content',
    marginLeft: '25px !important',
    marginTop: '20px',
    selectors: {
      '& .ms-MessageBar-innerText': {
        selectors: {
          '& span': {
              whiteSpace: 'pre-line',
          }
        }
      }
    }
  },
  recommendedIcon: {
    color: myTheme.palette.yellow,
    marginLeft: '15px',
    fontSize: '15px',
  },
  dialogMain: {
    selectors: {
      '.ms-Dialog-content': {
        selectors: {
          '.ms-Dialog-header': {
            display: 'none',
          }
        }
      }
    }
  }
});

@inject(Stores.LanguageStore)
@inject(Stores.CalculationStore)
@inject(Stores.IndividualOfferStore)
export class CalculationContentView extends GenericContentView {
  private calculation: CalculationDto | any = defaultCalculation;
  private apkData: any = {};
	private calculationData: any = {};
  private individualOffer: IndividualOfferDto = defaultIndividualOffer;
  private individualOfferAttachedFile: IndividualOfferAttachedFileDto = defaultIndividualOfferAttachedFile;
  private isEditMode: boolean = false;
  private fileUploadInputRef: any;
  private fileUploadInputRefPdf: any;
  private fileUploadInputRefOffer: any;
  private pdfOfferFiles: any;
  private pdfFiles: any;
  private createdOfferts: any;
  private selectedFileForUploadPdf: any = {
    name: "" as string,
    src: "" as string
  };
  private selectedFileForUpload: any = {
    name: "" as string,
    src: "" as string,
  };
  private createIndividualOfferError: string = '';
  private onUploadPdfError: string = '';
  private attachedFiles: any = {
		totalCount: 0,
		items: []
	};
  private showMakeIndividualOfferDialog: boolean = false;
  private entityId: number = 0;
  private supportedCalculationSegments: string[] = [
    InsurerAttachedFilePolicyType.Vehicle, InsurerAttachedFilePolicyType.Children, InsurerAttachedFilePolicyType.Travel, InsurerAttachedFilePolicyType.CancelTravel
  ];
  private productAttributeResultItems: any[] = [];
  private productDataDownloaded: boolean = false;
  private productDataProcessed: boolean = false;
  private gnLanguage: any = {};
  private showInsurerCommunicationsDialog: boolean = false;
  private userCommunications: any[] = [];
  private selectedInsurer: string | number | undefined = undefined;

  constructor(props: any) {
    super(props);

    this.fileUploadInputRef = React.createRef();
    this.fileUploadInputRefPdf = React.createRef();
    this.createdOfferts = [];

    this.entityId = this.props.payload.entityId ? this.props.payload.entityId : (this.props.customData && this.props.customData.calculationId ? this.props.customData.calculationId : this.props.payload.id);
  };
  
  async componentDidMount() {
    this.asyncActionInProgress = true;
    this.forceUpdate();

    const getAttachedItems: any = await policyCalculationAttachedFilesService.getByCalculationId(this.entityId);
    this.attachedFiles = {
      totalCount: getAttachedItems.items.length,
      items: [...getAttachedItems.items]
    };

    this.checkIfDataIsLoaded("policyCalculation");
    
    // await this.props.individualOfferStore!.getByCalculationId(this.entityId).then((response:any) => {
    //   this.createdOfferts = response;
    // });
    
    await this.props.languageStore?.getAll(this.props.languageStore?.defaultRequest);
    this.gnLanguage = getGrandNodeLanguage(this.props.languageStore?.dataSet);
    
    this.asyncActionInProgress = false;
    this.forceUpdate();
  };

  private async getProductData() {
    this.asyncActionInProgress = true;
    this.forceUpdate();

    await productAttributeService.getAll().then((productAttributeResult: any) => {
      if(productAttributeResult && productAttributeResult.items) {
        this.productAttributeResultItems = productAttributeResult.items;

        this.asyncActionInProgress = false;
        this.forceUpdate();
      }
    }).catch((error: any) => {
      console.error(error);

      this.asyncActionInProgress = false;
      this.forceUpdate();
    });
  };

  private triggerUploadOffer = () => {
    this.fileUploadInputRefOffer.current.click();
  };

  private triggerUploadPdf = () => {
    this.fileUploadInputRefPdf.current.click();
  };

  private triggerUpload = () => {
    this.fileUploadInputRef.current.click();
  };

  private async sendIndividualOffer() {
    this.asyncActionInProgress = true;

    await individualOfferService.sendIndividualOffer(parseInt(this.individualOfferAttachedFile.id)).then(async (response: any) => {
      await this.props.individualOfferStore!.getByCalculationId(this.props.calculationStore?.model.id ? Number(this.props.calculationStore?.model.id) : 0).then((response:any) => {
        this.createdOfferts = response;
        this.forceUpdate();
      });
    })
    
    this.asyncActionInProgress = false;
    this.forceUpdate()
  }
  
  private async createIndividualOffer() {
    this.asyncActionInProgress = true;
    this.showMakeIndividualOfferDialog = false;

    await individualOfferService.create({
      "calculationId": this.props.calculationStore?.model.id ? Number(this.props.calculationStore?.model.id) : 0,
      "id": "",
      "status": this.individualOffer.status,
      "insurerName": this.individualOffer.insurerName,
      "insuranceCoverage": this.individualOffer.insuranceCoverage,
      "installments": this.individualOffer.installments,
      "total": Number(this.individualOffer.total),
      "paymentDates": [],
      "fees": []
    }).then(async (response: any) => {
      await this.props.individualOfferStore!.getByCalculationId(this.props.calculationStore?.model.id ? Number(this.props.calculationStore?.model.id) : 0).then((response:any) => {
        this.createdOfferts = response;
        this.forceUpdate();
      });
      
      this.createIndividualOfferError = '';
      this.asyncActionInProgress = false;
      this.forceUpdate();
    }).catch((error: any) => {
      console.error(error);
      this.createIndividualOfferError = catchErrorMessage(error);

      this.asyncActionInProgress = false;
      this.forceUpdate();
    });
  }

  private async onUploadPdf() {
    this.asyncActionInProgress = true;
    const selectedFile = !!this.fileUploadInputRefPdf.current.files && this.fileUploadInputRefPdf.current.files.length ? this.fileUploadInputRefPdf.current.files[0] : null;

    if(!!selectedFile) {
      this.selectedFileForUploadPdf.name = selectedFile.name;
      this.selectedFileForUploadPdf.src = URL.createObjectURL(selectedFile);
    }

    this.forceUpdate();

    let result = await uploadFileToAzure(selectedFile)

    await individualOfferAttachedFileService.create({
      "individualOfferId": this.createdOfferts.items[0].id,
      "id": "",
      "fileUrl": result.url,
      "originalFileName": selectedFile.name,
      "blobFileName": result.name,
      "description": "",
    }).then(async (response: any) => {
      if(!!response.id) {
        this.individualOfferAttachedFile.id = response.id
      }
      this.pdfFiles = await individualOfferAttachedFileService.getPdfFiles(Number(this.individualOfferAttachedFile.id));

      this.onUploadPdfError = '';
      this.asyncActionInProgress = false;
      this.forceUpdate();
    }).catch((error: any) => {
      console.error(error);
      this.onUploadPdfError = catchErrorMessage(error);

      this.asyncActionInProgress = false;
      this.forceUpdate();
    })
  }

  private async onUpload() {
    this.asyncActionInProgress = true;
    const selectedFile = !!this.fileUploadInputRef.current.files && this.fileUploadInputRef.current.files.length ? this.fileUploadInputRef.current.files[0] : null;
    
    if(!!selectedFile) {
      this.selectedFileForUpload.name = selectedFile.name;
      this.selectedFileForUpload.src = URL.createObjectURL(selectedFile);
    }

    this.forceUpdate();

    let result = await uploadFileToAzure(selectedFile);

    let sessionData: any = localStorage.getItem(AzureB2CStorageKey);
    if(!!sessionData) {
      sessionData = JSON.parse(sessionData);
    }

    await policyCalculationAttachedFilesService.createNew({
      "fileUrl": result.url,
      "originalFileName": selectedFile.name,
      "blobFileName": result.name,
      "policyCalculationId": this.entityId,
      "type": "APK",
      "recommended": false,
      "status": 'Sended',
      "insurer": null,
      "customerId": !!sessionData ? sessionData.b2c.uniqueId : "",
      "id": '0',
    }).then(async (response: any) => {
        this.attachedFiles = await policyCalculationAttachedFilesService.getAllFiles();

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }).catch((error: any) => {
        console.error(error);

        this.asyncActionInProgress = false;
        this.forceUpdate();
    });
  }

  private async generateRequestAndResponse() {
    this.asyncActionInProgress = true;

    await policyCalculationService.getInsurerCommunicationData(this.entityId).then(async (response: any) => {
      if(response && response.userCommunications) {
        this.userCommunications = response.userCommunications;
      }
      this.asyncActionInProgress = false;
      this.forceUpdate();
    }).catch((error: any) => {
      console.error(error);
      this.onUploadPdfError = catchErrorMessage(error);

      this.asyncActionInProgress = false;
      this.forceUpdate();
    })
  }
  
  mod97(stringNumber: string): number {
    let checksum = 0;
    for (let i = 0; i < stringNumber.length; i++) {
      checksum = (checksum * 10 + parseInt(stringNumber[i], 10)) % 97;
    }
    return checksum;
  }

  validateBankAccountNumber(bankAccountNumber: string): boolean {
    const cleanNumber = bankAccountNumber.replace(/\s+/g, '').toUpperCase();
    
    if (cleanNumber.length !== 26 || !/^\d{26}$/.test(cleanNumber)) {
      return false;
    }
    
    const iban = "PL00" + cleanNumber;
    const modifiedIban = iban.slice(4) + "2521" + iban.slice(2, 4);
    
    return this.mod97(modifiedIban) === 1;
  }

  renderDataPickersAndFee() {
    const count: any = this.createdOfferts.items[0].installments;
    const datePickersAndFee = [];

    for (let i = 0; i < count; i++) {
      datePickersAndFee.push(
        <>
          <DatePickerBase
            key={'paymentDates' + i}
            required={true}
            label={L('Installment payment date')}
            value={this.individualOffer.paymentDates && this.individualOffer.paymentDates[i]}
            disabled={false}
            isDataLoaded={true}
            customLabelStyles={{ width: "350px", minWidth: "350px" }}
            onChange={(e: any) => {
              if (this.individualOffer.paymentDates[i] !== e) {
                this.individualOffer.paymentDates[i] = e;
                this.forceUpdate();
              }
            }}
          />

          <LabeledTextField
            key={'fee' + i}
            required={true}
            label={L('Amount of installment')}
            rows={1}
            multiline={false}
            value={this.individualOffer.fees && this.individualOffer.fees[i]}
            disabled={false}
            isDataLoaded={true}
            customLabelStyles={{ width: "350px", minWidth: "350px" }}
            onChange={(e: any, newValue: string | undefined) => {
              this.individualOffer.fees[i] = !!newValue ? newValue : '';
              this.forceUpdate();
            }}
          />
        </>
      );
    }

    return datePickersAndFee;
  }

  cancelInsurerCommunicationsDialog() {
    setTimeout(() => {
      this.selectedInsurer = undefined;
      this.userCommunications = [];
      this.forceUpdate();
    }, 1000);
    
    this.showInsurerCommunicationsDialog = false; 
    this.forceUpdate(); 
  }
  
  renderContent() {
    this.calculation = this.props.payload.model ? this.props.payload.model : this.props.payload;
    
    if(!this.productDataDownloaded) {
      this.productDataDownloaded = true;
      this.forceUpdate();
      
      this.getProductData();
    }
    
    if(this.productAttributeResultItems.length > 0 && !this.productDataProcessed) {
      this.productDataProcessed = true;
      const processDataResult: {apkData: any, calculationData: any} = calculationProductProcessData(this.calculation.payload, this.productAttributeResultItems, this.gnLanguage);
      
      if(processDataResult) {
        this.apkData = processDataResult.apkData;
        this.calculationData = processDataResult.calculationData;
      }

      this.forceUpdate();
    }

    if(dateFormat(this.calculation.creationTime) !== 'Invalid date') {
			this.calculation.creationTime = dateFormat(this.calculation.creationTime);
		}
		if(dateFormat(this.calculation.startDate) !== 'Invalid date') {
			this.calculation.startDate = dateFormat(this.calculation.startDate);
		}
    
    let pdfOfferFilesList: JSX.Element[] = [];

    if(!!this.pdfOfferFiles) {
      pdfOfferFilesList.push(
        <li key={this.pdfOfferFiles.id} className={classNames.attachedFilesListItem}>
          {dateFormat(this.pdfOfferFiles.creationTime, "DD.MM.YYYY HH:mm", true)}{` - `}
          <a href={this.pdfOfferFiles.fileUrl} title={L("Download file")}>
              {!!this.pdfOfferFiles.displayedFileName ? this.pdfOfferFiles.displayedFileName :
                (!!this.pdfOfferFiles.originalFileName ? this.pdfOfferFiles.originalFileName : `${L('File name placeholder')}`)}
            </a>
        </li>
      )
    }

    let pdfFilesList: JSX.Element[] = [];

    if(!!this.pdfFiles) {
        pdfFilesList.push(
          <li key={this.pdfFiles.id} className={classNames.attachedFilesListItem}>
            {dateFormat(this.pdfFiles.creationTime, "DD.MM.YYYY HH:mm", true)}{` - `}
            <a href={this.pdfFiles.fileUrl} title={L("Download file")}>
              {!!this.pdfFiles.displayedFileName ? this.pdfFiles.displayedFileName :
                (!!this.pdfFiles.originalFileName ? this.pdfFiles.originalFileName : `${L('File name placeholder')}`)}
            </a>
          </li>
        );
    }

    const sendPdf = <PrimaryButton
                      className={classNames.uploadButton}
                      theme={myTheme}
                      text={L('Upload PDF')}
                      type={'button'}
                      onClick={this.triggerUploadPdf}
                      disabled={this.asyncActionInProgress === true}
                      style={{marginLeft: 25, marginRight: 25}}
                    />;

    let attachedFilesList: JSX.Element[] = [];

    if(!!this.attachedFiles && !!this.attachedFiles.items && this.attachedFiles.totalCount > 0) {
      this.attachedFiles.items.forEach((file: any) => {
        if(file.policyCalculationId === this.entityId) {
          attachedFilesList.push(
            <li key={file.id} className={classNames.attachedFilesListItem}>
              {dateFormat(file.creationTime, "DD.MM.YYYY HH:mm", true)}{` - `} 
              <a href={file.fileUrl} title={L("Download file")}>
                {!!file.insurer ? file.insurer : file.type}
                {file.recommended && 
                  <Icon iconName={'FavoriteStarFill'} className={`${classNames.recommendedIcon}`} title={L('Marked as recommended')} />
                }
              </a>
            </li>
          );
        }
      });
    }

    let createdOffertsList: JSX.Element[] = [];

		if(!!this.createdOfferts && this.createdOfferts.items && this.createdOfferts.totalCount > 0) {
      this.createdOfferts.items.forEach((items: any) => {
        createdOffertsList.push(
          <li key={this.createdOfferts.items.id}>
            {(items.status === NewOfferStatus.Created || items.status === NewOfferStatus.Accepted || items.status === NewOfferStatus.Sended) && (
            <Stack horizontal style={{marginTop: 20}}>
              <div hidden={true} className={`${classNames.sectionContainter}`}>
                <Text className={`${classNames.fontBold} ${classNames.sectionContainterTitle}`}>{L("Add individual offer")}</Text>
                <Stack horizontal={false}>
                  <DropdownBase key={'status'} label={L("Status")} options={ enumToDropdownOptions(NewOfferStatus, true, true, "string")}
                    value={items.status} disabled={!this.isEditMode} isDataLoaded={true} required={true} customDropdownWidth={'300px'} customLabelStyles={{ width: "350px", minWidth: "350px" }}
                    onChange={(e: any) => {
                      if(items.status !== e) {
                        items.status = e;
                        if(items.status === 'Sended') {
                          this.individualOffer.id = items.id;
                        }
                        this.forceUpdate()
                      }
                    }}
                  />

                  <DropdownBase key={'insurerName'} label={L("Insurance company")}  options={ enumToDropdownOptions(InsuranceCompanyNewOffer, true, true, "string")}
                    value={items.insurerName} disabled={!this.isEditMode} isDataLoaded={true} required={true} customDropdownWidth={'300px'} customLabelStyles={{ width: "350px", minWidth: "350px" }}
                    onChange={(e: any) => {
                      if(items.insurerName !== e) {
                        items.insurerName = e;
                        this.forceUpdate();
                      }
                    }}
                  />

                  {(this.calculation && this.calculation.segment === InsurerAttachedFilePolicyType.Vehicle) &&
                    <div style={{display: "flex"}}>
                      <CheckBoxBase key={'insuranceCoverage OC'} label={L("OC")} 
                        value={items.insuranceCoverage.includes('OC')} disabled={!this.isEditMode} required={true} containerCustomStyles={{ width: "70px", minWidth: "70px" }}
                        onChange={(isChecked) => {
                          const coverage = items.insuranceCoverage;
                          if (isChecked && !coverage.includes('OC')) {
                            coverage.push('OC');
                          } else if (!isChecked && coverage.includes('OC')) {
                            const index = coverage.indexOf('OC');
                            coverage.splice(index, 1);
                          }
                          this.forceUpdate();
                        }}
                      />

                      <CheckBoxBase key={'insuranceCoverage AC'} label={L("AC")} 
                        value={items.insuranceCoverage.includes('AC')} disabled={!this.isEditMode} required={true} containerCustomStyles={{ width: "70px", minWidth: "70px" }}
                        onChange={(isChecked) => {
                          const coverage = items.insuranceCoverage;
                          if (isChecked && !coverage.includes('AC')) {
                            coverage.push('AC');
                          } else if (!isChecked && coverage.includes('AC')) {
                            const index = coverage.indexOf('AC');
                            coverage.splice(index, 1);
                          }
                          this.forceUpdate();
                        }}
                      />

                      <CheckBoxBase key={'insuranceCoverage NNW'} label={L("NNW")} 
                        value={items.insuranceCoverage.includes('NNW')} disabled={!this.isEditMode} required={true} containerCustomStyles={{ width: "80px", minWidth: "80px" }}
                        onChange={(isChecked) => {
                          const coverage = items.insuranceCoverage;
                          if (isChecked && !coverage.includes('NNW')) {
                            coverage.push('NNW');
                          } else if (!isChecked && coverage.includes('NNW')) {
                            const index = coverage.indexOf('NNW');
                            coverage.splice(index, 1);
                          }
                          this.forceUpdate();
                        }}
                      />

                      <CheckBoxBase key={'insuranceCoverage Assistance'} label={L("Assistance")} 
                        value={items.insuranceCoverage.includes('Assistance')} disabled={!this.isEditMode} required={true} containerCustomStyles={{ width: "110px", minWidth: "110px" }}
                        onChange={(isChecked) => {
                          const coverage = items.insuranceCoverage;
                          if (isChecked && !coverage.includes('Assistance')) {
                            coverage.push('Assistance');
                          } else if (!isChecked && coverage.includes('Assistance')) {
                            const index = coverage.indexOf('Assistance');
                            coverage.splice(index, 1);
                          }   
                          this.forceUpdate();
                        }}
                      />

                      <CheckBoxBase key={'insuranceCoverage Glass'} label={L("Glass")} 
                        value={items.insuranceCoverage.includes('Glass')} disabled={!this.isEditMode} required={true} containerCustomStyles={{ width: "110px", minWidth: "110px" }}
                        onChange={(isChecked) => {
                        const coverage = items.insuranceCoverage;
                        if (isChecked && !coverage.includes('Glass')) {
                          coverage.push('Glass');
                        } else if (!isChecked && coverage.includes('Glass')) {
                          const index = coverage.indexOf('Glass');
                          coverage.splice(index, 1);
                        }   
                        this.forceUpdate();
                        }}
                      />
                    </div>
                  }

                  <DropdownBase key={'installments'} label={L("Number of installments")} options={ enumToDropdownOptions(NumberOfInstallments, true, true, "string")}
                    value={items.installments} disabled={!this.isEditMode} isDataLoaded={true} required={true} customDropdownWidth={'300px'} customLabelStyles={{ width: "350px", minWidth: "350px" }}
                    onChange={(e: any) => {
                      if(items.installments !== e) {
                      items.installments = e;
                      this.forceUpdate()
                      }
                    }}
                  />

                  <LabeledTextField key={'total'} required={true} label={L('Individual offer contribution ')} type='number'
                    rows={1} multiline={false} value={items.total.toString()} disabled={!this.isEditMode} isDataLoaded={true} customLabelStyles={{ width: "350px", minWidth: "350px" }}
                    onChange={(e: any, newValue: string | undefined) => {
                      items.total = !!newValue ? newValue : '';
                      this.forceUpdate();
                    }}
                  />

                  <Stack horizontal style={{marginTop: 20}}>
                    <input ref={this.fileUploadInputRefPdf} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUploadPdf()}/>
                    {sendPdf}
                    
                    <PrimaryButton theme={myTheme} text={L('Send individual offer')} 
                      disabled={!items.id}
                      className={classNames.uploadButton}
                      onClick={() => { this.sendIndividualOffer();  this.forceUpdate(); }}
                    />
                  </Stack>

                  {pdfFilesList.length > 0 && <Stack>
                    <Text variant='large'  className={classNames.attachedFilesLabel}>
                      { L('Pdf offer attached files:') }
                    </Text>

                    <ul>
                      { pdfFilesList }
                    </ul>
                  </Stack> 
                  }

                  {(items.status === NewOfferStatus.Sended || items.status === NewOfferStatus.Accepted) && (<>
                    <LabeledTextField key={'policyNumber'} required={true} label={L('Policy number')} 
                      rows={1} multiline={false} value={this.individualOffer.policyNumber} disabled={false} isDataLoaded={true} customLabelStyles={{ width: "350px", minWidth: "350px" }}
                      onChange={(e: any, newValue: string | undefined) => {
                        this.individualOffer.policyNumber = !!newValue ? newValue : '';
                        this.forceUpdate();
                      }}
                    />

                    <LabeledTextField key={'bankAccountNumber'} required={true} label={L('Bank account number')} 
                      rows={1} multiline={false} value={this.individualOffer.bankAccountNumber} disabled={false} isDataLoaded={true} customLabelStyles={{ width: "350px", minWidth: "350px" }}
                      onChange={(e: any, newValue: string | undefined) => {
                        this.individualOffer.bankAccountNumber = !!newValue ? newValue : '';
                        if (newValue && newValue.replace(/\s+/g, '').length === 26 && !this.validateBankAccountNumber(newValue)) {
                          alert("Niepoprawny numer konta!");
                        }
                        this.forceUpdate();
                      }}
                    />

                    <div>
                      {this.renderDataPickersAndFee()}
                    </div>

                    <DatePickerBase key={'startDate'} required={true} label={L('Policy start date')} value={this.individualOffer.startDate}
                      disabled={false} isDataLoaded={true} customLabelStyles={{ width: "350px", minWidth: "350px" }}
                      onChange={(e: any) => {
                        if(this.individualOffer.startDate !== e) {
                          this.individualOffer.startDate = e;
                          this.forceUpdate();
                        }
                      }}
                    />

                    <DatePickerBase key={'endDate'} required={true} label={L('Policy end date')} value={this.individualOffer.endDate}
                      disabled={false} isDataLoaded={true} customLabelStyles={{ width: "350px", minWidth: "350px" }}
                      onChange={(e: any) => {
                        if(this.individualOffer.endDate !== e) {
                          this.individualOffer.endDate = e;
                          this.forceUpdate();
                        }
                      }}
                    />

                    <Stack horizontal style={{marginTop: 20}}>
                      <input ref={this.fileUploadInputRefPdf} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUploadPdf()}/>
                      {sendPdf}
                    </Stack>

                    { pdfOfferFilesList.length > 0 && <Stack>
                      <Text variant='large'  className={classNames.attachedFilesLabel}>
                        { L('Pdf policy attached files:') }
                      </Text>

                      <ul>
                        { pdfOfferFilesList }
                      </ul>
                    </Stack>
                    }
                  </>)}
                </Stack>
              </div>
            </Stack>
            )}
          </li>
        );
      });
		}

    const activitiesPivotItem: JSX.Element = <PivotItem headerText={L('Activities')} key={'Activities'}>
      {/* <DefaultButton theme={myTheme} text={L('Toggle edit mode')} disabled={this.asyncActionInProgress === true} 
        style={{marginTop: 15}} 
        onClick={() => {
          if(typeof this.props.toggleConfirm === 'function') {
            this.props.toggleConfirm(!this.isEditMode);
          }
          this.isEditMode = !this.isEditMode;
          this.forceUpdate();
        }}
      /> */}

      <div style={{display: 'flex', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start'}}>
        {/* {this.calculation && this.calculation.segment === InsurerAttachedFilePolicyType.Vehicle &&
          <DefaultButton theme={myTheme} text={L('Add individual offer')} disabled={this.asyncActionInProgress === true} 
            className={classNames.makeIndividualOfferButton} style={{marginLeft: 0}}
            onClick={() => {
              this.showMakeIndividualOfferDialog = true;
              this.forceUpdate();
            }}
          />
        } */}

        <DefaultButton theme={myTheme} text={L('Generate request and response')} disabled={this.asyncActionInProgress === true} 
          className={classNames.makeIndividualOfferButton} style={{marginLeft: 0}}
          onClick={() => {
            this.generateRequestAndResponse();
            this.showInsurerCommunicationsDialog = true;
            this.forceUpdate();
          }}
        />
      </div>

      { createdOffertsList.length > 0 && 
        <Stack>
          <ul>
            {createdOffertsList}
          </ul>
        </Stack>
      }
    </PivotItem>;

    return <>
        <Dialog
          hidden={!this.showInsurerCommunicationsDialog}
          onDismiss={() => this.cancelInsurerCommunicationsDialog()}
          dialogContentProps={{
            type: DialogType.normal,
            title: L('Insurer communications'),
            className: classNames.dialogMain,
          }}
          modalProps={{
            isBlocking: true,
          }}
          minWidth={595}
        >
          <DialogContent>
            {this.userCommunications.length > 0 ? (
              <>
                <DropdownBase key={'insurerDropdown'} required={false} customDropdownWidth={'200px'}
                  value={this.selectedInsurer ?? undefined} disabled={false} isDataLoaded={true}
                  options={
                    this.userCommunications
                      .map((userCommunication: any) => userCommunication.insurerCommunications)
                      .reduce((acc: any[], curr: any[]) => acc.concat(curr), [])
                      .map((insurerCommunication: any) => ({
                        key: insurerCommunication.insurerName,
                        text: insurerCommunication.insurerName,
                      }))
                      .filter((value: any, index: any, self: any) =>
                        self.findIndex((v: any) => v.key === value.key) === index
                      )
                      .sort((a: any, b: any) => a.text.localeCompare(b.text))
                  }
                  onChange={(e: string | number | undefined) => {
                    if (e !== this.selectedInsurer) {
                      this.selectedInsurer = e;
                      this.forceUpdate();
                    }
                  }}
                />

                {this.selectedInsurer && (
                  this.userCommunications.map((userCommunication: any) => {
                    const filteredCommunications = userCommunication.insurerCommunications.filter((insurerCommunication: any) => insurerCommunication.insurerName === this.selectedInsurer);
                    if (filteredCommunications.length === 0) {
                      return null;
                    }

                    return (
                      <>
                        <h3>{L(`${userCommunication.type} - ${userCommunication.status}`)} {dateFormat(userCommunication.communicationDate)}</h3>
                        <ul>
                          {filteredCommunications.map((insurerCommunication: any, index: number) =>
                            <li key={index}>
                              <p><b>{insurerCommunication.insurerName}</b> - {insurerCommunication.directionType} ({L(insurerCommunication.description)})</p>
                              <DefaultButton text={`${L('Download')} ${insurerCommunication.fileType}`} style={{ display: 'block', margin: '5px 0' }}
                                onClick={() => {
                                  let fileData = insurerCommunication.data;

                                  let filename = `${this.calculation.id}_${insurerCommunication.insurerName}_${insurerCommunication.directionType.substring(0,3).toLowerCase()}_${userCommunication.type.substring(0,3).toLowerCase()}_${insurerCommunication.description.toLowerCase()}.${insurerCommunication.fileType.toLowerCase()}`;
                                  let linkElement = document.createElement('a');
                                  let bb = new Blob([fileData], {type: insurerCommunication.fileType === "JSON" ? 'application/json' : 'text/plain'});

                                  linkElement.setAttribute('href', window.URL.createObjectURL(bb));
                                  linkElement.setAttribute('download', filename);

                                  linkElement.dataset.downloadurl = [insurerCommunication.fileType === "JSON" ? 'application/json' : 'text/plain', linkElement.download, linkElement.href].join(':');
                                  linkElement.draggable = true;

                                  linkElement.click();
                                }} />
                            </li>
                          )}
                        </ul>
                      </>
                    );
                  })
                )}
              </>
            ) : (
              <p>{this.asyncActionInProgress ? '' : L('No data')}</p>
            )}
          </DialogContent>

          <DialogFooter>
            {this.asyncActionInProgress && (
              <div style={{position: 'absolute', top: '-22px', left: '0'}}>
                <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
              </div>
            )}
          
            <DefaultButton theme={myTheme} text={L('Cancel')} disabled={this.asyncActionInProgress === true}
              onClick={() => this.cancelInsurerCommunicationsDialog()}
            />
          </DialogFooter>
        </Dialog>

        {this.calculation && this.calculation.segment === InsurerAttachedFilePolicyType.Vehicle &&
          <CalculationProductVehicle calculation={this.calculation} showMakeIndividualOfferDialog={this.showMakeIndividualOfferDialog} isEditMode={this.isEditMode} isDataLoaded={this.isDataLoaded}
            asyncActionInProgress={this.asyncActionInProgress} individualOffer={this.individualOffer} activitiesPivotItem={activitiesPivotItem}
            toggleShowMakeIndividualOfferDialog={(value: boolean) => { this.showMakeIndividualOfferDialog = value; this.forceUpdate(); }}
            createIndividualOffer={() => this.createIndividualOffer()} onUploadPdf={() => this.onUploadPdf()} renderDataPickersAndFee={() => this.renderDataPickersAndFee()}
            validateBankAccountNumber={(bankAccountNumber: string) => this.validateBankAccountNumber(bankAccountNumber)}
            renderElement={(element: ContentViewModelProperty, error: any, value: any, tableInputData?: any) => this.renderElement(element, error, value, tableInputData)}
            updateIndividualOfferValue={(newIndividualOffer: IndividualOfferDto) => { this.individualOffer = newIndividualOffer; this.forceUpdate(); }}
            apkData={this.apkData} calculationData={this.calculationData} productAttributeResultItems={this.productAttributeResultItems} gnLanguage={this.gnLanguage}
          />
        }

        {this.calculation && this.calculation.segment === InsurerAttachedFilePolicyType.Children &&
          <CalculationProductChildren calculation={this.calculation} isEditMode={this.isEditMode} isDataLoaded={this.isDataLoaded} activitiesPivotItem={activitiesPivotItem}
            apkData={this.apkData} calculationData={this.calculationData} productAttributeResultItems={this.productAttributeResultItems} gnLanguage={this.gnLanguage}
            renderElement={(element: ContentViewModelProperty, error: any, value: any, tableInputData?: any) => this.renderElement(element, error, value, tableInputData)}
          />
        }

        {this.calculation && this.calculation.segment === InsurerAttachedFilePolicyType.Travel &&
          <CalculationProductTravel calculation={this.calculation} isEditMode={this.isEditMode} isDataLoaded={this.isDataLoaded} activitiesPivotItem={activitiesPivotItem}
            apkData={this.apkData} calculationData={this.calculationData} productAttributeResultItems={this.productAttributeResultItems} gnLanguage={this.gnLanguage}
            renderElement={(element: ContentViewModelProperty, error: any, value: any, tableInputData?: any) => this.renderElement(element, error, value, tableInputData)}
          />
        }

        {this.calculation && this.calculation.segment === InsurerAttachedFilePolicyType.CancelTravel &&
          <CalculationProductCancelTravel calculation={this.calculation} isEditMode={this.isEditMode} isDataLoaded={this.isDataLoaded} activitiesPivotItem={activitiesPivotItem}
            apkData={this.apkData} calculationData={this.calculationData} productAttributeResultItems={this.productAttributeResultItems} gnLanguage={this.gnLanguage}
            renderElement={(element: ContentViewModelProperty, error: any, value: any, tableInputData?: any) => this.renderElement(element, error, value, tableInputData)}
          />
        }
          
        {!this.supportedCalculationSegments.includes(this.calculation.segment) &&
          <p>{L('Not supported calculation segment.')}</p>
        }

        {(this.asyncActionInProgress || !this.productDataProcessed) && (
          <div style={{marginTop: '20px', marginLeft: '-45px'}}>
            <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
          </div>
        )}
    </>
  }
}