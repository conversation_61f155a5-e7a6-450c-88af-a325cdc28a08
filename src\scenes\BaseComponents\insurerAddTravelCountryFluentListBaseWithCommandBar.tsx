import {FluentTableBase} from "../Fluent/base/fluentTableBase";
import {ITableColumn} from "./ITableColumn";
import {myTheme} from "../../styles/theme";
import {L} from "../../lib/abpUtility";
import {ICrudPermissons} from "./commandBarBase";
import {<PERSON>fault<PERSON><PERSON>on, <PERSON><PERSON>, <PERSON><PERSON>Footer, PrimaryButton, TextField, ThemeProvider} from "@fluentui/react";
import { DropdownBase } from "./dropdownBase";
import { TravelCountryDto } from "../../services/travelCountry/dto/travelCountryDto";
import travelCountryCoverageService from "../../services/travelCountryCoverage/travelCountryCoverageService";
import { GeographicalZoneType } from "../../services/travelCountryCoverage/geographicalZoneEnums";

const dialogStyles = {
    main: {
        selectors: {
            '@media (min-width: 0px)': {
                maxWidth: 580,
                width: 580
            }
        }
    }
};

export class InsurerAddTravelCountryFluentListBaseWithCommandBar extends FluentTableBase<TravelCountryDto> {
    private shouldReloadItems: boolean = false;
    private showPopUpDialog: boolean = false;
    private insurerCountryId: string = "";
    private selectedGeographicalZone: string = "";
    private geographicalZoneTypeOptions = [{key: "Poland", text: L(GeographicalZoneType.Poland)}, {key: "Europe", text: L(GeographicalZoneType.Europe)}, {key: "RestOfTheWorldWithoutUSA", text: L(GeographicalZoneType.RestOfTheWorldWithoutUSA)}, {key: "RestOfTheWorld", text: L(GeographicalZoneType.RestOfTheWorld)}]
    
    disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;

    getColumns(): ITableColumn[] {
        return this.getTableColumns(this.props);
    }

    private getTableColumns(props: any): ITableColumn[] {
        return [
            {
                name: L('Country'),
                fieldName: 'country',
                onRender: (item: TravelCountryDto) => {
                    return L(item.name);
                }
            }
        ];
    }

    getCrudPermission(): ICrudPermissons {
        return {
            create: false,
            update: false,
            delete: false,
            customActions: true,
        };
    }

    getCommandBarBaseProps() {
        let props = super.getCommandBarBaseProps();
        return {
            ...props,
            customActionsProps: [
                {
                    displayFor: 'single',
                    buttonText: L("Add"),
                    buttonIcon: "Add",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                {
                    displayFor: 'none',
                    buttonText: L("Add a general list of countries"),
                    buttonIcon: "Add",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                }
            ],
            customActions: [
                () => {
                    this.handleShowPopUpDialog();
                },
                () => {
                    this.props.customData.handleAddCountryFromGeneralList();
                }
            ]
        }
    }

    private createTravelCountryCoverage = async () => {
        await travelCountryCoverageService.create({
            countryId: this.props.customData.selectedTravelCountry.id,
            insurerId: this.props.customData.insurerId,
            insurerCountryId: this.insurerCountryId,
            geographicalZone: this.selectedGeographicalZone
        }).then((response: any) => {
            if(response) {
                this.showPopUpDialog = false;
                this.reloadItems();
            }
        }).catch((error: any) => {
            console.error(error);
        })
        this.forceUpdate();
    }

    private handleShowPopUpDialog() {
        this.insurerCountryId = '';
        this.selectedGeographicalZone = '';
        this.showPopUpDialog = true;
        this.forceUpdate();
    }

    private reloadListOnDialogClose() {
        this.showPopUpDialog = false;
    
        if(this.shouldReloadItems) {
            this.reloadItems();
        }
    
        this.forceUpdate();
    }

    private async reloadItems() {
        this.selectionSetAllSelected(false);
        if(typeof this.props.refreshItems !== 'undefined') {
            await this.props.refreshItems!();
        }
    }  

    renderAll(pageInfo: string, values: any, columns: any) {
        return <>
            <Dialog
                hidden={!this.showPopUpDialog}
                onDismiss={() => this.reloadListOnDialogClose()}
                modalProps={{
                    isBlocking: true,
                    styles: dialogStyles
                }}
            >
                <TextField 
                    label={L("Enter the country ID")}
                    value={this.insurerCountryId}
                    onChange={(e: any, newValue?: string) => {
                        this.insurerCountryId = newValue || '';
                        this.forceUpdate();
                    }}
                />
                <DropdownBase key="GeographicalZoneType" required={true} label={L("Select geographical zone")} options={this.geographicalZoneTypeOptions} value={this.selectedGeographicalZone ? this.selectedGeographicalZone : this.props.customData.selectedSport?.coverageType} 
                    isDataLoaded={true} 
                    customLabelStyles={{width: "200px", minWidth: "200px"}}
                    onChange={(value) => {
                        if (typeof value === 'string') {
                            this.selectedGeographicalZone = value;
                            this.forceUpdate();
                        } else {
                        }
                    }}
                />
                <DialogFooter theme={myTheme}>
                    <PrimaryButton
                        onClick={() => {
                            this.createTravelCountryCoverage();
                        }}
                        text={L('Save')}
                        theme={myTheme}
                    />
                    <DefaultButton theme={myTheme} onClick={() => this.reloadListOnDialogClose()} text={L('Cancel')} />
                </DialogFooter>
            </Dialog>
            
            <ThemeProvider theme={myTheme}>
                { this.renderAnnounced(pageInfo) }
                { this.renderCommandBarBase() }

                { this.renderListScrollablePane(values, columns) }
            </ThemeProvider>
        </>;
    }
}