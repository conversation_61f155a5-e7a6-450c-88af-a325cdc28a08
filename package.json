{"name": "reactjs", "version": "4.7.1", "private": true, "dependencies": {"@azure/msal-browser": "^2.16.1", "@azure/msal-react": "^1.0.1", "@azure/storage-blob": "^12.5.0", "@craco/craco": "^6.2.0", "@fluentui/font-icons-mdl2": "^8.1.11", "@fluentui/react": "^8.30.1", "@fluentui/react-components": "^9.55.0", "@microsoft/signalr": "^5.0.9", "abp-web-resources": "^5.4.0", "axios": "^0.21.1", "classnames": "^2.3.1", "famfamfam-flags": "^1.0.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "mobx": "^5.15.0", "mobx-react": "^6.1.4", "moment": "^2.24.0", "moment-timezone": "^0.5.31", "object-hash": "^3.0.0", "query-string": "^6.9.0", "react": "^16.12.0", "react-checkbox-tree": "^1.8.0", "react-document-title": "^2.0.3", "react-dom": "^16.12.0", "react-loadable": "^5.5.0", "react-router-dom": "^5.1.2", "react-scripts": "^4.0.3", "recharts": "^1.8.5"}, "scripts": {"start": "craco --openssl-legacy-provider start", "build": "craco --openssl-legacy-provider build", "test": "craco test", "eject": "react-scripts eject", "cy:open": "cypress open"}, "devDependencies": {"@types/classnames": "^2.2.9", "@types/jest": "^24.0.23", "@types/moment": "^2.13.0", "@types/moment-timezone": "^0.5.12", "@types/node": "^12.12.14", "@types/react": "^16.9.13", "@types/react-document-title": "^2.0.3", "@types/react-dom": "^16.9.4", "@types/react-loadable": "^5.5.2", "@types/react-router-dom": "^5.1.3", "@types/recharts": "^1.8.3", "copy-webpack-plugin": "6.4.1", "cypress": "^13.15.0", "ts-import-plugin": "^1.6.7", "typescript": "4.4.2", "yarn-audit-fix": "^10.0.9"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}