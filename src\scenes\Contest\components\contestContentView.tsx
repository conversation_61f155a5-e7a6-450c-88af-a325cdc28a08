import { <PERSON><PERSON>downOption, MessageBar, MessageBarType, Pivot, PivotItem, PrimaryButton, Spinner, SpinnerSize, Stack, mergeStyleSets } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { additionalTheme, myTheme } from '../../../styles/theme';
import { ContestDto } from '../../../services/contest/dto/contestDto';
import { defaultContest } from '../../../stores/contestStore';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { Controls } from '../../BaseComponents/controls';
import { DropdownBase } from '../../BaseComponents/dropdownBase';
import { mapProductSeNameToPolicyType } from '../../../utils/modelUtils';
import { MultiDropdownBase } from '../../BaseComponents/multiDropdownBase';
import { LabeledTextField } from '../../../components/LabeledTextField';
import { catchErrorMessage, enumToDropdownOptions, isJsonString } from '../../../utils/utils';
import moment from 'moment';
import { ContestTypeEnum } from '../../../services/contest/dto/contestTypeEnum';
import React from 'react';
import { uploadFileToAzure } from '../../../services/azureService';
import contestAttachedFilesService from '../../../services/contestAttachedFiles/contestAttachedFilesService';
import { LabelContainerComponent } from '../../BaseComponents/labelContainerComponent';
import { LabelComponent } from '../../BaseComponents/labelComponent';

const classNames = mergeStyleSets({
    customErrorMessage: {
        color: myTheme.palette.redDark,
        fontSize: '12px',
        fontWeight: 400,
        marginLeft: '380px'
    },
    uploadButton: {
        width: 'fit-content',
        padding: '25px 50px',
    },
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    messageBar: {
        width: 'fit-content',
        marginTop: '20px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
    uploadedImage: {
        maxHeight: '250px',
        maxWidth: '70%',
        width: 'auto',
        border: '1px solid rgba(0, 0, 0, 0.2)',
    },
});

export class ContestContentView extends GenericContentView {
    private contest: ContestDto = defaultContest;
    private productOptions: IDropdownOption[] = [];
    private prizeOptions: IDropdownOption[] = [];
    private selectedPrizes: string[] = [];
    private minPriceForConditions: number = 0;
    private maxPriceForConditions: number = 0;
    private fileUploadInputRef: any;
    private selectedFileForUpload: any = {
        name: "" as string,
        src: "" as string,
    };
    private onUploadError: string = '';

    constructor(props: any) {
        super(props);
        this.fileUploadInputRef = React.createRef();
    }

    async componentDidMount() {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        this.checkIfDataIsLoaded("contest");

        if(this.contest.prizeItems && this.contest.prizeItems.length > 0) {
            this.contest.prizeItems.forEach((item: any) => {
                this.selectedPrizes.push(item.prizeId);
            });
        }

        if(!!this.contest.conditions && typeof this.contest.conditions === 'string' && isJsonString(this.contest.conditions)) {
            const tempConditions: any = JSON.parse(this.contest.conditions);

            this.minPriceForConditions = parseInt(tempConditions.MinPrice);
            this.maxPriceForConditions = parseInt(tempConditions.MaxPrice);
        }

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    private triggerUpload = () => {
        this.fileUploadInputRef.current.click();
    };
    
    private async onUpload() {
        this.asyncActionInProgress = true;
        
        this.contest = this.props.payload.model ? this.props.payload.model : this.props.payload;
        const selectedFile = !!this.fileUploadInputRef.current.files && this.fileUploadInputRef.current.files.length ? this.fileUploadInputRef.current.files[0] : null;
        
        if(!!selectedFile) {
            this.selectedFileForUpload.name = selectedFile.name;
            this.selectedFileForUpload.src = URL.createObjectURL(selectedFile);
        }

        this.forceUpdate();

        let result = await uploadFileToAzure(selectedFile);

        await contestAttachedFilesService.createNew({
            'id': '',
            'contestId': parseInt(this.contest.id),
            'contest': this.contest,
            'fileUrl': result.url,
            'originalFileName': selectedFile.name,
            'blobFileName': result.name,
            'description': '',
        }).then(async (response: any) => {
            if(!!response.fileUrl) {
                this.contest.imageUrl = response.fileUrl;
            }
            
            this.onUploadError = '';
            this.asyncActionInProgress = false;
            this.forceUpdate();
        }).catch((error: any) => {
            console.error(error);
            this.onUploadError = catchErrorMessage(error);

            this.asyncActionInProgress = false;
            this.forceUpdate();
        });
    }

    private validateData(contest: ContestDto): any {
        const inputErrors: any = {
            prizesAmount: '',
            startDate: '',
            endDate: '',
            minPrice: '',
            maxPrice: '',
            errorsCount: 0,
        };

        if(typeof this.minPriceForConditions === 'number' && typeof this.maxPriceForConditions === 'number' && this.minPriceForConditions > this.maxPriceForConditions) {
            inputErrors.minPrice = L("The minimum price cannot be higher than the maximum price.");
            inputErrors.maxPrice = L("The maximum price cannot be lower than the minimum price.");
            inputErrors.errorsCount++;
        }

        if(contest.infinitePrizes === false && 
            (!contest.prizesAmount || (typeof contest.prizesAmount === 'string' && (contest.prizesAmount === '' || contest.prizesAmount === '0')) || 
            contest.prizesAmount === 0)
        ) {
            inputErrors.prizesAmount = L("The number of prizes must be specified and greater than 0.");
            inputErrors.errorsCount++;
        }

        if(!!contest.startDate && !!contest.endDate && (moment(contest.startDate).isValid() && moment(contest.endDate).isValid()) && moment(contest.startDate).isAfter(moment(contest.endDate))) {
            this.contest.endDate = contest.startDate;
            inputErrors.startDate = L("The start date cannot be later than the end date.");
            inputErrors.endDate = L("The end date cannot be earlier than the start date.");
            inputErrors.errorsCount++;
        }

        const valuesToCheck: string[] = ['descriptionTitle', 'description', 'endDate', 'startDate', 'promotionRules', 'productType', 'name', 'receivedPromotionDescription'];
        
        if(this.contest.contestType === Object.keys(ContestTypeEnum).find(key => ContestTypeEnum[key] === ContestTypeEnum.Special)) {
            valuesToCheck.push('section1Name', 'section1Description', 'section2Name', 'section2Description');
        }

        valuesToCheck.some((value: string) => {
            const splittedValue: string[] = value.split('.');

            if(splittedValue.length === 1 && (!this.contest[value] || this.contest[value].length === 0)) {
                inputErrors.errorsCount++;
                return true;
            } else if(splittedValue.length > 1) {
                let tempContestDeeper: any = this.contest;
                splittedValue.forEach((value: string) => {
                    tempContestDeeper = tempContestDeeper[value];
                });

                if(!tempContestDeeper || tempContestDeeper.length === 0) {
                    inputErrors.errorsCount++;
                    return true;
                } else {
                    return false;
                }
            }
            return false;
        });
        
        return inputErrors;
    }

    renderContent() {
        this.contest = this.props.payload.model ? this.props.payload.model : this.props.payload;

        if(this.productOptions.length === 0 && this.props.productStore && this.props.productStore.dataSet && this.props.productStore.dataSet.items.length > 0) {
            this.props.productStore.dataSet.items.forEach((product: any) => {
                const mappedSeNameToPolicyType: string = mapProductSeNameToPolicyType(product.SeName);
                if(!!mappedSeNameToPolicyType) {
                    this.productOptions.push({ key: mappedSeNameToPolicyType, text: product.Name });
                }
            }); 
        }

        if(this.prizeOptions.length === 0 && this.props.prizeStore && this.props.prizeStore.dataSet && this.props.prizeStore.dataSet.items.length > 0) {
            this.props.prizeStore.dataSet.items.forEach((prize: any) => {
                this.prizeOptions.push({ key: prize.id, text: prize.name });
            }); 
        }

        let inputErrors: any = this.validateData(this.contest);
        if(inputErrors.errorsCount !== this.props.inputErrorsCount && this.props.onSetInputErrorsCount) {
            this.props.onSetInputErrorsCount(inputErrors.errorsCount);
            this.forceUpdate();
        }

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed,
                    }
                }
            }
        };
        
        const fileUploadButton = <PrimaryButton 
                                    className={classNames.uploadButton}
                                    theme={myTheme}
                                    text={L('Upload image')}
                                    type={'file'}
                                    onClick={this.triggerUpload}
                                    disabled={this.asyncActionInProgress === true}
                                    iconProps={{ iconName: 'Upload' }}
                                />;

        return <Pivot  styles={pivotStyles} theme={myTheme}>
            <PivotItem headerText={L('General')} key={'General'}>
                {!!this.contest.imageUrl &&
                    <LabelContainerComponent>
                        <LabelComponent customStyles={{marginRight: 15}} label={L('Image')} />
                        <img alt="Contest logo" src={this.contest.imageUrl} className={classNames.uploadedImage} />
                    </LabelContainerComponent>
                }

                { (this.contest && !!this.contest.id && (typeof this.contest.id === 'number' && this.contest.id > 0)) &&
                    <>
                        <Stack horizontal style={{marginTop: 20}}>
                            <input ref={this.fileUploadInputRef} type="file" accept="image/png, image/jpeg" style={{display: 'none'}} onChange={() => this.onUpload()} />
                            { fileUploadButton }

                            {this.asyncActionInProgress && (
                                <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                            )}
                        </Stack>

                        {!!this.onUploadError &&
                            <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`}
                                onDismiss={() => { this.onUploadError = ''; this.forceUpdate(); }}    
                            >
                                {this.onUploadError}
                            </MessageBar>
                        }
                    </>
                }

                {this.renderElement(new ContentViewModelProperty("contestType", L("Contest type"), Controls.Picker, true, {dropdown: enumToDropdownOptions(ContestTypeEnum, false, true, "string", true)}, false, { isDataLoaded: this.isDataLoaded}), [], {'contestType': this.contest.contestType })}
                {this.renderElement(new ContentViewModelProperty("scale", L("Scale"), Controls.Text, true, [], false, { isDataLoaded: this.isDataLoaded, textType: 'number', validationData: {'min': '1'}}), [], {'scale': this.contest.scale })}
                {this.renderElement(new ContentViewModelProperty('name', L('Name'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'name': this.contest.name})}

                <DropdownBase key="product" required={true} label={L("Select product")} options={this.productOptions} value={this.contest.productType} 
                    disabled={!this.isDataLoaded}
                    isDataLoaded={this.productOptions.length > 0} 
                    customLabelStyles={{ width: "350px", minWidth: "350px" }}
                    onChange={(value: string | number | undefined) => {
                        this.contest.productType = typeof value === 'string' ? value : '';
                        this.forceUpdate();
                    }} />

                <LabeledTextField key={'minPrice'} required={true} label={L('Condition minimum price')} value={this.minPriceForConditions.toString()}
                    disabled={!this.isDataLoaded} isDataLoaded={!this.asyncActionInProgress} validationData={{mask: "******", maskFormat: {'*': /[0-9]/}, maskChar: "", maxlength: 6,}}
                    errorMessage={inputErrors.minPrice}
                    onChange={(e: any) => {
                        let target = e.target as any;
                        let newValue: string | number | undefined;
                        if(target && (typeof target.value === 'string' || typeof target.value === 'number')) {
                            newValue = target.value;
                        } else if(typeof e === 'string') {
                            newValue = e;
                        }
                        this.minPriceForConditions = !!newValue ? (typeof newValue === 'string' ? parseInt(newValue) : newValue) : 0;
                        this.contest.conditions = JSON.stringify({
                            'MinPrice': this.minPriceForConditions,
                            'MaxPrice': this.maxPriceForConditions,
                        });
                        this.forceUpdate();
                    }} />

                <LabeledTextField key={'maxPrice'} required={true} label={L('Condition maximum price')} value={this.maxPriceForConditions.toString()}
                    disabled={!this.isDataLoaded} isDataLoaded={!this.asyncActionInProgress} validationData={{mask: "******", maskFormat: {'*': /[0-9]/}, maskChar: "", maxlength: 6,}}
                    errorMessage={inputErrors.maxPrice}
                    onChange={(e: any) => {
                        let target = e.target as any;
                        let newValue: string | number | undefined;
                        if(target && (typeof target.value === 'string' || typeof target.value === 'number')) {
                            newValue = target.value;
                        } else if(typeof e === 'string') {
                            newValue = e;
                        }
                        this.maxPriceForConditions = !!newValue ? (typeof newValue === 'string' ? parseInt(newValue) : newValue) : 0;
                        this.contest.conditions = JSON.stringify({
                            'MinPrice': this.minPriceForConditions,
                            'MaxPrice': this.maxPriceForConditions,
                        });
                        this.forceUpdate();
                    }} />

                {this.renderElement(new ContentViewModelProperty('conditions', L('Conditions'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'conditions': this.contest.conditions})}
                {this.renderElement(new ContentViewModelProperty('isEnabled', L('Is Active'), Controls.ChoiceGroup, true, {choicegroup: [{key: 'true', text: L('Yes')}, {key: 'false', text: L('No')}]}, false, {isDataLoaded: this.isDataLoaded, parseToBoolean: true}), [], {'isEnabled': this.contest.isEnabled.toString()})}
                {this.renderElement(new ContentViewModelProperty('infinitePrizes', L('Infinity prizes'), Controls.ChoiceGroup, true, {choicegroup: [{key: 'true', text: L('Infinite')}, {key: 'false', text: L('Finite')}]}, false, {isDataLoaded: this.isDataLoaded, parseToBoolean: true}), [], {'infinitePrizes': this.contest.infinitePrizes.toString()})}
                {this.contest.infinitePrizes !== true &&
                    this.renderElement(new ContentViewModelProperty('prizesAmount', L('Prizes amount'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, validationData: {
                        mask: "******", maskFormat: {'*': /[0-9]/}, maskChar: "", maxlength: 6,
                    }}), {'prizesAmount': inputErrors.prizesAmount}, {'prizesAmount': this.contest.prizesAmount})
                }

                <MultiDropdownBase key="prizes" label={L("Select prizes")} options={this.prizeOptions} value={this.selectedPrizes}
                    disabled={!this.isDataLoaded} isDataLoaded={this.prizeOptions.length > 0}
                    onChange={(value: string[]) => {
                        this.selectedPrizes = value;

                        const tempPrizeItems: any[] = [];
                        value.forEach((prizeId: string) => {
                            tempPrizeItems.push({"prizeId": prizeId});
                        });
                        this.contest.prizeItems = tempPrizeItems;

                        this.forceUpdate();
                    }} />

                {this.renderElement(new ContentViewModelProperty('startDate', L('Start date'), Controls.Date, true, [], false, {isDataLoaded: this.isDataLoaded, validationData: {minDate: 'TODAY', isOutOfBoundsErrorMessage: ''}}), {'startDate': inputErrors.startDate}, {'startDate': this.contest.startDate})}
                {inputErrors.startDate && <span className={classNames.customErrorMessage}>{inputErrors.startDate}</span>}
                {this.renderElement(new ContentViewModelProperty('endDate', L('End date'), Controls.Date, true, [], false, {isDataLoaded: this.isDataLoaded, validationData: {minDate: !!this.contest.startDate ? this.contest.startDate : 'TODAY', isOutOfBoundsErrorMessage: ''}}), {'endDate': inputErrors.endDate}, {'endDate': this.contest.endDate})}
                {inputErrors.endDate && <span className={classNames.customErrorMessage}>{inputErrors.endDate}</span>}
                {this.renderElement(new ContentViewModelProperty('promotionRules', L('Promotion rules'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'promotionRules': this.contest.promotionRules})}
                {this.renderElement(new ContentViewModelProperty('descriptionTitle', L('Contest description title'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'descriptionTitle': this.contest.descriptionTitle})}
                {this.renderElement(new ContentViewModelProperty('description', L('Contest description'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'description': this.contest.description})}
                
                {this.contest.contestType === Object.keys(ContestTypeEnum).find(key => ContestTypeEnum[key] === ContestTypeEnum.Special) && 
                    <>
                        {this.renderElement(new ContentViewModelProperty('section1Name', L('Section 1 name'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'section1Name': this.contest.section1Name})}
                        {this.renderElement(new ContentViewModelProperty('section1Description', L('Section 1 description'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'section1Description': this.contest.section1Description})}
                        {this.renderElement(new ContentViewModelProperty('section2Name', L('Section 2 name'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'section2Name': this.contest.section2Name})}
                        {this.renderElement(new ContentViewModelProperty('section2Description', L('Section 2 description'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'section2Description': this.contest.section2Description})}
                    </>
                }
                
                {this.renderElement(new ContentViewModelProperty('receivedPromotionDescription', L('Received promotion description'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'receivedPromotionDescription': this.contest.receivedPromotionDescription})}
            </PivotItem>
        </Pivot>
    }
}