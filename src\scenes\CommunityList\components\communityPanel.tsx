import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { CommunityContentView } from '../../Community/components/communityContentView';
import { CommunityDto } from '../../../services/community/dto/communityDto';
import communityService from '../../../services/community/communityService';

export class CommunityPanel extends GenericPanel {
    private prevEntityId: any = 0;
    private dataDownloaded: boolean = false;

    getPanelTitle(): string {
        return L("Community");
    }

    private async getEntityModel(entityId: string) {
        await communityService.get({ id: entityId } as CommunityDto).then(async (community: CommunityDto) => {
            this.props.payload.model = await communityService.get({ id: entityId } as CommunityDto);
            this.dataDownloaded = true;
            this.forceUpdate();
        });
    }

    renderContent() {
        if(typeof this.props.payload.entityId === 'number' && this.props.payload.entityId > 0 && this.props.payload.entityId !== this.prevEntityId) {
            this.prevEntityId = this.props.payload.entityId;
            this.getEntityModel(this.prevEntityId.toString());
        } else if(this.dataDownloaded && (this.state.model.value.id === 0 || this.state.model.value.id === "")) {
            this.prevEntityId = 0;
            this.dataDownloaded = false;
        }

        return <CommunityContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as CommunityDto } />;
    }
}