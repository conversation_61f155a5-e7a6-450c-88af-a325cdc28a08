import { Pivot, PivotItem } from "@fluentui/react";
import React from "react";
import { ContentViewModelProperty } from "../../BaseComponents/contentViewBase";
import { Controls } from "../../BaseComponents/controls";
import { L } from "../../../lib/abpUtility";
import { additionalTheme, myTheme } from "../../../styles/theme";
import { checkUsedKeysForDataSet } from "./calculationProductProcessData";

const pivotStyles = {
	root: {
		marginLeft: '-8px'
	},
	linkIsSelected: {
		color: myTheme.palette.red,
		selectors: {
			':before': {
				height: '5px',
				backgroundColor: additionalTheme.darkerRed
			}
		}
	}
};

export interface ICalculationProductChildrenProps {
	calculation: any;
	calculationData: any;
	apkData: any;
	isEditMode: boolean;
	isDataLoaded: boolean;
	activitiesPivotItem: JSX.Element;
	productAttributeResultItems: any[];
	gnLanguage: any;
	renderElement: (element: ContentViewModelProperty, error: any, value: any, tableInputData?: any) => JSX.Element;
}

export class CalculationProductChildren extends React.Component<ICalculationProductChildrenProps> {
	// private usedAPKInputsKeys: string[] = [];
	private usedInputsKeys: string[] = ['ChildrenInsurance.Variant', 'ChildrenInsurance.AmountOfInjury', 'ChildrenInsurance.ProtectAgainstOnlineHate', 'ChildrenInsurance.SportsProfessionsallyOrAmature', 'ChildrenInsurance.BiteInsurance', 'ChildrenInsurance.CarePlus', 'ChildrenInsurance.StartDate', 'ChildrenInsurance.FirstName', 'ChildrenInsurance.LastName', 'ChildrenInsurance.ChildPESEL', 'ChildrenInsurance.OcInPrivateLife', 'ChildrenInsurance.Student', 'ChildrenInsurance.InsurerIsInsured', 'ChildrenInsurance.PaymentMethod', 'ChildrenInsurance.InstallmentCount'];
	private usedInputsKeysChecked: boolean = false;

	render() {
		const { calculation, isDataLoaded, calculationData, renderElement } = this.props;

		// some saved calculations doesn't have full data causing errors
		if(Object.keys(calculationData).length > 0 && !this.usedInputsKeysChecked) {
			// checkUsedKeysForDataSet(apkData, this.usedAPKInputsKeys);
			checkUsedKeysForDataSet(calculationData, this.usedInputsKeys, this.props.productAttributeResultItems, this.props.gnLanguage);

			this.usedInputsKeysChecked = true;
			this.forceUpdate();
		}

		return (
			<>
				<Pivot theme={myTheme} styles={pivotStyles}>		
					<PivotItem headerText={L('General')} key={'General'}>
						{renderElement(new ContentViewModelProperty('calculationId', L("Calculation ID"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'calculationId': calculation.id})}
						{renderElement(new ContentViewModelProperty('translatedSegment', L("Product"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'translatedSegment': L(calculation.segment)})}
						{renderElement(new ContentViewModelProperty('status', L("Status"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'status': L(calculation.lastCreatedPolicyStatus)})}
						{renderElement(new ContentViewModelProperty('creationTime', L("Creation date"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'creationTime': calculation.creationTime})}
						{renderElement(new ContentViewModelProperty('startDate', L("Policy start date"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'startDate': calculation.startDate})}
						{renderElement(new ContentViewModelProperty('customerName', L("Customer name"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerName': calculation.client.user.name})}
						{renderElement(new ContentViewModelProperty('customerSurname', L("Customer surname"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerSurname': calculation.client.user.surname})}
						{renderElement(new ContentViewModelProperty('pesel', L("Pesel"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'pesel': calculation.client.pesel})}
						{renderElement(new ContentViewModelProperty('customerEmail', L("Customer email"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerEmail': calculation.client.user.emailAddress})}
						{renderElement(new ContentViewModelProperty('phoneNumber', L("Customer phone number"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'phoneNumber': calculation.client.phone})}
					</PivotItem>
					
					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('APK')} key={'APK'}>
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.Variant', calculationData['ChildrenInsurance.Variant'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.Variant': calculationData['ChildrenInsurance.Variant'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.AmountOfInjury', calculationData['ChildrenInsurance.AmountOfInjury'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.AmountOfInjury': calculationData['ChildrenInsurance.AmountOfInjury'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.ProtectAgainstOnlineHate', calculationData['ChildrenInsurance.ProtectAgainstOnlineHate'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.ProtectAgainstOnlineHate': calculationData['ChildrenInsurance.ProtectAgainstOnlineHate'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.SportsProfessionsallyOrAmature', calculationData['ChildrenInsurance.SportsProfessionsallyOrAmature'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.SportsProfessionsallyOrAmature': calculationData['ChildrenInsurance.SportsProfessionsallyOrAmature'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.BiteInsurance', calculationData['ChildrenInsurance.BiteInsurance'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.BiteInsurance': calculationData['ChildrenInsurance.BiteInsurance'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.CarePlus', calculationData['ChildrenInsurance.CarePlus'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.CarePlus': calculationData['ChildrenInsurance.CarePlus'].valueLocale})}
						</PivotItem>
					}

					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('Complete the data')} key={'Complete the data'}>
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.InsurerIsInsured', calculationData['ChildrenInsurance.InsurerIsInsured'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.InsurerIsInsured': calculationData['ChildrenInsurance.InsurerIsInsured'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.StartDate', calculationData['ChildrenInsurance.StartDate'].label, Controls.Date, false, [], true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.StartDate': calculationData['ChildrenInsurance.StartDate'].value})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.FirstName', calculationData['ChildrenInsurance.FirstName'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.FirstName': calculationData['ChildrenInsurance.FirstName'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.LastName', calculationData['ChildrenInsurance.LastName'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.LastName': calculationData['ChildrenInsurance.LastName'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.ChildPESEL', calculationData['ChildrenInsurance.ChildPESEL'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.ChildPESEL': calculationData['ChildrenInsurance.ChildPESEL'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.OcInPrivateLife', calculationData['ChildrenInsurance.OcInPrivateLife'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.OcInPrivateLife': calculationData['ChildrenInsurance.OcInPrivateLife'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.Student', calculationData['ChildrenInsurance.Student'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.Student': calculationData['ChildrenInsurance.Student'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.PaymentMethod', calculationData['ChildrenInsurance.PaymentMethod'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.PaymentMethod': calculationData['ChildrenInsurance.PaymentMethod'].valueLocale})}
							{renderElement(new ContentViewModelProperty('ChildrenInsurance.InstallmentCount', calculationData['ChildrenInsurance.InstallmentCount'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'ChildrenInsurance.InstallmentCount': calculationData['ChildrenInsurance.InstallmentCount'].valueLocale})}
						</PivotItem>
					}

					{this.props.activitiesPivotItem}
				</Pivot>
			</>
		);
	}
}