import http from '../httpService';
import { isUserLoggedIn } from '../../utils/authUtils';
import { OrganizationUnitDto } from './dto/organizationUnitDto';

export class OrganizationUnitService {
  public async getOrganizationUnitTree(): Promise<OrganizationUnitDto[]> {
    isUserLoggedIn();
    let result = await http.get('api/services/app/OrganizationUnit/GetTree');
    return !!result.data && !!result.data.result ? result.data.result : result.data;
  }
}

const exportOrganizationUnitService: OrganizationUnitService = new OrganizationUnitService();
export default exportOrganizationUnitService;
