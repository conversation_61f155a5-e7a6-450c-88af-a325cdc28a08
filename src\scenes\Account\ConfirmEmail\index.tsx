import * as React from 'react';
import { inject, observer } from 'mobx-react';
import Stores from '../../../stores/storeIdentifier';
import AccountStore from '../../../stores/accountStore';
import { L } from '../../../lib/abpUtility';
import { Link, mergeStyleSets } from '@fluentui/react';
import Loading from '../../../components/Loading';
import { myTheme } from '../../../styles/theme';

export interface IProps {
  accountStore: AccountStore;
}

export interface IState {
  isLoading: boolean,
  isError: boolean
}
const classNames = mergeStyleSets({
  verificate: {
    height: '80vh',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
  },

  success: {
    color: myTheme.palette.greenLight,
  },

  error: {
    color: myTheme.palette.red,
  },

  info: {
    color: myTheme.palette.themePrimary,
  },

  verificateTitle: {
    fontWeight: 'bold',
    paddingBottom: '8px',
    width: '200px',
    textAlign: 'center',
  },

  top5: {
    marginTop: '5px',
  },
});
@inject(Stores.AccountStore)
@observer
export class Account extends React.Component<IProps, IState> {

  state = {
    isLoading: true,
    isError: false
  }
  async componentDidMount() {
    try {
      await this.props.accountStore.confirmEmail(window.location.search);
    }
    catch{
      this.setState({ isError: true })
    }
    this.setState({ isLoading: false })
  }

  render() {
    return (
      <div className={classNames.verificate}>
        {this.state.isLoading && <Loading />}
        {!this.state.isLoading &&
          <>
            {this.state.isError &&
              <>
                <div className={`${classNames.error} ${classNames.verificateTitle}`}>{L('Error.')}</div>
                <div>{L('Some error occured.')}</div>
                <div>{L('Go to home page ')} <Link theme={myTheme} href="/user/login">{L("here")}</Link>.</div>
              </>}
            {!this.state.isError &&
              <>
                <div className={`${classNames.success} ${classNames.verificateTitle}`}>{L('Success.')}</div>
                <div >{L('Contgratulations, your account has been confirmed.')}</div>
                <div>{L('Now you can login ')} <Link theme={myTheme} href="/user/login">{L("here")}</Link>.</div>
              </>}
          </>
        }
      </div>
    );
  }
}

export default Account;
