interface IVehicleDataGeneric {
    errorsCount: number, 
    error: string, 
    gettingDataInProgress: boolean,
}

export interface IEurotaxVehicleTypes extends IVehicleDataGeneric {
    types?: Array<any>,
}

export interface IEurotaxVehicleConfiguration extends IVehicleDataGeneric {
    enginePower?: any,
}

export interface IEurotaxVehicleBrands extends IVehicleDataGeneric {
    typeId?: Array<any>,
}

export interface IEuroTaxEngineCapacity extends IVehicleDataGeneric {
    model?: Array<any>,
}

export interface IEuroTaxEnginePower extends IVehicleDataGeneric {
    engineCapacity?: Array<any>,
}

export interface IEuroTaxFuelType extends IVehicleDataGeneric {
    fuelType?: Array<any>,
}

export interface IVehicleBrands extends IVehicleDataGeneric {
    year?: Array<any>,
}

export interface IVehicleModels extends IVehicleDataGeneric {
    brand?: Array<any>,
}

export interface IVehicleModelData extends IVehicleDataGeneric {
    [year: number]: {
        [brand: string]: {
            data: any
        }
    },
}

export type ICustomInputsBoxGeneric = { 
    date: Date;
    customInputsAsyncActionInProgress: boolean;
    vehicleModels: IVehicleModels;
};