export const AzureFileUpload = () => {}

// import React, { useState, useEffect, forwardRef } from "react";
// import { UploadedFile, uploadFileToAzure } from '../../services/azureService';
// import { Upload } from 'antd';
// import { RcFile, UploadFile, UploadProps } from "antd/lib/upload/interface";
// import { DefaultButton, Icon, IImageProps, Image, ImageFit, Label, mergeStyleSets } from '@fluentui/react';
// import { myTheme } from "../../styles/theme";


// const classNames = mergeStyleSets({
//   uploadImageClass: {
//     justifyContent: "space-between",
//     selectors: {
//       '.ant-upload-list-item-name': {
//         marginLeft: '20px',
//         marginRight: '-25px'
//       }
//     }
//   }
// });

// export interface FileUploadProps {
//   /**
//  * Url for uploaded file, if use antd field decorator leave undefined and wrap component into getFieldDecorator
//  */
//   value?: string;
//   onChange?: (value: string) => void;
//   label?: string;
//   multiple?: boolean;
//   width?: string | number;
//   height?: string | number;
//   maxImageSizeInKB?: number;
// }

// export type ImageErrorType = "size" | "upload";

// export interface ImageError {
//   type: ImageErrorType;
//   message: string;
// }
// export const AzureFileUpload = forwardRef((props: FileUploadProps) => {

//   const [data, setData] = useState<UploadedFile>();
//   const [uploading, setUploading] = useState<boolean>(false);
//   const [fileList, setFileList] = useState<Array<UploadFile>>([]);
//   const [errors, setErrors] = useState<Array<ImageError>>();
  
//   let currentErrors: Array<ImageError> = [];

//   useEffect(() => {
//     setData({
//       name: props.value ?? "",
//       url: props.value ?? "",
//     })
//   }, [props.value]);

//   // These props are defined up here so they can easily be applied to multiple Images.
//   // Normally specifying them inline would be fine.
//   const imageProps: IImageProps = {
//     imageFit: ImageFit.centerContain,
//     width: props.width ?? 280,
//     height: props.height ?? 150,
//   };

//   const handleUpload = async () => {
//     cleanImageError("upload");
//     if (fileList[0].originFileObj && errors?.length == 0) {
//       setUploading(true);
//       try {
//         let uploadResult = await uploadFileToAzure(fileList[0].originFileObj as File);
//         setData(uploadResult);
//         if (props.onChange) {
//           props.onChange(uploadResult.url);
//         }
//       } catch (err) {
//         setImageError(err, "upload");
//       }
//       finally {
//         setUploading(false);
//       }
//     }
//   }

//   const uploadProps: UploadProps = {
//     onRemove: file => {
//       setFileList(value => {
//         const index = value.indexOf(file);
//         const newFileList = value.slice();
//         newFileList.splice(index, 1);
//         return newFileList;
//       });
//       cleanImageError("size");
//       cleanImageError("upload");
//     },

//     beforeUpload: (file: RcFile) => {
//       if(props.maxImageSizeInKB) {
//         const limitSize = props.maxImageSizeInKB * 1024;
//         validateImageSize(file, limitSize);
//       }

//       let upFile: UploadFile = {
//         ...file,
//         name: file.name,
//         uid: file.uid,
//         originFileObj: file,
//       }
//       if (props.multiple && props.multiple == true) {
//         setFileList([...fileList, upFile]);
//       } else {
//         setFileList([upFile]);
//       }
//       return false;
//     },
//     fileList,
//     multiple: false,
//   };

//   const validateImageSize = (file: File, limitSize: number) => {
//     if(file.size > limitSize) {
//       const errorMessage = `Upload image size is too large (max. ${props.maxImageSizeInKB} KB).`;
//       setImageError(errorMessage, "size");
//     } else {
//       cleanImageError("size");
//     }
//   }

//   const setImageError = (value: string, type: ImageErrorType) => {
//     const restErrors = currentErrors?.filter(x => x.type != type) || [];
//     const result: Array<ImageError> = [...restErrors, {message: value, type: type}];
//     currentErrors = result;
//     setErrors(result);
//   }

//   const cleanImageError = (type: ImageErrorType) => {
//     const result = currentErrors.filter(x => x.type != type);
//     currentErrors = result;
//     setErrors(result);
//   } 

//   return (
//     <div>
//       <Label>{props.label}</Label>

//       <Upload {...uploadProps} className={classNames.uploadImageClass} >
//         <DefaultButton>
//           <Icon iconName='upload' /> Select File
//         </DefaultButton>
//       </Upload>
//       <DefaultButton
//         theme={myTheme}
//         onClick={handleUpload}
//         disabled={fileList.length === 0 || (errors?.length || 0) > 0}
//       >
//         {uploading ? 'Uploading' : 'Start Upload'}
//       </DefaultButton>

//       {data?.url && (
//         <Image
//           {...imageProps}
//           src={data.url}
//           alt={data.name}
//         />
//       )}
//       {errors && (
//         <Label style={{color: 'red', width:props.width}}>{errors.map(x => x.message).join(", ")}</Label>
//       )}
//     </div>
//   );
// })