import AppConsts from './appconst';
import Util from '../utils/utils';

declare var abp: any;

export class SignalRAspNetCoreHelper {
  initSignalR() {
    var encryptedAuthToken = abp.utils.getCookieValue(AppConsts.authorization.encrptedAuthTokenName);
    abp.signalr = {
      autoConnect: true,
      connect: undefined,
      hubs: undefined,
      qs: AppConsts.authorization.encrptedAuthTokenName + '=' + encodeURIComponent(encryptedAuthToken),
      remoteServiceBaseUrl: AppConsts.remoteServiceBaseUrl,
      url: '/signalr'
    };

    Util.loadScript(AppConsts.appBaseUrl + '/dist/abp.signalr-client.js');
  }
}
const exportSignalRAspNetCoreHelper: SignalRAspNetCoreHelper = new SignalRAspNetCoreHelper();
export default exportSignalRAspNetCoreHelper;
