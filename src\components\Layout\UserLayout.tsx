import * as React from 'react';
import { Redirect, Route, Switch } from 'react-router-dom';
import DocumentTitle from 'react-document-title';
import { userRouter } from '../Router/router.config';
import utils from '../../utils/utils';
import { mergeStyleSets} from '@fluentui/merge-styles';
import LoginFooter from '../../scenes/Login/components/loginFooter';
import { additionalTheme } from '../../styles/theme';

const classNames = mergeStyleSets({
  container: {
    backgroundColor: additionalTheme.darkerWhite,
    display: 'flex',
    flexDirection: 'column',
    height: '100vh',
  }
});

class UserLayout extends React.Component<any> {
  render() {
    const {
      location: { pathname },
    } = this.props;

    const routes = userRouter
      .filter((item: any) => !item.isLayout)
      .map((item: any, index: number) => (
        <Route key={index} path={item.path} component={item.component} exact={item.exact} />
      ));
    
    return (
      <DocumentTitle title={utils.getPageTitle(pathname)}>
        <div className={classNames.container} style={{backgroundRepeat: 'round'}}>
          <Switch>
            {routes}
            <Redirect from="/user" to="/user/login" />
          </Switch>
          <LoginFooter />
        </div>
      </DocumentTitle>
    );
  }
}

export default UserLayout;