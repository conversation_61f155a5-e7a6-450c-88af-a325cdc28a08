
import { ComboBox, IComboBoxOption, Icon, mergeStyleSets, MessageBar, MessageBarType, Spinner, SpinnerSize, TextField } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import AppConfig from "../../../lib/appconfig";
import AppConsts from "../../../lib/appconst";
import vehicleCodeService from "../../../services/vehicleCode/vehicleCodeService";
import { myTheme } from "../../../styles/theme";
import { catchErrorMessage } from "../../../utils/utils";
import { ICustomInputsBoxGeneric, IEuroTaxEngineCapacity, IEuroTaxEnginePower, IEuroTaxFuelType, IEurotaxVehicleBrands, IEurotaxVehicleConfiguration, IEurotaxVehicleTypes } from "../../PolicyCalculation/components/types";

var _ = require('lodash');

const classNames = mergeStyleSets({
    hide: {
        display: 'none !important',
    },
    fontBold: {
        fontWeight: '800',
    },
    toolbar: {
        selectors: {
            '& .ms-Pivot': {
                display: 'none',
            }
        }
    },
    messageBar: {
        width: 'fit-content'
    },
    messageBarMargin: {
        marginTop: '15px'
    },
    closeIcon: {
        position: 'absolute',
        top: 20,
        right: 20,
        cursor: 'pointer',
        transition: 'all 150ms',
        selectors: {
            ':hover': {
                transform: 'scale(1.1)',
            },
            ':active': {
                transform: 'scale(0.9) rotate(90deg)',
            },
        }
    },
    customInputsWrapper: {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: 'fit-content',
        height: 'auto',
        marginTop: '20px',
        padding: '20px',
        boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
        borderRadius: '3px',
        minWidth: '502px',
        boxSizing: 'border-box',
    },
    comboBoxStyles: {
        width: '100%',
        marginTop: '15px',
    },
    loadSpinner: {
        display: 'inline-flex',
        marginTop: '15px',
        marginLeft: 'auto',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinnerTopBar: {
        display: 'inline-flex',
        marginLeft: '15px',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
});

export interface IEurotaxExpertInfoMixedBoxProps {
    collapseForm: boolean,
    customInputsData: any,
    onFormCollapse: () => void,
    onInputChange: (id: string, value: any, customInputData?: boolean | undefined) => void,
}

type IEurotaxExpertInfoMixedBoxState = ICustomInputsBoxGeneric & {
    vehicleTypes: IEurotaxVehicleTypes;
    vehicleConfiguration: IEurotaxVehicleConfiguration;
    vehicleBrands: IEurotaxVehicleBrands;
    enginePower: IEuroTaxEngineCapacity;
    engineCapacity: IEuroTaxEnginePower;
    fuelType: IEuroTaxFuelType;
    customInputsPrevData: {
        vehicleTypeId: string,
        vehicleBrandId: string,
        productionYear: number,
        engineCapacity: string,
        enginePower: number,
        fuelType: string,
        vehicleModelId: string,
        vehicleConfigurationEurotaxId: string,
        vehicleConfigurationInfoExpertId: string,
        vehicleInfo: string,
    },
    maxErrorsCount: number,
    operationEurotaxSuccess: boolean,
    operationInfoEkspertSuccess: boolean,
    valueBeforeDebounce: any,
};

export class EurotaxExpertInfoMixedBox extends React.Component<IEurotaxExpertInfoMixedBoxProps, IEurotaxExpertInfoMixedBoxState> {
    constructor(props: IEurotaxExpertInfoMixedBoxProps) {
        super(props);
    
        this.state = {
            ...this.state,
            date: new Date(),
            customInputsAsyncActionInProgress: false,
            vehicleTypes: { errorsCount: 0, error: "", gettingDataInProgress: false, types: [] },
            vehicleBrands: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, typeId: [...brands] */ },
            vehicleModels: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, brandId: [...models]" */ },
            vehicleConfiguration: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            engineCapacity: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            enginePower: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            fuelType: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            customInputsPrevData: {
                vehicleTypeId: '',
                vehicleBrandId: '',
                productionYear: 0,
                engineCapacity: '',
                enginePower: 0,
                fuelType: '',
                vehicleModelId: '',
                vehicleConfigurationEurotaxId: '',
                vehicleConfigurationInfoExpertId: '',
                vehicleInfo: '',
            },
            maxErrorsCount: AppConfig.eurotaxCollectDataMaxConnectionErrorsCount,
            operationEurotaxSuccess: false,
            operationInfoEkspertSuccess: false,
            valueBeforeDebounce: {},
        };
    }

    private debouncedOnInputChange: any = _.debounce((inputKey: string, value: any) => {
        if(inputKey === 'productionYear') {
            this.props.onInputChange('productionYear', value.toString());
        }
        this.props.onInputChange(inputKey, value);
    }, AppConsts.defaultInputsDelay, []);

    private async getDataForCustomInputs(requestType: string, vehicleTypeId?: string, vehicleBrandId?: string, productionYear?: string, fuelTypeInput?: string,
                                            engineCapacityInput?: string, vehicleModelId?: string, enginePowerInput?: number, vehicleConfigurationEurotaxId?: string) 
    {
        const { vehicleTypes, vehicleBrands, vehicleModels, vehicleConfiguration, engineCapacity, enginePower, fuelType, customInputsPrevData } = this.state;

        switch(requestType) {
            case "getTypes":
                if(!vehicleTypes['gettingDataInProgress']) {
                    vehicleTypes['gettingDataInProgress'] = true;

                    await vehicleCodeService.getVehicleTypes().then((response: any) => {
                        if(response && response.items) {
                            vehicleTypes["error"] = "";
                            vehicleTypes["types"] = response.items;
                        }
                
                        vehicleTypes['errorsCount'] = 0;
                        vehicleTypes['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, vehicleTypes: vehicleTypes, customInputsAsyncActionInProgress: false, 
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                vehicleBrandId: '',
                                productionYear: 0,
                                engineCapacity: '',
                                enginePower: 0,
                                fuelType: '',
                                vehicleModelId: '',
                                vehicleInfo: '',
                            }}
                        ));
                    }).catch((error: any) => {
                        vehicleTypes["errorsCount"]++;
                        vehicleTypes["error"] = catchErrorMessage(error);
                        vehicleTypes['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, vehicleTypes: vehicleTypes, customInputsAsyncActionInProgress: false, 
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                vehicleBrandId: '',
                                productionYear: 0,
                                engineCapacity: '',
                                enginePower: 0,
                                fuelType: '',
                                vehicleModelId: '',
                                vehicleInfo: '',
                            }}
                        ));
                    });
                }
            break;
            
            case "getBrands":
                if(!vehicleBrands['gettingDataInProgress']) {
                    vehicleBrands['gettingDataInProgress'] = true;
                    await vehicleCodeService.getVehicleBrands(vehicleTypeId!).then((response: any) => {
                        if(response && response.items) {
                            vehicleBrands["error"] = "";
                            vehicleBrands[vehicleTypeId!] = response.items;
                        }
                
                        vehicleBrands['errorsCount'] = 0;
                        vehicleBrands['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, vehicleBrands: vehicleBrands, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleTypeId: vehicleTypeId!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                productionYear: 0,
                                engineCapacity: '',
                                enginePower: 0,
                                fuelType: '',
                                vehicleModelId: '',
                                vehicleInfo: '',
                            }}
                        ));
                    }).catch((error: any) => {
                        vehicleBrands["errorsCount"]++;
                        vehicleBrands["error"] = catchErrorMessage(error);
                        vehicleBrands['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, vehicleBrands: vehicleBrands, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleTypeId: vehicleTypeId!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                productionYear: 0,
                                engineCapacity: '',
                                enginePower: 0,
                                fuelType: '',
                                vehicleModelId: '',
                                vehicleInfo: '',
                            }}
                        ));
                    });
                }
            break;

            case "getFuelType":
                if(!fuelType['gettingDataInProgress']) {
                    fuelType['gettingDataInProgress'] = true;
                    await vehicleCodeService.getVehicleFuelTypes(vehicleTypeId!, vehicleBrandId!, productionYear!).then((response: any) => {
                        if(response && response.items) {
                            fuelType["error"] = "";
                            fuelType[vehicleTypeId!] = {};
                            fuelType[vehicleTypeId!][vehicleBrandId!] = {};
                            fuelType[vehicleTypeId!][vehicleBrandId!][productionYear!] = response.items;
                        }
                
                        fuelType['errorsCount'] = 0;
                        fuelType['gettingDataInProgress'] = false;

                        this.setState((prevState: any) => ({...prevState, fuelType: fuelType, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleBrandId: vehicleBrandId!, 
                                productionYear: parseInt(productionYear!), 
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                engineCapacity: '',
                                enginePower: 0,
                                vehicleModelId: '',
                                vehicleInfo: '',
                            }    
                        }));
                    }).catch((error: any) => {
                        fuelType["errorsCount"]++;
                        fuelType["error"] = catchErrorMessage(error);
                        fuelType['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, fuelType: fuelType, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleBrandId: vehicleBrandId!, 
                                productionYear: parseInt(productionYear!), 
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                engineCapacity: '',
                                enginePower: 0,
                                vehicleModelId: '',
                                vehicleInfo: '',
                            }    
                        }));
                    });
                }
            break;

            case "getEngineCapacity":
                if(!engineCapacity['gettingDataInProgress']) {
                    engineCapacity['gettingDataInProgress'] = true;
                    await vehicleCodeService.getVehicleEngineCapacities(vehicleTypeId!, vehicleBrandId!, productionYear!, fuelTypeInput!).then((response: any) => {
                        if(response && response.items) {
                            engineCapacity["error"] = "";
                            engineCapacity[vehicleTypeId!] = {};
                            engineCapacity[vehicleTypeId!][vehicleBrandId!] = {};
                            engineCapacity[vehicleTypeId!][vehicleBrandId!][productionYear!] = {};
                            engineCapacity[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!] = response.items;
                        }
                        engineCapacity['errorsCount'] = 0;
                        engineCapacity['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, engineCapacity: engineCapacity, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                fuelType: fuelTypeInput!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                enginePower: 0,
                                vehicleModelId: '',
                                vehicleInfo: '',
                            } 
                        }));
                    }).catch((error: any) => {
                        engineCapacity["errorsCount"]++;
                        engineCapacity["error"] = catchErrorMessage(error);
                        engineCapacity['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, engineCapacity: engineCapacity, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                fuelType: fuelTypeInput!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                enginePower: 0,
                                vehicleModelId: '',
                                vehicleInfo: '',
                            } 
                        }));
                    });
                }
            break;

            case "getModels":
                if(!vehicleModels['gettingDataInProgress']) {
                    vehicleModels['gettingDataInProgress'] = true;
                    await vehicleCodeService.getVehicleModels(vehicleTypeId!, vehicleBrandId!, productionYear!, fuelTypeInput!, engineCapacityInput!).then((response: any) => {
                        if(response && response.items) {
                            vehicleModels["error"] = "";
                            vehicleModels[vehicleTypeId!] = {};
                            vehicleModels[vehicleTypeId!][vehicleBrandId!] = {};
                            vehicleModels[vehicleTypeId!][vehicleBrandId!][productionYear!] = {};
                            vehicleModels[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!] = {};
                            vehicleModels[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!] = response.items;
                        }
                
                        vehicleModels['errorsCount'] = 0;
                        vehicleModels['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, vehicleModels: vehicleModels, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                engineCapacity: engineCapacityInput!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                enginePower: 0,
                            }    
                        }));
                    }).catch((error: any) => {
                        vehicleModels["errorsCount"]++;
                        vehicleModels["error"] = catchErrorMessage(error);
                        vehicleModels['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, vehicleModels: vehicleModels, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                engineCapacity: engineCapacityInput!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                                enginePower: 0,
                            }    
                        }));
                    });
                }
            break;

            case "getEnginePower":
                if(!enginePower['gettingDataInProgress']) {
                    enginePower['gettingDataInProgress'] = true;
                    await vehicleCodeService.getVehicleEnginePowers(vehicleTypeId!, vehicleBrandId!, productionYear!, fuelTypeInput!, engineCapacityInput!, vehicleModelId!).then((response: any) => {
                        if(response && response.items) {
                            enginePower["error"] = "";
                            enginePower[vehicleTypeId!] = {};
                            enginePower[vehicleTypeId!][vehicleBrandId!] = {};
                            enginePower[vehicleTypeId!][vehicleBrandId!][productionYear!] = {};
                            enginePower[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!] = {};
                            enginePower[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!] = {};
                            enginePower[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!][vehicleModelId!] = response.items;
                        }
                
                        enginePower['errorsCount'] = 0;
                        enginePower['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, enginePower: enginePower, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleModelId: vehicleModelId!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                            } 
                        }));
                    }).catch((error: any) => {
                        enginePower["errorsCount"]++;
                        enginePower["error"] = catchErrorMessage(error);
                        enginePower['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, enginePower: enginePower, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleModelId: vehicleModelId!,
                                vehicleConfigurationEurotaxId: '',
                                vehicleConfigurationInfoExpertId: '',
                            } 
                        }));
                    });
                }
            break;

            case "getConfiguration":
                if(!vehicleConfiguration['gettingDataInProgress']) {
                    vehicleConfiguration['gettingDataInProgress'] = true;
                    await vehicleCodeService.getVehicleCodes(vehicleTypeId!, vehicleBrandId!, productionYear!, fuelTypeInput!, engineCapacityInput!, vehicleModelId!, enginePowerInput!).then((response: any) => {
                        if(response) {
                            vehicleConfiguration["error"] = "";
                            vehicleConfiguration[vehicleTypeId!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!][vehicleModelId!] = {};
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!][vehicleModelId!][enginePowerInput!] = response;
                        }
                        
                        vehicleConfiguration['errorsCount'] = 0;
                        vehicleConfiguration['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, vehicleConfiguration: vehicleConfiguration, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                enginePowerInput: enginePowerInput!,
                            }
                        }));
                    }).catch((error: any) => {
                        vehicleConfiguration["errorsCount"]++;
                        vehicleConfiguration["error"] = catchErrorMessage(error);
                        vehicleConfiguration['gettingDataInProgress'] = false;
                        this.setState((prevState: any) => ({...prevState, vehicleConfiguration: vehicleConfiguration, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                enginePowerInput: enginePowerInput!,
                            }
                        }));
                    });
                }
            break;

            case "getInfoEkspertCodes":
                if(!vehicleConfiguration['gettingDataInProgress']) {
                    vehicleConfiguration['gettingDataInProgress'] = true;
                    await vehicleCodeService.getInfoEkspertCode(parseInt(vehicleConfigurationEurotaxId!), parseInt(productionYear!)).then(async (response: any) => {
                        if(response) {
                            vehicleConfiguration["error"] = "";
                            vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!][vehicleModelId!][enginePowerInput!] = {
                                ...vehicleConfiguration[vehicleTypeId!][vehicleBrandId!][productionYear!][fuelTypeInput!][engineCapacityInput!][vehicleModelId!][enginePowerInput!],
                                'infoExperts': [response]
                            };
                        }
                        vehicleConfiguration['errorsCount'] = 0;
                        vehicleConfiguration['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleConfiguration: vehicleConfiguration, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleConfigurationEurotaxId: vehicleConfigurationEurotaxId!,
                            }
                        }));
                    }).catch((error: any) => {
                        vehicleConfiguration["errorsCount"]++;
                        vehicleConfiguration["error"] = catchErrorMessage(error);
                        vehicleConfiguration['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleConfiguration: vehicleConfiguration, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleConfigurationEurotaxId: vehicleConfigurationEurotaxId!,
                            }
                        }));
                    });
                }
            break;
        }
    }

    private toggleCustomInputsAsyncActionInProgressFlag(newState: boolean) {
        if(this.state.customInputsAsyncActionInProgress !== newState) {
            this.setState((prevState: any) => ({...prevState, customInputsAsyncActionInProgress: newState }));
        }
    }

    private checkInputsAndResetIfUndefined(inputs: any[]) {
        let copyCustomInputsPrevData: any = {...this.state.customInputsPrevData};

        inputs.forEach((input: any) => {
            if(typeof input.value === 'undefined') {
                copyCustomInputsPrevData[input.key] = input.type === 'number' ? 0 : '';
            } else {
                copyCustomInputsPrevData[input.key] = input.value;
            }
        });

        if(JSON.stringify(this.state.customInputsPrevData) !== JSON.stringify(copyCustomInputsPrevData)) {
            this.setState((prevState: any) => ({...prevState, customInputsPrevData: copyCustomInputsPrevData }));
        }
    }

    render() {
        const { operationEurotaxSuccess, operationInfoEkspertSuccess, maxErrorsCount, customInputsAsyncActionInProgress, vehicleTypes, vehicleBrands, fuelType, 
                vehicleModels, vehicleConfiguration, customInputsPrevData, engineCapacity, enginePower } = this.state;

        let typeOptions: IComboBoxOption[] = [];
        let brandOptions: IComboBoxOption[] = [];
        let modelOptions: IComboBoxOption[] = [];
        let engineCapacityOptions: IComboBoxOption[] = [];
        let fuelTypeOptions: IComboBoxOption[] = [];
        let enginePowerOptions: IComboBoxOption[] = [];
        let vehicleBrandSelected: boolean = false;
        let configurationOptions: any = {
            eurotax: [] as IComboBoxOption[],
            infoekspert: [] as IComboBoxOption[],
        };
        let errors: any = {
            typeError: '',
            brandError: '',
            fuelTypeError: '',
            yearError: '',
            modelError: '',
            engineCapacityError: '',
            enginePowerError: '',
            configurationError: '',
            eurotaxInfoekspertError: '',
            eurotaxError: '',
            infoekspertError: '',
        };

        const vehicleTypeId = this.props.customInputsData.vehicleTypeId;
        const vehicleBrandId = this.props.customInputsData.vehicleBrandId;
        const productionYear = this.props.customInputsData.productionYear;
        const fuelTypeInput = this.props.customInputsData.fuelType;
        const vehicleModelId = this.props.customInputsData.vehicleModelId;
        const engineCapacityInput = this.props.customInputsData.engineCapacity;
        const enginePowerInput = this.props.customInputsData.enginePower;
        const vehicleConfigurationEurotaxId = this.props.customInputsData.vehicleConfigurationEurotaxId;
        const vehicleConfigurationInfoExpertId = this.props.customInputsData.vehicleConfigurationInfoExpertId;
        const vehicleInfo = this.props.customInputsData.vehicleInfo;

        this.checkInputsAndResetIfUndefined([
            {key: "vehicleTypeId", value: vehicleTypeId, type: 'string'},
            {key: "vehicleBrandId", value: vehicleBrandId, type: 'string'},
            {key: "productionYear", value: productionYear, type: 'number'},
            {key: "fuelType", value: fuelTypeInput, type: 'string'},
            {key: "vehicleModelId", value: vehicleModelId, type: 'string'},
            {key: "engineCapacity", value: engineCapacityInput, type: 'string'},
            {key: "enginePower", value: enginePowerInput, type: 'number'},
            {key: "vehicleConfigurationEurotaxId", value: vehicleConfigurationEurotaxId, type: 'string'},
            {key: "vehicleConfigurationInfoExpertId", value: vehicleConfigurationInfoExpertId, type: 'string'},
            {key: "vehicleInfo", value: vehicleInfo, type: 'string'},
        ]);

        if(vehicleTypes["types"]!.length > 0 && vehicleTypes['error'].length === 0 && vehicleTypes['errorsCount'] < maxErrorsCount) {
            vehicleTypes["types"]!.forEach((type: any) => {
                typeOptions.push({ key: type.key, text: type.translatedName, selected: (vehicleTypeId === type.key ? true : false) });
            });
        } else {
            if(vehicleTypes['errorsCount'] < maxErrorsCount && vehicleTypes['error'].length === 0) {
                this.toggleCustomInputsAsyncActionInProgressFlag(true);
                this.getDataForCustomInputs("getTypes");
            } else {
                errors.typeError = L(vehicleTypes['error']);
            }
        }

        let stopChecking: boolean = false;

        // vehicle types
        if(vehicleTypeId && vehicleTypeId.length > 0) {
            if(vehicleBrands[vehicleTypeId] && vehicleBrands[vehicleTypeId].length > 0 && vehicleBrands['error'].length === 0 && vehicleBrands['errorsCount'] < maxErrorsCount) {
                vehicleBrands[vehicleTypeId].forEach((brand: any, index: number) => {
                    brandOptions.push({ key: brand.key, text: brand.translatedName });
                });
            } else {
                if((vehicleTypeId && vehicleTypeId.length > 0 && customInputsPrevData['vehicleTypeId'] !== vehicleTypeId) && vehicleBrands['errorsCount'] < maxErrorsCount && vehicleTypes['error'].length === 0) {
                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                    this.getDataForCustomInputs("getBrands", vehicleTypeId);
                } else {
                    errors.brandError = L(vehicleBrands['error']);
                }
            }
        } else {
            errors.brandError = L('Please select vehicle type.');
            stopChecking = true;
        }

        // vehicle brands
        if(vehicleBrandId && vehicleBrandId.length > 0 && !stopChecking) {
            vehicleBrandSelected = true;
        } else if(!stopChecking) {
            errors.yearError = L('Please select vehicle brand.');
            stopChecking = true;
        }
        
        // production year & fuel type
        if(productionYear && !stopChecking) {
            if(fuelType[vehicleTypeId] && fuelType[vehicleTypeId][vehicleBrandId] && fuelType[vehicleTypeId][vehicleBrandId][productionYear] && fuelType[vehicleTypeId][vehicleBrandId][productionYear].length > 0 && fuelType['error'].length === 0 && fuelType['errorsCount'] < maxErrorsCount) {
                fuelType[vehicleTypeId][vehicleBrandId][productionYear].forEach((fuelType: any, index: number) => {
                    fuelTypeOptions.push({ key: fuelType.key, text: fuelType.translatedName });
                });
            } else {
                let hasYear: boolean = false;
                if(fuelType && fuelType[vehicleTypeId] && fuelType[vehicleTypeId][vehicleBrandId] && fuelType[vehicleTypeId][vehicleBrandId].hasOwnProperty(productionYear)) {
                    hasYear = true;
                }

                if((productionYear && productionYear > 0 && (customInputsPrevData['productionYear'] !== productionYear || hasYear === false)) && fuelType['errorsCount'] < maxErrorsCount && fuelType['error'].length === 0) {
                    if(!(productionYear > 1922 && productionYear <= new Date().getFullYear())) {
                        errors.yearError = L("Vehicle production year must be between 1922 and current year.")
                    } else {
                        this.toggleCustomInputsAsyncActionInProgressFlag(true);
                        this.getDataForCustomInputs("getFuelType", vehicleTypeId, vehicleBrandId, productionYear);
                    }
                } else if(productionYear === 0 || errors.yearError.length > 0) {
                    errors.fuelTypeError = L('Please select year of production.');
                }
            }
        } else if(!stopChecking) {
            errors.yearError = L('Please select year of production.');
            stopChecking = true;
        }

        // engine capacity
        if(fuelTypeInput && fuelTypeInput.length > 0 && !stopChecking) {
            if(engineCapacity[vehicleTypeId] && engineCapacity[vehicleTypeId][vehicleBrandId] && engineCapacity[vehicleTypeId][vehicleBrandId][productionYear] && engineCapacity[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && engineCapacity[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput].length > 0 && engineCapacity['error'].length === 0 && engineCapacity['errorsCount'] < maxErrorsCount) {
                engineCapacity[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput].forEach((engineCapacity: any, index: number) => {
                    if(engineCapacity === null) {
                        allowVehicleConfigWithoutAllData = true;
                    } else {
                        engineCapacityOptions.push({key: engineCapacity, text: engineCapacity});
                    }
                })
            } else {
                if((fuelTypeInput && fuelTypeInput.length > 0 && customInputsPrevData['fuelType'] !== fuelTypeInput) && vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0) {
                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                    this.getDataForCustomInputs("getEngineCapacity", vehicleTypeId, vehicleBrandId, productionYear, fuelTypeInput);
                } else {
                    errors.eurotaxInfoekspertError = L(vehicleConfiguration['error']);
                }
            }
        } else if(!stopChecking) {
            errors.engineCapacityError = L('Please select vehicle fuel type.');
            stopChecking = true;
        }

        // vehicle model
        if(engineCapacityInput && engineCapacityInput > 0 && !stopChecking) {
            if(vehicleModels[vehicleTypeId] && vehicleModels[vehicleTypeId][vehicleBrandId] && vehicleModels[vehicleTypeId][vehicleBrandId][productionYear] && vehicleModels[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && vehicleModels[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && vehicleModels[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput].length > 0 && vehicleModels['error'].length === 0 && vehicleModels['errorsCount'] < maxErrorsCount) {
                vehicleModels[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput].forEach((model: any, index: number) => {
                    modelOptions.push({key: model.key, text: model.translatedName});
                });
            } else {
                if((engineCapacityInput && engineCapacityInput > 0 && customInputsPrevData['engineCapacity'] !== engineCapacityInput) && vehicleModels['errorsCount'] < maxErrorsCount && vehicleModels['error'].length === 0) {
                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                    this.getDataForCustomInputs("getModels", vehicleTypeId, vehicleBrandId, productionYear, fuelTypeInput, engineCapacityInput);
                } else {
                    errors.modelError = L("Please select engine capacity.");
                }
            }
        } else if(!stopChecking) {
            errors.modelError = L("Please select engine capacity.");
            stopChecking = true;
        }

        let allowVehicleConfigWithoutAllData: boolean = false;

        // engine power
        if(vehicleModelId && vehicleModelId.length > 0 && !stopChecking) {
            if(enginePower[vehicleTypeId] && enginePower[vehicleTypeId][vehicleBrandId] && enginePower[vehicleTypeId][vehicleBrandId][productionYear] && enginePower[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && enginePower[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && enginePower[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId] && enginePower[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId].length > 0 && enginePower['error'].length === 0 && enginePower['errorsCount'] < maxErrorsCount) {
                enginePower[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId].forEach((enginePower: any, index: number) => {
                    if(enginePower === null) {
                        allowVehicleConfigWithoutAllData = true;
                    } else {
                        enginePowerOptions.push({key: enginePower, text: enginePower});
                    }
                });
            } else {
                if((vehicleModelId && vehicleModelId.length > 0 && customInputsPrevData['vehicleModelId'] !== vehicleModelId) && vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0) {
                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                    this.getDataForCustomInputs("getEnginePower", vehicleTypeId, vehicleBrandId, productionYear, fuelTypeInput, engineCapacityInput, vehicleModelId);
                } else {
                    errors.eurotaxInfoekspertError = L(vehicleConfiguration['error']);
                }
            }
        } else if(!stopChecking) {
            errors.enginePowerError = L('Please select vehicle model.');
            stopChecking = true;
        }

        if(!operationInfoEkspertSuccess) {
            // vehicle configuration
            if(((enginePowerInput && enginePowerInput > 0) || allowVehicleConfigWithoutAllData) && !stopChecking) {
                if(((vehicleConfiguration[vehicleTypeId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]) ||
                    (vehicleConfiguration[vehicleTypeId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && allowVehicleConfigWithoutAllData))
                    && vehicleConfiguration['error'].length === 0 && vehicleConfiguration['errorsCount'] < maxErrorsCount
                ) {
                    if(vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['euroTaxs'] &&
                        Array.isArray(vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['euroTaxs'])    
                    ) {
                        vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['euroTaxs'].forEach((configuration: any, index: number) => {
                            configurationOptions.eurotax.push({ key: configuration.id, text: configuration.name });
                        });
                    }
    
                    // if(vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts'] &&
                    //     Array.isArray(vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts'])    
                    // ) {
                    //     vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts'].forEach((configuration: any, index: number) => {
                    //         configurationOptions.infoekspert.push({ key: configuration.id, text: configuration.name });
                    //     });
                    // }
    
                    if(vehicleConfigurationEurotaxId && vehicleConfigurationEurotaxId.length > 0) {
                        if(customInputsPrevData['vehicleConfigurationEurotaxId'] !== vehicleConfigurationEurotaxId) {
                            this.setState((prevState) => ({...prevState, operationEurotaxSuccess: true, 
                                customInputsPrevData: {...customInputsPrevData,
                                    vehicleConfigurationEurotaxId: vehicleConfigurationEurotaxId,
                                }
                            }));
    
                            this.props.onInputChange("eurotaxCarId", vehicleConfigurationEurotaxId.toString());
                            this.props.onInputChange("eurotax", vehicleConfigurationEurotaxId.toString());
                        }
                    }
    
                    // if(vehicleConfigurationEurotaxId && vehicleConfigurationEurotaxId.length > 0 && customInputsPrevData['vehicleConfigurationEurotaxId'] !== vehicleConfigurationEurotaxId) {
                    //     this.setState((prevState) => ({...prevState, operationEurotaxSuccess: true, 
                    //         customInputsPrevData: {...customInputsPrevData,
                    //             vehicleConfigurationEurotaxId: vehicleConfigurationEurotaxId,
                    //         }
                    //     }));
    
                    //     this.props.onInputChange("eurotaxCarId", vehicleConfigurationEurotaxId.toString());
                    //     this.props.onInputChange("eurotax", vehicleConfigurationEurotaxId.toString());
                    // }
    
                    // if(vehicleConfigurationInfoExpertId && vehicleConfigurationInfoExpertId.length > 0 && customInputsPrevData['vehicleConfigurationInfoExpertId'] !== vehicleConfigurationInfoExpertId) {
                    //     this.setState((prevState) => ({...prevState, operationInfoEkspertSuccess: true,
                    //         customInputsPrevData: {...customInputsPrevData,
                    //             vehicleConfigurationInfoExpertId: vehicleConfigurationInfoExpertId,
                    //         }
                    //     }));
                    //     this.props.onInputChange("infoExpertId", vehicleConfigurationInfoExpertId.toString());
                    // }
                } else {
                    if(((enginePowerInput && enginePowerInput > 0 && customInputsPrevData['enginePower'] !== enginePowerInput) || allowVehicleConfigWithoutAllData) && vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0) {
                        this.toggleCustomInputsAsyncActionInProgressFlag(true);
                        this.getDataForCustomInputs("getConfiguration", vehicleTypeId, vehicleBrandId, productionYear, fuelTypeInput, engineCapacityInput, vehicleModelId, enginePowerInput);
                    } else {
                        errors.eurotaxInfoekspertError = L(vehicleConfiguration['error']);
                    }
                }
            } else if(!stopChecking && configurationOptions.eurotax.length === 0) {
                errors.eurotaxError = L("Please select engine power.");
                stopChecking = true;
            }
    
            // vehicle configuration - infoEkspert codes
            if(((vehicleConfigurationEurotaxId && vehicleConfigurationEurotaxId.length > 0) || allowVehicleConfigWithoutAllData) && !stopChecking) {
                if(((vehicleConfiguration[vehicleTypeId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts']) ||
                    (vehicleConfiguration[vehicleTypeId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput] && vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts'] && allowVehicleConfigWithoutAllData))
                    && vehicleConfiguration['error'].length === 0 && vehicleConfiguration['errorsCount'] < maxErrorsCount
                ) {
                    if(configurationOptions.infoekspert.length === 0) {
                        vehicleConfiguration[vehicleTypeId][vehicleBrandId][productionYear][fuelTypeInput][engineCapacityInput][vehicleModelId][enginePowerInput]['infoExperts'].forEach((configuration: any, index: number) => {
                            configurationOptions.infoekspert.push({ key: configuration.id, text: configuration.name });
                        });
                    }
    
                    if(vehicleConfigurationInfoExpertId && vehicleConfigurationInfoExpertId.length > 0) {
                        if(customInputsPrevData['vehicleConfigurationInfoExpertId'] !== vehicleConfigurationInfoExpertId) {
                            this.setState((prevState) => ({...prevState, operationInfoEkspertSuccess: true,
                                customInputsPrevData: {...customInputsPrevData,
                                    vehicleConfigurationInfoExpertId: vehicleConfigurationInfoExpertId
                                }
                            }));
                            this.props.onInputChange("vehicleConfigurationInfoExpertId", vehicleConfigurationInfoExpertId.toString());
                            this.props.onInputChange("infoExpertId", vehicleConfigurationInfoExpertId.toString());
                        }
                    } else if(configurationOptions.infoekspert.length === 1 && customInputsPrevData['vehicleConfigurationInfoExpertId'] !== configurationOptions.infoekspert[0].key) {
                        this.setState((prevState) => ({...prevState, operationInfoEkspertSuccess: true,
                            customInputsPrevData: {...customInputsPrevData,
                                vehicleConfigurationInfoExpertId: configurationOptions.infoekspert[0].key
                            }
                        }));
                        this.props.onInputChange("vehicleConfigurationInfoExpertId", configurationOptions.infoekspert[0].key.toString());
                        this.props.onInputChange("infoExpertId", configurationOptions.infoekspert[0].key.toString());
                    }
                } else {
                    if(((productionYear && productionYear > 0) || allowVehicleConfigWithoutAllData) && vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0) {
                        this.toggleCustomInputsAsyncActionInProgressFlag(true);
                        this.getDataForCustomInputs("getInfoEkspertCodes", vehicleTypeId, vehicleBrandId, productionYear, fuelTypeInput, engineCapacityInput, vehicleModelId, enginePowerInput, vehicleConfigurationEurotaxId);
                    } else {
                        errors.eurotaxInfoekspertError = L(vehicleConfiguration['error']);
                    }
                }
            } else if(!stopChecking) {
                if(!vehicleConfigurationEurotaxId || vehicleConfigurationEurotaxId.length === 0) {
                    errors.infoekspertError = L("Please select eurotax code.");
                }
                stopChecking = true;
            }
        }
        
        return <div className={`${classNames.customInputsWrapper}`}>
            <MessageBar messageBarType={MessageBarType.info} isMultiline={false} className={classNames.messageBar}>
                {L('Enter vehicle details to find Eurotax ID and Expert Info ID.')}
            </MessageBar>

            <Icon iconName={this.props.collapseForm ? "Down" : "Up"} title={L(this.props.collapseForm ? 'Show' : 'Hide')} style={{ position: 'absolute', right: '10px', top: '25px', fontSize: '20px', cursor: 'pointer' }}
                onClick={() => {
                    this.props.onFormCollapse();
                }
            } />

            { !this.props.collapseForm &&
                <>
                    { customInputsAsyncActionInProgress && <Spinner label={L('Collecting data...')} className={classNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" /> }
                    
                    <ComboBox
                        label={L("Select vehicle type")}
                        allowFreeform={false}
                        autoComplete={'off'}
                        options={typeOptions}
                        className={classNames.comboBoxStyles}
                        key={`selectType`}
                        errorMessage={errors.typeError}
                        disabled={typeOptions.length <= 0 || customInputsAsyncActionInProgress}
                        selectedKey={!!vehicleTypeId ? vehicleTypeId : null}
                        onChange={(e: any, value: any) => this.props.onInputChange("vehicleType", value.key)}
                    />

                    <ComboBox
                        label={L("Select vehicle brand")}
                        allowFreeform={false}
                        autoComplete={'on'}
                        options={brandOptions}
                        className={classNames.comboBoxStyles}
                        key={`selectBrand-${vehicleTypeId}`}
                        errorMessage={errors.brandError}
                        disabled={brandOptions.length <= 0 || customInputsAsyncActionInProgress}
                        selectedKey={!!vehicleBrandId ? vehicleBrandId : null}
                        onChange={(e: any, value: any) => this.props.onInputChange('vehicleBrandId', value.key, true)}
                    />

                    <TextField
                        key={`productionYear`}
                        type="number"
                        label={L("Enter year of production")}
                        errorMessage={errors.yearError}
                        className={classNames.comboBoxStyles}
                        disabled={!vehicleBrandSelected || customInputsAsyncActionInProgress}
                        value={!!this.state.valueBeforeDebounce['productionYear'] ? this.state.valueBeforeDebounce['productionYear'] : productionYear}
                        onChange={(e: any, value: any) => {
                            this.setState((prevState) => ({...prevState, valueBeforeDebounce: {...this.state.valueBeforeDebounce, 'productionYear': value } }));
                            this.debouncedOnInputChange('productionYear', parseInt(value))
                        }}
                    />

                    <ComboBox
                        key={`selectFuelType-${vehicleTypeId}-${vehicleBrandId}`}
                        label={L("Select fuel type")}
                        allowFreeform={false}
                        autoComplete={'on'}
                        options={fuelTypeOptions}
                        className={classNames.comboBoxStyles}
                        errorMessage={errors.fuelTypeError}
                        disabled={fuelTypeOptions.length <= 0 || customInputsAsyncActionInProgress}
                        selectedKey={!!fuelTypeInput ? fuelTypeInput : null}
                        onChange={(e: any, value: any) => this.props.onInputChange('fuelType', value.key)}
                    />
                    
                    <ComboBox
                        label={L("Select engine capacity")}
                        key={`selectEngineCapacity-${vehicleTypeId}-${vehicleBrandId}-${fuelType}-${vehicleModelId}`}
                        allowFreeform={false}
                        autoComplete={'on'}
                        options={engineCapacityOptions}
                        className={classNames.comboBoxStyles}
                        errorMessage={errors.engineCapacityError}
                        disabled={engineCapacityOptions.length <=0 || customInputsAsyncActionInProgress}
                        selectedKey={!!engineCapacityInput ? engineCapacityInput : null}
                        onChange={(e: any, value: any) => this.props.onInputChange('engineCapacity', value.key, true)}
                    />

                    <ComboBox
                        label={L("Select vehicle model")}
                        allowFreeform={false}
                        autoComplete={'on'}
                        options={modelOptions}
                        className={classNames.comboBoxStyles}
                        key={`selectModel-${vehicleTypeId}-${vehicleBrandId}-${fuelType}`}
                        errorMessage={errors.modelError}
                        disabled={modelOptions.length <= 0 || customInputsAsyncActionInProgress}
                        selectedKey={!!vehicleModelId ? vehicleModelId : null}
                        onChange={(e: any, value: any) => {
                            this.props.onInputChange('vehicleModelId', value.key, true);
                            this.props.onInputChange('vehicleInfo', `${this.props.customInputsData.vehicleBrandId}, ${value.key}`, true);
                        }}
                    />

                    <ComboBox
                        label={L("Select engine power (kW)")}
                        key={`selectEnginePower-${vehicleTypeId}-${vehicleBrandId}-${fuelType}-${vehicleModelId}-${engineCapacity}`}
                        allowFreeform={false}
                        autoComplete={'on'}
                        options={enginePowerOptions}
                        className={classNames.comboBoxStyles}
                        errorMessage={errors.enginePowerError}
                        disabled={enginePowerOptions.length <= 0 || customInputsAsyncActionInProgress}
                        selectedKey={!!enginePowerInput ? enginePowerInput : null}
                        onChange={(e: any, value: any) => this.props.onInputChange('enginePower', value.key, true)}
                    />

                    <ComboBox
                        label={L("Select vehicle configuration (Eurotax)")}
                        allowFreeform={false}
                        autoComplete={'on'}
                        options={configurationOptions.eurotax}
                        className={classNames.comboBoxStyles}
                        key={`selectEurotaxConfiguration-${vehicleTypeId}-${vehicleBrandId}-${fuelType}-${vehicleModelId}-${engineCapacity}-${enginePower}`}
                        errorMessage={errors.eurotaxError}
                        disabled={configurationOptions.eurotax.length <= 0 || customInputsAsyncActionInProgress}
                        selectedKey={!!vehicleConfigurationEurotaxId ? vehicleConfigurationEurotaxId : null}
                        onChange={(e: any, value: any) => {
                            this.props.onInputChange('eurotaxCarId', value.key);
                            this.props.onInputChange('vehicleConfigurationEurotaxId', value.key, true);
                        }}
                    />

                    <ComboBox
                        label={L("Vehicle configuration (InfoEkspert)")}
                        allowFreeform={false}
                        autoComplete={'on'}
                        options={configurationOptions.infoekspert}
                        className={classNames.comboBoxStyles}
                        key={`selectInfoEkspertConfiguration-${vehicleTypeId}-${vehicleBrandId}-${fuelType}-${vehicleModelId}-${engineCapacity}-${enginePower}`}
                        errorMessage={errors.infoekspertError}
                        disabled={true}
                        selectedKey={!!vehicleConfigurationInfoExpertId ? vehicleConfigurationInfoExpertId : null}
                        onChange={(e: any, value: any) => {
                            this.props.onInputChange('infoExpertId', value.key);
                            this.props.onInputChange('vehicleConfigurationInfoExpertId', value.key, true);
                        }}
                    />
                </>
            }

            { operationEurotaxSuccess && 
                <MessageBar messageBarType={MessageBarType.success} isMultiline={false} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}
                    onDismiss={() => this.setState((prevState) => ({...prevState, operationEurotaxSuccess: false })) } >
                    { L('Success! Eurotax ID is now set.') }
                </MessageBar> }
            { operationInfoEkspertSuccess && 
                <MessageBar messageBarType={MessageBarType.success} isMultiline={false} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}
                    onDismiss={() => this.setState((prevState) => ({...prevState, operationInfoEkspertSuccess: false })) } >
                    { L("Success! Expert's information ID is now set.") }
                </MessageBar> }
                
            { errors.eurotaxInfoekspertError.length > 0 && 
                <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}>
                    {errors.eurotaxInfoekspertError}
                </MessageBar> }

            { customInputsAsyncActionInProgress && <Spinner label={L('Collecting data...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" /> }
        </div>;
    }
}