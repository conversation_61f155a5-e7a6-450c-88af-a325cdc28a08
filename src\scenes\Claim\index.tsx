import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { ClaimDto } from '../../services/claim/dto/claimDto';
import { ClaimContentView } from './components/claimContentView';
import { mergeStyleSets, FocusZone, MessageBar, MessageBarType, PrimaryButton, Link } from '@fluentui/react';
import ClaimStore from '../../stores/claimStore';
import claimService from '../../services/claim/claimService';
import { PolicyType } from '../../services/policy/policyTypeEnums';
import { ClaimChildrenDto } from '../../services/claim/dto/claimChildrenDto';
import { ClaimHomeDto } from '../../services/claim/dto/claimHomeDto';
import { ClaimLifeDto } from '../../services/claim/dto/claimLifeDto';
import { ClaimTravelDto } from '../../services/claim/dto/claimTravelDto';
import { ClaimVehicleDto } from '../../services/claim/dto/claimVehicleDto';
import { catchErrorMessage } from '../../utils/utils';
import { L } from '../../lib/abpUtility';
import { RouterPath } from '../../components/Router/router.config';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
	messageBar: {
        width: 'fit-content',
        marginTop: '40px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
});

export interface IProps {
	claimStore: ClaimStore;
	match: any
}

@inject(Stores.ClaimStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private claimId: string = this.props.match.params.id;
	private policyType: string = this.props.match.params.policyType;
	private entityError: string = '';

	private response: any = {};

	async componentDidMount() {
		this.entityError = "";
		
		switch(PolicyType[this.policyType]) {
			case PolicyType.Children:
				await claimService.getChildren({ id: this.claimId } as ClaimChildrenDto).then((response: any) => {
					if(response && response.id) {
						this.response = response;
					} else {
						this.entityError = L('Something went wrong. Try again later or contact with administrator.');
					}
				}).catch((error: any) => {
					console.error(error);
					this.entityError = catchErrorMessage(error);
				});
				break;
			case PolicyType.Home:
				await claimService.getHome({ id: this.claimId } as ClaimHomeDto).then((response: any) => {
					if(response && response.id) {
						this.response = response;
					} else {
						this.entityError = L('Something went wrong. Try again later or contact with administrator.');
					}
				}).catch((error: any) => {
					console.error(error);
					this.entityError = catchErrorMessage(error);
				});
			break;
			case PolicyType.Life:
				await claimService.getLife({ id: this.claimId } as ClaimLifeDto).then((response: any) => {
					if(response && response.id) {
						this.response = response;
					} else {
						this.entityError = L('Something went wrong. Try again later or contact with administrator.');
					}
				}).catch((error: any) => {
					console.error(error);
					this.entityError = catchErrorMessage(error);
				});
			break;
			case PolicyType.Travel:
				await claimService.getTravel({ id: this.claimId } as ClaimTravelDto).then((response: any) => {
					if(response && response.id) {
						this.response = response;
					} else {
						this.entityError = L('Something went wrong. Try again later or contact with administrator.');
					}
				}).catch((error: any) => {
					console.error(error);
					this.entityError = catchErrorMessage(error);
				});
			break;
			case PolicyType.Vehicle:
				await claimService.getVehicle({ id: this.claimId } as ClaimVehicleDto).then((response: any) => {
					if(response && response.id) {
						this.response = response;
					} else {
						this.entityError = L('Something went wrong. Try again later or contact with administrator.');
					}
				}).catch((error: any) => {
					console.error(error);
					this.entityError = catchErrorMessage(error);
				});
			break;
			default:
				await this.props.claimStore.get({ id: this.claimId } as ClaimDto).then((response: any) => {
					if(response && response.id) {
						this.response = response;
					} else {
						this.entityError = L('Something went wrong. Try again later or contact with administrator.');
					}
				}).catch((error: any) => {
					console.error(error);
					this.entityError = catchErrorMessage(error);
				});
		}
		this.props.claimStore.model = this.response;
		this.forceUpdate();
	}

	public render() {
		return (!!this.entityError ? 
			<>
				<MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`}>
					{this.entityError}
				</MessageBar>

				<Link href={`/${RouterPath.ClaimList}`}>
					<PrimaryButton text={L('Back')} style={{marginTop: 30}} />
				</Link>
			</>
            : 
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<ClaimContentView store={this.props.claimStore} payload={ this.props.claimStore.model as ClaimDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;