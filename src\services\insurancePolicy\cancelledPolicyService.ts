import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { CrudServiceBase } from '../base/crudServiceBase';
import { CancelledPolicyDto } from './cancelledPolicyDto';

export class CancelledPolicyService extends CrudServiceBase<CancelledPolicyDto> {
    constructor() {
        super(Endpoint.CancelledPolicy);
        this.internalHttp = httpApi;
    }
}

const exportCancelledPolicyService: CancelledPolicyService = new CancelledPolicyService();
export default exportCancelledPolicyService;