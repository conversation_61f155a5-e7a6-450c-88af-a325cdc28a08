import * as React from 'react';
import { ComboBox, IComboBox, IComboBoxOption, ITextFieldProps, mergeStyleSets } from '@fluentui/react';
import { inject } from 'mobx-react';
import Stores from '../../stores/storeIdentifier';
import CountryStore from '../../stores/countryStore';
import { getSourceDataFromUserFields } from '../../utils/storeUtils';
import LanguageStore from '../../stores/languageStore';
import { getGrandNodeLanguage } from '../../utils/languageUtils';
import { LabelContainerComponent } from './labelContainerComponent';
import { LabelComponent } from './labelComponent';
import { filterBySome } from '../../utils/utils';

const classNames = mergeStyleSets({
    comboBoxStyles: {
        width: '300px',
    },
    requiredMarker: {
        color: 'rgb(164, 38, 44)',
        marginLeft: '5px',
    }
});

export interface INationalityComboBoxProps extends ITextFieldProps {
    key: string;
    label: string;
    inputText?: string;
    value?: string;
    countryStore?: CountryStore;
    languageStore?: LanguageStore;
    errorMessage?: string;
    disabled?: boolean;
    required?: boolean;
    allowFreeform?: boolean;
    asyncActionInProgress?: boolean;
    onInputChange: (id: string | number | undefined, value: any) => void,
}

type INationalityComboBoxState = { 
    nationalityOptions: IComboBoxOption[],
    errorMessage: string,
    gnLanguage: any,
    removePresettedValue: boolean,
};

@inject(Stores.LanguageStore)
@inject(Stores.CountryStore)
export class NationalityComboBox extends React.Component<INationalityComboBoxProps, INationalityComboBoxState> {
    constructor(props: INationalityComboBoxProps) {
        super(props);
        
        this.state = {
            ...this.state,
            nationalityOptions: [] as IComboBoxOption[],
            errorMessage: '',
            gnLanguage: undefined,
            removePresettedValue: false,
        };
    }

    async componentDidMount() {
        if(!this.props.languageStore!.dataSet || this.props.languageStore!.dataSet.totalCount <= 0) {
            await this.props.languageStore?.getAll(this.props.languageStore?.defaultRequest);
        }
        let gnLanguage = getGrandNodeLanguage(this.props.languageStore?.dataSet);
        let allNationalityOptions: IComboBoxOption[] = [];

        if(!this.props.countryStore!.dataSet || this.props.countryStore!.dataSet.totalCount <= 0) {
			await this.props.countryStore!.getAll(this.props.countryStore!.defaultRequest);
		}
        allNationalityOptions = getSourceDataFromUserFields(this.props.countryStore, "TwoLetterIsoCode", gnLanguage, this.props.value, 'comboBox', 'TwoLetterIsoCode', false, true);

        this.setState({ nationalityOptions: allNationalityOptions, gnLanguage: gnLanguage });
    }

    private getSelectedOptionText(nationalityOptions: IComboBoxOption[]): string {
        if(this.state.removePresettedValue === true) {
            return "";
        }

        let foundText: string = "";
        
        let filteredCountryOption: any = filterBySome(nationalityOptions, 'selected', true);
        if(!!filteredCountryOption && !!filteredCountryOption.key) {
            foundText = filteredCountryOption.text;
        }
    
        return foundText;
    } 

    render() {
        const { key, label, errorMessage, disabled, asyncActionInProgress, required, allowFreeform, inputText } = this.props;
        const { nationalityOptions, removePresettedValue } = this.state;

        const presettedInputText: string = this.getSelectedOptionText(nationalityOptions);

        return <LabelContainerComponent>
                    <LabelComponent label={label || ''} required={required} />

                    <ComboBox
                        label={''}
                        text={!!inputText ? inputText : (!!presettedInputText ? presettedInputText : undefined)}
                        required={required}
                        allowFreeform={!!allowFreeform ? allowFreeform : false}
                        autoComplete={'on'}
                        options={nationalityOptions}
                        className={classNames.comboBoxStyles}
                        styles={{optionsContainer: {minWidth: 50}}}
                        key={`${key}NationalityComboBox`}
                        errorMessage={!!errorMessage ? errorMessage : this.state.errorMessage}
                        disabled={disabled || asyncActionInProgress}
                        onChange={(event: React.FormEvent<IComboBox>, option?: IComboBoxOption, index?: number, value?: string) => this.props.onInputChange(option?.key, option?.text)}
                        onPendingValueChanged={(option?: IComboBoxOption, index?: number, value?: string) => {
                            if(removePresettedValue === false)
                                this.setState({ removePresettedValue: true });
                        }} 
                    />
                    {(required && required === true) && <span className={classNames.requiredMarker}>*</span>}
                </LabelContainerComponent>;
    }
}
