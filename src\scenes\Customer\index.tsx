import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { CustomerContentView } from './components/customerContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import ClientStore from '../../stores/clientStore';
import { ClientDto } from '../../services/client/dto/clientDto';

const classNames: any = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	clientStore: ClientStore;
	match: any;
}

@inject(Stores.ClientStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private clientId: string = this.props.match.params.id;
	
	async componentDidMount() {
		await this.props.clientStore.get({ id: this.clientId } as ClientDto);		
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<CustomerContentView store={this.props.clientStore} payload={ this.props.clientStore.model as ClientDto } renderFooter={{
					show: true, options: {backOnly: true}
				}} />
			</FocusZone>
		);
	}
}

export default Index;