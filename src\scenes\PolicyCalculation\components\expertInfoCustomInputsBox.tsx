
import { ComboBox, IComboBoxOption, Icon, mergeStyleSets, MessageBar, MessageBarType, Spinner, SpinnerSize, TextField } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import AppConfig from "../../../lib/appconfig";
import policyDictionaryService from "../../../services/policyDictionary/policyDictionaryService";
import { myTheme } from "../../../styles/theme";
import { catchErrorMessage } from "../../../utils/utils";
import { ICustomInputsBoxGeneric, IVehicleBrands, IVehicleModelData } from "./types";

const classNames = mergeStyleSets({
    hide: {
        display: 'none !important',
    },
    fontBold: {
        fontWeight: '800',
    },
    toolbar: {
        selectors: {
            '& .ms-Pivot': {
                display: 'none',
            }
        }
    },
    messageBar: {
        width: 'fit-content'
    },
    messageBarMargin: {
        marginTop: '15px'
    },
    closeIcon: {
        position: 'absolute',
        top: 15,
        right: 10,
        cursor: 'pointer',
        transition: 'all 150ms',
        selectors: {
            ':hover': {
                transform: 'scale(1.1)',
            },
            ':active': {
                transform: 'scale(0.9) rotate(90deg)',
            },
        }
    },
    customInputsWrapper: {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: 'fit-content',
        height: 'auto',
        marginTop: '20px',
        padding: '20px',
        border: `1px solid ${myTheme.palette.themePrimary}`,
        borderRadius: '3px',
        minWidth: '502px',
        boxSizing: 'border-box',
    },
    comboBoxStyles: {
        width: '100%',
        marginTop: '15px',
    },
    loadSpinner: {
        display: 'inline-flex',
        marginTop: '15px',
        marginLeft: 'auto',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinnerTopBar: {
        display: 'inline-flex',
        marginLeft: '15px',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
});

export interface IExpertInfoCustomInputsBoxProps {
    showCustomInputsBox: boolean;
    customInputsData: any;
    onInputChange: (id: string, value: any) => void;
    onToggleClose: () => void;
    mapKeyToId: (mapType: string, key: string) => string;
}

type IExpertInfoCustomInputsBoxState = ICustomInputsBoxGeneric & {
    vehicleModelData: IVehicleModelData;
    vehicleBrands: IVehicleBrands;
    customInputsPrevData: {
        vehicleType: string,
        productionYear: string,
        vehicleBrand: string,
        vehicleModel: string,
        vehicleModelDetails: string,
    }
};

export class ExpertInfoCustomInputsBox extends React.Component<IExpertInfoCustomInputsBoxProps, IExpertInfoCustomInputsBoxState> {
    constructor(props: IExpertInfoCustomInputsBoxProps) {
        super(props);
    
        this.state = {
            ...this.state,
            date: new Date(),
            customInputsAsyncActionInProgress: false,
            vehicleBrands: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, year: [...brands] */ },
            vehicleModels: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, brand: [...models]" */ },
            vehicleModelData: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, year: { brand: {model: data} } */ },
            customInputsPrevData: {
                vehicleType: "",
                productionYear: "",
                vehicleBrand: "",
                vehicleModel: "",
                vehicleModelDetails: "",
            },
        };
    }

    private async getDataForCustomInputs(requestType: string, vehicleType: string, productionYear: string, vehicleBrand?: string, vehicleModel?: string) {
        const { vehicleBrands, vehicleModels, vehicleModelData } = this.state;
        
        switch(requestType) {
            case "getBrands":
                if(!vehicleBrands['gettingDataInProgress']) {
                    vehicleBrands['gettingDataInProgress'] = true;
                    await policyDictionaryService.getBrandsByVehicleTypeAndProductionYear(vehicleType, productionYear).then((response) => {
                        if(response.data && response.data.result) {
                            vehicleBrands["error"] = "";
                            vehicleBrands[productionYear] = response.data.result;
                        }
                
                        vehicleBrands['errorsCount'] = 0;
                        vehicleBrands['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleBrands: vehicleBrands, customInputsAsyncActionInProgress: false }));
                    }).catch((error: any) => {
                        vehicleBrands["errorsCount"]++;
                        vehicleBrands["error"] = catchErrorMessage(error);
                        vehicleBrands['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleBrands: vehicleBrands, customInputsAsyncActionInProgress: false }));
                    });
                }
            break;

            case "getModels":
                if(!vehicleModels['gettingDataInProgress']) {
                    vehicleModels['gettingDataInProgress'] = true;
                    await policyDictionaryService.getModelsByVehicleBrand(vehicleType, productionYear, vehicleBrand!).then((response: any) => {
                        if(response.data && response.data.result) {
                            vehicleModels["error"] = "";
                            vehicleModels[productionYear] = {};
                            vehicleModels[productionYear][vehicleBrand!] = response.data.result;
                        }
                
                        vehicleModels['errorsCount'] = 0;
                        vehicleModels['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleModels: vehicleModels, customInputsAsyncActionInProgress: false }));
                    }).catch((error: any) => {
                        vehicleModels["errorsCount"]++;
                        vehicleModels["error"] = catchErrorMessage(error);
                        vehicleModels['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleModels: vehicleModels, customInputsAsyncActionInProgress: false }));
                    });
                }
            break;

            case "getModelDetails":
                if(!vehicleModelData['gettingDataInProgress']) {
                    vehicleModelData['gettingDataInProgress'] = true;
                    await policyDictionaryService.getModelDetails(vehicleType, productionYear, vehicleBrand!, vehicleModel!).then((response) => {
                        if(response.data && response.data.result) {
                            vehicleModelData["error"] = "";
                            vehicleModelData[productionYear] = {};
                            vehicleModelData[productionYear][vehicleBrand!] = {};
                            vehicleModelData[productionYear][vehicleBrand!][vehicleModel!] = response.data.result;
                        }
                        
                        vehicleModelData['errorsCount'] = 0;
                        this.setState((prevState) => ({...prevState, vehicleModelData: vehicleModelData, customInputsAsyncActionInProgress: false }));
                        vehicleModelData['gettingDataInProgress'] = false;
                    }).catch((error: any) => {
                        vehicleModelData["errorsCount"]++;
                        vehicleModelData["error"] = catchErrorMessage(error);
                        vehicleModelData['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleModelData: vehicleModelData, customInputsAsyncActionInProgress: false }));
                    });
                }
            break;
        }
    }

    private toggleCustomInputsAsyncActionInProgressFlag(newState: boolean) {
        if(this.state.customInputsAsyncActionInProgress !== newState) {
            this.setState((prevState) => ({...prevState, customInputsAsyncActionInProgress: newState }));
        }
    }

    render() {
        const { date, customInputsAsyncActionInProgress, vehicleBrands, vehicleModels, vehicleModelData, customInputsPrevData } = this.state;

        let brandOptions: IComboBoxOption[] = [];
        let modelOptions: IComboBoxOption[] = [];
        let errors: any = {
            productionYearError: '',
            brandError: '',
            modelError: '',
            modelDataError: '',
        };
        let operationSuccess: boolean = false;

        const vehicleType = this.props.customInputsData.vehicleType;
        const productionYear = this.props.customInputsData.productionYear;
        const vehicleBrand = this.props.customInputsData.vehicleBrand;
        const vehicleModel = this.props.customInputsData.vehicleModel;

        if(vehicleType && vehicleType.length > 0) {
            if(productionYear && (parseInt(productionYear) >= 1922 && parseInt(productionYear) <= date.getFullYear())) {
                const maxErrorsCount = AppConfig.eurotaxCollectDataMaxConnectionErrorsCount;

                if(vehicleBrands[productionYear] && vehicleBrands[productionYear].length > 0 && vehicleBrands['error'].length === 0 && vehicleBrands['errorsCount'] < maxErrorsCount) {
                    vehicleBrands[productionYear].forEach((brand: string, index: number) => {
                        brandOptions.push({ key: index, text: brand });
                    });

                    if(vehicleBrand && vehicleBrand.length > 0) {
                        if(vehicleModels[productionYear] && vehicleModels[productionYear][vehicleBrand] && vehicleModels[productionYear][vehicleBrand].length > 0 && vehicleModels['error'].length === 0 && vehicleModels['errorsCount'] < maxErrorsCount) {
                            vehicleModels[productionYear][vehicleBrand].forEach((model: string, index: number) => {
                                modelOptions.push({ key: index, text: model });
                            });

                            if(vehicleModelData[productionYear] && vehicleModelData[productionYear][vehicleBrand] && vehicleModelData[productionYear][vehicleBrand][vehicleModel] && vehicleModelData['error'].length === 0 && vehicleModelData['errorsCount'] < maxErrorsCount) {
                                operationSuccess = true;

                                if(customInputsPrevData['vehicleModelDetails'] !== vehicleModelData[productionYear][vehicleBrand][vehicleModel]['model']) {
                                    customInputsPrevData['vehicleModelDetails'] = vehicleModelData[productionYear][vehicleBrand][vehicleModel]['model'];
                                    this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId","expertInfoId"), vehicleModelData[productionYear][vehicleBrand][vehicleModel]['infoExpertId']);
                                    // this.props.onInputChange(mapAttributeNameToId('eurotaxId'), this.vehicleModelData[productionYear][vehicleBrand][vehicleModel]['eurotaxCarId']);
                                    this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId","seatsCount"), vehicleModelData[productionYear][vehicleBrand][vehicleModel]['seatsCount']);
                                    this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId","fuelType"), this.props.mapKeyToId("mapAttributeValueToOptionId",vehicleModelData[productionYear][vehicleBrand][vehicleModel]['fuelType']));
                                    // this.props.onInputChange(mapAttributeNameToId('usageType'), mapAttributeValueToOptionId(vehicleModelData[productionYear][vehicleBrand][vehicleModel]['usageType']));
                                }
                            } else {
                                if(vehicleModel && vehicleModel.length > 0) {
                                    if(vehicleModelData['errorsCount'] < maxErrorsCount && (vehicleModelData['error'].length === 0 || customInputsPrevData['vehicleModel'] !== vehicleModel)) {
                                        this.toggleCustomInputsAsyncActionInProgressFlag(true);
                                        customInputsPrevData['vehicleModel'] = vehicleModel;
                                        this.getDataForCustomInputs("getModelDetails", vehicleType, productionYear, vehicleBrand, vehicleModel);
                                    } else {
                                        errors.modelDataError = L(vehicleModelData['error']);
                                    }
                                }
                            }
                        } else {
                            if(vehicleModels['errorsCount'] < maxErrorsCount && (vehicleBrands['error'].length === 0 || customInputsPrevData['vehicleBrand'] !== vehicleBrand)) {
                                this.toggleCustomInputsAsyncActionInProgressFlag(true);
                                customInputsPrevData['vehicleBrand'] = vehicleBrand;
                                this.getDataForCustomInputs("getModels", vehicleType, productionYear, vehicleBrand);
                            } else {
                                errors.modelError = L(vehicleModels['error']);
                            }
                        }
                    } else {
                        errors.modelError = L('Please select vehicle brand.');
                    }
                } else {
                    if(vehicleBrands['errorsCount'] < maxErrorsCount && (vehicleBrands['error'].length === 0 || (customInputsPrevData['vehicleType'] !== vehicleType || 
                                                                    customInputsPrevData['productionYear'] !== productionYear))) {
                                                                        this.toggleCustomInputsAsyncActionInProgressFlag(true);
                        customInputsPrevData['vehicleType'] = vehicleType;
                        customInputsPrevData['productionYear'] = productionYear;
                        this.getDataForCustomInputs("getBrands", vehicleType, productionYear);
                    } else {
                        errors.brandError = L(vehicleBrands['error']);
                    }
                }
            } else {
                errors.productionYearError = L('Vehicle production year must be between 1922 and current year.');
            }
        } else {
            errors.brandError = L('The vehicle type is incorrect.');
        }

        return <div className={`${classNames.customInputsWrapper} ${this.props.showCustomInputsBox ? '' : classNames.hide}`}>
            <Icon iconName="ChromeClose" onClick={() => this.props.onToggleClose()} title={L('Close')} className={classNames.closeIcon} />
            
            <MessageBar messageBarType={MessageBarType.info} isMultiline={false} className={classNames.messageBar}>
                {L("Enter vehicle details to find Expert's information ID.")}
            </MessageBar>

            <TextField
                type="number"
                autoComplete={'off'}
                label={L("Enter year of production")}
                errorMessage={errors.productionYearError}
                className={classNames.comboBoxStyles}
                disabled={customInputsAsyncActionInProgress}
                value={productionYear}
                onChange={(e: any, value: any) => this.props.onInputChange('productionYear', parseInt(value))}
            />

            <ComboBox
                label={L("Select vehicle brand")}
                allowFreeform={false}
                autoComplete={'on'}
                options={brandOptions}
                className={classNames.comboBoxStyles}
                key={`selectBrand-${vehicleType}-${productionYear}`}
                errorMessage={!!productionYear && errors.brandError}
                disabled={brandOptions.length <= 0 || customInputsAsyncActionInProgress}
                onChange={(e: any, value: any) => this.props.onInputChange('vehicleBrand', value.text)}
            />

            <ComboBox
                label={L("Select vehicle model")}
                allowFreeform={false}
                autoComplete={'on'}
                options={modelOptions}
                className={classNames.comboBoxStyles}
                key={`selectModel-${vehicleType}-${productionYear}-${vehicleBrand}`}
                errorMessage={errors.modelError}
                disabled={modelOptions.length <= 0 || customInputsAsyncActionInProgress}
                onChange={(e: any, value: any) => this.props.onInputChange('vehicleModel', value.text)}
            />

            { operationSuccess && 
                <MessageBar messageBarType={MessageBarType.success} isMultiline={false} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}>
                    { L("Success! Expert's information ID is now set.") }
                </MessageBar> }
            { errors.modelDataError.length > 0 && 
                <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}>
                    {errors.modelDataError}
                </MessageBar> }
            { customInputsAsyncActionInProgress && <Spinner label={L('Collecting data...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" /> }
        </div>;
    }
}