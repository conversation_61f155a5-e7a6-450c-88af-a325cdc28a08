import { ProductDto } from '../../../services/product/productDto';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { GeneralSportsListContentView } from '../../Product/components/generalSportsListContentView';

export class GeneralSportsListPanel extends GenericPanel {
    getPanelTitle(): string {
        return L("General sports list");
    }

    renderConfirm = () => {
        return <></>;
    };

    renderContent() {
        return <GeneralSportsListContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ProductDto } customData={this.props.customData} />;
    }
}