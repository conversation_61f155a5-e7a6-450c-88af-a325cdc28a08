@import '../styles/variables.less';

.ms-Layer {
  z-index: 800;
}
.ant-modal-wrap {
}
.ant-modal.ant-modal-confirm.ant-modal-confirm-error {
}
.ant-modal-content {
  z-index: 999;
  position: relative;
  border-radius: 2px;
  .ant-modal-body {
    .ant-modal-confirm-body-wrapper {
      .ant-modal-confirm-body {
        .anticon.anticon-close-circle {
          display: none;
        }
        .ant-modal-confirm-title {
          font-size: 20px;
          font-weight: 600;
          color: rgb(50, 49, 48);
        }
        .ant-modal-confirm-content {
          font-size: 14px;
          color: rgb(96, 94, 92);
          margin-left: 0;
        }
      }
      .ant-modal-confirm-btns {
        .ant-btn.ant-btn-primary {
          border-radius: 2px;
          background-color: @primary-color-fabric-ui;
          border-color: @primary-color-fabric-ui;
        }
      }
    }
  }
}
