# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

pool:
  vmImage: ubuntu-latest

steps:
  - task: gitversion/setup@0
    displayName: Install GitVersion
    inputs:
      versionSpec: '5.x'
  - task: gitversion/execute@0
    displayName: Determine Version
  - task: Docker@2
    inputs:
      containerRegistry: 'TopContainerRegistry'
      repository: 'top-abp-frontend'
      command: 'buildAndPush'
      Dockerfile: 'dockerfile'
      tags: |
        $(Build.BuildId)
