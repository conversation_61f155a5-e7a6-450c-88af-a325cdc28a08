import { IDropdownOption } from '@fluentui/react';
import http from './httpService';

export class AbpUserConfigurationService {
  public async getAll() {
    const result = await http.get('/AbpUserConfiguration/GetAll');
    return result;
  }

  async GetDataResult(path: string): Promise<IDropdownOption[]> {
    const result = await http.get(path);
    return !!result.data && !!result.data.result ? result.data.result : result.data;
  }
}

const exportAbpUserConfigurationService: AbpUserConfigurationService = new AbpUserConfigurationService();
export default exportAbpUserConfigurationService;
