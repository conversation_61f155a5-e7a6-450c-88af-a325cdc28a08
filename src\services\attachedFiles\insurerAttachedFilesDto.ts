import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";
import { InsurerAttachedFilePolicyType } from "./enums/insurerAttachedFilePolicyTypeEnums";

export interface InsurerAttachedFilesDto extends BaseApiEntityModel {
  // id: number;
  insurerName: string,
  status: string,
  type: string,
  includeInCalculationsEmail: boolean,
  fileUrl: string,
  originalFileName: string,
  blobFileName: string,
  policyType: InsurerAttachedFilePolicyType | null,
  // description: string,
  // lastModificationTime: string,
  // lastModifierUserId: number,
  // creationTime: string,
  // creatorUserId: number,
}