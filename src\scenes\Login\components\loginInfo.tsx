import React from 'react';
import { DefaultButton, Image, Text, Panel, PanelType, mergeStyleSets, Link }  from '@fluentui/react';
import RegistrationForm from './registrationForm';
import AccountStore from '../../../stores/accountStore';
import { Container } from '../../../stores/storeInitializer';
import { L } from '../../../lib/abpUtility';
import {additionalTheme, myTheme} from '../../../styles/theme';
import aSoftLogo from '../../../images/asoft_logo.png';

import { createOrUpdateClassNames } from '../../BaseComponents/createOrUpdate';
import AppConsts from '../../../lib/appconst';

interface ILoginInfoState {
  isPriceListOpen: boolean;
  isRegistrationOpen: boolean;
  html: string;
  refresh: boolean;
}
interface ILoginInfoProps {
  accountStore: AccountStore;
}
const classNames = mergeStyleSets({
  loginInfo: {
    flex: 1,
    padding: 30,
    paddingTop: 0,
    paddingBottom: 0,
    backgroundColor: myTheme.palette.themePrimary,
    display: 'flex',
    flexDirection: 'column',
  },
  infoSection: {
    display: 'flex',
    flexDirection: 'column',
    paddingBottom: 20,
    alignItems: 'center',
  },
  sectionText: {
    textAlign: 'center',
    color: additionalTheme.white,
    marginBottom: 1,
  },
  headerText: {
    textAlign: 'center',
    color: additionalTheme.white,
    marginBottom: 1,
    fontWeight: 'bold'
  },
  sectionBtn: {
    color: myTheme.palette.themePrimary,
    borderColor: myTheme.palette.themePrimary,
    padding: '2px 5px',
  },
  logoWrapper: {
    display: 'flex',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 20,
    width: 170,
    selectors: {
      '& img': {
        height: 80,
      },
    },
  },
});

// todo Jaroslaw ? to remove this is not used anywhere 
export class LoginInfo extends React.Component<ILoginInfoProps, ILoginInfoState> {
  formRef: any;
  state = {
    isPriceListOpen: false,
    isRegistrationOpen: false,
    html: '',
    refresh: false,
  };

  dismissPanel = (panelState: string): void => {
    Container.EventBus.customErrorHandling = false;
    let changePanel = {};
    changePanel[panelState] = false;
    this.setState(changePanel);
  };
  openRegisterPanel = () => {
    this.props.accountStore.createCustomer();
    this.openPanel('isRegistrationOpen');
    this.formRef.props.form.setFieldsValue({ ...this.props.accountStore.customer });
  };
  openPanel = (panelState: string): void => {
    Container.EventBus.customErrorHandling = true;
    let changePanel = {};
    changePanel[panelState] = true;
    this.setState(changePanel);
  };

  async componentDidMount() {
    var html = await this.props.accountStore.getHtml();
    this.setState({ html: html });
    document.querySelector('#pricelistMap');
  }

  handleCreate = () => {
    const form = this.formRef.props.form;

    form.validateFields(async (err: any, values: any) => {
      if (err) {
        Container.EventBus.setFormError(err);
        return;
      } else {
        Container.EventBus.clearFormError();
        await this.props.accountStore.registerCustomer(values);
        if (Container.EventBus.HttpError) {
          this.setState({ refresh: !this.state.refresh });
          return;
        }
      }
      form.resetFields();
      this.dismissPanel('isRegistrationOpen');
    });
  };

  onRenderFooterContent = () => {
    return (
      <div className={createOrUpdateClassNames.panelActions}>
        <DefaultButton theme={myTheme} onClick={() => this.dismissPanel('isPriceListOpen')} text={L('Close')} />
      </div>
    );
  };

  render() {
    const { isPriceListOpen, isRegistrationOpen } = this.state;
    return (
      <div className={classNames.loginInfo}>
        <div className={classNames.infoSection} style={{ paddingTop: 80 }}>
          <Text theme={myTheme} className={classNames.headerText} variant={'medium'}>
            {L('Playing with Confidence')}
          </Text>
          <Text theme={myTheme} className={classNames.headerText} variant={'medium'}>
            {L('Passion and Respect')}
          </Text>
          <div className={classNames.logoWrapper}>
            <Image theme={myTheme} src={aSoftLogo} alt="Sodo" />
          </div>
          <DefaultButton
            theme={myTheme}
            className={classNames.sectionBtn}
            text={L('Handbooks')}
            onClick={() => this.openPanel('isPriceListOpen')}
          />
        </div>
        <div className={classNames.infoSection}>
          <Text theme={myTheme} className={classNames.sectionText} variant={'xxLarge'}>
            {L('Registration')}
          </Text>
          <Text theme={myTheme} className={classNames.sectionText} variant={'medium'}>
            {L('1. Click below')}
          </Text>
          <Text theme={myTheme} className={classNames.sectionText} variant={'medium'}>
            {L('2. Create your profile')}
          </Text>
          <Text theme={myTheme} className={classNames.sectionText} variant={'medium'}>
            {L('3. Submit')}
          </Text>
          <Text theme={myTheme} className={classNames.sectionText} variant={'medium'}>
            {L('4. Receive confirmation')}
          </Text>
          <DefaultButton theme={myTheme}
            style={{ marginTop: 45 }}
            className={classNames.sectionBtn} text={L('Register')} onClick={() => this.openRegisterPanel()} />
          <div style={{ marginTop: 10, fontSize: 11 }}>
            <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'center' }}>By logging in you agree to our&nbsp;<strong> <a style={{ color : "rgba(0, 0, 0, 0.65)"}} href="/account/terms-of-use"> {L("Terms of Use")} </a>  </strong></div>
            <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'center' }}>and to our&nbsp;<strong><a style={{ color : "rgba(0, 0, 0, 0.65)"}} href="/account/privacy-policy">{L("Privacy Policy")} </a></strong>.</div>
          </div>
        </div>
        <Panel
          headerText={L('Handbooks')}
          isOpen={isPriceListOpen}
          onDismiss={() => this.dismissPanel('isPriceListOpen')}
          closeButtonAriaLabel={L('Close')}
          type={PanelType.smallFixedFar}
          theme={myTheme}
          onRenderFooterContent={this.onRenderFooterContent}
          isFooterAtBottom={true}
        >
          <div>
            <Link href={`${AppConsts.remoteServiceBaseUrl}/html/p1.pdf`}>RCLL-Handbook</Link>
          </div>
          <div>
            <Link href={`${AppConsts.remoteServiceBaseUrl}/html/p2.pdf`}>SDA-Handbook</Link>
          </div>

        </Panel>

        <RegistrationForm
          onCancel={() => this.dismissPanel('isRegistrationOpen')}
          handleCreate={this.handleCreate}
          isRegistrationOpen={isRegistrationOpen}
        />
      </div>
    );
  }
}
