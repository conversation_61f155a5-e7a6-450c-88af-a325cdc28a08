import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";
import { AddressDto } from "./addressDto";

export interface PickupPoint extends BaseApiEntityModel {
    name: string;
    description: string;
    adminComment: string;
    address: AddressDto;
    warehouseId: string;
    storeId: string;
    pickupFee: number;
    latitude: number | null;
    longitude: number | null;
    displayOrder: number;
}
