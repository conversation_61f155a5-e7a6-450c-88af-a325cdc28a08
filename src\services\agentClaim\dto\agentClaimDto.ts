import { ClientDto } from "../../client/dto/clientDto";
import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";
import { InsurancePolicyDto } from "../../insurancePolicy/insurancePolicyDto";
import { PolicyType } from "../../policy/policyTypeEnums";
import { UserDto } from "../../user/dto/userDto";

export interface AgentClaimDto extends BaseApiEntityModel {
    claimNumber: string;
    insurer: string;
    clientId: number;
    client: ClientDto;
    policyNumber: string;
    policyId: number | null;
    policy: InsurancePolicyDto;
    claimSubject: string;
    claimType: PolicyType;
    applicationDate: string;
    claimDate: string;
    emailAddress: string;
    note: string;
    compensationPaid: boolean;
    invoicePaid: boolean;
    daysSinceStatusChanged: number | null;
    claimStatus: string;
    claimStatusUpdateDate: string;
    daysSinceClaimReport: number | null;
    agentId: number;
    agent: UserDto;
    insuranceClaimType: string;
}