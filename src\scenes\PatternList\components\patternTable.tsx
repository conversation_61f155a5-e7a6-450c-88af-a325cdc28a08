import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { PatternDto } from '../../../services/pattern/dto/patternDto';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { PatternPanel } from './patternPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { RouterPath } from "../../../components/Router/router.config";
import { Link } from "@fluentui/react";

export class PatternTable extends FluentTableBase<PatternDto> {
  
  getColumns(): ITableColumn[] {
    return PatternTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Pattern type'),
        fieldName: 'patternType',
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Pattern}/${item.id}`);
                      }} 
                        href={`/${RouterPath.Pattern}/${item.id}`}>
                  {item.patternType}
                </Link>
        }
      },
      {
        name: L('Value'),
        fieldName: 'value',
      },
      {
        name: L('Description'),
        fieldName: 'description',
      },
    ];
  }

  getTitle(): string {
    return L('Patterns');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: true,
      delete: false,
      customActions: false,
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <PatternPanel
        {...props}
      />
    </>
  }
}