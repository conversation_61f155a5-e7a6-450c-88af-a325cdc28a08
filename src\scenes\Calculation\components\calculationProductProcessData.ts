import { L } from "../../../lib/abpUtility";
import { isJsonString } from "../../../utils/utils";

export function calculationProductProcessData(calculationPayload: string, productAttributeResultItems: any, gnLanguage: any): {apkData: any, calculationData: any} {
	const parsedJson: any = calculationPayload && isJsonString(calculationPayload) ? JSON.parse(calculationPayload) : undefined;
	const apk: any[] = parsedJson && parsedJson.apkData ? parsedJson.apkData : [];
	const data: any[] = parsedJson && parsedJson.data ? parsedJson.data : [];
	
	const apkData: any = {};
	const calculationData: any = {};

	data.forEach((item: any) => {
		if(calculationData[item.core_path]) {
			return;
		} else {
			productAttributeResultItems.some((arrayItem: any) => {
				let itemFound: boolean = false;
				
				arrayItem.UserFields.some((UserField: any) => {
					if((UserField.Key === 'core_path' && UserField.Value === item.core_path) || (UserField.Key === 'key' && UserField.Value === item.core_path)) {
						calculationData[item.core_path] = setCalculationDataForItem(item.core_path, item, arrayItem, gnLanguage);

						itemFound = true;
						return true;
					}
					return false;
				});

				if(itemFound) {
					return true;
				}

				return false;
			});
		}
	});

	apk.forEach((item: any) => {
		if(apkData[item.key]) {
			return;
		} else {
			productAttributeResultItems.some((arrayItem: any) => {
				let itemFound: boolean = false;

				arrayItem.UserFields.some((UserField: any) => {
					if((UserField.Key === 'key' && UserField.Value === item.key) || (UserField.Key === 'core_path' && UserField.Value === item.key)) {
						apkData[item.key] = {
							productAttribute: arrayItem, 
							label: "", 
							value: item.valueName,
							valueLocale: ""
						};

						item.locales.forEach((locale: any) => {
							if(locale.languageId === gnLanguage.id && locale.localeKey === "Name") {
								apkData[item.key].label = locale.localeValue;
							}
						});

						item.valueLocales.forEach((locale: any) => {
							if(locale.languageId === gnLanguage.id && locale.localeKey === "Name") {
								apkData[item.key].valueLocale = locale.localeValue;
							}
						});

						itemFound = true;
						return true;
					}
					return false;
				});

				if(itemFound) {
					return true;
				}

				return false;
			});
		}
	});

	return {apkData, calculationData};
}

function setCalculationDataForItem(corePath: string, item: any, arrayItem: any, gnLanguage: any, fromMissingKeys: boolean = false) {
	const calculationData: any = {};

	calculationData[corePath] = {
		productAttribute: arrayItem, 
		label: "", 
		value: item.valueId,
		valueLocale: item.value
	};

	if(arrayItem.PredefinedProductAttributeValues && arrayItem.PredefinedProductAttributeValues.length > 0) {
		arrayItem.PredefinedProductAttributeValues.some((productAttributeValue: any) => {
			if(productAttributeValue.Name === item.value) {
				productAttributeValue.Locales.forEach((valueLocale: any) => {
					if(valueLocale.LanguageId === gnLanguage.id && valueLocale.LocaleKey === "Name") {
						calculationData[corePath].valueLocale = valueLocale.LocaleValue;
					}
				});
				return true;
			}
			return false;
		});
	}

	let splittedValues = calculationData[corePath] && !!calculationData[corePath].valueLocale ? calculationData[corePath].valueLocale.split(', ') : [''];
	splittedValues.forEach((value: string, valueIndex: number) => {
		if(value && typeof value === 'string') {
			splittedValues[valueIndex] = L(value);
		}
	});

	if(splittedValues.length > 1) {
		calculationData[corePath].valueLocale = splittedValues.join(', ');
	} else {
		calculationData[corePath].valueLocale = splittedValues[0];
	}

	arrayItem.Locales.forEach((locale: any) => {
		if(locale.LanguageId === gnLanguage.id && locale.LocaleKey === "Name") {
			calculationData[corePath].label = locale.LocaleValue;
		}
	});

	// set default value for missing keys
	if(fromMissingKeys === true && calculationData[corePath].valueLocale === "" && calculationData[corePath].value === "") {
		let value: boolean | string | number = false;
		let valueLocale: string = L("No");

		switch(corePath) {
			case "VehicleInsurance.AssTypes":
			case "VehicleInsurance.YearOfBirthOldestChild":
			case "TravelInsurance.TypeOfCalculation":
			case "TravelInsurance.LuggageInsuranceSum":
			case "TravelInsurance.EqSportInsuranceSum":
			case "TravelInsurance.PaymentMethod":
			case "ChildrenInsurance.InsurerIsInsured":
			case "ChildrenInsurance.PaymentMethod":
				value = "";
				valueLocale = "-";
				break;
			case "VehicleInfo.NumberOfKeys":
			case "ChildrenInsurance.InstallmentCount":
				value = 1;
				valueLocale = "1";
				break;
		}

		calculationData[corePath].value = value;
		calculationData[corePath].valueLocale = valueLocale;
	}

	return calculationData[corePath];
}

export function checkUsedKeysForDataSet(dataToCheck: any, usedInputsKeys: string[], productAttributeResultItems?: any[], gnLanguage?: any): {dataToCheck: any} {
	usedInputsKeys.forEach((key: string) => {
		if(!dataToCheck[key]) {
			if(productAttributeResultItems) {
				productAttributeResultItems.some((arrayItem: any) => {
					let itemFound: boolean = false;
					
					arrayItem.UserFields.some((UserField: any) => {
						if((UserField.Key === 'core_path' && key === UserField.Value) || (UserField.Key === 'key' && key === UserField.Value)) {
							dataToCheck[UserField.Value] = setCalculationDataForItem(UserField.Value, {value: "", valueId: ""}, arrayItem, gnLanguage, true);
							itemFound = true;
							return true;
						}
						return false;
					});
			
					if(itemFound) {
						return true;
					}
			
					return false;
				});
			}

			if(!dataToCheck[key]) {
				dataToCheck[key] = {
					productAttribute: {}, 
					label: `(${L('Input data is missing')})`, 
					value: "",
					valueLocale: ""
				};
			}
		}
	});

	return dataToCheck;
}