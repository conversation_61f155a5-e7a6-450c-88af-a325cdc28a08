import { IStackStyles, IStackTokens, mergeStyleSets, MessageBar, Stack } from "@fluentui/react";
import React from "react";
import {additionalTheme, myTheme} from "../../../styles/theme";

const stackStyles: IStackStyles = {
	root: {
		// background: DefaultPalette.themeTertiary,
		// width: 300,
	},
};

const columnTextSpacingStackTokens: IStackTokens = {
	childrenGap: "1%",
	padding: "25px 0",
};

const classNames = mergeStyleSets({
	fontBold: {
		fontWeight: "800",
	},
	confirmCalculationButton: {
		width: "fit-content",
		padding: "25px 50px",
		marginTop: "0 !important",
		marginRight: "25px",
		marginBottom: "25px",
	},
	additionalActionButton: {
		width: "fit-content",
		padding: "20px 20px",
		marginBottom: "5px",
		marginRight: "25px",
	},
	summaryText: {
		selectors: {
			"& span": {
				fontWeight: "bold",
			},
		},
	},
	//   summaryAttributesWrapper: {
	//     display: "flex",
	//     flexDirection: "row",
	//     flexWrap: "wrap",
	//     justifyContent: "flex-start",
	//     alignItems: "flex-start",
	//     border: `1px solid ${myTheme.palette.themeLight}`,
	//     padding: "15px",
	//     maxWidth: "90%",
	//     marginBottom: "15px",
	//   },
	atributeHorizontal: {
		gridColumn: "span 2",
		gridRow: "span 1",
		boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.25)",
		padding: "0 20px",
		margin: "10px 1px",
		borderRadius: "12px",
	},
	atributeVertical: {
		gridColumn: "span 1",
		gridRow: "span 2",
		boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.25)",
		width: "100%",
		margin: "10px 20px",
		borderRadius: "12px",
		display: "flex",
	},
	atributeVerticalContainer: {
		boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.25)",
		width: "100%",
		margin: "10px 1px",
		borderRadius: "12px",
		display: "flex",
		flexDirection: "column",
		padding: "10px 20px 10px",
		boxSizing: "border-box",
	},
	atributeVerticalElement: {
		width: "50%",
		padding: "0 20px",
	},
	summaryAttributesWrapper: {
		display: "grid" /* 1 */,
		gridTemplateColumns: "repeat(3, auto)",
		gridTemplateRows: "repeat(2, auto)",
		maxWidth: "90%",
	},
	summaryAttribute: {
		color: `${additionalTheme.white} !important`,
		padding: "5px 10px",
		marginRight: "10px",
		marginBottom: "5px",
		background: myTheme.palette.themeLighter,
		fontSize: "0.8rem",
		whiteSpace: "pre-line",
		selectors: {
			"&:last-child": {
				borderRight: "none",
			},
		},
	},
	warningMessageBar: {
		width: "fit-content",
	},
	messageBar: {
		width: "fit-content",
		selectors: {
			"& .ms-MessageBar-innerText": {
				selectors: {
					"& span": {
						whiteSpace: "pre-line",
					},
				},
			},
		},
	},
	confrimButtonWrapper: {
		display: "inline-flex",
		flexDirection: "row",
		flexWrap: "wrap",
		justifyContent: "flex-start",
		alignItems: "center",
		width: "fit-content",
		marginTop: "50px !important",
	},
	loadSpinner: {
		display: "inline-flex",
		selectors: {
			"& .ms-Spinner-label": {
				color: myTheme.palette.themePrimary,
			},
		},
	},
});

export interface IPolicyFromStep3Props {
	gnLanguage: any;
	summaryMessageBoxData: any;
	asyncActionInProgress: boolean | undefined;
	isEditMode: boolean;
	onMessageBarDismiss: (type: string) => void;
}

export class PolicyFormStep3 extends React.Component<IPolicyFromStep3Props> {
	render() {
		const { summaryMessageBoxData } = this.props;

		return (
			<>
				<Stack styles={stackStyles} tokens={columnTextSpacingStackTokens}>
					<MessageBar
						messageBarType={summaryMessageBoxData.policyFinalization.type} isMultiline={false}
						className={`${summaryMessageBoxData.policyFinalization.hide && "hide"} ${classNames.messageBar}`}
						// onDismiss={() =>
						// 	this.props.onMessageBarDismiss("policyFinalization")
						// }
					>
						{summaryMessageBoxData.policyFinalization.text}
					</MessageBar>
				</Stack>
			</>
		);
	}
}
