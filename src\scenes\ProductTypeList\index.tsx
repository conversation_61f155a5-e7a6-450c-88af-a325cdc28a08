import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import ProductTypeStore from '../../stores/productTypeStore';
import { ProductTypeTable } from './components/productTypeTable';

export interface IProps {
  searchStore: SearchStore;
  productTypeStore: ProductTypeStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.ProductTypeStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  public render() {
    return (
      <>
        <ProductTypeTable
          searchText={this.props.searchStore.searchText}
          items={this.props.productTypeStore.dataSet ? this.props.productTypeStore.dataSet.items : []}
          store={this.props.productTypeStore}
          history={this.props.history}
        />
      </>
    );
  }
}

export default Index;