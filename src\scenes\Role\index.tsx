import { inject, observer } from 'mobx-react';

import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import RoleStore from '../../stores/roleStore';
import { RoleDto } from '../../services/role/dto/roleDto';
import { RoleContentView } from './components/roleContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	roleStore: RoleStore;
	match: any
}

@inject(Stores.RoleStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	async componentDidMount() {
		await this.props.roleStore.get({ id: this.props.match.params.id } as RoleDto);
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<RoleContentView store={this.props.roleStore} payload={ this.props.roleStore.model as RoleDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;