import * as React from 'react';
import { L } from '../../lib/abpUtility';
import EmptyUserPhoto from '../../images/empty_profile.png';
import logo from '../../images/logoTopHorizontalBlue.png';
import arrow from '../../images/Shape.png'
import globe from '../../images/ShapeSearch.svg'
import {Image, IPersonaSharedProps, mergeStyleSets, Link, Stack} from '@fluentui/react';
import Search from './components/search';
import SearchStore from '../../stores/searchStore';
import UserStore from '../../stores/userStore';
import {additionalTheme, myTheme} from '../../styles/theme';
import { HeaderHeight, LogoHeight } from '../../styles/aboutApp';
import LanguageSelect from '../LanguageSelect';
import { AzureB2CStorageKey } from '../../scenes/Login/AzureB2C/signInButton';
import AppConsts from '../../lib/appconst';

declare var abp: any;

const mainHeight = HeaderHeight;

const classNames = mergeStyleSets({
  headerContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: mainHeight,
    color: myTheme.palette.neutralDark,
  },
  headerLeftArea: {
    display: 'flex',
    alignItems: 'center',
  },
  headerLogo: {
    color:myTheme.palette.themeDarker,
    selectors: {
      '& img': {
        height: LogoHeight,
      },
    },
    padding: '15px 0 15px 0',
  },
  headerSearch: {
    display: 'flex', 
    justifyContent: 'center', 
    width: '300px',
  },
  headerRightSide: {
    height: mainHeight,
    display: 'flex',

  },
  headerIconsCol: {
    height: mainHeight,
    flex: 1,
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    padding: '0px 15px 0px 15px',
    selectors: {
      '& .ms-Button': {
        padding: '0px',
        border: 'none',
        backgroundColor: 'transparent',
        minWidth: 'unset',
        selectors: {
          ':hover': {
            backgroundColor: 'transparent',
          },
          '& .ms-Button-menuIcon': {
            display: 'none',
          },
        },
      },
    },
  },
  icons: {
    height: '40px',
    width: '40px',
    flex: 1,
    display: 'flex',
    alignItems: 'center',
  },
  iconClass: {
    color: myTheme.palette.black,
    lineHeight: "0px",
  },
  personaText: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: '30px',
    height: mainHeight,
    flex: 1,
    font: 'Segoe UI',
    justifyContent: 'space-between',
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  personaTextContainerLink: {
    display: 'flex',
    alignItems: 'center',
    margin: '0 35px 0 80px',
  },
  personaTextName: {
    marginLeft:'80px',
    fontSize: 14,
    height: '20px',
    color: additionalTheme.grey,
    fontWeight: 700,
    display:'inline',
    textDecoration: 'none',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
  },
  personaTextExit: {
    fontSize: 12,
    fontWeight: 400,
    color: additionalTheme.grey,
    cursor: 'pointer',
  },
  persona: {
    flex: 1,
    justifyContent: 'flex-end',
    height: mainHeight,
    width: '100%',
    display: 'block',
  },
  logoContainer: {
    display:'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  arrow: {
    color: myTheme.palette.neutralDark,
    paddingTop: '5px',
    marginLeft: '30px',
    marginRight: '45px',
    cursor: 'pointer',
  },
  verticalLine:  {
    height: '100%',
    width: '1px',
    backgroundColor: "#D2D2D2",
    marginRight: '40px',
  },
  globeIcon: {
    color: myTheme.palette.neutralDark,
    marginLeft: '35px'
  },
  leftHeaderText: {
    fontSize: 20,
    height: '30px',
    color: myTheme.palette.black,
    display:'inline',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
  },
  rightHeaderText: {
    fontSize: 20,
    height: '30px',
    color: myTheme.palette.black,
    display:'inline',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
  },
});

export interface IHeaderProps {
  collapsed?: any;
  toggle?: any;
  searchStore?: SearchStore;
  userStore?: UserStore;
  pathname?: string;
}

export class Header extends React.Component<IHeaderProps> {
  state = {
    persona: this.props.userStore?.defaultValue,
  };

  async componentDidMount() {
    if(this.props.userStore) {
      let userId = abp.session.userId;
      let persona = this.props.userStore.defaultValue;
      
      const getFromLocalStorage: string | null = localStorage.getItem(AzureB2CStorageKey);
      const parsedLocalStorageData: any = !!getFromLocalStorage ? JSON.parse(getFromLocalStorage) : undefined;

      if(parsedLocalStorageData && parsedLocalStorageData.user) {
        let user = parsedLocalStorageData.user;
        persona = {...persona, emailAddress: user.emailAddress, id: user.id, name: user.name, surname: user.surname, userName: user.userName};
      } else if(userId) {
        await this.props.userStore.get({ id: abp.session.userId });
        persona = this.props.userStore.editUser;
      }
      
      this.setState({ persona: persona });
    }
  };

  render() {
    const { persona } = this.state;

    const examplePersona: IPersonaSharedProps = {
      imageUrl: persona?.avatarUrl ? persona.avatarUrl : EmptyUserPhoto,
      text: !!persona?.fullName && persona?.fullName.length > 1 ? persona?.fullName : `${persona?.name} ${persona?.surname}`,
      secondaryText: L('view profile'),
      tertiaryText: L('Sign out'),
    };
    
    const showSearchBar = this.props.searchStore && (!this.props.pathname || this.props.pathname !== '/dashboard') ? true : false;

    return (
      <Stack horizontal horizontalAlign="space-between" wrap tokens={{childrenGap: 'l1'}}>
        <Stack.Item className={classNames.headerLeftArea}>
          <Stack.Item align="center">
            {persona && (
              <Stack horizontal horizontalAlign="space-between" wrap tokens={{childrenGap: 'm'}}>
                <div className={classNames.personaText}>
                  <div>
                    <Link href="/profile" className={classNames.personaTextName}>{examplePersona.text}</Link>
                  </div>
                  <div className={classNames.personaTextContainerLink }>
                    <Link href="/logout" className={classNames.personaTextExit}>{examplePersona.tertiaryText}</Link>
                  </div>
                  <div className={classNames.verticalLine}></div>
                </div>
              </Stack>)}
          </Stack.Item>
          <Stack.Item align="center"> 
            {showSearchBar && (
              <div className={classNames.headerSearch}>
                <Search searchStore={this.props.searchStore!}/>
              </div>)}
          </Stack.Item>
          {persona && (
            <Stack horizontal horizontalAlign="center" verticalAlign="center" tokens={{childrenGap: 'l1'}}>
              <Image theme={myTheme} className={classNames.globeIcon} src={globe}/>
              <LanguageSelect />
            </Stack>
          )}
        </Stack.Item>
        <Stack.Item align="center">
          <p className={classNames.rightHeaderText}>{AppConsts.rightHeaderText}</p>
        </Stack.Item>
        <Stack.Item align="center">
          <div className={classNames.logoContainer}>
            <Image theme={myTheme} className={classNames.headerLogo} src={!!AppConsts.logoPath ? AppConsts.logoPath : logo} 
              alt={!!AppConsts.logoAlt ? AppConsts.logoAlt : "TOP-Ubezpieczenia"} />
            {persona && (
              <Link href="/logout">
                <Image theme={myTheme} className={classNames.arrow} src={arrow}/>
              </Link>
            )}
          </div>
        </Stack.Item>
      </Stack>
    );
  }
}

export default Header;