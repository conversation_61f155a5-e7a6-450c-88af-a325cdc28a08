import { CrudServiceBase } from '../base/crudServiceBase';
import Endpoint from '../endpoint';
import { PolicyDictionaryDto } from './policyDictionaryDto';
import { httpApi } from '../httpService';

export class PolicyDictionaryService extends CrudServiceBase<PolicyDictionaryDto> {
    constructor() {
        super(Endpoint.PolicyDictionary);
        this.internalHttp = httpApi;
    }

    async getBrandsByVehicleTypeAndProductionYear(vehicleType: string, productionYear: string) {
        return await this.internalHttp.get(this.endpoint.Custom(`Vehicle/Brand/${vehicleType}/${productionYear}`, true));
    }

    async getModelsByVehicleBrand(vehicleType: string, productionYear: string, brand: string) {
        return await this.internalHttp.get(this.endpoint.Custom(`Vehicle/Model/${vehicleType}/${productionYear}/${brand}`, true));
    }

    async getModelDetails(vehicleType: string, productionYear: string, brand: string, model: string) {
        return await this.internalHttp.get(this.endpoint.Custom(`Vehicle/Info/${vehicleType}/${productionYear}/${brand}/${model}`, true));
    }

    async getVehicleTypes() {
        return await this.internalHttp.get(this.endpoint.Custom(`Vehicle/GetVehicleTypes`, true));
    }

    async getFuelTypes() {
        return await this.internalHttp.get(this.endpoint.Custom(`GetFuelTypes`, true));
    }

    async getEurotaxType() {
        return await this.internalHttp.get(this.endpoint.Custom(`Eurotax/Type`, true));
    }

    async getEurotaxEngineCapacity(modelId:number) {
        return await this.internalHttp.get(this.endpoint.Custom(`Eurotax/EngineCapacity/${modelId}`, true));
    }

    async getEurotaxEnginePower(modelId:number, engineCapacity:number) {
        return await this.internalHttp.get(this.endpoint.Custom(`Eurotax/EnginePower/${modelId}/${engineCapacity}`, true));
    }

    async getEurotaxBrand(typeId: number) {
        return await this.internalHttp.get(this.endpoint.Custom(`Eurotax/Brand/${typeId}`, true));
    }

    async getEurotaxModel(brandId: number,vehicleType:number, productionYear:number) {
        return await this.internalHttp.get(this.endpoint.Custom(`Eurotax/Model/${vehicleType}/${brandId}/${productionYear}`, true));
    }

    async getEurotaxConfiguration(modelId: number, engineCapacity:number, enginePower:number) {
        return await this.internalHttp.get(this.endpoint.Custom(`Eurotax/Configuration/${modelId}/${enginePower}/${engineCapacity}`, true));
    }

    async getTypes() {
        const result = await this.internalHttp.get(this.endpoint.Custom(`GetTypes`, true));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportPolicyDictionaryService: PolicyDictionaryService = new PolicyDictionaryService();
export default exportPolicyDictionaryService;