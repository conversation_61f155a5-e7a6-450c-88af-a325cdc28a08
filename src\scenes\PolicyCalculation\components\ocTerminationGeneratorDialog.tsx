import { <PERSON><PERSON>, DialogFooter, <PERSON><PERSON>Type, Spinner, SpinnerSize, mergeStyleSets, PrimaryButton, DialogContent, IChoiceGroupOption } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import { myTheme } from "../../../styles/theme";
import { LabeledTextField } from "../../../components/LabeledTextField";
import { DatePickerBase } from "../../BaseComponents/datePickerBase";
import { ChoiceGroupBase } from "../../BaseComponents/ChoiceGroupBase";
import { DropdownBase } from "../../BaseComponents";
import { enumToDropdownOptions } from "../../../utils/utils";
import { InsuranceCompanyNewOffer } from "../../../services/calculation/dto/insuranceCompanyNewOfferEnum";
import { OcTerminationReason } from "../../../services/calculation/dto/ocTerminationReasonEnums";
import moment from "moment";

const classNames = mergeStyleSets({
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    customChoiceGroupContainer: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
    }
});

export interface IOcTerminationGeneratorDialogProps {
    asyncActionInProgress: boolean;
    showDialog: boolean;
    toggleShowDialog: (value: boolean) => void;
    generateOcTermination: (formData: any) => void;
}

export class OcTerminationGeneratorDialog extends React.Component<IOcTerminationGeneratorDialogProps> {
    private formData: any = {
        oldPolicyNumber: '' as string,
        oldInsurerName: '' as string,
        terminationDate: '' as string,
        terminationReason: '' as string,
    };

    validateFormData(): boolean {
        let hasAnyError: boolean = false;

        for(let key in this.formData) {
            if(this.formData.hasOwnProperty(key) && (!this.formData[key] || this.formData[key].length === 0)) {
                hasAnyError = true;
            }
        }

        return hasAnyError;
    }

    render() {
        return <Dialog
            hidden={!this.props.showDialog}
            onDismiss={() => this.props.toggleShowDialog(false) }
            dialogContentProps={{
                type: DialogType.normal,
                title: L('OC termination'),
            }}
            modalProps={{
                isBlocking: true,
            }}
            minWidth={800}
        >
            <DialogContent>
                <DropdownBase 
                    isDataLoaded={true}
                    label={L("Insurance company name")}
                    disabled={this.props.asyncActionInProgress}
                    options={enumToDropdownOptions(InsuranceCompanyNewOffer, true, false, 'string')} 
                    value={this.formData.oldInsurerName}
                    required={true}
                    onChange={(value: string | number | undefined) => {
                        if(typeof value !== 'undefined') {
                            this.formData.oldInsurerName = value;
                            this.forceUpdate();
                        }
                    }}
                />

                <LabeledTextField
                    isDataLoaded={true}
                    label={L("Policy number")}
                    disabled={this.props.asyncActionInProgress}
                    value={this.formData.oldPolicyNumber}
                    required={true}
                    onChange={(event: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string | undefined) => {
                        if(typeof newValue !== 'undefined') {
                            this.formData.oldPolicyNumber = newValue;
                            this.forceUpdate();
                        }
                    }}
                />

                <DatePickerBase
                    isDataLoaded={true}
                    label={L("Termination date")}
                    disabled={this.props.asyncActionInProgress}
                    value={!!this.formData.terminationDate ? moment(this.formData.terminationDate) : this.formData.terminationDate}
                    required={true}
                    validationData={{'minDate': 'SUBTRACT_7_DAYS'}}
                    onChange={(value: string | undefined) => {
                        if(!!value) {
                            this.formData.terminationDate = moment(value).utc().format('YYYY-MM-DDTHH:mm:ss[Z]');
                            this.forceUpdate();
                        }
                    }}
                />

                <ChoiceGroupBase
                    label={L("The article with which the insurance contract is terminated")}
                    disabled={this.props.asyncActionInProgress}
                    value={this.formData.terminationReason}
                    required={true}
                    options={[
                        {key: OcTerminationReason.Last_period_day, text: L('Wypowiedzenie z końcem okresu ubezpieczenia art. 28')},
                        {key: OcTerminationReason.Another_insurer, text: L('Wypowiedzenie podwójnego ubezpieczenia art. 28a')},
                        {key: OcTerminationReason.After_vehicle_acquiring, text: L('Wypowiedzenie przez nabywcę pojazdu art. 31')},
                    ]}
                    customContainerClassNames={classNames.customChoiceGroupContainer}
                    onChange={(option: IChoiceGroupOption | undefined) => {
                        if(option && !!option.key) {
                            this.formData.terminationReason = option.key;
                            this.forceUpdate();
                        }
                    }}
                />
            </DialogContent>

            <DialogFooter>
                {this.props.asyncActionInProgress && (
                    <div style={{position: 'absolute', bottom: '0', left: '0'}}>
                        <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
                    </div>
                )}

                <PrimaryButton theme={myTheme} text={L('Save')} disabled={this.validateFormData()}
                    onClick={() => this.props.generateOcTermination(this.formData) } 
                />
            </DialogFooter>
        </Dialog>;
    }
}