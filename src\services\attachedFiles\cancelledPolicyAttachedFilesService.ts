import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { CrudServiceBase } from '../base/crudServiceBase';
import { CancelledPolicyAttachedFilesDto } from './cancelledPolicyAttachedFilesDto';
import { isUserLoggedIn } from '../../utils/authUtils';
import { getPartialModel } from '../../utils/modelUtils';

export class CancelledPolicyAttachedFilesService extends CrudServiceBase<CancelledPolicyAttachedFilesDto> {
    constructor() {
        super(Endpoint.CancelledPolicyAttachedFiles);
        this.internalHttp = httpApi;
    }

    public async createNew(createInsurerAttachedFileInput: CancelledPolicyAttachedFilesDto) {
        const copyCreateInsurerAttachedFileInput = getPartialModel(createInsurerAttachedFileInput, [], ['id']);

        let result = await httpApi.post(this.endpoint.Create(), copyCreateInsurerAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getAllFiles() {
        let result = await httpApi.get(this.endpoint.GetAll());
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByPolicyId(policyId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetByPolicyId?policyId=${policyId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportCancelledPolicyAttachedFilesService: CancelledPolicyAttachedFilesService = new CancelledPolicyAttachedFilesService();
export default exportCancelledPolicyAttachedFilesService;