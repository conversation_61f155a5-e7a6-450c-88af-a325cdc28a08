import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { CustomerPanel } from './customerPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { dateFormat, formatPhoneNumber } from "../../../utils/utils";
import { ClientDto } from "../../../services/client/dto/clientDto";
import { ClientTypeEnum } from "../../../services/client/clientTypeEnums";
import {additionalTheme, myTheme} from "../../../styles/theme";
import clientService from "../../../services/client/clientService";
import { Dialog, DialogType } from "@fluentui/react";

export class CustomerTableForPopUp extends FluentTableBase<ClientDto> {
  private shouldReloadItems: boolean = false;
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";
  
  disableGetAllOnMount = true;

  getItemDisplayNameOf(item: ClientDto): string {
    if(!!item.company) {
      return item.company;
    } else if(!!item.user) {
      return `${item.user.name} ${item.user.surname}`; 
    } else {
      return item.id;
    }
  }

  getColumns(): ITableColumn[] {
    return CustomerTableForPopUp.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Name'),
        fieldName: 'company',
        minWidth: 200,
        maxWidth: 250,
        onRender: (item: any): any => {
          if(item.clientType === ClientTypeEnum.Individual || item.clientType === null) { // || item.clientType === ClientTypeEnum.SoleTrader
            return `${item.user.name} ${item.user.surname}`;
          } else {
            return !!item.company ? item.company : (!!item.user ? `${item.user.name} ${item.user.surname}` : '');
          }
        }
      },
      {
        name: L('First name'),
        fieldName: 'user.name',
        minWidth: 80,
        maxWidth: 100,
        onRender: (item: any): any => {
          if(item.clientType === ClientTypeEnum.Individual || item.clientType === ClientTypeEnum.SoleTrader) {
            return item.user && !!item.user.name ? item.user.name : '';
          } else {
            return '';
          }
        },
      },
      {
        name: L('Last name'),
        fieldName: 'user.surname',
        minWidth: 80,
        maxWidth: 100,
        onRender: (item: any): any => {
          if(item.clientType === ClientTypeEnum.Individual || item.clientType === ClientTypeEnum.SoleTrader) {
            return item.user && !!item.user.surname ? item.user.surname : '';
          } else {
            return '';
          }
        },
      },
      {
        name: L('Pesel'),
        fieldName: 'pesel',
        minWidth: 80,
        maxWidth: 100,
        onRender: (item: ClientDto): any => {
          if(item.clientType === ClientTypeEnum.Individual || item.clientType === ClientTypeEnum.SoleTrader) {
            return item.pesel;
          } else {
            return '';
          }
        }
      },
      {
        name: L('Regon'),
        fieldName: 'regon',
        minWidth: 80,
        maxWidth: 100,
      },
      {
        name: L('NIP'),
        fieldName: 'nip',
        minWidth: 80,
        maxWidth: 100,
      },
      {
        name: L('E-mail'),
        fieldName: 'user.emailAddress',
        onRender: (item: any): any => {
          return item.user.emailAddress;
        }
      },
      {
        name: L('Additional e-mail'),
        fieldName: 'emailAdditional',
      },
      {
        name: L('Phone'),
        fieldName: 'phone',
        onRender: (item: any): any => {
          return (item.phone && !!item.phone) ? formatPhoneNumber(item.phone) : '';
        }
      },
      {
        name: L('Note'),
        fieldName: 'note',
        onRender: (item: any): any => {
          return <span title={item.note}>{item.note}</span>
        }
      },
      {
        name: L('Registered'),
        fieldName: 'creationTime',
        onRender: (item: any): any => {
          return dateFormat(item.creationTime);
        }
      },
      {
        name: L('Customer type'),
        fieldName: 'clientType',
        onRender: (item: ClientDto): any => {
          return L(item.clientType);
        }
      },
    ];
  }

  onItemInvoked = (item: any): void => {
    // this.props.history.push(`/${RouterPath.PolicyCalculation}/?entityType=customer&entityId=${item.id}`);
    if(this.props.onFillDataButtonClick)
      this.props.onFillDataButtonClick();
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: false,
      customActions: true,
    };
  }

  private reloadListOnDialogClose() {
    this.showPopUpDialog = false;

    if(this.shouldReloadItems) {
      this.reloadItems();
    }

    this.forceUpdate();
  }

  private async reloadItems() {
    this.selectionSetAllSelected(false);
    if(typeof this.props.refreshItems !== 'undefined') {
      await this.props.refreshItems!();
    } else {
      this.togglePopUpDialog("Error", "The list could not be refreshed automatically, please refresh the page manually.");
    }
  }

  private togglePopUpDialog(title: string, text: string, visibility?: boolean) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.forceUpdate();
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    return {
      ...props,
      customActionsProps: [{
        displayFor: 'single',
        buttonText: L("Duplicate"),
        buttonIcon: "none",
        buttonColor: myTheme.palette.black,
        buttonIconColor: myTheme.palette.black,
        buttonBackground: additionalTheme.darkerWhite,
      },
      {
        displayFor: 'single',
        buttonText: L("Fill in the form with the data of the selected customer"),
        buttonIcon: "none",
        buttonColor: myTheme.palette.black,
        buttonIconColor: myTheme.palette.black,
        buttonBackground: additionalTheme.darkerWhite,
      }],
      customActions: [
        async (item: ClientDto) => {
          let copyResult = await clientService.copyClient(parseInt(item.id));

          if(copyResult && copyResult.hasOwnProperty('id')) {
            this.reloadItems();
            this.createOrUpdateModalOpen(copyResult);
          } else {
            this.togglePopUpDialog("Error", "Error occured during attempt of client duplicate.");
          }
        },
        (item: ClientDto) => {
          if(this.props.onFillDataButtonClick)
            this.props.onFillDataButtonClick();
        }
      ]
    }
  }

  getTitle(): string {
    return L('Customer list');
  }
  
  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
        <Dialog
          hidden={!this.showPopUpDialog}
          onDismiss={() => this.reloadListOnDialogClose()}
          dialogContentProps={{
              type: DialogType.normal,
              title: L(this.popUpDialogTitle),
              subText: L(this.popUpDialogText),
          }}
          modalProps={{
              isBlocking: true
          }}
        >
      </Dialog>
      
      <CustomerPanel
        {...props}
      />

    </>
      ;
  }
  
  copyAndSort<T>(items: T[], columnKey: string, isSortedDescending?: boolean): T[] {
    const key = columnKey as keyof T;
    if(key === 'company') {
      let concatColumn: any[] = [];
      items.forEach((item: any, index: number) => {
        if(item.clientType === ClientTypeEnum.Individual) {
          concatColumn.push({index: index, name: `${item.user.name} ${item.user.surname}`});
        } else {
          concatColumn.push({index: index, name: `${item.company}`});
        }
      });
      concatColumn.sort((a: any, b: any) => { 
        if(a.name < b.name)
          return isSortedDescending ? -1 : 1;
        if(a.name > b.name)
          return isSortedDescending ? 1 : -1;
        return 0;
      });

      let sortedItems: any[] = [];
      concatColumn.forEach((col: any) => {
        sortedItems.push(items[col.index]);
      });
      return sortedItems;
    } else {
      return items.slice(0).sort((a: any, b: any) => { 
        return (isSortedDescending ? a[key] < b[key] : a[key] > b[key]) ? 1 : -1;
      });
    }
  }
}