import { CommandBar, ICommandBarItemProps } from '@fluentui/react';
import { L } from "../../lib/abpUtility";
import { myTheme } from "../../styles/theme";
import { CommandBarBase, ICommandBarBaseProps, classNames, CustomButton } from "./commandBarBase";

export interface ISendMessageCommandBarBase<T> extends ICommandBarBaseProps<T> {
  onSendMessageForParents?: () => void;
  onSendMessageForPlayers?: () => void;
  onSendMessageForAll?: () => void;
}

export class SendMessageCommandBarBase<T, TProps extends ISendMessageCommandBarBase<T> = ISendMessageCommandBarBase<T>> extends CommandBarBase<T, TProps> {

  getForNone(): ICommandBarItemProps[] {
    return [];
  }

  protected getSendMessageForParents(): ICommandBarItemProps {
    var cmdButtonSendForParents = this.getMsg();
    cmdButtonSendForParents.key = 'cmdButtonSendForParents';
    cmdButtonSendForParents.text = L('MSG Parents');
    cmdButtonSendForParents.onClick = this.props.onSendMessageForParents;
    return cmdButtonSendForParents;
  }

  protected getSendMessageForPlayers(): ICommandBarItemProps {
    var cmdButtonSendForPlayers = this.getMsg();
    cmdButtonSendForPlayers.key = 'cmdButtonSendForPlayers';
    cmdButtonSendForPlayers.text = L('MSG Players');
    cmdButtonSendForPlayers.onClick = this.props.onSendMessageForPlayers;
    return cmdButtonSendForPlayers;
  }

  protected getSendMessageForAll(): ICommandBarItemProps {
    var cmdButtonSendForSquad = this.getMsg();
    cmdButtonSendForSquad.key = 'cmdButtonSendForAll';
    cmdButtonSendForSquad.text = L('MSG For All');
    cmdButtonSendForSquad.onClick = this.props.onSendMessageForAll;
    return cmdButtonSendForSquad;
  }

  protected createSendMessageButtons(): ICommandBarItemProps[] {
    var items: ICommandBarItemProps[] = [];
    if (this.props.onSendMessageForParents) items.push(this.getSendMessageForParents());
    if (this.props.onSendMessageForPlayers) items.push(this.getSendMessageForPlayers());
    if (this.props.onSendMessageForAll) items.push(this.getSendMessageForAll());
    return items;
  }

  protected createSendMessageCommandBarItems(): ICommandBarItemProps[] {
    const selectionCount = this.getSelectedCount();
    switch (selectionCount) {
      case 0:
        return this.getForNone();
      default:
        return this.createSendMessageButtons();
    }
  }

  private renderSendMessageCommandBar(): JSX.Element {
    var items = this.createSendMessageCommandBarItems();
    return <CommandBar theme={myTheme} items={items} className={classNames.customCommandbarMsg} buttonAs={CustomButton} />
  }

  protected renderCommandBarBase() {
    return <CommandBarBase<T> {...this.props} />
  }

  render() {
    return <>
      <div style={{
        display: 'flex',
        flexDirection: 'row',
      }}>
        {this.renderCommandBarBase()}
        {this.renderSendMessageCommandBar()}
        <div style={{
          width: 137,
        }}/>
      </div>
      
    </>
  }
}