import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { ServiceBase } from '../base/serviceBase';
import { ClientAttachedFilesDto } from './clientAttachedFilesDto';

export class ClientAttachedFilesService extends ServiceBase {
    constructor() {
        super(Endpoint.ClientAttachedFiles);
    }

    public async sendAgreements(payload: string, clientId: number) {
        let result = await httpApi.post(this.endpoint.Custom(`SendAgreements`), {payload, clientId});
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async createNew(createClientAttachedFileInput: ClientAttachedFilesDto) {
        let result = await httpApi.post(this.endpoint.Create(), createClientAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async update(updateClientAttachedFileInput: ClientAttachedFilesDto) {
        let result = await httpApi.put(this.endpoint.Update(), updateClientAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getAllFiles() {
        let result = await httpApi.get(this.endpoint.GetAll());
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async checkIsClientHaveAgreements(clientId: string) {
        let result = await httpApi.get(this.endpoint.Custom(`CheckIsClientHaveAgreements/?clientId=${clientId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async sendPolicyMail(policyId: number) {
        let result = await httpApi.post(this.endpoint.Custom(`SendPolicyMail/?policyId=${policyId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async sendClientAgreements(clientId: string, mandatoryAgreement: boolean, commercialAgreement: boolean, withSmsAcceptance: boolean) {
        const payload = { clientId, mandatoryAgreement, commercialAgreement, withSmsAcceptance };
        let result = await httpApi.post(this.endpoint.Custom("SendClientAgreements"), payload);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async sendSmsCode(clientId: string) {
        let result = await httpApi.post(this.endpoint.Custom(`SendSmsCode/?clientId=${clientId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async checkSmsCode(clientId: string, code: string) {
        let result = await httpApi.post(this.endpoint.Custom(`CheckSmsCode/?clientId=${clientId}&code=${code}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async uploadScan(clientId: string, fileUrl: string) {
        let result = await httpApi.post(this.endpoint.Custom(`UploadScan/?clientId=${clientId}&fileUrl=${fileUrl}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getLastUploadedScan(clientId: string) {
        let result = await httpApi.get(this.endpoint.Custom(`GetLastUploadedScan/?clientId=${clientId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async cancelAgreement(clientId: string, comment: string) {
        let result = await httpApi.post(this.endpoint.Custom(`CancelAgreement?clientId=${clientId}&comment=${comment}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getClientAgreementsHistory(clientId: string) {
        let result = await httpApi.get(this.endpoint.Custom(`GetClientAgreementsHistory/?clientId=${clientId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
    
    public async sendSmsCodeByUuid(uuid: string) {
        let result = await httpApi.post(this.endpoint.Custom(`SendSmsCodeByUuid/${uuid}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
    
    public async getPdfLink(clientId: string) {
        let result = await httpApi.get(this.endpoint.Custom(`GetClientAgreementsPdfLinkByClientId/?clientId=${clientId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportClientAttachedFilesService: ClientAttachedFilesService = new ClientAttachedFilesService();
export default exportClientAttachedFilesService;