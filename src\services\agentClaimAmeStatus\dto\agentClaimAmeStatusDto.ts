import { AgentClaimDto } from "../../agentClaim/dto/agentClaimDto";
import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";

export interface AgentClaimAmeStatusDto extends BaseApiEntityModel {
    claimId: number;
    claim: AgentClaimDto;
    appraiserFV: number,
    pricing: number,
    approvedQuotation: number,
    lastModificationTime: string,
    lastModifierUserId: number,
    creationTime: string,
    creatorUserId: number,
    currency: string
}