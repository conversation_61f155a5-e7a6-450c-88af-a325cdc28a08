import { Icon, Layer, mergeStyleSets } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import { additionalTheme, myTheme } from "../../../styles/theme";

const classNames = mergeStyleSets({
    fontBold: {
        fontWeight: '600',
        color: additionalTheme.darkerRed
    },
    layer: {
        position: 'absolute',
        top: '70px',
        left: '5px',
        selectors: {
            '& .ms-Layer-content': {
                color: myTheme.palette.black,
            }
        }
    },
});

export interface IPolicyFormStepsLayerProps {
    step: number;
    hostId: string;
}

export class PolicyFormStepsLayer extends React.Component<IPolicyFormStepsLayerProps> {
    render() {
        return <Layer hostId={this.props.hostId} className={classNames.layer}>
            <p>
                <span className={this.props.step === 1 ? classNames.fontBold : ''}>{L('Customer data')}</span>
                <Icon iconName="Forward" style={{marginRight: '25px', marginLeft: '25px'}} />
                <span className={this.props.step === 2 ? classNames.fontBold : ''}>{L('Complete the application')}</span>
            </p>
        </Layer>;
    }
}