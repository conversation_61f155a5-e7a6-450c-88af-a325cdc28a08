import { IconButton, mergeStyleSets, Pivot, PivotItem, PrimaryButton, Spinner, SpinnerSize, Stack, Text } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { myTheme } from '../../../styles/theme';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { AgentClaimAmeStatusDto } from '../../../services/agentClaimAmeStatus/dto/agentClaimAmeStatusDto';
import { defaultAgentClaimAmeStatus } from '../../../stores/agentClaimAmeStatusStore';
import { dateFormat, enumToDropdownOptions } from '../../../utils/utils';
import React from 'react';
import { uploadFileToAzure } from '../../../services/azureService';
import agentClaimAmeStatusAttachedFilesService from '../../../services/agentClaimAmeStatus/agentClaimAmeStatusAttachedFilesService';
import { AgentClaimAmeStatusAttachedFilesDto } from '../../../services/agentClaimAmeStatus/dto/agentClaimAmeStatusAttachedFilesDto';
import { AgentClaimAmeStatusTypeEnum } from '../../../services/agentClaimAmeStatus/agentClaimAmeStatusTypeEnums';
import { AgentClaimStatusCurrencyEnum } from '../../../services/agentClaimAmeStatus/agentClaimStatusCurrencyEnum';

const classNames = mergeStyleSets({
    callout: {
        width: 320,
        maxWidth: '90%',
        padding: '20px',
        cursor: 'pointer',
    },
    calloutText: {
        whiteSpace: 'pre-line',
    },    
    fileInputContainer: {
        display: 'flex',
        alignItems: 'center'
    },
    uploadButton: {
        width: 'fit-content',
        padding: '25px 50px',
        marginTop: '20px',
    },
    fileInputLabel: {
        fontWeight: 'bold',
        marginRight: '20px',
        width: '200px',
    },
    attachedFilesLabel: {
        fontWeight: 'bold',
        marginTop: '20px',
        marginBottom: '10px',
    },
    attachedFilesListItem: {
        listStyleType: 'decimal',
        marginBottom: '15px',
        padding: '10px',
        background: myTheme.palette.themeLighterAlt,
    },
    recommendedIcon: {
        color: myTheme.palette.yellow,
        marginLeft: '15px',
        fontSize: '15px',
    },
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
});

export class AgentClaimAmeStatusContentView extends GenericContentView {
    private agentClaimAmeStatus: AgentClaimAmeStatusDto = defaultAgentClaimAmeStatus;
    private claimId: number = this.props.customData.claimId;
    private fileUploadInputOpinionRef: any;
    private fileUploadInputQuotationRef: any;
    private fileUploadInputApprovedQuotationRef: any;
    private fileUploadInputFvRef: any;
    private asyncActionInProgressOpinion: boolean = false;
    private asyncActionInProgressQuotation: boolean = false;
    private asyncActionInProgressApprovedQuotation: boolean = false;
    private asyncActionInProgressFv: boolean = false;

    private opinionFiles: any = {
        totalCount: 0,
        items: []
    };
    
    private quotationFiles: any = {
        totalCount: 0,
        items: []
    };  

    private approvedQuotationFiles: any = {
        totalCount: 0,
        items: []
    };  

    private fvFiles: any = {
        totalCount: 0,
        items: []
    };

    constructor(props: any) {
        super(props);
        this.fileUploadInputOpinionRef = React.createRef();
        this.fileUploadInputQuotationRef = React.createRef();
        this.fileUploadInputApprovedQuotationRef = React.createRef();
        this.fileUploadInputFvRef = React.createRef();
    }

    async componentDidMount() {
        this.checkIfDataIsLoaded("agentClaimAmeStatus");

        if (this.claimId) {
            this.state.model.value.claimId = this.claimId;
        }

        if(this.agentClaimAmeStatus.id) {
            this.asyncActionInProgressOpinion = true;
            this.asyncActionInProgressQuotation = true;
            this.asyncActionInProgressApprovedQuotation = true;
            this.asyncActionInProgressFv = true;

            await agentClaimAmeStatusAttachedFilesService.getOpinionsByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                if(response && response.items) {
                    this.opinionFiles.items = response.items
                }
            }).catch((error: any) => {
                console.error(error);
            });
            this.asyncActionInProgressOpinion = false;
            await agentClaimAmeStatusAttachedFilesService.getQuotationByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                if(response && response.items) {
                    this.quotationFiles.items = response.items
                }
            }).catch((error: any) => {
                console.error(error);
            });
            this.asyncActionInProgressQuotation = false;
            await agentClaimAmeStatusAttachedFilesService.getFileApprovedQuotationByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                if(response && response.items) {
                    this.approvedQuotationFiles.items = response.items
                }
            }).catch((error: any) => {
                console.error(error);
            });
            this.asyncActionInProgressApprovedQuotation = false;
            await agentClaimAmeStatusAttachedFilesService.getFvByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                if(response && response.items) {
                    this.fvFiles.items = response.items
                }
            }).catch((error: any) => {
                console.error(error);
            });
            this.asyncActionInProgressFv = false;
            this.forceUpdate(); 
        }
    }

    private triggerUpload = (type: AgentClaimAmeStatusTypeEnum) => {
        if(type === AgentClaimAmeStatusTypeEnum.Opinion) {
            this.fileUploadInputOpinionRef.current.click();
        } else if (type === AgentClaimAmeStatusTypeEnum.Quotation) {
            this.fileUploadInputQuotationRef.current.click();
        } else if (type === AgentClaimAmeStatusTypeEnum.FileApprovedQuotation) {
            this.fileUploadInputApprovedQuotationRef.current.click();
        } else if (type === AgentClaimAmeStatusTypeEnum.FV) {
            this.fileUploadInputFvRef.current.click();
        } 
    };

    private async onUpload(type: AgentClaimAmeStatusTypeEnum) {
        let selectedFile;

        if(type === AgentClaimAmeStatusTypeEnum.Opinion) {
            this.asyncActionInProgressOpinion = true;
            selectedFile = !!this.fileUploadInputOpinionRef.current.files && this.fileUploadInputOpinionRef.current.files.length ? this.fileUploadInputOpinionRef.current.files[0] : null;
        } else if (type === AgentClaimAmeStatusTypeEnum.Quotation) {
            this.asyncActionInProgressQuotation = true;
            selectedFile = !!this.fileUploadInputQuotationRef.current.files && this.fileUploadInputQuotationRef.current.files.length ? this.fileUploadInputQuotationRef.current.files[0] : null;
        } else if (type === AgentClaimAmeStatusTypeEnum.FileApprovedQuotation) {
            this.asyncActionInProgressApprovedQuotation = true;
            selectedFile = !!this.fileUploadInputApprovedQuotationRef.current.files && this.fileUploadInputApprovedQuotationRef.current.files.length ? this.fileUploadInputApprovedQuotationRef.current.files[0] : null;
        } else if (type === AgentClaimAmeStatusTypeEnum.FV) {
            this.asyncActionInProgressFv = true;
            selectedFile = !!this.fileUploadInputFvRef.current.files && this.fileUploadInputFvRef.current.files.length ? this.fileUploadInputFvRef.current.files[0] : null;
        } 

        this.forceUpdate();

        let result = await uploadFileToAzure(selectedFile);

        if(!!this.agentClaimAmeStatus.id) {
            await agentClaimAmeStatusAttachedFilesService.createNew({
                "id": "0",
                "ameStatusId": parseInt(this.agentClaimAmeStatus.id),
                "ameStatus": this.props.customData.agentClaim,
                "agentClaimAmeStatusFileType": type,
                "fileUrl": result.url,
                "originalFileName": selectedFile.name,
                "blobFileName": result.name,
                "description": "",
            }).then(async (response: any) => {
                if(type === AgentClaimAmeStatusTypeEnum.Opinion && this.agentClaimAmeStatus.id) {
                    await agentClaimAmeStatusAttachedFilesService.getOpinionsByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                        if(response && response.items) {
                            this.opinionFiles.items = response.items;
                        }
                    }).catch((error: any) => {
                        console.error(error);
                    });
                    this.asyncActionInProgressOpinion = false;
                    this.forceUpdate();
                } else if (type === AgentClaimAmeStatusTypeEnum.Quotation && this.agentClaimAmeStatus.id) {
                    await agentClaimAmeStatusAttachedFilesService.getQuotationByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                        if(response && response.items) {
                            this.quotationFiles.items = response.items;
                        }
                    }).catch((error: any) => {
                        console.error(error);
                    });
                    this.asyncActionInProgressQuotation = false;
                    this.forceUpdate(); 
                } else if (type === AgentClaimAmeStatusTypeEnum.FileApprovedQuotation && this.agentClaimAmeStatus.id) {
                    await agentClaimAmeStatusAttachedFilesService.getFileApprovedQuotationByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                        if(response && response.items) {
                            this.approvedQuotationFiles.items = response.items;
                        }
                    }).catch((error: any) => {
                        console.error(error);
                    });
                    this.asyncActionInProgressApprovedQuotation = false;
                    this.forceUpdate(); 
                } else if (type === AgentClaimAmeStatusTypeEnum.FV && this.agentClaimAmeStatus.id) {
                    await agentClaimAmeStatusAttachedFilesService.getFvByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                        if(response && response.items) {
                            this.fvFiles.items = response.items;
                        }
                    }).catch((error: any) => {
                        console.error(error);
                    });
                    this.asyncActionInProgressFv = false;
                    this.forceUpdate(); 
                } 
            }).catch((error: any) => {
                console.error(error);
        
                this.asyncActionInProgressOpinion = false;
                this.asyncActionInProgressQuotation = false;
                this.asyncActionInProgressApprovedQuotation = false;
                this.asyncActionInProgressFv = false;
                this.forceUpdate();
            });
        } else if(this.props.customData && this.props.customData.passUploadedFilesResults) {
            const payload: any = {
                "id": "0",
                "ameStatusId": null,
                "ameStatus": this.props.customData.agentClaim,
                "agentClaimAmeStatusFileType": type,
                "fileUrl": result.url,
                "originalFileName": selectedFile.name,
                "blobFileName": result.name,
                "description": "",
            };

            this.props.customData.passUploadedFilesResults(type, payload);

            const currentDate = + new Date();

            if(type === AgentClaimAmeStatusTypeEnum.Opinion) {
                this.opinionFiles.items.push({...payload, "creationTime": currentDate});
                this.asyncActionInProgressOpinion = false;
                this.forceUpdate();
            } else if (type === AgentClaimAmeStatusTypeEnum.Quotation) {
                this.quotationFiles.items.push({...payload, "creationTime": currentDate});
                this.asyncActionInProgressQuotation = false;
                this.forceUpdate(); 
            } else if (type === AgentClaimAmeStatusTypeEnum.FileApprovedQuotation) {
                this.approvedQuotationFiles.items.push({...payload, "creationTime": currentDate});
                this.asyncActionInProgressApprovedQuotation = false;
                this.forceUpdate(); 
            } else if (type === AgentClaimAmeStatusTypeEnum.FV) {
                this.fvFiles.items.push({...payload, "creationTime": currentDate});
                this.asyncActionInProgressFv = false;
                this.forceUpdate(); 
            }
        } else {
            this.asyncActionInProgressOpinion = false;
            this.asyncActionInProgressQuotation = false;
            this.asyncActionInProgressApprovedQuotation = false;
            this.asyncActionInProgressFv = false;
            this.forceUpdate();
        }
    }

    async deleteAttachedFile(file: AgentClaimAmeStatusAttachedFilesDto) {
        if(file.agentClaimAmeStatusFileType === AgentClaimAmeStatusTypeEnum.Opinion) {
            this.asyncActionInProgressOpinion = true;
        } else if (file.agentClaimAmeStatusFileType === AgentClaimAmeStatusTypeEnum.Quotation) {
            this.asyncActionInProgressQuotation = true;
        } else if (file.agentClaimAmeStatusFileType === AgentClaimAmeStatusTypeEnum.FileApprovedQuotation) {
            this.asyncActionInProgressApprovedQuotation = true;
        } else if (file.agentClaimAmeStatusFileType === AgentClaimAmeStatusTypeEnum.FV) {
            this.asyncActionInProgressFv = true;
        } 
        this.forceUpdate();
    
        await agentClaimAmeStatusAttachedFilesService.delete(file).then(async (deleteResponse: any) => {
            if(file.agentClaimAmeStatusFileType === AgentClaimAmeStatusTypeEnum.Opinion) {
                await agentClaimAmeStatusAttachedFilesService.getOpinionsByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                    if(response && response.items) {
                        this.opinionFiles.items = response.items
                    }
                }).catch((error: any) => {
                    console.error(error);
                });
                this.asyncActionInProgressOpinion = false;
                this.forceUpdate();
            } else if (file.agentClaimAmeStatusFileType === AgentClaimAmeStatusTypeEnum.Quotation) {
                await agentClaimAmeStatusAttachedFilesService.getQuotationByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                    if(response && response.items) {
                        this.quotationFiles.items = response.items
                    }
                }).catch((error: any) => {
                    console.error(error);
                });
                this.asyncActionInProgressQuotation = false;
                this.forceUpdate(); 
            } else if (file.agentClaimAmeStatusFileType === AgentClaimAmeStatusTypeEnum.FileApprovedQuotation) {
                await agentClaimAmeStatusAttachedFilesService.getFileApprovedQuotationByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                    if(response && response.items) {
                        this.approvedQuotationFiles.items = response.items
                    }
                }).catch((error: any) => {
                    console.error(error);
                });
                this.asyncActionInProgressApprovedQuotation = false;
                this.forceUpdate(); 
            } else if (file.agentClaimAmeStatusFileType === AgentClaimAmeStatusTypeEnum.FV) {
                await agentClaimAmeStatusAttachedFilesService.getFvByAmeStatus(parseInt(this.agentClaimAmeStatus.id)).then((response: any) => {
                    if(response && response.items) {
                        this.fvFiles.items = response.items
                    }
                }).catch((error: any) => {
                    console.error(error);
                });
                this.asyncActionInProgressFv = false;
                this.forceUpdate(); 
            } 
        }).catch((error: any) => {
            console.error(error);
            this.asyncActionInProgressOpinion = false;
            this.asyncActionInProgressQuotation = false;
            this.asyncActionInProgressApprovedQuotation = false;
            this.asyncActionInProgressFv = false;
            this.forceUpdate();
        });
    }

    private createFileList(files: any[]): JSX.Element[] {
        const list: JSX.Element[] = [];
    
        if(files && files.length > 0) {
            files.forEach((file: any) => {
                list.push(
                    <li key={file.id} className={classNames.attachedFilesListItem}>
                        <IconButton
                            styles={{
                                root: {
                                    color: myTheme.palette.red,
                                    marginRight: '2px',
                                },
                                rootHovered: {
                                    color: myTheme.palette.redDark,
                                },
                            }}
                            iconProps={{ iconName: 'Delete' }}
                            ariaLabel={L("Close popup modal")}
                            onClick={() => { 
                                this.deleteAttachedFile(file);
                            }}
                        />
                        {dateFormat(file.creationTime, "DD.MM.YYYY HH:mm", true)}{` - `} 
                        <a href={file.fileUrl} title={L("Download file")}>{file.originalFileName}</a>
                    </li>
                );
            });
        }
        return list;
    }

    renderContent() { 
        this.agentClaimAmeStatus = this.props.payload.model ? this.props.payload.model : this.props.payload;
        let opinionAttachedFilesList: JSX.Element[] = this.createFileList(this.opinionFiles.items);
        let quotationAttachedFilesList: JSX.Element[] = this.createFileList(this.quotationFiles.items);
        let approvedQuotationAttachedFilesList: JSX.Element[] = this.createFileList(this.approvedQuotationFiles.items);
        let fvAttachedFilesList: JSX.Element[] = this.createFileList(this.fvFiles.items);

        return (
            <Pivot theme={myTheme}>
                <PivotItem headerText={L("General")} key={"General"}>
                    {this.renderElement(new ContentViewModelProperty("appraiserFV", L("Appraiser FV"), Controls.Text, true, [], false, { isDataLoaded: this.isDataLoaded, textType: 'number' }), [], { appraiserFV: this.agentClaimAmeStatus.appraiserFV })}
                    {this.renderElement(new ContentViewModelProperty("pricing", L("Pricing"), Controls.Text, true, [], false, { isDataLoaded: this.isDataLoaded, textType: 'number' }), [], { pricing: this.agentClaimAmeStatus.pricing })}
                    {this.renderElement(new ContentViewModelProperty("approvedQuotation", L("Approved quotation"), Controls.Text, true, [], false, { isDataLoaded: this.isDataLoaded, textType: 'number' }), [], { approvedQuotation: this.agentClaimAmeStatus.approvedQuotation })}
                    {this.renderElement(new ContentViewModelProperty("currency", L("Currency"), Controls.Picker, true, {dropdown: enumToDropdownOptions(AgentClaimStatusCurrencyEnum, true, true, "string")}, false, { isDataLoaded: this.isDataLoaded}), [], { currency: this.agentClaimAmeStatus.currency })}

                    <>
                        <Stack horizontal={false} verticalAlign={'center'} style={{border: '1px solid #000', padding: '10px 20px 10px', marginTop: '15px'}}>
                            <Stack horizontal={true} className={classNames.fileInputContainer}>
                                <Text variant="large" className={classNames.fileInputLabel}>
                                    { L('File Opinion') }
                                </Text>

                                <input ref={this.fileUploadInputOpinionRef} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUpload(AgentClaimAmeStatusTypeEnum.Opinion)} />
                                <PrimaryButton className={classNames.uploadButton} theme={myTheme} text={L('Upload file')} type={'file'}
                                    disabled={this.asyncActionInProgressOpinion} iconProps={{ iconName: 'Upload' }} style={{marginTop: '0px'}}
                                    onClick={() => this.triggerUpload(AgentClaimAmeStatusTypeEnum.Opinion)}
                                />

                                {this.asyncActionInProgressOpinion && (
                                    <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                                )}
                            </Stack>
                            
                            { opinionAttachedFilesList.length > 0 && <Stack>
                                <Text variant="large" className={classNames.attachedFilesLabel}>
                                    { L('Attached files:') }
                                </Text>

                                <ul>
                                    { opinionAttachedFilesList }
                                </ul>
                            </Stack> }
                        </Stack>
                            
                        <Stack horizontal={false} verticalAlign={'center'} style={{border: '1px solid #000', padding: '10px 20px 10px', marginTop: '15px'}}>
                            <Stack horizontal={true} className={classNames.fileInputContainer}>
                                <Text variant="large" className={classNames.fileInputLabel}>
                                    { L('File Quotation') }
                                </Text>

                                <input ref={this.fileUploadInputQuotationRef} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUpload(AgentClaimAmeStatusTypeEnum.Quotation)} />
                                <PrimaryButton className={classNames.uploadButton} theme={myTheme} text={L('Upload file')} type={'file'}
                                    disabled={this.asyncActionInProgressQuotation} iconProps={{ iconName: 'Upload' }} style={{marginTop: '0px'}}
                                    onClick={() => this.triggerUpload(AgentClaimAmeStatusTypeEnum.Quotation)}
                                />

                                {this.asyncActionInProgressQuotation && (
                                    <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                                )}
                            </Stack>

                            { quotationAttachedFilesList.length > 0 && <Stack>
                                <Text variant="large" className={classNames.attachedFilesLabel}>
                                    { L('Attached files:') }
                                </Text>

                                <ul>
                                    { quotationAttachedFilesList }
                                </ul>
                            </Stack> }
                        </Stack>

                        <Stack horizontal={false} verticalAlign={'center'} style={{border: '1px solid #000', padding: '10px 20px 10px', marginTop: '15px'}}>
                            <Stack horizontal={true} className={classNames.fileInputContainer}>
                                <Text variant="large" className={classNames.fileInputLabel}>
                                    { L('File approved quotation') }
                                </Text>

                                <input ref={this.fileUploadInputApprovedQuotationRef} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUpload(AgentClaimAmeStatusTypeEnum.FileApprovedQuotation)} />
                                <PrimaryButton className={classNames.uploadButton} theme={myTheme} text={L('Upload file')} type={'file'}
                                    disabled={this.asyncActionInProgressApprovedQuotation} iconProps={{ iconName: 'Upload' }} style={{marginTop: '0px'}}
                                    onClick={() => this.triggerUpload(AgentClaimAmeStatusTypeEnum.FileApprovedQuotation)}
                                />

                                {this.asyncActionInProgressApprovedQuotation && (
                                    <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                                )}
                            </Stack>

                            { approvedQuotationAttachedFilesList.length > 0 && <Stack>
                                <Text variant="large" className={classNames.attachedFilesLabel}>
                                    { L('Attached files:') }
                                </Text>

                                <ul>
                                    { approvedQuotationAttachedFilesList }
                                </ul>
                            </Stack> }
                        </Stack>

                        <Stack horizontal={false} verticalAlign={'center'} style={{border: '1px solid #000', padding: '10px 20px 10px', marginTop: '15px'}}>
                            <Stack horizontal={true} className={classNames.fileInputContainer}>
                                <Text variant="large" className={classNames.fileInputLabel}>
                                    { L('Fv') }
                                </Text>

                                <input ref={this.fileUploadInputFvRef} type="file" accept="application/pdf" style={{display: 'none'}} onChange={() => this.onUpload(AgentClaimAmeStatusTypeEnum.FV)} />
                                <PrimaryButton className={classNames.uploadButton} theme={myTheme} text={L('Upload file')} type={'file'}
                                    disabled={this.asyncActionInProgressFv} iconProps={{ iconName: 'Upload' }} style={{marginTop: '0px'}}
                                    onClick={() => this.triggerUpload(AgentClaimAmeStatusTypeEnum.FV)}
                                />

                                {this.asyncActionInProgressFv && (
                                    <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                                )}
                            </Stack>

                            { fvAttachedFilesList.length > 0 && <Stack>
                                <Text variant="large" className={classNames.attachedFilesLabel}>
                                    { L('Attached files:') }
                                </Text>

                                <ul>
                                    { fvAttachedFilesList }
                                </ul>
                            </Stack> }
                        </Stack>
                    </>
                </PivotItem>
            </Pivot>
        );
    }
}