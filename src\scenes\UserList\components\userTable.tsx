import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { UserDto } from '../../../services/user/dto/userDto';
import { ICrudPermissons } from '../../BaseComponents/commandBarBase';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { UserPanel } from './userPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import {additionalTheme, myTheme} from "../../../styles/theme";
import { formatPhoneNumber } from "../../../utils/utils";

export class UserTable extends FluentTableBase<UserDto> {

  getColumns(): ITableColumn[] {
    return UserTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    const storedUser: string | null = localStorage.getItem('AzureB2CStorageKey');
    let userIsAdmin: boolean = false;

    if (storedUser !== null) {
        const user: any = JSON.parse(storedUser);
        const tempExtensionRole = user.b2c.idTokenClaims.extension_Role.split(',');

        if(tempExtensionRole.includes('Admin')) {
          userIsAdmin = true;
        }
    }

    const tableColumns: ITableColumn[] = [
      {
        name: L('UserName'),
        fieldName: 'userName',
      },
      {
        name: L('CustomerName'),
        fieldName: 'name',
      },
      {
        name: L('Surname'),
        fieldName: 'surname',
      },
      {
        name: L('E-mail'),
        fieldName: 'emailAddress',
      },
      {
        name: L('Phone'),
        fieldName: 'phoneNumber',
        onRender: (item: any): any => {
          return (item.phoneNumber && !!item.phoneNumber) ? formatPhoneNumber(item.phoneNumber) : '';
        }
      },
      {
        name: L('Roles'),
        fieldName: 'roleNames',
        onRender: (item: UserDto) => {
          let roleNames = item.roleNames.reduce((role: string, roles: string) => roles + ", " + role, "");
          return roleNames.slice(0, roleNames.length - 2);
        },
      },
      {
        name: L('Active'),
        fieldName: 'isActive',
        onRender: (item: UserDto) => {
          return item.isActive ? (
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.green, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
          ) : (
            <span style={{color: additionalTheme.white, backgroundColor:additionalTheme.lighterRed}}>{L('No')}</span>
          );
        }
      }
    ];

    if(userIsAdmin !== true) {
      tableColumns.splice(5, 1);
    }

    return tableColumns;
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: true,
      delete: true
    };
  }

  getTitle(): string {
    return L('Users');
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <UserPanel
      {...props}
    />
  }
}