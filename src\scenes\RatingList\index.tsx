import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import { RatingTable } from './components/ratingTable';
import RatingStore from '../../stores/ratingStore';
import { L } from '../../lib/abpUtility';
import { isConfigForAG, isConfigForProduction } from '../../utils/authUtils';
import {mergeStyleSets} from "@fluentui/react";
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
});

export interface IProps {
  searchStore: SearchStore;
  ratingStore: RatingStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.RatingStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  async componentDidMount() {
		if(isConfigForAG() || isConfigForProduction()) {
			window.location.href = '/exception';
		}
	}

  public render() {
    let items = this.props.ratingStore.dataSet ? this.props.ratingStore.dataSet.items : [];

    if(isConfigForAG() || isConfigForProduction()) {
			return <>
				<p style={{marginTop: 50, fontWeight: 'bold', fontSize: '1.5rem'}}>{L('You do not have access to this page.')}</p>
			</>;
    } else {
      return (
        <>
          <div className={classNames.titleContainer}>
            <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Rating List')}</h2>
          </div>
          
          <RatingTable
            searchText={this.props.searchStore.searchText}
            items={items}
            store={this.props.ratingStore}
            history={this.props.history}
          />
        </>
      );
		}
  }
}

export default Index;