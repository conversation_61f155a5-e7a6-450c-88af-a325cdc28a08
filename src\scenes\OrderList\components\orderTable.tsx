import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { OrderDto } from '../../../services/order/dto/orderDto';
import { ICrudPermissons } from '../../BaseComponents/commandBarBase';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { OrderPanel } from './orderPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { Dialog, DialogType, Link } from "@fluentui/react";
import { RouterPath } from "../../../components/Router/router.config";
import { OrderStatus } from "../../../services/order/enums/orderStatusEnums";
import policyApplicationService from "../../../services/policyApplication/policyApplicationService";
import { catchErrorMessage } from "../../../utils/utils";
import { getCustomerAddressData } from "../../../utils/storeUtils";
import { KeysToIdMapper } from "../../../services/product/productDto";
import { defaultKeysToIdMapper } from "../../../stores/productStore";
import { getProductKeysToIdMapper, mapAttributeNameToId } from "../../../utils/policyCalculationUtils";
import {additionalTheme, myTheme} from "../../../styles/theme";

export class OrderTable extends FluentTableBase<OrderDto> {
  private shouldReloadItems: boolean = false;
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";
  private customActionButtonDisabled: boolean = false;
  private keysToIdMapper: KeysToIdMapper = defaultKeysToIdMapper;

  getColumns(): ITableColumn[] {
    return OrderTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Number'),
        fieldName: 'orderNumber',
        maxWidth: 100,
        minWidth: 100,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Order}/${item.id}`);
                      }} 
                        href={`/${RouterPath.Order}/${item.id}`}>
                  {item.orderNumber}
                </Link>
        }
      },
      {
        name: L('Status'),
        fieldName: 'orderStatusId',
        maxWidth: 100,
        minWidth: 100,
        onRender: (item: OrderDto) => {
          let Color = additionalTheme.white;
          let Background = myTheme.palette.orange;

          if (['Complete', 'Saved', 'Submitted'].includes(OrderStatus[item.orderStatusId])) {
            Color = additionalTheme.white;
            Background = myTheme.palette.green;
          } else if (['Processing', 'Pending'].includes(OrderStatus[item.orderStatusId])) {
            Color = additionalTheme.white;
            Background = myTheme.palette.orange;
          } else if (OrderStatus[item.orderStatusId] === 'Cancelled') {
            Color = additionalTheme.white;
            Background = additionalTheme.lighterRed;
          }

          return (
              <span style={{color: Color, backgroundColor: Background, padding: '2px 5px', borderRadius: '2px'}}>
      {!!item.orderStatusId ? L(OrderStatus[item.orderStatusId]) : L('None')}
    </span>
          );
        }
      },
      {
        name: L('Customer'),
        fieldName: 'customerEmail',
        onRender: (item: any): any => {
          return !!item.customerEmail ? item.customerEmail : (!!item.billingAddress && !!item.billingAddress.email ? item.billingAddress.email : '-')
        }
      },
      {
        name: L('CustomerName'),
        fieldName: 'firstName',
        onRender: (item: any): any => {
          return getCustomerAddressData(item, "firstName", true)
        }
      },
      {
        name: L('Surname'),
        fieldName: 'lastName',
        onRender: (item: any): any => {
          return getCustomerAddressData(item, "lastName", true)
        }
      },
      {
        name: L('Order total'),
        fieldName: 'orderTotal',
      }
    ];
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: true,
      delete: false,
      customActions: true,
    };
  }

  getTitle(): string {
    return L('Orders');
  }

  private async reloadItems() {
    this.selectionSetAllSelected(false);
    if(typeof this.props.refreshItems !== 'undefined') {
      await this.props.refreshItems!();
    } else {
      this.togglePopUpDialog("Error", "The list could not be refreshed automatically, please refresh the page manually.");
    }
  }

  private togglePopUpDialog(title: string, text: string, visibility?: boolean) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.forceUpdate();
  }

  private reloadListOnDialogClose() {
    this.showPopUpDialog = false;

    if(this.shouldReloadItems) {
      this.reloadItems();
    }

    this.forceUpdate();
  }

  private async sendCalculationFromList(order: any, that: any) {
    if(order.orderStatusId === OrderStatus.Saved) {
      if(order.orderItems && order.orderItems.length > 0) {
        this.shouldReloadItems = false;

        if(this.keysToIdMapper === defaultKeysToIdMapper) {
          this.keysToIdMapper = await getProductKeysToIdMapper();
        }

        order.orderItems.forEach(async (orderItem: any) => {
          let apiCall: any = {};
          let dataObjFiltered: any = {};
          let foundKeys: number = 0;

          orderItem.attributes.some((attr: any) => {
            if(attr.key === mapAttributeNameToId("apiCall", orderItem.productId, this.keysToIdMapper)) {
              apiCall = JSON.parse(attr.value);
              foundKeys++;
            } else if(Object.keys(apiCall).length === 0 && attr.key === "") {
              const parsedAttr: any = JSON.parse(attr.value);
              if(parsedAttr && parsedAttr.source) {
                apiCall = parsedAttr;
                foundKeys++;
              }
            }

            if(attr.key === mapAttributeNameToId("dataObjFiltered", orderItem.productId, this.keysToIdMapper)) {
              dataObjFiltered = JSON.parse(attr.value);
              foundKeys++;
            } else if(Object.keys(dataObjFiltered).length === 0 && attr.key === "") {
              const parsedAttr: any = JSON.parse(attr.value);
              if(parsedAttr && parsedAttr.length > 0 && parsedAttr[0].core_path) {
                dataObjFiltered = parsedAttr;
                foundKeys++;
              }
            }

            if(foundKeys === 2) {
              return true;
            }
            return false;
          });

          if(Object.keys(apiCall).length > 0 && apiCall?.translated && Object.keys(apiCall?.translated).length > 0 && dataObjFiltered && Object.keys(dataObjFiltered).length > 0) {
            const translated: any = apiCall?.translated;

            let requestBody = {
              "type": translated.calculationType,
              "gnOrderId": order.id,
              "insurerName": translated.insurerName,
              "offerNumber": translated && !!translated.offerNumber ? translated.offerNumber : '',
              "customerId": order.customerId,
              "customerEmail": order.customerEmail,
              "customerName": getCustomerAddressData(order, "firstName", true),
              "customerSurname": getCustomerAddressData(order, "lastName", true),
              "orderId": order.id,
              "orderNumber": order.orderNumber,
              "orderDate": order.createdOnUtc,
              "data": dataObjFiltered,
            };

            if(translated.insurerName === "TUZ") {
              requestBody["calculationId"] = translated.calculationId;
            }

            await policyApplicationService.applicate(requestBody).then((response: any) => {
              if(response && response.data && response.data.result && response.data.result.policy) {
                let policy = response.data.result.policy;
  
                if(policy.success) {
                  this.shouldReloadItems = true;
                  this.togglePopUpDialog("Success", "Calculation has been successfully sent!");
                } else if(policy.errors && policy.errors.length > 0) {
                  let text = "";
  
                  policy.errors.forEach((error: string) => {
                    text += error;
                    if(policy.errors.length > 1) {
                      text += "\n\r\n\r";
                    }
                  });
  
                  this.togglePopUpDialog("Error", text);
                } else {
                  this.togglePopUpDialog("Something went wrong", "Not enough data available.");
                }
              } else {
                this.togglePopUpDialog("Something went wrong", "Response from server is not valid.");
              }

              that.toggleCustomActionButton(false, true);
            }).catch((error) => {
              this.togglePopUpDialog("Something went wrong", catchErrorMessage(error));
              that.toggleCustomActionButton(false, true);
            });
          } else {
            this.togglePopUpDialog("Something went wrong", "Not enough data available.");
            that.toggleCustomActionButton(false, true);
          }
        });
      } else {
        this.togglePopUpDialog("Something went wrong", "Not enough data available.");
        that.toggleCustomActionButton(false, true);
      }
    } else {
      if(order.orderStatusId === OrderStatus.Submitted) {
        this.togglePopUpDialog("Action unavailable", "This order is already being processed.");
      } else if(order.orderStatusId === OrderStatus.Complete) {
        this.togglePopUpDialog("Action unavailable", "This order is now completed.");
      } else if(order.orderStatusId === OrderStatus.Cancelled) {
        this.togglePopUpDialog("Action unavailable", "This order is canceled.");
      } else {
        this.togglePopUpDialog("Action unavailable", "Something is wrong with this order status.");
      }

      that.toggleCustomActionButton(false, true);
    }
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    return {
      ...props,
      setButtons: () => {
        return {
          'newItem': {text: 'New', icon: 'Add'},
          'delete': {text: 'Delete', icon: 'Delete'},
          'edit': {text: 'Details'},
          'newMsg': {text: 'Msg', icon: ''},
        }
      },
      customActionsProps: [
        // {
        //   displayFor: 'none',
        //   buttonText: L("New calculation"),
        //   buttonIcon: "AddToShoppingList",
        //   buttonColor: myTheme.palette.white,
        //   buttonIconColor: myTheme.palette.white,
        //   buttonBackground: myTheme.palette.themePrimary,
        // }, 
        {
          displayFor: 'single',
          buttonText: L("Send calculation"),
          buttonIcon: "none",
          buttonDisabled: this.customActionButtonDisabled,
        },
        // {
        //   displayFor: 'single',
        //   buttonText: L("Edit order"),
        //   buttonIcon: "Edit",
        //   buttonDisabled: this.customActionButtonDisabled,
        //   buttonColor: myTheme.palette.white,
        //   buttonIconColor: myTheme.palette.white,
        //   buttonBackground: myTheme.palette.themeDark,
        // }
      ],
      customActions: [
        // () => {
        //   this.props.history.push(`/${RouterPath.PolicyCalculation}/`);
        // },
        async (order: OrderDto, that: any) => {
          that.toggleCustomActionButton(true, true);
          await this.sendCalculationFromList(order, that);
        },
        // (order: OrderDto) => {
        //   if(order.orderStatusId === OrderStatus.Saved) {
        //     this.props.history.push(`/${RouterPath.PolicyCalculation}/?entityType=order&entityId=${order.id}`);
        //   } else {
        //     this.togglePopUpDialog("Action unavailable", "This order cannot be edited.");
        //   }
        // }
      ]
    }
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <Dialog
        hidden={!this.showPopUpDialog}
        onDismiss={() => this.reloadListOnDialogClose()}
        dialogContentProps={{
            type: DialogType.normal,
            title: L(this.popUpDialogTitle),
            subText: L(this.popUpDialogText),
        }}
        modalProps={{
            isBlocking: true
        }}
      >
      </Dialog>

      <OrderPanel
        {...props}
      />
    </>
  }
}