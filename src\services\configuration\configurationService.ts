import { isUserLoggedIn } from '../../utils/authUtils';
import { CrudServiceBase } from '../base/crudServiceBase';
import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { ConfigurationDto } from './dto/configurationDto';

export class ConfigurationService extends CrudServiceBase<ConfigurationDto> {
    constructor() {
        super(Endpoint.Configuration);
        this.internalHttp = httpApi;
    }

    public async checkEnvironment() {
        isUserLoggedIn();
        let result = await this.internalHttp.post(this.endpoint.Custom(`CheckEnvironment`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportConfigurationService: ConfigurationService = new ConfigurationService();
export default exportConfigurationService;