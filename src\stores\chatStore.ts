import { action, observable } from "mobx";
import { AzureB2CStorageKey } from "../scenes/Login/AzureB2C/signInButton";
import { OutputChatMessageDto } from "../services/chat/chatMessageDto";
import { ChatModel } from "../services/chat/chatModel";
import chatService, { ChatService } from "../services/chat/chatService";
import { ChatThreadDto } from "../services/chat/chatThreadDto";
import { ChatUserDto } from "../services/chat/chatUserDto";
import { isUserLoggedIn } from "../utils/authUtils";
import { WebHookType } from "./WebHook/WebHookMessage";
import webHookStore from "./WebHook/WebHookStore";
import { HubMessageTypeEnum } from "../services/chat/hubMessageTypeEnums";

class ChatStore {
    chatService: ChatService;

    @observable users: ChatUserDto[] = [];
    @observable threads: ChatThreadDto[] = [];
    @observable waitingList: any[] = [];
    @observable current?: ChatModel;
    @observable username?: string;
    @observable threadsTotalCount?: number;
    @observable moreMessages?: OutputChatMessageDto[];
    @observable moreMessagesTotalCount?: number;

    constructor() {
        this.chatService = chatService
    }
    
    @action
    async getAll(skipCount?: number) {
        isUserLoggedIn();
        
        const getFromLocalStorage: string | null = localStorage.getItem(AzureB2CStorageKey);
        const parsedLocalStorageData: any = JSON.parse(!!getFromLocalStorage ? getFromLocalStorage : '');
        
        this.users = await this.chatService.getUsers(parsedLocalStorageData && parsedLocalStorageData.user && parsedLocalStorageData.user.id ? parsedLocalStorageData.user.id : 0);

        const result = await this.chatService.getThreads({maxResultCount: 25, keyword: '', skipCount: skipCount!});
        if (skipCount! > 24) {
            this.threads = this.threads.concat(result.items);
        } else {
            this.threads = result.items
        }
        this.threadsTotalCount = result.totalCount
        this.waitingList = await this.chatService.getWaitingList();
    }

    @action
    async enterWaitingRoom() {
        isUserLoggedIn();

        const enterWaitingRoom = await chatService.enterWaitingRoom();
        const threadResult = await this.chatService.getThread(enterWaitingRoom.result.threadId);

        this.current = {
            thread: threadResult,
            messages: []
        };

        localStorage.setItem('currentChatThreadId', threadResult.id);

        webHookStore.push({type: WebHookType.Notification, payload: JSON.stringify([{type: HubMessageTypeEnum.OpenWaitingRoom, value: ''}])});
        return;
    }

    @action
    async deleteChatThread(threadId: string) {
        isUserLoggedIn();
        await this.chatService.deleteChatThread(threadId);
    }

    @action 
    async getMessagesClient(threadId: string) {
        isUserLoggedIn();

        const threadResult = await this.chatService.getThread(threadId);

        const result = await this.chatService.getMessages(threadId, {keyword: '', skipCount: 0, maxResultCount: 25});
        const messages = result.items;
        this.current = {
            thread: threadResult,
            messages
        };
    }

    @action
    async setAsEnded(threadId: string) {
        isUserLoggedIn();
        await this.chatService.setAsEnded(threadId);
    }

    @action
    async joinWaitingRoom(waitingRoomId: number, waitingRoomThreadId: string) {
        isUserLoggedIn();

        await chatService.joinWaitingRoom(waitingRoomId).then(async (response: any) => {
            if (response && response.success) {
                this.current = {
                    thread: {
                        deletedOn: null,
                        id: waitingRoomThreadId,
                        lastMessageReceivedOn: "",
                        topic: "",
                    },
                    messages: (await this.chatService.getMessages(waitingRoomThreadId)).items
                };
                return;
            }
        }).catch((error: any) => {
            console.error(error);
        });
        webHookStore.push({type: WebHookType.Notification, payload: ''});
        return;
    }

    @action 
    async setUsername(username?: string) {
        this.username = username;
    }

    @action 
    async getMoreMessages(threadId: string, skipCount: number) {
        const result = await this.chatService.getMessages(threadId, {keyword: '', skipCount: skipCount, maxResultCount: 25});
        this.moreMessages = result.items;
        this.moreMessagesTotalCount = result.totalCount;
    }

    @action
    async select(threadId?: string, userId?: number, guestId?: number) {
        isUserLoggedIn();

        if (threadId) {
            const result = await this.chatService.getMessages(threadId, {keyword: '', skipCount: 0, maxResultCount: 25});
            const messages = result.items;
            this.current = {
                thread: this.threads.find(x => x.id === threadId)!,
                messages
            };
            this.moreMessagesTotalCount = result.totalCount;
            return;
        }

        if (userId) {
            const newThreadId = await this.chatService.beginChat(userId);
            const result = await this.chatService.getMessages(newThreadId);
            const messages = result.items;
            this.current = {
                thread: this.threads.find(x => x.id === newThreadId)!,
                messages
            };
            return;
        }
        
        this.current = undefined;
    }
}

export const defaultMessage: OutputChatMessageDto = {
    id: '',
    type: {},
    sequenceId: '',
    version: '',
    content: {
        message: null,
        topic: null,
        participants: [],
        initiator: { id: '' },
    },
    senderDisplayName: null,
    createdOn: new Date(),
    sender: null,
    deletedOn: null,
    editedOn: null,
    metadata: {},
    userId: 0,
}

const chatStore = new ChatStore();
export default chatStore;