import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { isGranted, L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { ContestPanel } from './contestPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { ClientTypeEnum } from "../../../services/client/clientTypeEnums";
import { myTheme } from "../../../styles/theme";
import { DefaultButton, Dialog, DialogContent, DialogFooter, DialogType, Icon, mergeStyleSets, PrimaryButton, SelectionMode, Spinner, SpinnerSize } from "@fluentui/react";
import { ContestDto } from "../../../services/contest/dto/contestDto";
import { dateFormat } from "../../../utils/utils";
import { spinnerClassNames } from "../../../styles/spinnerStyles";
import { ContestParticipantsFluentListBaseWithCommandBar } from "../../BaseComponents/contestParticipantsFluentListBaseWithCommandBar";
import { defaultContest } from "../../../stores/contestStore";
import ClientContestStore from "../../../stores/clientContestStore";

const classNames = mergeStyleSets({
  dialog: {
    selectors: {
      '.ms-Dialog-main': {
        maxWidth: '70%',
        minWidth: "1200px"
      }
    }
  },
  dialogContent: {
    selectors: {
      '.ms-Dialog-header': {
        display: 'none',
      },
      '.ms-Dialog-inner': {
          padding: 0,
      },
      'ol': {
        padding: '0 0 0 14px',
        margin: 0,
        selectors: {
          'li': {
            width: '100%',
            marginBottom: 5,
            padding: 2,
            boxSizing: 'border-box',
            selectors: {
              ':nth-child(odd)': {
                backgroundColor: 'rgba(0, 0, 0, 0.1)',
              }
            }
          }
        }
      }
    }
  },
  contentContainer: {
    display: 'block',
    width: '100%',
    height: '500px',
    maxHeight: '500px',
    position: 'relative',
    border: `1px solid ${myTheme.palette.themeLighter}`,
    overflowX: 'hidden',
    boxSizing: 'border-box',
  },
});

export class ContestTable extends FluentTableBase<ContestDto> {
  private asyncActionInProgress: boolean = false;
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";
  private popUpDialogAdditionalData: any = undefined;
  private participants: any;
  private showDeletePopUpDialog: boolean = false;
  private selectedItemToDelete: ContestDto = defaultContest;
  private selectedItemId: number = 0;
  private participantSearchText: string = '';
  
  disableGetAllOnMount = true;

  componentWillUnmount(): void {
    this.participants = undefined;
    this.forceUpdate();
  }

  getItemDisplayNameOf(item: ContestDto): string {
    return item.id;
  }

  getColumns(): ITableColumn[] {
    return ContestTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Name'),
        fieldName: 'name',
        minWidth: 150,
        maxWidth: 150,
      },
      {
        name: L('Product type'),
        fieldName: 'productType',
        minWidth: 100,
        maxWidth: 100,
      },
      {
        minWidth: 80,
        maxWidth: 80,
        name: L('Is enabled'),
        fieldName: 'isEnabled',
        onRender: (item: any) => {
          return <Icon style={item.isEnabled === true ? {color: "green"} : {color: "red"}} 
                      iconName={item.isEnabled === true ? "SkypeCheck" : "StatusCircleErrorX"} />
        }
      },
      {
        minWidth: 80,
        maxWidth: 80,
        name: L('Infinity prizes'),
        fieldName: 'infinitePrizes',
        onRender: (item: any) => {
          return <Icon style={item.infinitePrizes === true ? {color: "green"} : {color: "red"}} 
                      iconName={item.infinitePrizes === true ? "SkypeCheck" : "StatusCircleErrorX"} />
        }
      },
      {
        name: L('Prizes amount'),
        fieldName: 'prizesAmount',
        minWidth: 90,
        maxWidth: 90,
      },
      {
        name: L('Start date'),
        fieldName: 'startDate',
        minWidth: 110,
        maxWidth: 110,
        onRender: (item: any) => {
          return dateFormat(item.startDate, 'DD.MM.YYYY');
        }
      },
      {
        name: L('End date'),
        fieldName: 'endDate',
        minWidth: 110,
        maxWidth: 110,
        onRender: (item: any) => {
          return dateFormat(item.endDate, 'DD.MM.YYYY');
        }
      },
      {
        name: L('Contest description'),
        fieldName: 'contestDescription',
        minWidth: 140,
        maxWidth: 140,
      },
      {
        name: L('Contest prize description'),
        fieldName: 'contestPrizeDescription',
        minWidth: 180,
        maxWidth: 180,
      },
      {
        name: L('Promotion rules'),
        fieldName: 'promotionRules',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Conditions'),
        fieldName: 'conditions',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Marketing agreements content'),
        fieldName: 'marketingAgreementsContent',
        minWidth: 160,
        maxWidth: 160,
      },
    ];
  }

  private togglePopUpDialog(title: string | undefined, text: string | undefined, visibility: boolean, additionalData?: any, additionalDataIsEmptyEl?: boolean) {
    if(!!title) {
      this.popUpDialogTitle = title;
    }
    if(!!text) {
      this.popUpDialogText = text;
    }
    if(!!additionalData) {
      if(additionalDataIsEmptyEl === true) {
        setTimeout(() => {
          this.popUpDialogAdditionalData = additionalData;
          this.forceUpdate();
        }, 600);
      } else {
        this.popUpDialogAdditionalData = additionalData;
      }
    }
    if(this.showPopUpDialog !== visibility) {
      this.participants = undefined;
    }
    this.showPopUpDialog = visibility;
    this.forceUpdate();
  }

  // private async refreshParticipants(contestId: number) {
  //   this.asyncActionInProgress = true;
  //   // this.togglePopUpDialog("Getting data", "Please wait...", true, <></>);

  //   await clientContestService.getParticipants(contestId, {...clientContestService.defaultRequest, keyword: this.participantSearchText}).then((response: PagedResultDto<ClientContestDto>) => {
  //     this.participants = response.items;

  //     let tempAdditionalData = <Stack>
  //       <div className={classNames.contentContainer}>
  //         <ContestParticipantsFluentListBaseWithCommandBar
  //           searchText={this.participantSearchText}
  //           store={this.props.customData.clientContestStore!}
  //           items={response.items}
  //           selectionMode={SelectionMode.single}
  //           history={this.props.history}
  //           // scrollablePanelMarginTop={120}
  //           customOnSearchTextChanged={(text: string) => {
  //             this.participantSearchText = text;
  //             this.forceUpdate();
  //             this.refreshParticipants(contestId);
  //           }}
  //           customData={{
  //             disableGetAllOnMount: true,
  //             refreshParticipants: () => this.refreshParticipants(contestId),
  //           }}
  //         />
  //       </div>
  //     </Stack>;

  //     this.asyncActionInProgress = false;
  //     this.togglePopUpDialog("Contest participants", `${L('Found contestants')}: ${response.totalCount}`, true, tempAdditionalData);
  //   }).catch((error: any) => {
  //     console.error(error);
  //     this.asyncActionInProgress = false;
  //     this.forceUpdate();
  //   });
  // }

  private closeDeletePopUpDialog() {
    this.showDeletePopUpDialog = false;
    this.forceUpdate();
    
    setTimeout(() => {
      this.asyncActionInProgress = false;
      this.selectedItemToDelete = defaultContest;
      this.forceUpdate();
    }, 600);
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: isGranted('Client.Create.A') || isGranted('Client.Create.S') || isGranted('Client.Create.O'),
      update: isGranted('Client.Update.A') || isGranted('Client.Update.S') || isGranted('Client.Update.O'),
      delete: false,
      customActions: true,
    };
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();

    return {
      ...props,
      customActionsProps: [
        {
          displayFor: 'single',
          buttonText: L("Show contestants"),
          buttonIcon: "none",
          buttonColor: myTheme.palette.black,
          buttonIconColor: myTheme.palette.white,
        },
        {
          displayFor: 'single',
          buttonText: L(`Delete`),
          buttonIcon: "none",
          buttonColor: myTheme.palette.white,
          buttonIconColor: myTheme.palette.white,
          buttonBackground: myTheme.palette.red,
          buttonClassNames: `ms-Button`,
        },
      ],
      customActions: [
        async (item: ContestDto) => {
          // this.asyncActionInProgress = true;
          this.selectedItemId = parseInt(item.id);

          // this.togglePopUpDialog("Getting data", "Please wait...", true, <></>);
          this.forceUpdate(); 

          // let participants: any = this.participants;

          // if(!participants || participants.length === 0) {
          //   await clientContestService.getParticipants(parseInt(item.id), {...clientContestService.defaultRequest, keyword: this.participantSearchText}).then((response: PagedResultDto<ClientContestDto>) => {
          //     participants = response.items;
          //     this.asyncActionInProgress = false;
          //     this.forceUpdate();
          //   }).catch((error: any) => {
          //     console.error(error);
          //     this.asyncActionInProgress = false;
          //     this.forceUpdate();
          //   });
          // }

          // let tempAdditionalData = <></>;

          // if(participants.length > 0) {
          //   tempAdditionalData = <Stack>
          //     <div className={classNames.contentContainer}>
          //       <ContestParticipantsFluentListBaseWithCommandBar
          //         searchText={this.participantSearchText}
          //         store={this.props.customData.clientContestStore!}
          //         items={participants}
          //         selectionMode={SelectionMode.single}
          //         history={this.props.history}
          //         // scrollablePanelMarginTop={120}
          //         customOnSearchTextChanged={(text: string) => {
          //           this.participantSearchText = text;
          //           this.forceUpdate();
          //           this.refreshParticipants(parseInt(item.id));
          //         }}
          //         customData={{
          //           disableGetAllOnMount: true,
          //           refreshParticipants: () => this.refreshParticipants(parseInt(item.id)),
          //         }}
          //       />
          //     </div>
          //   </Stack>;
          // }

          this.togglePopUpDialog("Contest participants", 
            `${L('Found contestants')}: ${ this.props.customData.clientContestStore.dataSet && this.props.customData.clientContestStore.dataSet.totalCount ? this.props.customData.clientContestStore.dataSet.totalCount : 0}`, 
            true, <></>
          );
        },
        (item: ContestDto) => {
          this.selectedItemToDelete = item;
          this.showDeletePopUpDialog = true;
          this.forceUpdate();
        },
      ]
    }
  }

  getTitle(): string {
    return L('Contest list');
  }
  
  renderPanelView(props: IGenericPanelProps): JSX.Element {
    const clientContestStore: ClientContestStore | undefined = this.props.customData && this.props.customData.clientContestStore ? this.props.customData.clientContestStore : undefined;

    return <>
      <Dialog
        hidden={!this.showDeletePopUpDialog}
        onDismiss={() => { this.closeDeletePopUpDialog(); }}
        dialogContentProps={{
            type: DialogType.normal,
            title: `${L("The promotion has active participants.")} ${L("Are you sure you want to delete")} ${this.selectedItemToDelete.name}?`,
            closeButtonAriaLabel: L('Close'),
        }}
        modalProps={{isBlocking: true, styles: {main: { maxWidth: 450 }},}}
        theme={myTheme}
      >
        <DialogFooter theme={myTheme}>
          <DefaultButton theme={myTheme} onClick={() => { this.closeDeletePopUpDialog(); }} text={L('No')} disabled={this.asyncActionInProgress} />
          <PrimaryButton text={L('Yes')} theme={myTheme} disabled={this.asyncActionInProgress}
            onClick={async () => {
              if(!!this.selectedItemToDelete.id && parseInt(this.selectedItemToDelete.id) > 0) {
                this.asyncActionInProgress = true;
                this.forceUpdate(); 

                await this.props.store.delete(this.selectedItemToDelete).then(async () => {
                  if(this.props.refreshItems) {
                    await this.props.refreshItems({resetDataSet: true});
                  } else {
                    await this.props.store.getAll();
                  }
                }).catch((error: any) => {
                  console.error(error);
                  this.closeDeletePopUpDialog();
                });
              }

              this.closeDeletePopUpDialog();
            }}
          />
        </DialogFooter>
      </Dialog>

      <Dialog
        hidden={!this.showPopUpDialog}
        onDismiss={() => this.togglePopUpDialog(undefined, undefined, false, <></>, true)}
        dialogContentProps={{
            type: DialogType.normal,
            title: L(this.popUpDialogTitle),
        }}
        modalProps={{
            isBlocking: true,
            className: classNames.dialog,
        }}
      >
        <DialogContent className={classNames.dialogContent}>
          <div className={classNames.contentContainer}>
            <ContestParticipantsFluentListBaseWithCommandBar
              searchText={undefined}
              store={clientContestStore!}
              items={clientContestStore && clientContestStore.dataSet && clientContestStore.dataSet.items ? clientContestStore.dataSet.items : []}
              selectionMode={SelectionMode.single}
              history={this.props.history}
              customData={{
                customRequest: {contestId: this.selectedItemId, onlyEligible: true},
                customLoadSpinnerStyle: {bottom: '22px', right: '10px', left: 'unset'}
              }}
            />
          </div>

          {this.asyncActionInProgress && (
            <Spinner className={`${spinnerClassNames.smallLoadSpinner}`} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
          )}
        </DialogContent>
      </Dialog>
      
      <ContestPanel
        {...props}
      />
    </>;
  }
  
  copyAndSort<T>(items: T[], columnKey: string, isSortedDescending?: boolean): T[] {
    const key = columnKey as keyof T;
    if(key === 'company') {
      let concatColumn: any[] = [];
      items.forEach((item: any, index: number) => {
        if(item.clientType === ClientTypeEnum.Individual) {
          concatColumn.push({index: index, name: `${item.user.name} ${item.user.surname}`});
        } else {
          concatColumn.push({index: index, name: `${item.company}`});
        }
      });
      concatColumn.sort((a: any, b: any) => { 
        if(a.name < b.name)
          return isSortedDescending ? -1 : 1;
        if(a.name > b.name)
          return isSortedDescending ? 1 : -1;
        return 0;
      });

      let sortedItems: any[] = [];
      concatColumn.forEach((col: any) => {
        sortedItems.push(items[col.index]);
      });
      return sortedItems;
    } else {
      return items.slice(0).sort((a: any, b: any) => { 
        return (isSortedDescending ? a[key] < b[key] : a[key] > b[key]) ? 1 : -1;
      });
    }
  }
}