import { Icon, SearchBox, ThemeProvider } from '@fluentui/react';
import { L, isGranted } from '../../lib/abpUtility';
import AppConsts from '../../lib/appconst';
import { ClientTypeEnum } from '../../services/client/clientTypeEnums';
import { ClientDto } from '../../services/client/dto/clientDto';
import { fluentTableClassNames } from '../../styles/fluentTableStyles';
import {additionalTheme, myTheme} from '../../styles/theme';
import { FluentTableBase } from '../Fluent/base/fluentTableBase';
import { ICrudPermissons } from './commandBarBase';
import { ITableColumn } from './ITableColumn';
import { LabelComponent } from './labelComponent';
import { LabelContainerComponent } from './labelContainerComponent';
import { CustomerPanel } from '../CustomerList/components/customerPanel';
import { IGenericPanelProps } from '../Fluent/base/genericPanel';
import { formatPhoneNumber } from '../../utils/utils';

var _ = require('lodash');

export class CustomerFluentListBaseWithCommandBar extends FluentTableBase<ClientDto> {
  private debouncedOnSearchboxChange: any = _.debounce((e: any, newValue: string | undefined, customPayload?: any) => {
    newValue = typeof newValue === 'undefined' || newValue.length === 0 ? " " : newValue;
    if(this.props.customOnSearchTextChanged) {
      this.props.customOnSearchTextChanged(newValue);
    } else {
      this.overrideFilter(newValue);
    }
  }, AppConsts.defaultSerachBarDelay, []);
  
  getItemDisplayNameOf(item: ClientDto): string {
    if(!!item.company) {
      return item.company;
    } else if(!!item.user) {
      return `${item.user.name} ${item.user.surname}`; 
    } else {
      return item.id;
    }
  }

  getColumns(): ITableColumn[] {
    return this.getTableColumns(this.props);
  }

  private getTableColumns(props: any): ITableColumn[] {
    return [          
      {
        name: L('Email'),
        fieldName: 'user.emailAddress',
        onRender: (item: any): any => {
          return item.user && !!item.user.emailAddress ? item.user.emailAddress : '';
        }
      },
      {
        name: L('Name'),
        fieldName: 'company',
        onRender: (item: any): any => {
          if(item.clientType === ClientTypeEnum.Individual || item.clientType === null) { // || item.clientType === ClientTypeEnum.SoleTrader
            return `${item.user.name} ${item.user.surname}`;
          } else {
            return !!item.company ? item.company : (!!item.user ? `${item.user.name} ${item.user.surname}` : '');
          }
        }
      },
      {
        name: L('First name'),
        fieldName: 'user.name',
        onRender: (item: any): any => {
          if(item.clientType === ClientTypeEnum.Individual || item.clientType === ClientTypeEnum.SoleTrader) {
            return item.user && !!item.user.name ? item.user.name : '';
          } else {
            return '';
          }
        }
      },
      {
        name: L('Last name'),
        fieldName: 'user.surname',
        onRender: (item: any): any => {
          if(item.clientType === ClientTypeEnum.Individual || item.clientType === ClientTypeEnum.SoleTrader) {
            return item.user && !!item.user.surname ? item.user.surname : '';
          } else {
            return '';
          }
        }
      },
      {
        name: L('Pesel'),
        fieldName: 'pesel',
        onRender: (item: any): any => {
          if(item.clientType === ClientTypeEnum.Individual || item.clientType === ClientTypeEnum.SoleTrader) {
            return item.pesel;
          } else {
            return '';
          }
        }
      },
      {
        name: L('Regon'),
        fieldName: 'regon',
      },
      {
        name: L('NIP'),
        fieldName: 'nip',
      },
      {
        name: L('Phone'),
        fieldName: 'phone',
        onRender: (item: any): any => {
          return (item.phone && !!item.phone) ? formatPhoneNumber(item.phone) : '';
        }
      },
      {
        name: L('Customer type'),
        fieldName: 'clientType',
        onRender: (item: ClientDto) => {
          return L(item.clientType);
        }
      },
    ];
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: isGranted('Client.Create.A') || isGranted('Client.Create.S') || isGranted('Client.Create.O'),
      update: isGranted('Client.Update.A') || isGranted('Client.Update.S') || isGranted('Client.Update.O'),
      delete: false,
      customActions: false,
    };
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    return {
      ...props,
      // customStyles: {marginTop: '-30px'},
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <CustomerPanel
        {...props}
      />
    </>;
  }

  renderAll(pageInfo: string, values: any, columns: any) {
    return <>
      <LabelContainerComponent marginTop={'0'}>
        <LabelComponent label={L('Select customer')} customStyles={{width: "200px", minWidth: "200px"}}/>
        <SearchBox
          theme={myTheme}
          styles={{
            root: {
              flex: 1,
              maxWidth: '252px !important',
              height: '32px', 
              backgroundColor: myTheme.palette.white,
              border: `1px solid ${myTheme.palette.black}`,
              boxSizing: 'border-box',
            },
            field: { borderRadius: '2px' },
          }}
          placeholder={ L('Search') }
          onChange={ (e: any, newValue: string | undefined) => {
            this.debouncedOnSearchboxChange(e, newValue);
          }}
        />

        {this.props.customData.selectedClient &&
          <p className={fluentTableClassNames.summaryAttribute}>
              <span>{this.props.customData.selectedClient}</span>
              <div style={{backgroundColor: additionalTheme.white, borderRadius: '50%', width: '20px', height: '20px', display: 'flex', justifyContent: 'center', alignItems: 'center', marginLeft: '10px'}}>
                <Icon iconName="Cancel" style={{marginRight: '0', cursor: 'pointer', color: additionalTheme.lighterGrey, fontWeight: 'bold'}} title={L("Delete")}
                  onClick={() => { if(this.props.customOnSelectionChanged) this.props.customOnSelectionChanged('deleteClient') }} />
              </div>
          </p>
        }
      </LabelContainerComponent>

      <ThemeProvider theme={myTheme}>
        {this.renderAnnounced(pageInfo)}
        {this.renderCommandBarBase()}

        {this.renderListScrollablePane(values, columns)}
      </ThemeProvider>

      {this.renderPanel()}
    </>;
  }
}