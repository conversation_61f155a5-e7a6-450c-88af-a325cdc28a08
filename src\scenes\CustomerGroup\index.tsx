import { inject, observer } from 'mobx-react';

import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import CustomerGroupStore from '../../stores/customerGroupStore';
import { CustomerGroupDto } from '../../services/customerGroup/customerGroupDto';
import { CustomerGroupContentView } from './components/customerGroupContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	customerGroupStore: CustomerGroupStore;
	match: any
}

@inject(Stores.CustomerGroupStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private customerGroupId = this.props.match.params.id;

	async componentDidMount() {
		await this.props.customerGroupStore.get({ id: this.customerGroupId } as CustomerGroupDto);
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<CustomerGroupContentView store={this.props.customerGroupStore} payload={ this.props.customerGroupStore.model as CustomerGroupDto } renderFooter={{
					show: true, options: {backOnly: true}
				}} />
			</FocusZone>
		);
	}
}

export default Index;