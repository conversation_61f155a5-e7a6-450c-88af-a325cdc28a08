import { AttributeControlType } from "../dto/AttributeControlType";
import { AttributeValueType } from "../dto/AttributeValueType";
import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";

export interface ProductAttributeMappingDto extends BaseApiEntityModel {
    ProductAttributeId: string;
    TextPrompt: string;
    IsRequired: boolean;
    DisplayOrder: number;
    ValidationMinLength: number | null;
    ValidationMaxLength: number | null;
    ValidationFileAllowedExtensions: string;
    ValidationFileMaximumSize: number | null;
    DefaultValue: string;
    ConditionAttributeXml: string;
    AttributeControlTypeId: AttributeControlType;
    ProductAttributeValues: ProductAttributeValueDto[];
}

export interface ProductAttributeValueDto extends BaseApiEntityModel {
    AssociatedProductId: string;
    Name: string;
    ColorSquaresRgb: string;
    ImageSquaresPictureId: string;
    PriceAdjustment: number;
    WeightAdjustment: number;
    Cost: number;
    Quantity: number;
    IsPreSelected: boolean;
    DisplayOrder: number;
    PictureId: string;
    AttributeValueTypeId: AttributeValueType;
}