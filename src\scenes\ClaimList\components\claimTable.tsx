import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { ClaimDto } from '../../../services/claim/dto/claimDto';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { ClaimPanel } from './claimPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { RouterPath } from "../../../components/Router/router.config";
import { dateFormat } from "../../../utils/utils";
import { Link } from "@fluentui/react";

export class ClaimTable extends FluentTableBase<ClaimDto> {
  // private showPopUpDialog: boolean = false;
  // private popUpDialogTitle: string = "";
  // private popUpDialogText: string = "";

  getItemDisplayNameOf(item: ClaimDto): string {
    return `${item.policyType} (id: ${item.id})`;
  }

  getColumns(): ITableColumn[] {
    return ClaimTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('ID'),
        fieldName: 'id',
        minWidth: 80,
        maxWidth: 80,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Claim}/${item.id}/${item.policyType}`);
                      }} 
                        href={`/${RouterPath.Claim}/${item.id}/${item.policyType}`}>
                  {item.id}
                </Link>
        }
      },
      {
        name: L('Date'),
        fieldName: 'claimDate',
        onRender: (item: any): any => {
          return dateFormat(item.claimDate, "DD.MM.YYYY", true);
        }
      },
      {
        name: L('Policy type'),
        fieldName: 'policyType',
        onRender: (item: any): any => {
          return L(`${item.policyType}2`);
        }
      },
      {
        name: L('Policy ID'),
        fieldName: 'policyId',
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.InsurancePolicy}/${item.policyId}`);
                      }} 
                        href={`/${RouterPath.InsurancePolicy}/${item.policyId}`}>
                  {item.policyId}
                </Link>
        }
      },
      {
        name: L('Insurer site link'),
        fieldName: 'insurerSiteLink',
      },
      {
        name: L('Comment'),
        fieldName: 'comment',
      },
    ];
  }

  getTitle(): string {
    return L('Claims');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: true,
      customActions: false,
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      {/* <Dialog
        hidden={!this.showPopUpDialog}
        onDismiss={() => this.dialogClose()}
        dialogContentProps={{
            type: DialogType.normal,
            title: L(this.popUpDialogTitle),
            subText: L(this.popUpDialogText),
        }}
        modalProps={{
            isBlocking: true
        }}
      >
      </Dialog> */}

      <ClaimPanel
        {...props}
        store={this.props.store}
      />
    </>
  }
}