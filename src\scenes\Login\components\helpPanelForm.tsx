import * as React from 'react';
import { Panel, PanelType, Pivot, PivotItem, PrimaryButton, DefaultButton } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { TextFieldBase } from '../../BaseComponents';
import { createOrUpdateClassNames } from '../../BaseComponents/createOrUpdate';
import {additionalTheme, myTheme} from '../../../styles/theme';

export interface IHelpPanelFormProps  {
  isOpen: boolean;
  onCancel: () => void;
}

export interface IHelpPanelFormState { }

class HelpPanelForm extends React.Component<IHelpPanelFormProps, IHelpPanelFormState> {
  public render() {

    const pivotStyles = {
      root: {
        marginLeft: '-8px'
      },
      linkIsSelected: {
        color: myTheme.palette.red,
        selectors: {
          ':before': {
            height: '5px',
            backgroundColor: additionalTheme.darkerRed,
          }
        }
      }
    };

    return (
      <form>
        <Panel
          headerText={L('Help')}
          isOpen={this.props.isOpen}
          onDismiss={() => this.props.onCancel()}
          closeButtonAriaLabel="Close"
          type={PanelType.smallFixedFar}
          theme={myTheme}
        >
          <Pivot theme={myTheme} styles={pivotStyles}>
            <PivotItem headerText={'Reset password'}>
              {/* todo add validation for email and fix this textfield  - add onchange */}
              (<TextFieldBase theme={myTheme} label={L('Email')} isDataLoaded={true} required />
              <div className={createOrUpdateClassNames.panelActions}>
                <PrimaryButton theme={myTheme} text={L('Send')} type={'submit'} onClick={() => {
                  // todo add onClick
                }} />
                <DefaultButton theme={myTheme} onClick={() => this.props.onCancel()} text={L('Cancel')} />
              </div>
            </PivotItem>
          </Pivot>
        </Panel>
      </form>
    );
  }
}

export default HelpPanelForm;
