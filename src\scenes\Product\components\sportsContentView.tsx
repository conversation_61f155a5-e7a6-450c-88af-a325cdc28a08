import { mergeStyleSets, Stack, SelectionMode, Selection } from '@fluentui/react';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import Stores from '../../../stores/storeIdentifier';
import { inject, observer } from 'mobx-react';
import { InsurerSportsFluentListBaseWithCommandBar } from '../../BaseComponents/insurerSportsFluentListBaseWithCommandBar';
import SportInsuranceCoverageStore from '../../../stores/sportInsuranceCoverageStore';

const classNames = mergeStyleSets({
    contentContainer: {
        display: 'block',
        width: '100%',
        height: '500px',
        maxHeight: '500px',
        position: 'relative',
        marginTop: '20px',
    },

});

export interface IProps {
	sportInsuranceCoverageStore: SportInsuranceCoverageStore;
}

@inject(Stores.LanguageStore)
@inject(Stores.ProductAttributeStore)
@inject(Stores.SportInsuranceCoverageStore)
@observer
export class SportsContentView extends GenericContentView {
    private selectedSport: any;
    private _sportListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedSportContest: any = this._sportListSelection.getSelection();
            if(Array.isArray(selectedSportContest) && selectedSportContest.length > 0 && !!selectedSportContest[0].id) {
                this.selectedSport = selectedSportContest[0];
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private overrideAllItemsTrigger: number = 0;

    private async reloadItems() {
        if(this.props.customData && this.props.customData.fetchSportsByInsurerId) {
            await this.props.customData.fetchSportsByInsurerId();
            this.overrideAllItemsTrigger = this.overrideAllItemsTrigger + 1;
            this.forceUpdate();
        }
    }

    renderContent() {
        return <>
            <Stack>
                <div className={classNames.contentContainer}>
                    <InsurerSportsFluentListBaseWithCommandBar 
                        store={this.props.sportInsuranceCoverageStore!}
                        items={
                            this.props.sportInsuranceCoverageStore?.dataSet && this.props.sportInsuranceCoverageStore?.dataSet.items
                                ? this.props.sportInsuranceCoverageStore?.dataSet.items
                                : []
                        }
                        customSelection={this._sportListSelection}
                        searchText={''}
                        history={this.props.history}
                        customData={{
                            selectedSport: this.selectedSport,
                            disableGetAllOnMount: true,
                            useOnlyRefreshItems: true,
                            overrideAllItemsTrigger: this.overrideAllItemsTrigger,
                        }}
                        scrollablePanelMarginTop={120}
                        refreshItems={() => this.reloadItems()}
                    />
                </div>
            </Stack>
        </>
    }
}