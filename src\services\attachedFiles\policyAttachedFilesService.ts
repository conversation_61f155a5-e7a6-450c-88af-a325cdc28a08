import Endpoint from '../endpoint';
import { PolicyAttachedFilesDto } from './policyAttachedFilesDto';
import { httpApi } from '../httpService';
import { ServiceBase } from '../base/serviceBase';

export class PolicyAttachedFilesService extends ServiceBase {
    constructor() {
        super(Endpoint.PolicyAttachedFiles);
    }

    public async createNew(createUserInput: PolicyAttachedFilesDto) {
        let result = await httpApi.post(this.endpoint.Create(), createUserInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByOrderId(orderId: string) {
        let result = await httpApi.get(this.endpoint.Custom(`GetByOrderId?orderId=${orderId}`, true));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByPolicyId(policyId: number) {
        let result = await httpApi.get(this.endpoint.Custom(`GetByPolicyId?policyId=${policyId}`, true));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getAllFiles() {
        let result = await httpApi.get(this.endpoint.GetAll());
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async SendPasswordBySms(uuid: string) {
        let result = await httpApi.post(this.endpoint.Custom(`SendPasswordBySms/${uuid}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportPolicyAttachedFilesService: PolicyAttachedFilesService = new PolicyAttachedFilesService();
export default exportPolicyAttachedFilesService;