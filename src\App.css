.App {
  text-align: center;
  background: #142c52;
}

.App-logo {
  animation: App-logo-spin infinite 20s linear;
  height: 80px;
}

.App-header {
  background-color: #222;
  height: 150px;
  padding: 20px;
  color: white;
}

.App-title {
  font-size: 1.5em;
}

.App-intro {
  font-size: large;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.success{
  color:#107c10
}

.error{
  color:#a80000
}

.info{
  color:rgb(0, 120, 212);
}

.hide {
  display: none;
}

.fade-in {
  display: block;
  opacity: 0;
  height: 0;
  animation: element-fade-in 120ms linear;
  animation-delay: 85ms;
  animation-fill-mode: forwards;
}

@keyframes element-fade-in {
  0% {
    height: 0;
  }

  1% {
    height: auto;
    opacity: 0;
  }

  100% {
    height: auto;
    opacity: 1;
  }
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 11px;
  height: 11px;
}

::-webkit-scrollbar-track {
  border: 1px solid #E1E1E1;
  background: #fff !important;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  border: 1px solid #E1E1E1;
  background: #EFEFEF !important;
  border-radius: 6px;
  transition: 0.2s;
}

::-webkit-scrollbar-thumb:hover {
  background: #ccc !important;
  }