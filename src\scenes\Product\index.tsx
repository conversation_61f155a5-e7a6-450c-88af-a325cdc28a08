import { inject, observer } from 'mobx-react';

import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import ProductStore from '../../stores/productStore';
import { ProductDto } from '../../services/product/productDto';
import { ProductContentView } from './components/productContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	productStore: ProductStore;
	match: any
}

@inject(Stores.ProductStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private productId = this.props.match.params.id;

	async componentDidMount() {
		await this.props.productStore.get({ id: this.productId } as ProductDto);
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<ProductContentView store={this.props.productStore} payload={ this.props.productStore.model as ProductDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;