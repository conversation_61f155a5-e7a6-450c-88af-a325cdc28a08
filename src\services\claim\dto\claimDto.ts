import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";
import { PolicyType } from "../../policy/policyTypeEnums";

export interface ClaimDto extends BaseApiEntityModel {
  claimDate: string;
  policyId: number;
  customerId: string;
  policyType: PolicyType;
  comment: string;
  bankName: string;
  accountNumber: string;
  insurerSiteLink: string;
  additionalInformation: string;
  payload?: string;
}