import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import LoyaltyPointsHistoryStore from '../../stores/loyaltyPointsHistoryStore';
import { LoyaltyPointsHistoryDto } from '../../services/loyaltyPointsHistory/loyaltyPointsHistoryDto';
import { LoyaltyPointsHistoryContentView } from './components/loyaltyPointsHistoryContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import { isConfigForAG, isConfigForProduction } from '../../utils/authUtils';
import { L } from '../../lib/abpUtility';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	loyaltyPointsHistoryStore: LoyaltyPointsHistoryStore;
	match: any
}

@inject(Stores.LoyaltyPointsHistoryStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private loyaltyPointsHistoryId = this.props.match.params.id;

	async componentDidMount() {
		if(isConfigForAG() || isConfigForProduction()) {
			window.location.href = '/exception';
		}
		
		await this.props.loyaltyPointsHistoryStore.get({ id: this.loyaltyPointsHistoryId } as LoyaltyPointsHistoryDto);
	}

	public render() {
		if(isConfigForAG() || isConfigForProduction()) {
			return <>
				<p style={{marginTop: 50, fontWeight: 'bold', fontSize: '1.5rem'}}>{L('You do not have access to this page.')}</p>
			</>;
		} else {
			return (
				<FocusZone as="div" className={classNames.subpageContentWrapper}>
					<LoyaltyPointsHistoryContentView store={this.props.loyaltyPointsHistoryStore} 
						payload={ this.props.loyaltyPointsHistoryStore.model as LoyaltyPointsHistoryDto } renderFooter={{show: true}} />
				</FocusZone>
			);
		}
	}
}

export default Index;