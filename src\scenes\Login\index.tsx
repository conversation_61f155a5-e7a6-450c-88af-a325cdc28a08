import * as React from 'react';

import { inject, observer } from 'mobx-react';

import AccountStore from '../../stores/accountStore';
import AuthenticationStore from '../../stores/authenticationStore';
import { L } from '../../lib/abpUtility';
import { Redirect } from 'react-router-dom';
import SessionStore from '../../stores/sessionStore';
import Stores from '../../stores/storeIdentifier';
import TenantAvailabilityState from '../../services/account/dto/tenantAvailabilityState';
import { Text, mergeStyleSets, MessageBarType, MessageBar, Spinner, SpinnerSize} from '@fluentui/react';

import HelpPanel from './components/helpPanel';
import {additionalTheme, myTheme} from '../../styles/theme';
import UserStore from '../../stores/userStore';
import Header from '../../components/Header';
import { Container } from '../../stores/storeInitializer';
import { SignInButton } from './AzureB2C/signInButton';

declare var abp: any;

const classNames = mergeStyleSets({
  loginWrapper: {
    backgroundColor: additionalTheme.darkerWhite,
    height: 'auto',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    overflowY: 'auto',
    paddingBottom: '90px',
    paddingTop: '50px',
    margin: 'auto 0',
  },
  loginHeader: {
    backgroundColor: myTheme.palette.white,
    width: 'calc(100% - 6.28rem)',
    position: 'absolute',
    top: '2.86rem',
    // left: '3.14rem',
    borderRadius: '12px',
    boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.10)',
    padding: '0.85rem',
  },
  headerImg: {
    marginBottom: '20px',
    selectors: {
      '& img': {
        width: '150px',
      },
    },
  },
  contentWrapper: {
    backgroundColor: additionalTheme.darkerWhite,
    display: 'flex',
    maxWidth: 730,
  },
  loginForm: {
    backgroundColor: additionalTheme.darkerWhite,
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between'
  },
  formBtnWrapper: {
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 20,
    display: 'flex'
  },
  logoWrapper: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 10,
    selectors: {
      '& img': {
        width: 150,
      },
    },
  },
  copyright: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 0,
    marginBottom: 20,
    selectors: {
      '& img': {
        width: 150,
      },
    },
  },
  titleText: {
    textAlign: 'center',
    display: 'flex',
    justifyContent: 'center',
    marginTop: 30,
  },
  input: {
    marginTop: 20
  },
  helpBtn: {
    width: 100,
    color: 'rgb(0, 120, 212);',
    marginLeft: 25
  },
  hide: {
    display: 'none !important',
  },
  messageBar: {
    marginTop: '25px',
    whiteSpace: 'pre-line',
  },
  loadSpinner: {
    display: 'flex',
    selectors: {
        '& .ms-Spinner-label': {
            color: myTheme.palette.themePrimary,
        }
    }
  },
});

export interface ILoginProps {
  authenticationStore?: AuthenticationStore;
  userStore?: UserStore;
  sessionStore?: SessionStore;
  accountStore: AccountStore;
  history: any;
  location: any;
}

interface ILoginState {
  isHelpOpen: boolean;
  isRegistrationOpen: boolean;
  refresh: boolean;
  password: string;
  login: string;
  messageBarData?: {
    hide: boolean;
    text: string;
    type: MessageBarType;
  };
}

@inject(Stores.AuthenticationStore, Stores.SessionStore, Stores.AccountStore, Stores.UserStore)
@observer
class Login extends React.Component<ILoginProps, ILoginState> {
  formRef: any;
  state = {
    isHelpOpen: false,
    isRegistrationOpen: false,
    refresh: false,
    password: '',
    login: '',
    messageBarData: {
      hide: true,
      text: '',
      type: MessageBarType.warning,
    },
  };
  
  changeTenant = async () => {
    // todo
    let tenancyName = "SODO";
    const { loginModel } = this.props.authenticationStore!;
    if (!tenancyName) {
      abp.multiTenancy.setTenantIdCookie(undefined);
      window.location.href = '/';
      return;
    } else {
      await this.props.accountStore!.isTenantAvailable(tenancyName);
      const { tenant } = this.props.accountStore!;
      switch (tenant.state) {
        case TenantAvailabilityState.Available:
          abp.multiTenancy.setTenantIdCookie(tenant.tenantId);
          loginModel.tenancyName = tenancyName;
          loginModel.toggleShowModal();
          window.location.href = '/';
          return;
        case TenantAvailabilityState.InActive:
          // todo display modal
          // Modal.error({ title: L('Error'), content: L('TenantIsNotActive') });
          break;
        case TenantAvailabilityState.NotFound:
          // todo display modal
          // Modal.error({ title: L('Error'), content: L('ThereIsNoTenantDefinedWithName{0}', tenancyName) });
          break;
      }
    }
  };

  handleSubmit = async (e: any) => {
    e.preventDefault();
    let tenancyName = "SODO";
    const { loginModel } = this.props.authenticationStore!;
    loginModel.tenancyName = tenancyName;
    loginModel.password = this.state.password;
    loginModel.userNameOrEmailAddress = this.state.login
    await this.props.authenticationStore!.login(loginModel);
    sessionStorage.setItem('rememberMe', loginModel.rememberMe ? '1' : '0');
    this.props.userStore!.get({ id: abp.session.userId });
    const { state } = this.props.location;
    window.location = state ? state.from.pathname : '/';
  }

  dismissPanel = (panelState: string): void => {
    let changePanel = {};
    changePanel[panelState] = false;
    this.setState(changePanel);
    Container.EventBus.customErrorHandling = false;
  };

  openPanel = (ev: React.MouseEvent<HTMLElement>, panelState: string): void => {
    ev.preventDefault();
    let changePanel = {};
    changePanel[panelState] = true;
    this.setState(changePanel);
    Container.EventBus.customErrorHandling = true;
  };

  openRegisterPanel = (ev: React.MouseEvent<HTMLElement>) => {
    this.props.accountStore.createCustomer();
    this.openPanel(ev, 'isRegistrationOpen');
  };

  handleCreate = (form: any) => {
    // todo handle button
    // form.validateFields(async (err: any, values: any) => {
    //   if (err) {
    //     Container.EventBus.setFormError(err);
    //     return;
    //   } else {
    //     Container.EventBus.clearFormError();
    //     await this.props.accountStore.registerCustomer(values);
    //     if (Container.EventBus.HttpError) {
    //       this.setState({refresh : !this.state.refresh})
    //       return;
    //     }
    //   }
    //   form.resetFields();
    //   this.dismissPanel('isRegistrationOpen');
    // });
  };

  public render() {
    const { isHelpOpen } = this.state;
    let { from } = this.props.location.state || { from: { pathname: '/' } };
    if(this.props.authenticationStore!.isAuthenticated) {
      return <Redirect to={from} />;
    }

    // todo make this Login working
    return (
      <div className={classNames.loginWrapper}>
        <div className={classNames.loginHeader}>
          <Header />
        </div>

        <div className={classNames.contentWrapper}>
          <div className={classNames.loginForm}>
              { this.props.authenticationStore!.isAuthenticated ? 
                <Spinner label={L('You will be redirected to the dashboard...')} className={classNames.loadSpinner} size={SpinnerSize.large}
                        ariaLive="assertive" labelPosition="top" />
                :
                <>
                  <Text style={{textAlign: 'center'}}>{L('The resources of this page are protected and require authentication.')}</Text>

                  <SignInButton 
                    setMessageBarData={
                      (hide: boolean, text: string, type: MessageBarType) => this.setState(prevState => { 
                        return ({...prevState, messageBarData: {hide, text, type}}) 
                      })
                    }
                    messageBarVisibleAndNotSuccess={!this.state.messageBarData.hide && this.state.messageBarData.type !== MessageBarType.success}
                    sessionStore={this.props.sessionStore}
                  />
                </>
              }

              <MessageBar messageBarType={this.state.messageBarData.type} isMultiline={true} className={`${this.state.messageBarData.hide && 'hide'} ${classNames.messageBar}`}
                onDismiss={() => { this.setState(prevState => { return ({...prevState, messageBarData: {...this.state.messageBarData, hide: true}}) }) }}>
                  {this.state.messageBarData.text}
              </MessageBar>
              
              {/* <div style={{ marginTop: 70, fontSize: 11 }}>
                <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', color: "black" }}>* {L('By logging in you agree to our')} &nbsp;<strong> <a style={{ color: myTheme.palette.themePrimary }} href="/account/terms-of-use"> {L("Terms of Use")} </a> </strong>&nbsp;{L('and to')}</div>
                <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', color: "black" }}>{L('receive emails & updates and acknowledge that')} </div>
                <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', color: "black" }}>{L('you’ve read our')}&nbsp;<strong> <a style={{ color: myTheme.palette.themePrimary }} href="/account/privacy-policy"> {L("Privacy Policy")} </a> </strong>. {L('You also acknowledge that')}</div>
                <div style={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', color: "black" }}>{L('uses cookies to give the best user experience')}.</div>
              </div> */}
          </div>
        </div>

        <HelpPanel isOpen={isHelpOpen} onCancel={() => this.dismissPanel('isHelpOpen')} accountStore={this.props.accountStore} />
      </div>
    );
  }
}

export default Login;
