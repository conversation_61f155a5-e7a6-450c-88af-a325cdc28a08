import { ITableColumn } from "./ITableColumn";
import { L } from "../../lib/abpUtility";
import { FluentTableBase } from "../Fluent/base/fluentTableBase";
import { IGenericPanelProps } from "../Fluent/base/genericPanel";
import { ICrudPermissons } from "./commandBarBase";
import { AgentClaimHistoryDto } from "../../services/agentClaim/dto/agentClaimHistoryDto";
import { AgentClaimHistoryPanel } from "../AgentClaimList/components/agentClaimHistoryPanel";
import { dateFormat } from "../../utils/utils";

export class AgentClaimHistoryFluentListBaseWithCommandBar extends FluentTableBase<AgentClaimHistoryDto> {
  disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;

  getItemDisplayNameOf(item: AgentClaimHistoryDto): string {
    return "";
  }

  getColumns(): ITableColumn[] {
    return this.getTableColumns(this.props);
  }

  private getTableColumns(props: any): ITableColumn[] {
    return [  
      {
        name: L('Claim ID'),
        fieldName: 'claimId',
      },        
      {
        name: L('Claim Status'),
        fieldName: 'status',
      },
      {
        name: L('Note Date'),
        fieldName: 'noteDate',
        onRender: (item: any): any => {
          return dateFormat(item.creationTime, "DD.MM.YYYY HH:mm", true)
        }
      },
      {
        name: L('Note'),
        fieldName: 'note',
        onRender: (item: any): any => {
          return <span title={item.note}>{item.note}</span>
        }
      }
    ];
  }

  getTitle(): string {
    return L('Claim History');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: true,
      customActions: false,
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <AgentClaimHistoryPanel
        {...props}
        store={this.props.store}
        customData={this.props.customData}
      />
    </>
  }
}