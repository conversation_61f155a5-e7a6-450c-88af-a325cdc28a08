import { CrudStoreBase } from './crudStoreBase';
import apkAttachedFilesService from '../services/apkAttachedFiles/apkAttachedFilesService';
import prizeAttachedFilesService from '../services/prizeAttachedFiles/prizeAttachedFilesService';
import { PrizeAttachedFilesDto } from '../services/prizeAttachedFiles/prizeAttachedFilesDto';
import { defaultPrize } from './prizeStore';

class PrizeAttachedFilesStore extends CrudStoreBase<PrizeAttachedFilesDto>{
  constructor() {
    super(prizeAttachedFilesService, defaultPrizeAttachedFile)
  }

  public async getAllFiles() {
		return apkAttachedFilesService.getAllFiles();
	}
}

export const defaultPrizeAttachedFile = {
  id: "",
  prizeId: 0,
  prize: defaultPrize,
  fileUrl: '',
  originalFileName: '',
  blobFileName: '',
  description: '',
}

export default PrizeAttachedFilesStore;