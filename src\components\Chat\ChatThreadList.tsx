import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Di<PERSON>Footer, <PERSON><PERSON>T<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>utton, <PERSON><PERSON>, <PERSON>nerSize, Stack } from '@fluentui/react';
import React, { useEffect, useRef, useState } from 'react';
import { L } from '../../lib/abpUtility';
import chatService from '../../services/chat/chatService';
import { ChatThreadDto } from '../../services/chat/chatThreadDto';
import { ChatUserDto } from '../../services/chat/chatUserDto';
import chatStore from '../../stores/chatStore';
import { myTheme } from '../../styles/theme';
import { dateFormatChat } from '../../utils/utils';
import { ThreadStatusEnum } from '../../services/chat/threadStatusEnums';
import { HubMessageTypeEnum } from '../../services/chat/hubMessageTypeEnums';

declare var abp: any;

interface IChatThreadListProps {
    users: ChatUserDto[];
    threads: ChatThreadDto[];
    asyncActionInProgress?: boolean;
    receivedMessagesData?: any;
    select: (threadId?: string, userId?: number, guestId?: number, sendChatMessage?: string, sendActionNotification?: HubMessageTypeEnum) => void;
    refreshChatStore?: () => void;
}

const ChatThreadList: React.FC<IChatThreadListProps> = (props: IChatThreadListProps) => {
    const { users, threads, receivedMessagesData, select } = props;
    const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
    const [chatThreadToDelete, setChatThreadToDelete] = useState<any>({});
    const [asyncChatActionError, setAsyncChatActionError] = useState<string>('');
    const [page, setPage] = useState(0)
    const [loader, setLoader] = useState(false)
    const [loading, setLoading] = useState(false)

    useEffect(() => {
        refreshThreads();
    }, [])

    const refreshThreads = () => {
        chatStore.threads = [];
        setPage(0);
    }

    const fetchData = async(page: number) => {
        setLoading(true);
        await chatStore.getAll(page*25);
        setLoading(false);
        setLoader(true);
    }

    useEffect(() => {
        fetchData(page);
    }, [page])

    const loadMore = () => {
        setPage(prevPage => prevPage + 1);
    }

    const pageEnd = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if(loader && pageEnd.current) {
            const observer = new IntersectionObserver(entries => {
                if(entries[0].isIntersecting) {
                    loadMore();
                }
            }, {threshold: 1});
            observer.observe(pageEnd.current);
        }
    }, [loader, pageEnd]);

    const findUsersByTopic = (topic: string): number[] => {
        const users = topic.split('_');
        return users.map(x => parseInt(x));
    }

    const findUserNameByTopic = (topic: string): string => {
        const ids = findUsersByTopic(topic);
        const otherId = ids.find(x => x !== abp.session.userId);
        const user = users.find(x => x.id === otherId);
        return `${user?.name}`;
    }

    const closeDeleteThreadDialog = () => {
        setIsDialogOpen(false);
        setTimeout(() => {
            setChatThreadToDelete({});
            setAsyncChatActionError('');
        }, 500);
    }

    let filterUsersTable: number[] = [];
    let threadComponents: any[] = [];
    
    if (threads && Array.isArray(threads)) {
        let filteredThreads: any[] = threads.filter(x => x.deletedOn === null);
        filteredThreads.forEach(x => {
            const users = findUsersByTopic(x.topic);
            filterUsersTable = [...filterUsersTable, ...users];
        });

        threadComponents = filteredThreads.map(x =>
            <div key={x.id} className={`chat__option ${props.asyncActionInProgress && 'chat__option--disabled'}`}
                onClick={() => { select(x.id); chatStore.setUsername(findUserNameByTopic(x.topic)); }}
            >
                <p className='chat__option-name'>{findUserNameByTopic(x.topic)}</p>
                <div>
                    {(receivedMessagesData && receivedMessagesData.counterOfReceivedMessagesPerThread && receivedMessagesData.counterOfReceivedMessagesPerThread[x.id]) &&
                        <div className='chat__option-circle'></div>
                    }
                    <Stack>
                        <p className='chat__option-date'>{dateFormatChat(x.lastMessageReceivedOn, undefined, true)}</p>
                        <p className={`chat__option-status ${x.status === ThreadStatusEnum.Ended ? 'chat__option-status--red' : ''}`}>{L(`Chat${x.status}`)}</p>
                    </Stack>

                    <span className={`chat__option-delete-icon--wrapper`}>
                        <Icon iconName="Delete" title={L("Delete")} className={`chat__option-delete-icon`} onClick={(event: any) => {
                            event.stopPropagation();
                            setChatThreadToDelete({ id: x.id, userName: findUserNameByTopic(x.topic), status: x.status });
                            setIsDialogOpen(true);
                        }} />

                        <Dialog
                            hidden={!isDialogOpen}
                            onDismiss={closeDeleteThreadDialog}
                            dialogContentProps={{
                                type: DialogType.normal,
                                title: !!asyncChatActionError ? asyncChatActionError : L("Are you sure you want to delete this chat thread?"),
                                subText: chatThreadToDelete.userName && (!asyncChatActionError || asyncChatActionError.length === 0) ? chatThreadToDelete.userName : '',
                                closeButtonAriaLabel: L('Close'),
                            }}
                            modalProps={{ isBlocking: false, styles: { main: { maxWidth: 450 } }, }}
                            theme={myTheme}
                        >
                            <DialogFooter theme={myTheme}>
                                <DefaultButton theme={myTheme} onClick={closeDeleteThreadDialog} text={L('No')} />
                                <PrimaryButton
                                    onClick={async () => {
                                        if (chatThreadToDelete && !!chatThreadToDelete.id) {
                                            let timeout: number = 0;
                                            if(chatThreadToDelete.status !== ThreadStatusEnum.Ended) {
                                                timeout = 2500;
                                                select(chatThreadToDelete.id, undefined, undefined, `#%#%${L('The chat has ended.')}#%#%`);
                                                // select(chatThreadToDelete.id, undefined, undefined, undefined, HubMessageTypeEnum.EndChat);
                                            }
                                            
                                            setTimeout(async () => {
                                                await chatService.deleteChatThread(chatThreadToDelete.id).then((response: any) => {
                                                    if (response.success && response.error === null) {
                                                        if (props.refreshChatStore){
                                                            props.refreshChatStore(); 
                                                            refreshThreads()
                                                        };
                                                    } else {
                                                        setAsyncChatActionError(L('Something went wrong. Try again later or contact with administrator.'));
                                                    }
                                                }).catch((error: any) => {
                                                    console.error(error);
                                                    setAsyncChatActionError(L('Something went wrong. Try again later or contact with administrator.'));
                                                });
                                                closeDeleteThreadDialog();
                                            }, timeout);
                                        }
                                    }}
                                    text={L('Yes')}
                                    theme={myTheme}
                                    disabled={!!asyncChatActionError}
                                />
                            </DialogFooter>
                        </Dialog>
                    </span>
                </div>
            </div>
        );
    }

    const userComponents = users
        .filter(x => !filterUsersTable.includes(x.id))
        .filter(x => x.id !== abp.session.userId)
        .map(x =>
            <div key={x.id} className={`chat__option chat__option--user ${props.asyncActionInProgress && 'chat__option--disabled'}`} onClick={() => { select(undefined, x.id) }}>
                {`${x.name} [${x.id}]`}
            </div>
        );

    if ((threadComponents.length + userComponents.length) < 1)
        return <Spinner label={''} className={`chat__load-spinner`} size={SpinnerSize.small} ariaLive="assertive" labelPosition="bottom" />

    return (
        <div>
            {threadComponents}
            {(chatStore.threadsTotalCount! > page*25+25) && <div ref={pageEnd}></div>}
            {loading && <Spinner className={`chat__load-spinner`} size={SpinnerSize.small} ariaLive="assertive" labelPosition="right" />}
        </div>
    );
}

export default ChatThreadList;