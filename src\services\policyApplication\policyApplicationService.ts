import { CrudServiceBase } from '../base/crudServiceBase';
import Endpoint from '../endpoint';
import { PolicyApplicationDto } from './policyApplicationDto';
import { httpApi } from '../httpService';

export class PolicyApplicationService extends CrudServiceBase<PolicyApplicationDto> {
    constructor() {
        super(Endpoint.PolicyApplication);
        this.internalHttp = httpApi;
    }

    async applicate(requestBody: any) {
        return await this.internalHttp.post(this.endpoint.Custom("Applicate", true), requestBody);
    }
}

const exportPolicyApplicationService: PolicyApplicationService = new PolicyApplicationService();
export default exportPolicyApplicationService;