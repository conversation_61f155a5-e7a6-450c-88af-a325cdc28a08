import * as React from 'react';
import { L } from '../../lib/abpUtility';
import { CommandBar, ICommandBarItemProps, ScrollablePane, ScrollbarVisibility, Sticky, StickyPositionType, ThemeProvider } from '@fluentui/react';
import { additionalTheme, myTheme } from '../../styles/theme';
import { classNames, CustomButton } from '../BaseComponents/commandBarBase';
import { AboutUsPage } from './components/aboutUsPage';
import { TermsAndConditionsPage } from './components/termsAndConditionsPage';
import { PrivacyPolicyPage } from './components/privacyPolicyPage';

export class About extends React.Component<any> {
  state = {
    currentPage: 'aboutUs',
  };

  protected getItems(): ICommandBarItemProps[] {
    let array: ICommandBarItemProps[] = [];
      array.push(this.getAboutUs());
      array.push(this.getPrivacyPolicy());
      array.push(this.getTermsAndConditions());
      return array;
  };

  commandBarClick = (currentItemName: string) => {
    this.setState({ currentPage: currentItemName });
  };

  protected getAboutUs(): ICommandBarItemProps {
    let currentItemName = 'aboutUs';
    return {
      key: currentItemName,
      text: L('About us'),
      className: classNames.buttons,
      style: {
        color: currentItemName === this.state.currentPage ? myTheme.palette.red : additionalTheme.grey,
        borderBottom: currentItemName === this.state.currentPage ? `5px solid ${additionalTheme.darkerRed}` : 'none',
      },
      onClick: (key) => {
        this.commandBarClick(currentItemName);
      },
    };
  };

  protected getTermsAndConditions(): ICommandBarItemProps {
    let currentItemName = 'termsAndConditions';
    return {
      key: currentItemName,
      text: L('Terms & Conditions'),
      className: classNames.buttons,
      style: {
        color: currentItemName === this.state.currentPage ? myTheme.palette.red : additionalTheme.grey,
        borderBottom: currentItemName === this.state.currentPage ? `5px solid ${additionalTheme.darkerRed}` : 'none',
      },
      onClick: () => {
        this.commandBarClick(currentItemName);
      },
    };
  };

  protected getPrivacyPolicy(): ICommandBarItemProps {
    let currentItemName = 'privacyPolicy';
    return {
      key: currentItemName,
      text: L('Privacy policy'),
      className: classNames.buttons,
      style: {
        color: currentItemName === this.state.currentPage ? myTheme.palette.red : additionalTheme.grey,
        borderBottom: currentItemName === this.state.currentPage ? `5px solid ${additionalTheme.darkerRed}` : 'none',
      },
      onClick: () => {
        this.commandBarClick(currentItemName);
      },
    };
  };

  renderCurrentPage(): JSX.Element {
    let currentPage = <AboutUsPage/>;

    switch (this.state.currentPage) {
      case 'aboutUs':
        currentPage = <AboutUsPage/>;
        break;
      case 'termsAndConditions':
        currentPage = <TermsAndConditionsPage/>;
        break;
      case 'privacyPolicy':
        currentPage = <PrivacyPolicyPage/>; 
        break;
      default:
        currentPage = <AboutUsPage/>;
        break;
    }

    return currentPage;
  };  

  public render() {
    let items = this.getItems();

    return (
      <>
        <div style={{ textTransform: "uppercase", display: "flex", alignItems: "center"}}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('About us')}</h2>
        </div>
        <ThemeProvider theme={myTheme}>
          <Sticky stickyPosition={StickyPositionType.Header}>
            <CommandBar theme={myTheme} items={items} className={classNames.customCommandbarAbout} buttonAs={CustomButton} />
          </Sticky>

          <ScrollablePane style={{marginTop:200}} scrollbarVisibility={ScrollbarVisibility.auto} theme={myTheme}>
            <Sticky stickyPosition={StickyPositionType.Header}>
              {this.renderCurrentPage()}
            </Sticky>
          </ScrollablePane>
        </ThemeProvider>
      </>
    );
  }
}

export default About;