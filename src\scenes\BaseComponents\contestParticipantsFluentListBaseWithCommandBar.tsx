import {FluentTableBase} from "../Fluent/base/fluentTableBase";
import {ITableColumn} from "./ITableColumn";
import {myTheme} from "../../styles/theme";
import {L} from "../../lib/abpUtility";
import {ICrudPermissons} from "./commandBarBase";
import {Default<PERSON>utton, Dialog, DialogContent, DialogFooter, DialogType, MessageBar, MessageBarType, PrimaryButton, SearchBox, Spinner, SpinnerSize, ThemeProvider, mergeStyleSets} from "@fluentui/react";
import {ClientContestDto} from "../../services/clientContest/dto/clientContestDto";
import { catchErrorMessage, dateFormat } from "../../utils/utils";
import { spinnerClassNames } from "../../styles/spinnerStyles";
import { LabeledTextField } from "../../components/LabeledTextField";
import clientContestService from "../../services/clientContest/clientContestService";
import { CheckBoxBase } from "./CheckBoxBase";
import { DatePickerBase } from "./datePickerBase";
import AppConsts from "../../lib/appconst";

const classNames = mergeStyleSets({
    dialog: {
        selectors: {
            '.ms-Dialog-main': {
                width: 'auto',
                maxWidth: '70%',
            }
        }
    },
    dialogContent: {
        selectors: {
            '.ms-Dialog-header': {
                display: 'none',
            },
            '.ms-Dialog-inner': {
                padding: 0,
            },
            'ol': {
                padding: '0 0 0 14px',
                margin: 0,
                selectors: {
                    'li': {
                        width: '100%',
                        marginBottom: 5,
                        padding: 2,
                        boxSizing: 'border-box',
                        selectors: {
                            ':nth-child(odd)': {
                                backgroundColor: 'rgba(0, 0, 0, 0.1)',
                            }
                        }
                    }
                }
            }
        }
    },
    messageBar: {
        width: 'fit-content',
        marginTop: '40px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
});

var _ = require('lodash');

export class ContestParticipantsFluentListBaseWithCommandBar extends FluentTableBase<ClientContestDto> {
    private debouncedOnSearchboxChange: any = _.debounce((e: any, newValue: string | undefined, customPayload?: any) => {
        newValue = typeof newValue === 'undefined' || newValue.length === 0 ? " " : newValue;
        if(this.props.customOnSearchTextChanged) {
            this.props.customOnSearchTextChanged(newValue);
        } else {
            this.overrideFilter(newValue);
        }
    }, AppConsts.defaultSerachBarDelay, []);

    disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;

    private showPopUpDialog: boolean = false;
    private asyncActionInProgress: boolean = false;
    private selectedItem: any | undefined = undefined;
    private editPrizeError: string | undefined = '';

    getItemDisplayNameOf(item: any): any {
        return item.client && item.client.fullName ? item.client.fullName : item.clientId;
    }

    getColumns(): ITableColumn[] {
        return this.getTableColumns(this.props);
    }

    private getTableColumns(props: any): ITableColumn[] {
        return [
            {
                name: L('Client'),
                fieldName: 'client.fullName',
                onRender: (item: any) => {
                    return item.client ? item.client.fullName : '-';
                }
            },
            {
                name: L('E-mail'),
                fieldName: 'client.emailAddress',
                onRender: (item: any) => {
                    return item.client && item.client.user ? item.client.user.emailAddress : '-';
                }
            },
            {
                name: L('Conditions meet'),
                fieldName: 'isConditionsMeet',
                onRender: (item: ClientContestDto) => {
                    return item.isConditionsMeet === true ? (
                        <span style={{ color: '#fff', backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
                    ) : (
                        <span>{L('No')}</span>
                    );
                }
            },
            {
                name: L('Is received'),
                fieldName: 'isReceived',
                maxWidth: 100,
                minWidth: 100,
                onRender: (item: ClientContestDto) => {
                    return item.isReceived === true ? (
                        <span style={{ color: '#fff', backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
                    ) : (
                        <span>{L('No')}</span>
                    );
                }
            },
            {
                name: L('Received prize'),
                fieldName: 'receivedPrize.name',
                maxWidth: 100,
                minWidth: 100,
                onRender: (item: ClientContestDto) => {
                    return item.receivedPrize && !!item.receivedPrize.name ? L(item.receivedPrize.name) : item.receivedPrizeId;
                }
            },
            {
                name: L('Prize number'),
                fieldName: 'prizeNumber',
                maxWidth: 100,
                minWidth: 100,
                onRender: (item: ClientContestDto) => {
                    return item.prizeNumber
                }
            },
            {
                name: L('Is prize send to client'),
                fieldName: 'isPrizeSentToClient',
                onRender: (item: ClientContestDto) => {
                    return item.isPrizeSentToClient === true ? (
                        <span style={{ color: '#fff', backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
                    ) : (
                        <span>{L('No')}</span>
                    );
                }
            },
            {
                name: L('Post date'),
                fieldName: 'postDate',
                onRender: (item: ClientContestDto) => {
                    return (new Date(item.postDate).getFullYear() < 1900 || item.isPrizeSentToClient !== true) ? '-' : dateFormat(item.postDate.toString(), "DD.MM.YYYY");
                },
            },
        ];
    }

    getCrudPermission(): ICrudPermissons {
        return {
            create: false,
            update: false,
            delete: false,
            customActions: true,
        };
    }

    getCommandBarBaseProps() {
        let props = super.getCommandBarBaseProps();

        let customActionsPropsWithoutButton = [{
            displayFor: 'single',
            buttonText: L(`Edit client's prize`),
            buttonIcon: "Giftbox",
        },
        ]
        return {
            ...props,
            customActionsProps: customActionsPropsWithoutButton,
            customActions: [
                (item: any) => {
                    this.selectedItem = {...item};
                    this.showPopUpDialog = true;
                    this.forceUpdate();
                },
            ]
        }
    }

    private closePopUpDialog() {
        this.showPopUpDialog = false;
        this.forceUpdate();

        setTimeout(() => {
            this.selectedItem = undefined;
            this.forceUpdate();
        }, 550);
    }

    renderAll(pageInfo: string, values: Array<ClientContestDto>, columns: any) {
        return <>
            <Dialog
                hidden={!this.showPopUpDialog}
                onDismiss={() => this.closePopUpDialog()}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L(`Edit client's prize`),
                    subText: this.selectedItem && this.selectedItem.client ? this.selectedItem.client.fullName : '',
                }}
                modalProps={{
                    isBlocking: true,
                    className: classNames.dialog,
                }}
            >
                <DialogContent className={classNames.dialogContent}>
                    <LabeledTextField key={'prizeNumber'} 
                        required={true} 
                        label={L('Prize number')}
                        value={this.selectedItem ? this.selectedItem.prizeNumber : ''} 
                        disabled={this.asyncActionInProgress} 
                        isDataLoaded={true}
                        onChange={(e: any, newValue?: string) => {
                            if(typeof newValue === 'string') {
                                this.selectedItem.prizeNumber = newValue;
                            } else {
                                this.selectedItem.prizeNumber = '';    
                            }
                            this.forceUpdate();
                        }}
                        labelContainerCustomStyles={{marginRight: '10px'}}
                    />

                    <CheckBoxBase key={'isPrizeSentToClient'} label={L("Prize sent to client")} 
                        value={this.selectedItem ? this.selectedItem.isPrizeSentToClient : false} 
                        disabled={this.asyncActionInProgress}
                        onChange={(value: boolean | undefined) => {
                            if(typeof value !== 'undefined') {
                                this.selectedItem.isPrizeSentToClient = value;
                            }
                            this.forceUpdate();
                        }}
                    />

                    <DatePickerBase key={'postDate'} label={L('Prize sent date')} 
                        value={(this.selectedItem && new Date(this.selectedItem.postDate).getFullYear() > 1900) ? this.selectedItem.postDate : ''} 
                        validationData={{minDate: new Date('1-1-1900'), maxDate: 'TODAY'}}
                        disabled={this.asyncActionInProgress} 
                        isDataLoaded={true} onChange={(value: string | undefined) => {
                            if(typeof value !== 'undefined') {
                                this.selectedItem.postDate = value;
                            }
                            this.forceUpdate();
                        }}
                    />

                    {!!this.editPrizeError &&
                        <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`} onDismiss={() => {this.editPrizeError = ''; this.forceUpdate();}}>
                            {this.editPrizeError}
                        </MessageBar>
                    }

                    {this.asyncActionInProgress && (
                        <Spinner className={`${spinnerClassNames.smallLoadSpinner}`} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" style={{marginTop: 15}} />
                    )}
                </DialogContent>
                
                <DialogFooter theme={myTheme}>
                    <PrimaryButton
                        onClick={async () => {
                            this.asyncActionInProgress = true;
                            this.forceUpdate();

                            await clientContestService.update(this.selectedItem).then((response: any) => {
                                this.getAll();
                                this.asyncActionInProgress = false;
                                this.editPrizeError = '';
                                this.closePopUpDialog();
                            }).catch((error: any) => {
                                console.error(error);
                                this.editPrizeError = catchErrorMessage(error);
                                this.asyncActionInProgress = false;
                            });
                            this.forceUpdate();
                        }}
                        disabled={this.selectedItem && !!this.selectedItem.postDate && !!this.selectedItem.prizeNumber && this.selectedItem.isPrizeSentToClient === true ? false : true}
                        text={L('Save')}
                        theme={myTheme}
                    />
                    <DefaultButton theme={myTheme} onClick={() => this.closePopUpDialog()} text={L('Cancel')} />
                </DialogFooter>
            </Dialog>

            <div style={{display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', alignItems: 'center', gap: 15}}>
                <SearchBox
                    theme={myTheme}
                    styles={{
                    root: {
                        flex: 1,
                        maxWidth: '252px !important',
                        height: '32px', 
                        backgroundColor: myTheme.palette.white,
                        border: `1px solid ${myTheme.palette.black}`,
                        boxSizing: 'border-box',
                    },
                    field: { borderRadius: '2px' },
                    }}
                    placeholder={ L('Search') }
                    onChange={ (e: any, newValue: string | undefined) => {
                        this.debouncedOnSearchboxChange(e, newValue);
                    }}
                />

                <p style={{margin: 0}}>
                    {L('Found contestants')}:{` ${this.props.store && this.props.store.dataSet && this.props.store.dataSet.totalCount ? this.props.store.dataSet.totalCount : 0}`}
                </p>
            </div>

            <ThemeProvider theme={myTheme}>
                { this.renderAnnounced(pageInfo) }
                { this.renderCommandBarBase() }

                { this.renderListScrollablePane(values, columns) }
            </ThemeProvider>
        </>;
    }

}