import {
  IBasePicker,
  IBasePickerSuggestionsProps,
  ITag,
  TagPickerBase,
} from '@fluentui/react';
import React, { useEffect, useState } from "react";
import { NamedEntityDto } from "../../services/dto/entityDto";
import Utils from "../../utils/utils";
import { LabelComponent } from "./labelComponent";
import { LabelContainerComponent } from "./labelContainerComponent";

export interface ITagPickerProps {
  onChange?: (value: NamedEntityDto[]) => void;
  value?: NamedEntityDto[];
  tagSource?: NamedEntityDto[];
  disabled?: boolean;
  label?: string;
  singleSelection?: boolean;
}

export const TagPicker = (props: ITagPickerProps) => {
  const [selectedTags, setSelectedTags] = useState<ITag[]>([]);
  const [tagSource, setTagSource] = useState<ITag[]>([]);

  useEffect(() => {
    try {
      if (props.value) {
        const newValue: ITag[] = props.value
          ? props.value?.map(entityToTag)
          : [];
        setSelectedTags(newValue);
        if (props.onChange) {
          props.onChange(props.value);
        }
      }
    } catch (error) {
      console.error(error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (props.tagSource) {
      setTagSource(props.tagSource.map(entityToTag));
    }
  }, [props.tagSource]);

  useEffect(() => {
    if (props.value) {
      const newValue: ITag[] = props.value
        ? props.value?.map(entityToTag)
        : [];
      setSelectedTags(newValue);
      if (props.onChange) {
        props.onChange(props.value);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.value]);

  const picker = React.useRef<IBasePicker<ITag>>(null);

  const pickerSuggestionsProps: IBasePickerSuggestionsProps = {
    suggestionsHeaderText: "Suggested tags",
    noResultsFoundText: "No tags found",
  };

  const listContainsTagList = (tag: ITag, tagList?: ITag[]) => {
    if (!tagList || !tagList.length || tagList.length === 0) {
      return false;
    }
    return tagList.some((compareTag) => compareTag.name === tag.name);
  };

  const filterSelectedTags = (
    filterText: string,
    tagList: ITag[] | undefined
  ): ITag[] => {
    const isSearchingTagExists = tagSource?.some((x) => x.name === filterText);
    const enrichedTagSource = isSearchingTagExists
      ? tagSource
      : [...tagSource, { name: filterText, key: 0 }];
    return filterText
      ? (enrichedTagSource !== undefined ? enrichedTagSource : []).filter(
        (tag) =>
          tag.name.toLowerCase().indexOf(filterText.toLowerCase()) === 0
      )
      : [];
  };

  const getTextFromItem = (item: ITag) => item.name;
  const onItemSelected = React.useCallback(
    (selectedTag: ITag | undefined): ITag | null => {
      // eslint-disable-next-line
      if (
        !selectedTag ||
        (picker.current &&
          listContainsTagList(selectedTag, picker.current.items))
      ) {
        return null;
      }

      setSelectedTags(Utils.addIfMissing(selectedTag, selectedTags, "name"));
      setTagSource(Utils.addIfMissing(selectedTag, tagSource, "name"));

      return selectedTag!;
    },
    []
  );

  const onChange = (selectedTags?: ITag[]) => {
    const newValue = selectedTags ? selectedTags?.map(tagToEntity) : [];
    if (props.onChange) {
      props.onChange(newValue);
    }
    if (selectedTags) {
      setSelectedTags(selectedTags);
    }
  };

  const tagToEntity = (tag: ITag): NamedEntityDto => ({
    name: tag.name as string,
    id: tag.key as number,
  });
  const entityToTag = (entity: NamedEntityDto): ITag => ({
    name: entity.name as string,
    key: entity.id as number,
  });

  return (
    <div>
      <LabelContainerComponent>
        <LabelComponent label={props.label || ''} />
        <TagPickerBase
          styles={{
            input: {
              width: '250px',
              height: '33px',
            }
          }}
          removeButtonAriaLabel="Remove"
          componentRef={picker}
          onResolveSuggestions={filterSelectedTags}
          onItemSelected={onItemSelected}
          getTextFromItem={getTextFromItem}
          pickerSuggestionsProps={pickerSuggestionsProps}
          disabled={props.disabled}
          selectedItems={selectedTags}
          onChange={onChange}
          itemLimit={props.singleSelection && props.singleSelection === true ? 1 : undefined}
        />
      </LabelContainerComponent>
    </div>
  );
};
