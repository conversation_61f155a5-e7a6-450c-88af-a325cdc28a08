import { DocumentCard, Text, mergeStyleSets } from '@fluentui/react';
import React from 'react';
import { myTheme } from '../../../../styles/theme';

interface IDashboardBtnProps {
  title: string;
  link?: string;
  onClick?: (ev?: React.SyntheticEvent<HTMLElement>) => void;
}
const classNames = mergeStyleSets({
  dashboardBtn: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
    margin: 10,
    marginTop: 20,
    width: 283,
    height: 91,
    flex: 1,
    backgroundColor: myTheme.palette.themePrimary,
  },
  btnTitle: {
    textTransform: 'uppercase',
    fontSize: 18,
    color: myTheme.palette.white ,
    alignText: 'center',
    align: 'center',
    font: 'Helvetica Neue',
  },
});

class DashboardBtnWithoutIcon extends React.Component<IDashboardBtnProps> {
  render() {
    const { title, link, onClick } = this.props;

    return (
      <DocumentCard aria-label={title} onClick={onClick} onClickHref={link} className={classNames.dashboardBtn} theme={myTheme}>
        <Text className={classNames.btnTitle} variant="smallPlus" theme={myTheme}>
          {title}
        </Text>
      </DocumentCard>
    );
  }
}
export default DashboardBtnWithoutIcon;