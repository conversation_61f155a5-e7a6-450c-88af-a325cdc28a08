import {FluentTableBase} from "../Fluent/base/fluentTableBase";
import {ITableColumn} from "./ITableColumn";
import {myTheme} from "../../styles/theme";
import {L} from "../../lib/abpUtility";
import {ICrudPermissons} from "./commandBarBase";
import {DefaultButton, Di<PERSON>, DialogFooter, PrimaryButton, TextField, ThemeProvider} from "@fluentui/react";
import { TravelCountryDto } from "../../services/travelCountry/dto/travelCountryDto";
import travelCountryService from "../../services/travelCountry/travelCountryService";

const dialogStyles = {
    main: {
        selectors: {
            '@media (min-width: 0px)': {
                maxWidth: 500,
                width: 500
            }
        }
    }
};

export class GeneralCountryFluentListBaseWithCommandBar extends FluentTableBase<TravelCountryDto> {
    private shouldReloadItems: boolean = false;
    private showPopUpDialog: boolean = false;
    private selectedCoverageType: string = "";
    private countryName: string = "";
    private createToEditFlag: boolean = false;
    
    disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;

    getColumns(): ITableColumn[] {
        return this.getTableColumns(this.props);
    }

    private getTableColumns(props: any): ITableColumn[] {
        return [
            {
                name: L('Country'),
                fieldName: 'country',
                onRender: (item: TravelCountryDto) => {
                    return L(item.name);
                }
            }
        ];
    }

    getCrudPermission(): ICrudPermissons {
        return {
            create: false,
            update: false,
            delete: true,
            customActions: true,
        };
    }

    getCommandBarBaseProps() {
        let props = super.getCommandBarBaseProps();
        return {
            ...props,
            customActionsProps: [
                {
                    displayFor: 'none',
                    buttonText: L("New"),
                    buttonIcon: "Add",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                {
                    displayFor: 'single',
                    buttonText: L("Edit"),
                    buttonIcon: "edit",
                    buttonColor: myTheme.palette.black,
                    buttonIconColor: myTheme.palette.black,
                    buttonBackground: myTheme.palette.white,
                }
            ],
            customActions: [
                () => {
                    this.createToEditFlag = true;
                    this.countryName = '';
                    this.handleShowPopUpDialog();
                },
                () => {
                    this.createToEditFlag = false;
                    this.getTravelCountry();
                }
            ]
        }
    }

    private createTravelCountry = async (name: string) => {
        await travelCountryService.create({
            name: name,
            id: '0'
        }).then((response: any) => {
            if(response) {
                this.showPopUpDialog = false;
                this.reloadItems();
            }
        }).catch((error: any) => {
            console.error(error);
        })
        this.forceUpdate();
    }

    private getTravelCountry = async () => {
        await  travelCountryService.get({id: this.props.customData.selectedTravelCountry.id}).then((response: any) => {
            if(response) {
                this.countryName = response.name
                this.showPopUpDialog = true;
                this.reloadItems();
             }
        })
        this.forceUpdate();
    }

    private editTravelCountry = async (name: string) => {
        await travelCountryService.update({
            name: name,
            id: this.props.customData.selectedTravelCountry.id
        }).then((response: any) => {
            if(response) {
                this.showPopUpDialog = false;
                this.reloadItems();
            }
        }).catch((error: any) => {
            console.error(error);
        })
        this.forceUpdate();
    }

    private handleShowPopUpDialog() {
        this.selectedCoverageType = '';
        this.showPopUpDialog = true;
        this.forceUpdate();
    }

    private reloadListOnDialogClose() {
        this.showPopUpDialog = false;
    
        if(this.shouldReloadItems) {
            this.reloadItems();
        }
    
        this.forceUpdate();
    }

    private handleSaveDialog(name: string) {
        if(this.createToEditFlag) {
            this.createTravelCountry(name)
        } else {
            this.editTravelCountry(name)
        }
    }

    private async reloadItems() {
        this.selectionSetAllSelected(false);
        if(typeof this.props.refreshItems !== 'undefined') {
            await this.props.refreshItems!();
        }
    }  

    renderAll(pageInfo: string, values: any, columns: any) {
        return <>
            <Dialog
                hidden={!this.showPopUpDialog}
                onDismiss={() => this.reloadListOnDialogClose()}
                modalProps={{
                    isBlocking: true,
                    styles: dialogStyles
                }}
            >
                <TextField 
                    label={L("Enter the country name")}
                    value={this.countryName}
                    onChange={(e: any, newValue?: string) => {
                        this.countryName = newValue || '';
                        this.forceUpdate();
                    }}
                />
                <DialogFooter theme={myTheme}>
                    <PrimaryButton
                        onClick={() => {
                            this.handleSaveDialog(this.countryName);
                        }}
                        text={L('Save')}
                        theme={myTheme}
                    />
                    <DefaultButton theme={myTheme} onClick={() => this.reloadListOnDialogClose()} text={L('Cancel')} />
                </DialogFooter>
            </Dialog>
            
            <ThemeProvider theme={myTheme}>
                { this.renderAnnounced(pageInfo) }
                { this.renderCommandBarBase() }

                { this.renderListScrollablePane(values, columns) }
            </ThemeProvider>

            {this.renderDialog()}
        </>;
    }
}