import { PrimaryButton } from '@fluentui/react';
import { CalculationDto } from '../../../services/calculation/dto/calculationDto';
import { CalculationContentView } from '../../Calculation/components/calculationContentView';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { myTheme } from '../../../styles/theme';
import { L } from '../../../lib/abpUtility';

export class CalculationPanel extends GenericPanel {
    getPanelTitle(): string {
        return "Calculation"
    };

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} onClick={() => this.props.customData.createPolicyFromOffer()} text={L('Save')} disabled={this.asyncActionInProgress} />;
    };

    renderContent() {
        return <CalculationContentView store={this.props.store}  createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as CalculationDto } />;
    };
}