import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { RouterPath } from "../../../components/Router/router.config";
import { dateFormat } from "../../../utils/utils";
import { Icon, Link } from "@fluentui/react";
import { AgentClaimPanel } from "./agentClaimPanel";
import { additionalTheme, myTheme } from "../../../styles/theme";
import { AgentClaimDto } from "../../../services/agentClaim/dto/agentClaimDto";

export class AgentClaimTable extends FluentTableBase<AgentClaimDto> {
  getItemDisplayNameOf(item: AgentClaimDto): string {
    return `${item.id}`;
  }

  getColumns(): ITableColumn[] {
    return AgentClaimTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('ID'),
        fieldName: 'id',
        minWidth: 20,
        maxWidth: 20,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.AgentClaim}/${item.id}/${item.policyType}`);
                      }} 
                        href={`/${RouterPath.AgentClaim}/${item.id}/${item.policyType}`}>
                  {item.id}
                </Link>
        }
      },
      {
        name: L('DOZS'),
        fieldName: 'claimStatusUpdateDate',
        minWidth: 40,
        maxWidth: 40,
        onRender: (item: any): any => {
          const getBackgroundColor = (value: any) => {
            if (item.daysSinceStatusChanged < 10) return myTheme.palette.green;
            if (item.daysSinceStatusChanged < 14 ) return additionalTheme.yellow;
            if (item.daysSinceStatusChanged < 29) return additionalTheme.brown;
            
            return myTheme.palette.red;
          };

          const getTextColor = (value: any) => {
            if (item.daysSinceStatusChanged < 14 ) return myTheme.palette.black;
            
            return myTheme.palette.white;
          };

          const bgColor = getBackgroundColor(item.daysSinceStatusChanged);
          const textColor = getTextColor(item.daysSinceStatusChanged);
          
          return (
            <div style={{backgroundColor: bgColor, color: textColor, padding: '2px 12px', borderRadius: '2px', textAlign: 'center'}}>
              {item.daysSinceStatusChanged}
            </div>
          );
        }
      },
      {
        name: L('Update date'),
        fieldName: 'claimStatusUpdateDate',
        minWidth: 80,
        maxWidth: 80,
        onRender: (item: any): any => {
          return dateFormat(item.claimStatusUpdateDate, "DD.MM.YYYY", true);
        }
      },
      {
        name: L('Claim status'),
        fieldName: 'claimStatus',
        minWidth: 80,
        maxWidth: 80,
      },
      {
        name: L('Claim number'),
        fieldName: 'claimNumber',
        minWidth: 140,
        maxWidth: 140,
      },
      {
        name: L('Client'),
        fieldName: 'client',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Customer}/${item.clientId}`);
                      }} 
                        href={`/${RouterPath.Customer}/${item.clientId}`}>
                  {item.client && !!item.client.fullName ? item.client.fullName : (item.client && item.client.user && !!item.client.user.fullName ? item.client.user.fullName : item.clientId)}
                </Link>
        }
      },
      {
        name: L('Policy number'),
        fieldName: 'policyNumber',
        minWidth: 100,
        maxWidth: 100,
        onRender: (item: any): any => {
          return item.policy ? item.policy.policyNumber : "";
        }
      },
      {
        name: L('Claim subject'),
        fieldName: 'claimSubject',
        minWidth: 100,
        maxWidth: 100,
      },
      {
        name: L('Assist'),
        fieldName: 'agent',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.RegisteredUser}/${item.agentId}`);
                      }} 
                        href={`/${RouterPath.RegisteredUser}/${item.agentId}`}>
                  {item.client && !!item.agent.fullName ? item.agent.fullName : item.agentId}
                </Link>
        }
      },
      {
        name: L('Claim type'),
        fieldName: 'insuranceClaimType',
        minWidth: 80,
        maxWidth: 80,
        onRender: (item: any): any => {
          return L(item.insuranceClaimType) || "";
        }
      },
      {
        name: L('Claim date'),
        fieldName: 'claimDate',
        minWidth: 80,
        maxWidth: 80,
        onRender: (item: any): any => {
          return dateFormat(item.claimDate, "DD.MM.YYYY", true);
        }
      },
      {
        name: L('DLS'),
        fieldName: 'applicationDate',
        minWidth: 40,
        maxWidth: 40,
        onRender: (item: any): any => {
          return item.daysSinceClaimReport;
        }
      },
      {
        name: L('Email'),
        fieldName: 'emailAddress',
        minWidth: 100,
        maxWidth: 100,
      },
      {
        name: L('Compensation paid'),
        fieldName: 'compensationPaid',
        minWidth: 50,
        maxWidth: 50,
        onRender: (item: any) => {
          return <Icon style={{ color: !!item.compensationPaid && item.compensationPaid === true ? "green" : "red", margin: '0 auto', display: 'block', width: 'min-content'}} 
                      iconName={ !!item.compensationPaid && item.compensationPaid === true ? "SkypeCheck" : "StatusCircleErrorX" } />
        }
      },
      {
        name: L('Is paid'),
        fieldName: 'invoicePaid',
        minWidth: 50,
        maxWidth: 50,
        onRender: (item: any) => {
          return <Icon style={{ color: !!item.invoicePaid && item.invoicePaid === true ? "green" : "red", margin: '0 auto', display: 'block', width: 'min-content'}} 
                      iconName={ !!item.invoicePaid && item.invoicePaid === true ? "SkypeCheck" : "StatusCircleErrorX" } />
        }
      },
      {
        name: L('Policy ID'),
        fieldName: 'policyId',
        minWidth: 100,
        maxWidth: 100,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.InsurancePolicy}/${item.policyId}`);
                      }} 
                        href={`/${RouterPath.InsurancePolicy}/${item.policyId}`}>
                  {item.policyId}
                </Link>
        }
      },
      {
        name: L('Note'),
        fieldName: 'note',
        onRender: (item: any): any => {
          return <span title={item.note}>{item.note}</span>
        }
      },
    ];
  }

  getTitle(): string {
    return L('Claims');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: true,
      customActions: false,
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <AgentClaimPanel
        {...props}
        store={this.props.store}
      />
    </>
  }
}