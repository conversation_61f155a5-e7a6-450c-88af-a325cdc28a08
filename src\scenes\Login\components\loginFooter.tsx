import * as React from 'react';
import { mergeStyleSets, Link, Image, Text, Stack } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { myTheme } from '../../../styles/theme';
import appStore from '../../../images/appStore.png';
import googlePlay from '../../../images/googlePlay.png';
import asoftLogo from '../../../images/asoft_logo.png';
import { AboutAppText } from '../../../styles/aboutApp';

const classNames = mergeStyleSets({
  footer: {
    position: 'fixed',
    left: 0,
    bottom: 0,
    width: '100%',
    textAlign: 'center',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: myTheme.palette.white,
    boxShadow: `5px 0 10px ${myTheme.palette.neutralQuaternaryAlt}`,
    padding: '10px 15px 10px 20px'
  },
  mobileAppWrapper: {
    width: 350,
    display: 'flex',
    alignItems: 'center',
  },
  mobileAppText: {
    fontWeight: 'bold',
    textAlign: 'center',
    display: 'inline-block',
    margin: 0,
    lineHeight: 1,
  },
  linkImg: {
    margin: '0 5px',
    selectors: {
      '& img': {
        width: 80,
        height: 27
      },
    },
  },
  logo: {
    padding: '4px',
    selectors: {
        '& img': {
          width: 80,
        },
      },
  },
});

class LoginFooter extends React.Component {
  render() {
    return (
      <footer className={classNames.footer}>
        <Stack horizontal horizontalAlign="center" wrap tokens={{childrenGap: 'l1'}} style={{width: '100%'}}>
          <Stack.Item align="center">
            <Link href="http://a-soft.pl/">
              <Image src={asoftLogo} className={classNames.logo} />
            </Link>
          </Stack.Item>
          
          <Stack.Item align="center" grow disableShrink>
            {AboutAppText}
          </Stack.Item>
          
          {/* <div className={classNames.mobileAppWrapper}> */}
          <Stack horizontal horizontalAlign="center" wrap style={{paddingRight: '25px'}}>
            <Stack.Item align="center">
              <Text theme={myTheme} className={classNames.mobileAppText} variant="medium">
                {L('Mobile app:')}
              </Text>
            </Stack.Item>
            <Stack.Item align="center">
              <Link theme={myTheme}>
                <Image theme={myTheme} className={classNames.linkImg} src={appStore} alt="download on the app store" />
              </Link>
              <Link theme={myTheme}>
                <Image theme={myTheme} className={classNames.linkImg} src={googlePlay} alt="android app on google play" />
              </Link>
            </Stack.Item>
          </Stack>
        </Stack>
      </footer>
    );
  }
}

export default LoginFooter;
