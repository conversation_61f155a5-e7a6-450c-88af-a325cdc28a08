import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { TestSetDto } from '../../../services/testSet/dto/testSetDto';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { TestSetPanel } from './testSetPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { RouterPath } from "../../../components/Router/router.config";
import { Dialog, DialogType, Link, mergeStyleSets } from "@fluentui/react";
import { catchErrorMessage, dateFormat } from "../../../utils/utils";
import testSetService from "../../../services/testSet/testSetService";
import moment from "moment";
import {additionalTheme, myTheme} from "../../../styles/theme";

const classNames = mergeStyleSets({
  dialog: {
    selectors: {
      '.ms-Dialog-subText': {
        whiteSpace: 'pre-line',
      }
    }
  }
});

export class TestSetTable extends FluentTableBase<TestSetDto> {
  private customActionButtonDisabled: boolean = false;
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";
  
  getColumns(): ITableColumn[] {
    return TestSetTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Name'),
        fieldName: 'name',
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.TestSet}/${item.id}`);
                      }} 
                    href={`/${RouterPath.TestSet}/${item.id}`}
                    title={item.name}
                  >
                  {item.name}
                </Link>
        }
      },
      {
        name: L('Comment'),
        fieldName: 'comment',
        minWidth: 90,
        maxWidth: 90,
        onRender: (item: TestSetDto) => {
          return <p style={{margin: 0}} title={item.comment}>{item.comment.split('(')[0]}</p>;
        }
      },
      {
        name: L('Is success'),
        fieldName: 'isSuccess',
        minWidth: 70,
        maxWidth: 70,
        onRender: (item: TestSetDto) => {
          return !item.lastTestDate || moment(item.lastTestDate).year() === 1 ? 
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.orange, padding: '2px 5px', borderRadius: '2px' }}>{L('Not tested')}</span>
            : (item.isSuccess ? (
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.green, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
            ) : (
            <span style={{ color: additionalTheme.white, backgroundColor: additionalTheme.lighterRed, padding: '2px 5px', borderRadius: '2px' }}>{L('No')}</span>
            )
          );
        }
      },
      {
        name: L('Last test date'),
        fieldName: 'lastTestDate',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: TestSetDto) => {
          return <p style={{margin: 0}}>
            {!item.lastTestDate || moment(item.lastTestDate).year() === 1 ? L('Not tested') : dateFormat(item.lastTestDate, undefined, true)}
          </p>;
        }
      },
      {
        name: L('Creation time'),
        fieldName: 'creationTime',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any) => {
          return <p style={{margin: 0}}>
            {item.creationTime ? dateFormat(item.creationTime, undefined, true) : '-'}
          </p>;
        }
      },
      {
        name: L('Test for'),
        fieldName: 'testFor',
        minWidth: 80,
        maxWidth: 80,
        onRender: (item: TestSetDto) => {
          return <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.green, padding: '2px 5px', borderRadius: '2px' }}>{!!item.insurerName ? item.insurerName : L('All')}</span>;
        }
      },
      {
        name: L('Error details'),
        fieldName: 'errorDetails',
        onRender: (item: TestSetDto) => {
          return <p style={{margin: 0}} title={item.errorDetails}>{item.errorDetails}</p>;
        }
      },
    ];
  }

  getTitle(): string {
    return L('TestSet list');
  }

  private togglePopUpDialog(title: string, text: string, visibility?: boolean) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.forceUpdate();
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: true,
      customActions: true,
    };
  }

  private async sendCalculationFromList(testSet: any, that: any) {
    if(testSet.payload !== "") {
      await testSetService.sendTestCalculation(testSet.id).then((response: any) => {
        if(response && response.data && response.data.result) {
          this.togglePopUpDialog("Test executed successfully", L("You can check test result details in it's details."));
        } else {
          this.togglePopUpDialog("Something went wrong", "Response from server is not valid.");
        }

        that.toggleCustomActionButton(false, true);
      }).catch((error) => {
        this.togglePopUpDialog("Something went wrong", catchErrorMessage(error));
        that.toggleCustomActionButton(false, true);
      });
    } else {
      this.togglePopUpDialog("Something went wrong", "Not enough data available.");
      that.toggleCustomActionButton(false, true);
    }
    
    that.toggleCustomActionButton(false, true);
    this.reloadItems();
  }

  private async sendMultipleCalculationsFromList(testSet: any, that: any) {
    let items = that.props && that.props.selection ? that.props.selection.getSelection() : [];
    this.sendTestsFromList(items, testSet, that);
  }

  private async sendAllCalculationsFromList(testSet: any, that: any) {
    let items = this.props && this.props.items ? this.props.items : [];
    this.sendTestsFromList(items, testSet, that);
  }

  private async sendTestsFromList(items: any[], testSet: any, that: any) {
    let successCount: number = 0;
    let error1Count: number = 0;
    let error2Count: number = 0;
    let error3Count: number = 0;
    let error3Text: string = "";

    for (const testSet of items) {
      if(testSet.payload !== "") {
        // eslint-disable-next-line
        await testSetService.sendTestCalculation(testSet.id).then((response: any) => {
          if(response && response.data && response.data.result) {
            successCount++;
          } else {
            error1Count++;
          }
        // eslint-disable-next-line
        }).catch((error) => {
          error3Text += catchErrorMessage(error) + '\n\r';
          error3Count++;
        });
      } else {
        error2Count++;
      }
    };
    this.togglePopUpDialog("Results", `${successCount}x - ${L('Completed successfully')}\n\r\n\r
                                      ${error1Count}x - ${L('Response from server is not valid.')}\n\r\n\r
                                      ${error2Count}x - ${L('Not enough data available.')}\n\r\n\r
                                      ${error3Count}x - ${L('Other')}:\n\r${error3Text}`);

    that.toggleCustomActionButton(false, true);
    this.reloadItems();
  }
  
  private async reloadItems() {
    this.selectionSetAllSelected(false);
    if(typeof this.props.refreshItems !== 'undefined') {
      await this.props.refreshItems!();
    } else {
      this.togglePopUpDialog("Error", "The list could not be refreshed automatically, please refresh the page manually.");
    }
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    return {
      ...props,
      setButtons: () => {
        return {
          'newItem': {text: 'New', icon: 'Add'},
          'delete': {text: 'Delete'},
          'edit': {text: 'Details'},
          'newMsg': {text: 'Msg', icon: ''},
        }
      },
      customActionsProps: [
        {
          displayFor: 'none',
          buttonText: L("Perform tests for all"),
          buttonIcon: "none",
          buttonDisabled: false,
        },
        {
          displayFor: 'single',
          buttonText: L("Perform test"),
          buttonIcon: "none",
          buttonDisabled: false,
        },
        {
          displayFor: 'multiple',
          buttonText: L("Perform tests for selected"),
          buttonIcon: "none",
          buttonDisabled: false,
        },
        // {
        //   displayFor: 'single',
        //   buttonText: L("Go to results page"),
        //   buttonIcon: "Tiles",
        //   buttonDisabled: false,
        //   buttonColor: myTheme.palette.white,
        //   buttonIconColor: myTheme.palette.white,
        //   buttonBackground: myTheme.palette.themeTertiary,
        // }
      ],
      customActions: [
        async (testSet: TestSetDto, that: any) => { // for all
          that.toggleCustomActionButton(true, true);
          await this.sendAllCalculationsFromList(testSet, that);
        },
        async (testSet: TestSetDto, that: any) => { // for single
          that.toggleCustomActionButton(true, true);
          await this.sendCalculationFromList(testSet, that);
        },
        async (testSet: TestSetDto, that: any) => { // for multiple
          that.toggleCustomActionButton(true, true);
          await this.sendMultipleCalculationsFromList(testSet, that);
        },
        // ######## TODO - DEV - saved for maybe future feature - connected with policyCalculationContentView componentDidMount
        // async (testSet: TestSetDto, that: any) => {
          // if(isLocalStorageAvailable()) {
          //   saveInStorage('calculateAllSavedResult', testSet.result);
          //   this.props.history.push(`/${RouterPath.PolicyCalculation}`);
          // } else {
          //   alert(L('Unfortunately Local Storage in your browser is unavailable.'));
          // }
        // }
      ]
    }
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <Dialog
        hidden={!this.showPopUpDialog}
        onDismiss={() => { this.showPopUpDialog = false; this.forceUpdate(); }}
        dialogContentProps={{
            type: DialogType.normal,
            title: L(this.popUpDialogTitle),
            subText: L(this.popUpDialogText),
        }}
        modalProps={{
            isBlocking: true
        }}
        className={classNames.dialog}
      >
      </Dialog>

      <TestSetPanel
        {...props}
      />
    </>
  }
}