import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { CrudServiceBase } from '../base/crudServiceBase';
import { isUserLoggedIn } from '../../utils/authUtils';
import { AgentClaimAmeStatusAttachedFilesDto } from './dto/agentClaimAmeStatusAttachedFilesDto';
import { getPartialModel } from '../../utils/modelUtils';

export class AgentClaimAmeStatusAttachedFilesService extends CrudServiceBase<AgentClaimAmeStatusAttachedFilesDto> {
    constructor() {
        super(Endpoint.AgentClaimAmeStatusAttachedFiles);
        this.internalHttp = httpApi;
    }

    public async createNew(createAgentClaimAmeStatusAttachedFileInput: AgentClaimAmeStatusAttachedFilesDto) {
        isUserLoggedIn();

        const newCreateAgentClaimAmeStatusAttachedFileInput = getPartialModel(createAgentClaimAmeStatusAttachedFileInput, [], ['id', 'ameStatus']);

        let result = await httpApi.post(this.endpoint.Create(), newCreateAgentClaimAmeStatusAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByAmeStatus(ameStatusId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetByAmeStatus?ameStatusId=${ameStatusId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getOpinionsByAmeStatus(ameStatusId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetOpinionsByAmeStatus?ameStatusId=${ameStatusId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getQuotationByAmeStatus(ameStatusId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetQuotationByAmeStatus?ameStatusId=${ameStatusId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getFileApprovedQuotationByAmeStatus(ameStatusId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetFileApprovedQuotationByAmeStatus?ameStatusId=${ameStatusId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getFvByAmeStatus(ameStatusId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetFvByAmeStatus?ameStatusId=${ameStatusId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getCostEstimateForeignByAmeStatus(ameStatusId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetCostEstimateForeignByAmeStatus?ameStatusId=${ameStatusId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getCostEstimatePlByAmeStatus(ameStatusId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetCostEstimatePLByAmeStatus?ameStatusId=${ameStatusId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getPaymentToClientByAmeStatus(ameStatusId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetPaymentToClientByAmeStatus?ameStatusId=${ameStatusId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportAgentClaimAmeStatusAttachedFilesService: AgentClaimAmeStatusAttachedFilesService = new AgentClaimAmeStatusAttachedFilesService();
export default exportAgentClaimAmeStatusAttachedFilesService;