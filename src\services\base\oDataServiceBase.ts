import { httpGrandNode } from '../httpService';
import { EntityStringDto } from '../dto/entityStringDto';
import { PagedResultDto } from '../dto/pagedResultDto';
import { PagedResultRequestDto } from "./pagedResultRequestDto";
import Endpoint from '../endpoint';
import { CrudServiceBase } from './crudServiceBase';
import { isUserLoggedIn } from '../../utils/authUtils';

export class ODataServiceBase<TDto extends EntityStringDto> extends CrudServiceBase<TDto> {
    constructor(endpoint: Endpoint) {
        super(endpoint, httpGrandNode);
    }

    public createDataModel(result: any) {
        let toReturn;

        if(result.data.value && result.data.value.length > 0) {
            result.data.value.forEach((element: any) => {
                if(element && element.Id) { element.id = element.Id; }
            });
            toReturn = result.data.value;
        } else if(result.data && result.data.result && result.data.result.items && result.data.result.items.length > 0) {
            result.data.result.items.forEach((element: any) => {
                if(element && element.Id) { element.id = element.Id; }
            });
            toReturn = result.data.result.items;
        } else if(result.data) {
            if(Array.isArray(result.data)) {
                result.data.forEach((element: any) => {
                    if(element && element.Id) { element.id = element.Id; }
                });
            }
            toReturn = result.data;
        }

        return toReturn;
    }

    public async get(entityDto: TDto): Promise<TDto> {
        isUserLoggedIn();
        let result = await this.internalHttp.get(this.endpoint.Get(entityDto.id));
        
        if(result.data.value && result.data.value.length > 0) {
            if(result.data.value[0].Id) {
                result.data.value[0].id = result.data.value[0].Id;
            }
            return result.data.value[0];
        } else {
            if(result.data.Id) {
                result.data.id = result.data.Id;
            }
            return result.data;
        }
    }

    public async getAll(pagedFilterAndSortedRequest: PagedResultRequestDto): Promise<PagedResultDto<TDto>> {
        isUserLoggedIn();
        let result = await this.internalHttp.get(this.endpoint.GetAll(), { params: pagedFilterAndSortedRequest });
        let toReturn = this.createDataModel(result);

        return {
            totalCount: toReturn.length,
            items: toReturn
        };
    }

    public async create(createUserInput: TDto) {
        isUserLoggedIn();
        let result = await this.internalHttp.post(this.endpoint.Create(), createUserInput);
        result.data.id = result.data.Id;
        return result.data;
    }

    public async update(updateUserInput: TDto) {
        isUserLoggedIn();
        let result = await this.internalHttp.put(this.endpoint.Update(), updateUserInput);
        return result.data;
    }

    public async delete(entityStringDto: TDto) {
        isUserLoggedIn();
        let result = await this.internalHttp.delete(this.endpoint.Delete(), { params: { key: entityStringDto.id } });
        return result.data;
    }
}
