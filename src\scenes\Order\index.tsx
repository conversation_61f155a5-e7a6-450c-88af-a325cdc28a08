import { inject, observer } from 'mobx-react';

import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import OrderStore from '../../stores/orderStore';
import { OrderDto } from '../../services/order/dto/orderDto';
import { OrderContentView } from './components/orderContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	orderStore: OrderStore;
	match: any
}

@inject(Stores.OrderStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private orderId = this.props.match.params.id;

	async componentDidMount() {
		await this.props.orderStore.get({ id: this.orderId } as OrderDto);
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<OrderContentView store={this.props.orderStore} payload={ this.props.orderStore.model as OrderDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;