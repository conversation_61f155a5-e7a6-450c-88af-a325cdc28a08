import { ProductDto } from '../../../services/product/productDto';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { TravelCountryContentView } from '../../Product/components/travelCountryContentView';

export class TravelCountryPanel extends GenericPanel {
    getPanelTitle(): string {
        return `${L("Country list")} - ${this.props.customData.insuranceName}`;
    }

    renderConfirm = () => {
        return <></>;
    };

    renderContent() {
        return <TravelCountryContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ProductDto } customData={this.props.customData} />;
    }
} 