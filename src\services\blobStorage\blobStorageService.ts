import { SasResult } from "../azureService";
import { ServiceBase } from "../base/serviceBase";
import Endpoint from "../endpoint";
import http from "../httpService";

export class BlobStorageService extends ServiceBase {
  constructor() {
    super(Endpoint.BlobStorage)
  }

  public async GenerateSasTokenForFile(fileNameWithExtension: string): Promise<SasResult> {
    let result = await http.post(this.endpoint.Custom(`GenerateSasTokenForFile?fileNameWithExtension=${fileNameWithExtension}`));
    return result.data.result;
  }

  public async GenerateSasTokenForRodo(fileNameWithExtension: string): Promise<SasResult> {
    let result = await http.post(this.endpoint.Custom(`GenerateSasTokenForRodo?fileNameWithExtension=${fileNameWithExtension}`));
    return result.data.result;
  }

  public async getSasTokenForFile(fileNameWithExtension: string): Promise<SasResult> {
    let result = await http.get(this.endpoint.Custom("GetSasTokenForFile"), { params: { fileNameWithExtension } });
    return result.data.result;
  }

  public async getLinkToFile(fileNameWithExtension: string): Promise<string> {
    let result = await http.get(this.endpoint.Custom("GetLinkToFile"), { params: { fileNameWithExtension } });
    return result.data.result;
  }

  public async getSasTokenForImage(fileNameWithExtension: string): Promise<SasResult> {
    let result = await http.get(this.endpoint.Custom("GetSasTokenForImage"), { params: { fileNameWithExtension } });
    return !!result.data && !!result.data.result ? result.data.result : result.data;
  }

  public async getLinkToImage(fileNameWithExtension: string): Promise<string> {
    let result = await http.get(this.endpoint.Custom("GetLinkToImage"), { params: { fileNameWithExtension } });
    return !!result.data && !!result.data.result ? result.data.result : result.data;
  }
}

const exportBlobStorageService: BlobStorageService = new BlobStorageService();
export default exportBlobStorageService;