import { IDropdownOption } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import { PagedResultDto } from './pagedResultDto';

export class EntityStringDto<T = string> {
  id!: T;
}

export interface NamedEntityStringDto extends EntityStringDto {
  name: string;
}

export class EntityStringUtils {
  static resultToOptions(result: PagedResultDto<NamedEntityStringDto>): IDropdownOption[] {
    if (result && result.items)
      return result.items.map(this.mapToOption);
    return [];
  }

  static mapToOption(namedItem: NamedEntityStringDto): IDropdownOption {
    return {
      key: namedItem.id,
      text: L(namedItem.name),
    }
  }

  static renderEnumName(namedItem?: NamedEntityStringDto): string {
    return L(namedItem?.name ?? "");
  }
}