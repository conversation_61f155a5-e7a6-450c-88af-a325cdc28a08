import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { CrudServiceBase } from '../base/crudServiceBase';
import { ContestAttachedFilesDto } from './contestAttachedFilesDto';
import { isUserLoggedIn } from '../../utils/authUtils';
import { getPartialModel } from '../../utils/modelUtils';

export class ContestAttachedFilesService extends CrudServiceBase<ContestAttachedFilesDto> {
    constructor() {
        super(Endpoint.ContestAttachedFiles);
        this.internalHttp = httpApi;
    }

    public async createNew(createContestAttachedFileInput: ContestAttachedFilesDto) {
        isUserLoggedIn();
        const copyCreateContestAttachedFileInput = getPartialModel(createContestAttachedFileInput, [], ['id', 'contest']);
        let result = await httpApi.post(this.endpoint.Create(), copyCreateContestAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getAllFiles() {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.GetAll());
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByContestId(contestId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetByContestId?contestId=${contestId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportContestAttachedFilesService: ContestAttachedFilesService = new ContestAttachedFilesService();
export default exportContestAttachedFilesService;