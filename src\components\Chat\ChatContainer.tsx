import { inject } from 'mobx-react';
import { useEffect, useRef, useState } from 'react';
import { HubConnection, HubConnectionBuilder, HubConnectionState, IHttpConnectionOptions } from '@microsoft/signalr';
import Chat from './Chat';
import AppConsts from '../../lib/appconst';
import { ChatMessageDto } from '../../services/chat/chatMessageDto';
import { HubMessageTypeEnum } from '../../services/chat/hubMessageTypeEnums';
import { isJsonString } from '../../utils/utils';
import { observer } from 'mobx-react';
import webHookStore from "../../stores/WebHook/WebHookStore";
import chatStore from "../../stores/chatStore";
import { removeFromStorage } from '../../utils/localStorageUtils';
import { L } from '../../lib/abpUtility';

declare var abp: any;

const ChatContainerJSX = ({ hooks: externalHooks }: any) => {
    const [asyncActionInProgress, setAsyncActionInProgress] = useState<boolean>(false);
    const [isChatEnded, setIsChatEnded] = useState<boolean>(false);
    const [connectionAdditionalData, setConnectionAdditionalData] = useState<any>({
        triggerEventListener: false as boolean,
        prevThreadId: '' as string,
    });
    const [receivedMessagesData, setReceivedMessagesData] = useState<any>(!!localStorage.getItem('chatNotifications') ? JSON.parse(localStorage.getItem('chatNotifications')!) : {
        guestActiveThreadId: '' as string,
        consultantJoinedWaitingRoom: false as boolean,
        counterOfOpenedWaitingRooms: 0 as number,
        counterOfReceivedMessages: 0 as number,
        counterOfReceivedMessagesPerThread: {} as any,
    });
    const [connectionNewMessage, setConnectionNewMessage] = useState<any>({});
    const [connection, setConnection] = useState<HubConnection | null>(null);
    const [connectionError, setConnectionError] = useState<string | null>(null);
    const [chat, setChat] = useState<ChatMessageDto[]>([]);
    const [triggerRefreshChatStore, setTriggerRefreshChatStore] = useState<number>(0);
    const latestChat = useRef<ChatMessageDto[]>([]);
    const [extensionRole, setExtensionRole] = useState<string>('');
    const [visible, setVisible] = useState<boolean>(false);

    latestChat.current = chat;

    useEffect(() => {
        (async () => {
            let tempExtensionRole: string = '';

            let storedUser: string | null = localStorage.getItem('AzureB2CStorageKey');
            if (storedUser !== null) {
                let user: any = JSON.parse(storedUser);
                tempExtensionRole = user.b2c.idTokenClaims.extension_Role;
                setExtensionRole(user.b2c.idTokenClaims.extension_Role);
            } else {
                console.error('User not found');
            }

            setAsyncActionInProgress(true);
            if(extensionRole.includes('Consultant') || tempExtensionRole.includes('Consultant')) {
                await chatStore.getAll();
            }

            const options: IHttpConnectionOptions = {
                headers: { Authorization: 'Bearer ' + abp.auth.getToken() },
                accessTokenFactory: () => Promise.resolve(abp.auth.getToken())
            };

            const newConnection = new HubConnectionBuilder()
                .withUrl(`${AppConsts.remoteServiceBaseUrl}chat`, options)
                .withAutomaticReconnect([1000, 2000, 3000, 4000, 5000, 5000, 5000, 5000])
                .build();

            setConnection(newConnection);
            setAsyncActionInProgress(false);
        })();
        // eslint-disable-next-line
    }, []);

    useEffect(() => {
        (async () => {
            if(extensionRole.includes('Consultant')) {
                if(externalHooks.length > 0 && externalHooks[externalHooks.length - 1] && externalHooks[externalHooks.length - 1].payload) {
                    setConnectionNewMessage({message: externalHooks[externalHooks.length - 1].payload});
                } else {
                    await chatStore.getAll();
                }
            }
        })();
        // eslint-disable-next-line
    }, [externalHooks.length]);

    useEffect(() => {
        if(connection) {
            (async () => {
                await connection.start()
                    .then((result: any) => {
                        console.info('Connected', result);
                        setTimeout(async () => {
                            setConnectionAdditionalData({ ...connectionAdditionalData, triggerEventListener: true });

                            let currentChatThreadId: string | null = localStorage.getItem('currentChatThreadId');
                            if(!!currentChatThreadId) {
                                await chatStore.getMessagesClient(currentChatThreadId);
                            }
                        }, 250);
                    })
                    .catch((e: any) => {
                        console.error('Connection failed: ', e);
                    });
            })();

            connection.onclose((error: any) => {
                console.error(error);
                if(!!error) {
                    setConnectionError(error);
                }
            });
        }
        // eslint-disable-next-line
    }, [connection]);

    useEffect(() => {
        if (visible === true && chatStore.current && chatStore.current.thread && !!chatStore.current.thread.id) {
            const newCounterOfReceivedMessagesPerThread: any = { ...receivedMessagesData.counterOfReceivedMessagesPerThread };
            const deletedNotifications: number = receivedMessagesData.counterOfReceivedMessages - newCounterOfReceivedMessagesPerThread[chatStore.current.thread.id];

            delete newCounterOfReceivedMessagesPerThread[chatStore.current.thread.id];
            setReceivedMessagesData({
                ...receivedMessagesData,
                counterOfReceivedMessages: isNaN(deletedNotifications) ? 0 : deletedNotifications,
                counterOfReceivedMessagesPerThread: newCounterOfReceivedMessagesPerThread,
            });
        } else if(isChatEnded === true) {
            removeFromStorage('currentChatThreadId');
            setIsChatEnded(false);
        }
        // eslint-disable-next-line
    }, [chatStore.current, visible]);

    useEffect(() => {
        localStorage.setItem('chatNotifications', JSON.stringify(receivedMessagesData));
    }, [receivedMessagesData]);

    useEffect(() => {
        (async () => {
            if(extensionRole.includes('Consultant')) {
                await chatStore.getAll();
            }
        })();
        // eslint-disable-next-line
    }, [triggerRefreshChatStore]);

    useEffect(() => {
        if (connection && connection.state === HubConnectionState.Connected) {
            connection.on('ReceiveMessage', message => {
                setConnectionNewMessage(message);
            });

            setConnectionAdditionalData({ ...connectionAdditionalData, prevThreadId: chatStore.current?.thread.id, receivedMessagesData });
        }

        return () => {
            connection?.off('ReceiveMessage');
        };
        // eslint-disable-next-line
    }, [connection, connectionAdditionalData.triggerEventListener, chatStore.current]);

    useEffect(() => {
        const { counterOfReceivedMessages, counterOfReceivedMessagesPerThread, counterOfOpenedWaitingRooms } = receivedMessagesData;

        const updatedChat = [...latestChat.current];
        let parsedMessageType: HubMessageTypeEnum | null = null;

        if(typeof connectionNewMessage.message === 'string' && isJsonString(connectionNewMessage.message)) {
            const parsedMessage: any = JSON.parse(connectionNewMessage.message)[0];
            parsedMessageType = parsedMessage.type;
        }

        switch(parsedMessageType) {
            case HubMessageTypeEnum.ChatMessage:
                if (visible === true && connectionNewMessage.threadId === chatStore.current?.thread.id) {
                    const newCounterOfReceivedMessagesPerThread: any = { ...counterOfReceivedMessagesPerThread };
                    const deletedNotifications: number = counterOfReceivedMessages - newCounterOfReceivedMessagesPerThread[connectionNewMessage.threadId];
                    delete newCounterOfReceivedMessagesPerThread[connectionNewMessage.threadId];

                    setReceivedMessagesData({
                        ...receivedMessagesData,
                        counterOfReceivedMessages: isNaN(deletedNotifications) ? 0 : deletedNotifications,
                        counterOfReceivedMessagesPerThread: newCounterOfReceivedMessagesPerThread,
                    });

                    updatedChat.push(connectionNewMessage);
                } else {
                    const newCounterOfReceivedMessagesPerThread: any = { ...counterOfReceivedMessagesPerThread };
                    if (connectionNewMessage.threadId && !!connectionNewMessage.threadId) {
                        newCounterOfReceivedMessagesPerThread[connectionNewMessage.threadId] = !!counterOfReceivedMessagesPerThread[connectionNewMessage.threadId] ? 
                            counterOfReceivedMessagesPerThread[connectionNewMessage.threadId] + 1 : 1;
                    }

                    setReceivedMessagesData({
                        ...receivedMessagesData,
                        counterOfReceivedMessages: counterOfReceivedMessages + 1,
                        counterOfReceivedMessagesPerThread: newCounterOfReceivedMessagesPerThread,
                    });

                    if(connectionNewMessage.threadId === chatStore.current?.thread.id) {
                        updatedChat.push(connectionNewMessage);
                    }
                }
            break;
            case HubMessageTypeEnum.OpenWaitingRoom:
                setReceivedMessagesData({
                    ...receivedMessagesData,
                    counterOfOpenedWaitingRooms: counterOfOpenedWaitingRooms + 1,
                });
                setTriggerRefreshChatStore(triggerRefreshChatStore + 1);
            break;
            case HubMessageTypeEnum.JoinWaitingRoom:
                setReceivedMessagesData({
                    ...receivedMessagesData,
                    consultantJoinedWaitingRoom: true,
                    guestActiveThreadId: chatStore?.current?.thread.id,
                });
                sendMessage(``, new Date().toString());
            break;
            case HubMessageTypeEnum.CloseWaitingRoom:
                setReceivedMessagesData({
                    ...receivedMessagesData,
                    consultantJoinedWaitingRoom: false,
                });
        }

        setChat(updatedChat);
        // eslint-disable-next-line
    }, [connectionNewMessage]);

    useEffect(() => {
        if(chatStore.waitingList && Array.isArray(chatStore.waitingList)) {
            setReceivedMessagesData({
                ...receivedMessagesData,
                counterOfOpenedWaitingRooms: chatStore.waitingList.length,
            });
        }
        // eslint-disable-next-line
    }, [chatStore.waitingList]);

    const sendMessage = async (message: string, time: string) => {
        const chatMessage: ChatMessageDto = {
            user: abp && abp.session && abp.session.userId ? abp.session.userId.toString() : '',
            message: JSON.stringify([{
                type: HubMessageTypeEnum.ChatMessage,
                value: message,
                time: time
            }]),
            authToken: abp.auth.getToken(),
            threadId: chatStore.current?.thread.id
        };

        if (connection && connection.state === HubConnectionState.Connected) {
            setAsyncActionInProgress(true);

            try {
                await connection.send('SendMessage', chatMessage);
            } catch (e) {
                console.error(e);
            }

            setAsyncActionInProgress(false);
        } else {
            console.warn('No connection to server yet.');
        }
    };

    const sendThreadAction = async (actionType: HubMessageTypeEnum) => {
        const genericMessage: ChatMessageDto = {
            user: '',
            message: JSON.stringify([{
                type: actionType,
                value: '',
            }]),
            authToken: abp.auth.getToken(),
            threadId: chatStore.current?.thread.id
        };

        if (connection && connection.state === HubConnectionState.Connected) {
            setAsyncActionInProgress(true);

            try {
                await connection.send('SendMessage', genericMessage);
            } catch (e) {
                console.error(e);
            }

            setAsyncActionInProgress(false);
        } else {
            console.warn('No connection to server yet.');
        }
    };

    const findUserNameByUserId = (userId: number): string => {
        const user = chatStore.users.find(x => x.id === userId);
        return `${user?.name}`;
    };

    return (
        <Chat
            userId={abp.session.userId}
            users={chatStore.users || []}
            threads={chatStore.threads || []}
            waitingList={chatStore.waitingList || []}
            current={chatStore.current}
            connection={connection}
            connectionError={connectionError}
            chat={chat}
            receivedMessagesData={receivedMessagesData}
            asyncActionInProgress={asyncActionInProgress}
            extensionRole={extensionRole ? extensionRole : ''}
            isChatEnded={isChatEnded}
            visible={visible}
            setAsyncActionInProgress={(value: boolean) => {
                if (typeof value === 'boolean')
                    setAsyncActionInProgress(value);
            }}
            setVisible={(value: boolean) => {
                if (typeof value === 'boolean')
                    setVisible(value);
            }}
            setIsChatEnded={(value: boolean) => {
                if (typeof value === 'boolean') {
                    setIsChatEnded(value);
                    if(value === true) {
                        // sendThreadAction(HubMessageTypeEnum.EndChat);
                        removeFromStorage('currentChatThreadId');
                    }
                }
            }}
            setChat={(chat: ChatMessageDto[]) => setChat(chat)}
            select={async (threadId?: string, userId?: number, guestId?: number, sendChatMessage?: string, sendActionNotification?: HubMessageTypeEnum) => {
                if (!asyncActionInProgress) {
                    setAsyncActionInProgress(true);
                    await chatStore.select(threadId, userId, guestId);
                    setAsyncActionInProgress(false);

                    if(sendChatMessage && !!sendChatMessage) {
                        sendMessage(sendChatMessage, new Date().toString());
                    }

                    if(sendActionNotification && !!sendActionNotification) {
                        sendThreadAction(sendActionNotification);
                    }
                }
            }}
            deleteChatThread={async (threadId: string) => await chatStore.deleteChatThread(threadId)}
            enterWaitingRoom={async () => {
                if (!asyncActionInProgress) {
                    setAsyncActionInProgress(true);
                    await chatStore.enterWaitingRoom();
                    sendThreadAction(HubMessageTypeEnum.OpenWaitingRoom);
                    setAsyncActionInProgress(false);
                }
            }}
            sendMessage={(message: string, time: string) => {
                sendMessage(message, time);
            }}
            sendActionNotification={(actionType: HubMessageTypeEnum) => {
                sendThreadAction(actionType);
            }}
            refreshChatStore={async () => {
                if(extensionRole.includes('Consultant')) {
                    setAsyncActionInProgress(true);
                    await chatStore.getAll();
                    setAsyncActionInProgress(false);
                }
            }}
            joinWaitingRoom={async (waitingRoomId: number, waitingRoomThreadId: string, guestUserId?: number) => {
                if (!asyncActionInProgress) {
                    setAsyncActionInProgress(true);
                    await chatStore.joinWaitingRoom(waitingRoomId, waitingRoomThreadId);
                    if(!!guestUserId) {
                        chatStore.setUsername(findUserNameByUserId(guestUserId));
                    }
                    sendThreadAction(HubMessageTypeEnum.JoinWaitingRoom);
                    // sendThreadAction(HubMessageTypeEnum.StartChat);
                    sendMessage(`%#%#${L('The chat has started.')}%#%#`, new Date().toString());
                    setAsyncActionInProgress(false);
                }
            }}
            setReceivedMessagesData={setReceivedMessagesData}
        />
    );
};

const ChatContainer = inject((stores: any) => ({
    chatStore: stores.chatStore,
    webHookStore: stores.webHookStore
}))(ChatContainerJSX);

export default observer(() => <ChatContainer chatStore={chatStore.waitingList} hooks={webHookStore.hooks} />);