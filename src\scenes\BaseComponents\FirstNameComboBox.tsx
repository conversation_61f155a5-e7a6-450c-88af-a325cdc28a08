import * as React from 'react';
import { ComboBox, IComboBox, IComboBoxOption, ITextFieldProps, mergeStyleSets } from '@fluentui/react';
import CountryStore from '../../stores/countryStore';
import LanguageStore from '../../stores/languageStore';
import { LabelContainerComponent } from './labelContainerComponent';
import { LabelComponent } from './labelComponent';
import { filterBySome } from '../../utils/utils';

const classNames = mergeStyleSets({
    comboBoxStyles: {
        width: '300px',
    },
    requiredMarker: {
        color: 'rgb(164, 38, 44)',
        marginLeft: '5px',
    }
});

export interface IFirstNameComboBoxProps extends ITextFieldProps {
    key: string;
    label: string;
    inputText?: string;
    value?: string;
    countryStore?: CountryStore;
    languageStore?: LanguageStore;
    errorMessage?: string;
    disabled?: boolean;
    required?: boolean;
    allowFreeform?: boolean;
    autoComplete?: "on" | "off" | undefined;
    asyncActionInProgress?: boolean;
    onInputChange: (id: string | number | undefined, value: any) => void,
}

type IFirstNameComboBoxState = { 
    firstNameOptions: IComboBoxOption[],
    errorMessage: string,
    removePresettedValue: boolean,
};

export class FirstNameComboBox extends React.Component<IFirstNameComboBoxProps, IFirstNameComboBoxState> {
    constructor(props: IFirstNameComboBoxProps) {
        super(props);
        
        this.state = {
            ...this.state,
            firstNameOptions: [] as IComboBoxOption[],
            errorMessage: '',
            removePresettedValue: false,
        };
    }

    async componentDidMount() {
        let allFirstNameOptions: IComboBoxOption[] = [];

        firstNamesArray.sort();
        firstNamesArray.forEach((name: string) => {
            allFirstNameOptions.push(
                { key: name, text: name, selected: name === this.props.value ? true : false }
            );
        });

        this.setState({ firstNameOptions: allFirstNameOptions });
    }

    private getSelectedOptionText(firstNameOptions: IComboBoxOption[]): string {
        // if(this.state.removePresettedValue === true) {
        //     return "";
        // }

        let foundText: string = "";
        
        let filteredCountryOption: any = filterBySome(firstNameOptions, 'selected', true);
        if(!!filteredCountryOption && !!filteredCountryOption.key) {
            foundText = filteredCountryOption.text;
        } else if(this.props.allowFreeform === true && this.props.value) {
            foundText = this.props.value;
        }
    
        return foundText;
    } 

    render() {
        const { key, label, errorMessage, disabled, asyncActionInProgress, required, allowFreeform, inputText, autoComplete } = this.props;
        const { firstNameOptions, removePresettedValue } = this.state;

        const presettedInputText: string = this.getSelectedOptionText(firstNameOptions);

        return <LabelContainerComponent>
                    <LabelComponent label={label || ''} required={required} />

                    <ComboBox
                        label={''}
                        text={!!inputText ? inputText : (!!presettedInputText ? presettedInputText : undefined)}
                        required={required}
                        allowFreeform={!!allowFreeform ? allowFreeform : false}
                        autoComplete={!!autoComplete ? autoComplete : 'on'}
                        options={firstNameOptions}
                        className={classNames.comboBoxStyles}
                        key={`${key}FirstNameComboBox`}
                        errorMessage={!!errorMessage ? errorMessage : this.state.errorMessage}
                        disabled={disabled || asyncActionInProgress}
                        onChange={(event: React.FormEvent<IComboBox>, option?: IComboBoxOption, index?: number, value?: string) => {
                            if(option && !!option.key && !!option.text)
                                this.props.onInputChange(option.key, option.text);
                        }}
                        onPendingValueChanged={(option?: IComboBoxOption, index?: number, value?: string) => {
                            this.props.onInputChange(value, value);

                            if(removePresettedValue === false)
                                this.setState({ removePresettedValue: true });
                        }} 
                    />
                    {(required && required === true) && <span className={classNames.requiredMarker}>*</span>}
                </LabelContainerComponent>;
    }
}

const firstNamesArray: string[] = [
    'Anna',
    'Piotr',
    'Maria',
    'Krzysztof',
    'Katarzyna',
    'Andrzej',
    'Małgorzata',
    'Jan',
    'Agnieszka',
    'Stanisław',
    'Barbara',
    'Tomasz',
    'Krystyna',
    'Paweł',
    'Ewa',
    'Marcin',
    'Elżbieta',
    'Michał',
    'Zofia',
    'Marek',
    'Teresa',
    'Grzegorz',
    'Magdalena',
    'Józef',
    'Joanna',
    'Łukasz',
    'Janina',
    'Adam',
    'Monika',
    'Zbigniew',
    'Danuta',
    'Jerzy',
    'Jadwiga',
    'Tadeusz',
    'Aleksandra',
    'Mateusz',
    'Halina',
    'Dariusz',
    'Irena',
    'Mariusz',
    'Beata',
    'Wojciech',
    'Marta',
    'Ryszard',
    'Dorota',
    'Jakub',
    'Helena',
    'Henryk',
    'Karolina',
    'Robert',
    'Grażyna',
    'Rafał',
    'Jolanta',
    'Kazimierz',
    'Iwona',
    'Jacek',
    'Marianna',
    'Maciej',
    'Natalia',
    'Kamil',
    'Bożena',
    'Janusz',
    'Stanisława',
    'Marian',
    'Justyna',
    'Mirosław',
    'Paulina',
    'Jarosław',
    'Urszula',
    'Sławomir',
    'Alicja',
    'Dawid',
    'Renata',
    'Wiesław',
    'Sylwia',
    'Artur',
    'Agata',
    'Roman',
    'Aneta',
    'Damian',
    'Patrycja',
    'Przemysław',
    'Izabela',
    'Sebastian',
    'Ewelina',
    'Daniel',
    'Julia',
    'Władysław',
    'Wanda',
    'Zdzisław',
    'Marzena',
    'Patryk',
    'Wiesława',
    'Bartosz',
    'Weronika',
    'Edward',
    'Wiktoria',
    'Mieczysław',
    'Klaudia',
    'Leszek',
    'Edyta',
    'Karol',
    'Emilia',
    'Arkadiusz',
    'Genowefa',
    'Czesław',
    'Dominika',
    'Waldemar',
    'Kazimiera',
    'Szymon',
    'Hanna',
    'Adrian',
    'Kamila',
    'Kacper',
    'Martyna',
    'Bogdan',
    'Kinga',
    'Eugeniusz',
    'Lucyna',
    'Bartłomiej',
    'Stefania',
    'Antoni',
    'Józefa',
    'Franciszek',
    'Alina',
    'Stefan',
    'Zuzanna',
    'Radosław',
    'Gabriela',
    'Zygmunt',
    'Władysława',
    'Dominik',
    'Mariola',
    'Krystian',
    'Lidia',
    'Konrad',
    'Mirosława',
    'Aleksander',
    'Henryka',
    'Bogusław',
    'Wioletta',
    'Ireneusz',
    'Czesława',
    'Włodzimierz',
    'Oliwia',
    'Zenon',
    'Regina',
    'Witold',
    'Bogumiła',
    'Sylwester',
    'Angelika',
    'Hubert',
    'Sabina',
    'Mikołaj',
    'Daria',
    'Filip',
    'Aniela',
    'Wiktor',
    'Bogusława',
    'Bronisław',
    'Leokadia',
    'Wacław',
    'Bronisława',
    'Bolesław',
    'Ilona',
    'Cezary',
    'Cecylia',
    'Norbert',
    'Marlena',
    'Lech',
    'Olga',
    'Oskar',
    'Sandra',
    'Edmund',
    'Łucja',
    'Igor',
    'Anita',
    'Miłosz',
    'Eugenia',
    'Emil',
    'Maja',
    'Maksymilian',
    'Milena',
    'Leon',
    'Zdzisława',
    'Julian',
    'Wioleta',
    'Bernard',
    'Daniela',
    'Lucjan',
    'Michalina',
    'Błażej',
    'Amelia',
    'Romuald',
    'Antonina',
    'Eryk',
    'Dagmara',
    'Ludwik',
    'Żaneta',
    'Alfred',
    'Nikola',
    'Remigiusz',
    'Adrianna',
    'Szczepan',
    'Bernadeta',
    'Feliks',
    'Karina',
    'Gabriel',
    'Gertruda',
    'Marcel',
    'Rozalia',
    'Alojzy',
    'Roksana',
    'Bogumił',
    'Aldona',
    'Lesław',
    'Franciszka',
    'Ignacy',
    'Malwina',
    'Gerard',
    'Mieczysława',
    'Albert',
    'Honorata',
    'Seweryn',
    'Celina',
    'Alan',
    'Kornelia',
    'Oliwier',
    'Róża',
    'Nikodem',
    'Violetta',
    'Joachim',
    'Julita',
    'Ernest',
    'Eliza',
    'Benedykt',
    'Jagoda',
    'Fabian',
    'Magda',
    'Wincenty',
    'Nina',
    'Gracjan',
    'Bożenna',
    'Hieronim',
    'Sara',
    'Leonard',
    'Alfreda',
    'Rajmund',
    'Adela',
    'Jędrzej',
    'Laura',
    'Tobiasz',
    'Brygida',
    'Rudolf',
    'Adriana',
    'Adolf',
    'Marcelina',
    'Teodor',
    'Wacława',
    'Klaudiusz',
    'Ludwika',
    'Tymoteusz',
    'Izabella',
    'Walenty',
    'Paula',
    'Zygfryd',
    'Marzanna',
    'Juliusz',
    'Elwira',
    'Olaf',
    'Anastazja',
    'Alfons',
    'Eleonora',
    'Kajetan',
    'Pelagia',
    'Augustyn',
    'Arleta',
    'Florian',
    'Apolonia',
    'Konstanty',
    'Diana',
    'Leopold',
    'Andżelika',
    'Albin',
    'Iga',
    'Kornel',
    'Aurelia',
    'Erwin',
    'Julianna',
    'Bohdan',
    'Zenona',
    'Fryderyk',
    'Romana',
    'Marceli',
    'Felicja',
    'Aleksy',
    'Waleria',
    'Brunon',
    'Zenobia',
    'Ariel',
    'Walentyna',
    'Denis',
    'Sonia',
    'Kuba',
    'Klara',
    'Teofil',
    'Liliana',
    'Gustaw',
    'Feliksa',
    'Cyprian',
    'Hildegarda',
    'Sergiusz',
    'Romualda',
    'Borys',
    'Marzenna',
    'Walerian',
    'Judyta',
    'Roland',
    'Teodozja',
    'Emilian',
    'Lena',
    'Herbert',
    'Marika',
    'Dorian',
    'Luiza',
    'Martin',
    'Kaja',
    'Wilhelm',
    'Lilianna',
    'Anatol',
    'Teodora',
    'Amadeusz',
    'Bernadetta',
    'Edwin',
    'Donata',
    'Beniamin',
    'Ludmiła',
    'Ferdynand',
    'Matylda',
    'Arnold',
    'Jowita',
    'Lechosław',
    'Longina',
    'Longin',
    'Otylia',
    'Klemens',
    'Anetta',
    'Olgierd',
    'Nadia',
    'Kewin'
];
