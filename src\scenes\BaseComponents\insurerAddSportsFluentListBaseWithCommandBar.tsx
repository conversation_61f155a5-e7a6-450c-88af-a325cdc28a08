import {FluentTableBase} from "../Fluent/base/fluentTableBase";
import {ITableColumn} from "./ITableColumn";
import {myTheme} from "../../styles/theme";
import {L} from "../../lib/abpUtility";
import {ICrudPermissons} from "./commandBarBase";
import {<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Footer, PrimaryButton, ThemeProvider} from "@fluentui/react";
import { DropdownBase } from "./dropdownBase";
import sportInsuranceCoverageService from "../../services/sportInsuranceCoverage/sportInsuranceCoverageService";
import { CoverageType } from "../../services/sportInsuranceCoverage/CoverageTypeEnums";
import { SportDisciplineDto } from "../../services/sportDiscipline/dto/sportDisciplineDto";

const dialogStyles = {
    main: {
        selectors: {
            '@media (min-width: 0px)': {
                maxWidth: 580,
                width: 580
            }
        }
    }
};

export class InsurerAddSportsFluentListBaseWithCommandBar extends FluentTableBase<SportDisciplineDto> {
    private shouldReloadItems: boolean = false;
    private showPopUpDialog: boolean = false;
    private selectedCoverageType: string = "";
    private coverageTypeOptions = [{key: "No", text: L(CoverageType.No)}, {key: "NoProtection", text: L(CoverageType.NoProtection)}, {key: "HighRisk", text: L(CoverageType.HighRisk)}, {key: "ExtremeSport", text: L(CoverageType.ExtremeSport)}, {key: "WinterSports", text: L(CoverageType.WinterSports)}, {key: "HighPerformance", text: L(CoverageType.HighPerformance)}]
    
    disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;

    getColumns(): ITableColumn[] {
        return this.getTableColumns(this.props);
    }

    private getTableColumns(props: any): ITableColumn[] {
        return [
            {
                name: L('Sport discipline'),
                fieldName: 'sportDiscipline',
                onRender: (item: SportDisciplineDto) => {
                    return L(item.name);
                }
            }
        ];
    }

    getCrudPermission(): ICrudPermissons {
        return {
            create: false,
            update: false,
            delete: false,
            customActions: true,
        };
    }

    getCommandBarBaseProps() {
        let props = super.getCommandBarBaseProps();
        return {
            ...props,
            customActionsProps: [
                {
                    displayFor: 'single',
                    buttonText: L("Add"),
                    buttonIcon: "Add",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                {
                    displayFor: 'none',
                    buttonText: L("Add a general list of sport disciplines"),
                    buttonIcon: "Add",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                }
            ],
            customActions: [
                () => {
                    this.handleShowPopUpDialog();
                },
                () => {
                    this.props.customData.handleAddSportsFromGeneralList();
                }
            ]
        }
    }

    private createSportInsuranceCoverage = async (coverageType: string) => {
        await sportInsuranceCoverageService.create({
            coverageType: coverageType,
            insurerId: this.props.customData.insurerId,
            sportDisciplineId: this.props.customData.selectedSport.id
        }).then((response: any) => {
            if(response) {
                this.showPopUpDialog = false;
                this.reloadItems();
            }
        }).catch((error: any) => {
            console.error(error);
        })
        this.forceUpdate();
    }

    private handleShowPopUpDialog() {
        this.selectedCoverageType = '';
        this.showPopUpDialog = true;
        this.forceUpdate();
    }

    private reloadListOnDialogClose() {
        this.showPopUpDialog = false;
    
        if(this.shouldReloadItems) {
            this.reloadItems();
        }
    
        this.forceUpdate();
    }

    private async reloadItems() {
        this.selectionSetAllSelected(false);
        if(typeof this.props.refreshItems !== 'undefined') {
            await this.props.refreshItems!();
        }
    }  

    renderAll(pageInfo: string, values: any, columns: any) {
        return <>
            <Dialog
                hidden={!this.showPopUpDialog}
                onDismiss={() => this.reloadListOnDialogClose()}
                modalProps={{
                    isBlocking: true,
                    styles: dialogStyles
                }}
            >
                <DropdownBase key="CoverageType" required={true} label={L("Select coverage type")} options={this.coverageTypeOptions} value={this.selectedCoverageType ? this.selectedCoverageType : this.props.customData.selectedSport?.coverageType} 
                    isDataLoaded={true} 
                    customLabelStyles={{width: "200px", minWidth: "200px"}}
                    onChange={(value) => {
                        if (typeof value === 'string') {
                            this.selectedCoverageType = value;
                            this.forceUpdate();
                        } else {
                        }
                    }}
                />
                <DialogFooter theme={myTheme}>
                    <PrimaryButton
                        onClick={() => {
                            this.createSportInsuranceCoverage(this.selectedCoverageType);
                        }}
                        text={L('Save')}
                        theme={myTheme}
                    />
                    <DefaultButton theme={myTheme} onClick={() => this.reloadListOnDialogClose()} text={L('Cancel')} />
                </DialogFooter>
            </Dialog>
            
            <ThemeProvider theme={myTheme}>
                { this.renderAnnounced(pageInfo) }
                { this.renderCommandBarBase() }

                { this.renderListScrollablePane(values, columns) }
            </ThemeProvider>
        </>;
    }
}