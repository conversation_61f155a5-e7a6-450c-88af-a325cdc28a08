import { ProductDto } from '../../../services/product/productDto';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { GeneralCountryListContentView } from '../../Product/components/generalCountryListContentView';

export class GeneralCountryListPanel extends GenericPanel {
    getPanelTitle(): string {
        return L("General country list");
    }

    renderConfirm = () => {
        return <></>;
    };

    renderContent() {
        return <GeneralCountryListContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ProductDto } customData={this.props.customData} />;
    }
}