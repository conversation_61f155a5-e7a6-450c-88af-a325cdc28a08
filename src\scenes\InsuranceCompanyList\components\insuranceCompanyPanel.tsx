import { InsuranceCompanyContentView } from '../../InsuranceCompany/components/insuranceCompanyContentView';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { InsurerDto } from '../../../services/insurer/dto/insurerDto';
import { DefaultButton, PrimaryButton } from '@fluentui/react';
import { myTheme } from '../../../styles/theme';

export class InsuranceCompanyPanel extends GenericPanel {
    private disableConfirmButton: boolean = false;
    private hideConfirmButton: boolean = false;

    getPanelTitle(): string {
        return L("Insurance company");
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} 
                    onClick={() => {
                        this.disableConfirmButton = false;
                        this.hideConfirmButton = false;
                        this.forceUpdate();
                        this._onConfirm();
                    }} 
                    text={L('Save')}
                    style={{display: this.hideConfirmButton ? 'none' : 'unset'}}
                    disabled={this.asyncActionInProgress || this.disableConfirmButton}
                />
    };

    renderCancel = () => {
        return <DefaultButton theme={myTheme} text={L('Cancel')}
            onClick={() => {
                this.disableConfirmButton = false;
                this.hideConfirmButton = false;
                this.forceUpdate();
                this._onCancel();
            }}
        />
    };

    renderContent() {
        return <InsuranceCompanyContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as InsurerDto }
            toggleConfirm={(show: boolean) => { this.disableConfirmButton = !show; this.forceUpdate(); }}
            toggleHideConfirm={(hide: boolean) => { this.hideConfirmButton = hide; this.forceUpdate(); }}
        />;
    }
}