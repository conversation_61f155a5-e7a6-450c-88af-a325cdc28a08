import { DocumentCard, Text, Icon, mergeStyleSets } from '@fluentui/react';
import React from 'react';
import { myTheme } from '../../../../styles/theme';
import { mapPathToIcon } from '../../../../utils/utils';

interface IDashboardBtnProps {
  title: string;
  iconName?: string;
  status?: string;
  link?: string;
  onClick?: (ev?: React.SyntheticEvent<HTMLElement>) => void;
}
const classNames = mergeStyleSets({
  dashboardBtn: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '15px'
  },
  btnHeader: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '50%',
    width: 130,
    maxWidth: 130,
    height: 130,
    maxHeight: 130,
    backgroundColor: myTheme.palette.themePrimary,
  },
  btnIcon: {
    fontSize: 52,
    textAlign: 'center',
    color: myTheme.palette.white,
  },
  btnInfo: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
    marginTop: '15px'
  },
  btnTitle: {
    textTransform: 'uppercase',
    fontWeight: 'bold',
    color: myTheme.palette.black
  },
});

class DashboardBtn extends React.Component<IDashboardBtnProps> {
  render() {
    const { title, status, link, onClick } = this.props;
    let iconName = this.props.iconName;

    if(!iconName && link) {
      iconName = mapPathToIcon(link);
    }

    return (
      <DocumentCard aria-label={title} onClick={onClick} onClickHref={link} className={classNames.dashboardBtn} theme={myTheme}>
        <div className={classNames.btnHeader}>
          <Icon iconName={iconName} className={classNames.btnIcon} theme={myTheme} />
        </div>
        <div className={classNames.btnInfo}>
          <Text className={classNames.btnTitle} variant="smallPlus" theme={myTheme}>
            {title}
          </Text>
          <Text variant="xxLarge">
            {status}
          </Text>
        </div>
      </DocumentCard>
    );
  }
}
export default DashboardBtn;
