import { MessageBar, MessageBarType, Pivot, PivotItem, PrimaryButton, Spinner, SpinnerSize, Stack, mergeStyleSets } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { additionalTheme, myTheme } from '../../../styles/theme';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { Controls } from '../../BaseComponents/controls';
import { defaultPrize } from '../../../stores/prizeStore';
import { PrizeDto } from '../../../services/prize/dto/prizeDto';
import React from 'react';
import prizeAttachedFilesService from '../../../services/prizeAttachedFiles/prizeAttachedFilesService';
import { uploadFileToAzure } from '../../../services/azureService';
import { LabelContainerComponent } from '../../BaseComponents/labelContainerComponent';
import { LabelComponent } from '../../BaseComponents/labelComponent';
import { catchErrorMessage } from '../../../utils/utils';

const classNames = mergeStyleSets({
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    uploadButton: {
        width: 'fit-content',
        padding: '25px 50px',
        marginTop: '20px',
    },
    attachedFilesLabel: {
        fontWeight: 'bold',
        marginTop: '20px',
        marginBottom: '10px',
    },
    attachedFilesListItem: {
        listStyleType: 'decimal',
        marginBottom: '15px',
        padding: '10px',
        background: myTheme.palette.themeLighterAlt,
        selectors: {
            // ':nth-child(even)': {
            //     background: myTheme.palette.neutralLight,
            // }
        }
    },
    uploadedImage: {
        maxHeight: '250px',
        maxWidth: '70%',
        width: 'auto',
        border: '1px solid rgba(0, 0, 0, 0.2)',
    },
    messageBar: {
        width: 'fit-content',
        marginTop: '20px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
});

export class PrizeContentView extends GenericContentView {
    private prize: PrizeDto = defaultPrize;
    private fileUploadInputRef: any;
    private selectedFileForUpload: any = {
        name: "" as string,
        src: "" as string,
    };
    private onUploadError: string = '';

    constructor(props: any) {
        super(props);
        this.fileUploadInputRef = React.createRef();
    }

    async componentDidMount() {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        this.checkIfDataIsLoaded("prize");

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    private triggerUpload = () => {
        this.fileUploadInputRef.current.click();
    };

    private async onUpload() {
        this.asyncActionInProgress = true;

        this.prize = this.props.payload.model ? this.props.payload.model : this.props.payload;
        const selectedFile = !!this.fileUploadInputRef.current.files && this.fileUploadInputRef.current.files.length ? this.fileUploadInputRef.current.files[0] : null;
        
        if(!!selectedFile) {
            this.selectedFileForUpload.name = selectedFile.name;
            this.selectedFileForUpload.src = URL.createObjectURL(selectedFile);
        }

        this.forceUpdate();

        let result = await uploadFileToAzure(selectedFile);

        await prizeAttachedFilesService.createNew({
            'id': '',
            'prizeId': parseInt(this.prize.id),
            'prize': this.prize,
            'fileUrl': result.url,
            'originalFileName': selectedFile.name,
            'blobFileName': result.name,
            'description': '',
        }).then(async (response: any) => {
            if(!!response.fileUrl) {
                this.prize.imageUrl = response.fileUrl;
            }
            
            this.onUploadError = '';
            this.asyncActionInProgress = false;
            this.forceUpdate();
        }).catch((error: any) => {
            console.error(error);
            this.onUploadError = catchErrorMessage(error);

            this.asyncActionInProgress = false;
            this.forceUpdate();
        });
    }
    
    renderContent() {
        this.prize = this.props.payload.model ? this.props.payload.model : this.props.payload;

        const fileUploadButton = <PrimaryButton 
                                    className={classNames.uploadButton}
                                    theme={myTheme}
                                    text={L('Upload image')}
                                    type={'file'}
                                    onClick={this.triggerUpload}
                                    disabled={this.asyncActionInProgress === true}
                                    iconProps={{ iconName: 'Upload' }}
                                />;

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed,
                    }
                }
            }
        };
                            
        return <Pivot styles={pivotStyles} theme={myTheme}>
            <PivotItem headerText={L('General')} key={'General'}>
                {this.renderElement(new ContentViewModelProperty('name', L('Name'), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'name': this.prize.name})}
                {this.renderElement(new ContentViewModelProperty('isEnabled', L('Is enabled'), Controls.ChoiceGroup, true, {choicegroup: [{key: true, text: L('Yes')}, {key: false, text: L('No')}]}, false, {isDataLoaded: this.isDataLoaded, parseToBoolean: true}), [], {'isEnabled': this.prize.isEnabled})}
                
                {!!this.prize.imageUrl &&
                    <LabelContainerComponent>
                        <LabelComponent customStyles={{marginRight: 15}} label={L('Image')} />
                        <img alt="Prize logo" src={this.prize.imageUrl} className={classNames.uploadedImage} />
                    </LabelContainerComponent>
                }

                { (this.prize && !!this.prize.id && (typeof this.prize.id === 'number' && this.prize.id > 0)) &&
                    <>
                        <Stack horizontal style={{marginTop: 20}}>
                            <input ref={this.fileUploadInputRef} type="file" accept="image/png, image/jpeg" style={{display: 'none'}} onChange={() => this.onUpload()} />
                            { fileUploadButton }

                            {this.asyncActionInProgress && (
                                <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                            )}
                        </Stack>

                        {!!this.onUploadError &&
                            <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`}
                                onDismiss={() => { this.onUploadError = ''; this.forceUpdate(); }}    
                            >
                                {this.onUploadError}
                            </MessageBar>
                        }
                    </>
                }
            </PivotItem>
        </Pivot>
    }
}