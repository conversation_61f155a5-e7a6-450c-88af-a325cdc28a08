import React from 'react';
// import { DropdownBase } from '../../BaseComponents';
import { L } from '../../../lib/abpUtility';
// import rules from './registrationForm.validation';
import { DefaultButton, PrimaryButton, Text, mergeStyleSets } from '@fluentui/react';
import { Container } from '../../../stores/storeInitializer';
import { createOrUpdateClassNames } from '../../BaseComponents/createOrUpdate';
import { myTheme } from '../../../styles/theme';
import { inject } from 'mobx-react';
import Stores from '../../../stores/storeIdentifier';
import AccountStore from '../../../stores/accountStore';
// import { Roles } from '../../../services/domain/enums';
import CustomPanel from '../../BaseComponents/customPanel';
// import { LabeledTextField } from '../../../components/LabeledTextField';

interface IRegistrationFormProps {
  onCancel: () => void;
  handleCreate: (form: any) => void;
  isRegistrationOpen: boolean;
  accountStore?: AccountStore;
}

const classNames = mergeStyleSets({
  formWrapper: {
    paddingTop: 20,
  },
  sectionTitle: {
    paddingTop: 20,
    display: 'block',
    fontWeight: 'bold',
  },
  error: {
    color: myTheme.palette.red,
  },
  loginData: {
    paddingBottom: 30,
    maxWidth: 250,
  },
  companyData: {
    paddingBottom: 50,
    selectors: {
      '& .ms-TextField:nth-of-type(1)': {
        padding: '5px 0',
        width: '100%',
      },
      '& .ms-TextField:nth-of-type(2)': {
        padding: '5px 0',
        width: '100%',
        marginRight: 10,
      },
      '& .ms-TextField:nth-of-type(3)': {
        padding: '5px 0',
        width: '100%',
      },
    },
  },
  inputsRow: {
    display: 'flex',
    selectors: {
      '& .ms-TextField:nth-of-type(1)': {
        padding: '5px 0',
        marginRight: 20,
        flex: 1,
      },
      '& .ms-TextField:nth-of-type(2)': {
        padding: '5px 0',
        flex: 1,
        marginRight: 0,
      },
    },
  },
  contactData: {
    display: 'flex',
    flexWrap: 'wrap',
    selectors: {
      '& .ms-TextField:nth-of-type(1)': {
        padding: '5px 0',
        marginRight: 20,
        flexBasis: '38%',
      },
      '& .ms-TextField:nth-of-type(2)': {
        padding: '5px 0',
        flexBasis: '58%',
        marginRight: 0,
      },
      '& .ms-TextField:nth-of-type(3)': {
        padding: '5px 0',
        flexBasis: '48%',
        marginRight: 0,
      },
    },
  },
});

@inject(Stores.AccountStore)
class RegistrationForm extends React.Component<IRegistrationFormProps, any> {
  state = {
    confirmDirty: false,
  };
  // compareToFirstPassword = (rule: any, value: any, callback: any) => {
  //   const form = this.props.form;
  //   if (value && value !== form.getFieldValue('password')) {
  //     callback('Two passwords that you enter is inconsistent!');
  //   } else {
  //     callback();
  //   }
  // };

  // validateToNextPassword = (rule: any, value: any, callback: any) => {
  //   const form = this.props.form;
  //   if (value && this.state.confirmDirty) {
  //     form.validateFields(['confirm'], { force: true });
  //   }
  //   callback();
  // };

  render() {
    // const { getFieldDecorator } = this.props.form;
    const { onCancel, isRegistrationOpen } = this.props;

    return (
      <CustomPanel
        isOpen={isRegistrationOpen}
        onDismiss={() => onCancel()}
        closeButtonAriaLabel="Close"
        headerText={L('Registration Form')}
        onRenderFooterContent={this.onRenderFooterContent}
      >
        <div className={classNames.formWrapper}>
          {/* <div className={classNames.loginData}>
            {getFieldDecorator('role', { rules: rules.login })(
              <DropdownBase label={L('Role')} placeholder="player, parent, coach" required options={Roles.Options()} isDataLoaded={true} />
            )}

            {getFieldDecorator('name', { rules: rules.companyName })(
              <LabeledTextField theme={myTheme} label={L('First name')} placeholder={L('name')} required />
            )}
            {getFieldDecorator('surname', { rules: rules.companyName })(
              <LabeledTextField theme={myTheme} label={L('Last name')} placeholder={L('surname')} required />
            )}
            {getFieldDecorator('email', { rules: rules.email })(
              <LabeledTextField theme={myTheme} label={L('Email')} placeholder={L('enter your email')} required />
            )}

            {getFieldDecorator('password', {
              rules: [
                {
                  required: true,
                  message: 'Please input your password!',
                },
                {
                  validator: this.validateToNextPassword,
                },
              ],
            })(<LabeledTextField theme={myTheme} label={L('Password')} type="password" placeholder={L('min. 8 characters')} required />)}
            {getFieldDecorator('confirmPassword', {
              rules: [
                {
                  required: true,
                  message: L('ConfirmPassword'),
                },
                {
                  validator: this.compareToFirstPassword,
                },
              ],
            })(<LabeledTextField theme={myTheme} label={L('Confirm Password')} type="password" placeholder={L('Confirm Password')} required />)}
            {getFieldDecorator('phone', { rules: rules.phone })(
              <LabeledTextField theme={myTheme} label={L('Contact number')} placeholder={L('phone number')} required />
            )}
            {getFieldDecorator('login', { rules: rules.login })(
              <LabeledTextField theme={myTheme} label={L('Login')} placeholder={L('enter your email')} required />
            )}
          </div> */}


          {Container.EventBus.HttpError && (
            <Text theme={myTheme} className={classNames.error} variant="small">
              {Container.EventBus.HttpError}
            </Text>
          )}
        </div>
      </CustomPanel>
    );
  }
  onRenderFooterContent = () => {
    const { onCancel } = this.props;

    return (<div className={createOrUpdateClassNames.panelActions}>
      <PrimaryButton theme={myTheme} text={L('Register')} />
      <DefaultButton theme={myTheme} onClick={onCancel} text={L('Cancel')} />
    </div>);
  };

}
export default RegistrationForm;
