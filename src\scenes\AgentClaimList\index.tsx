import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import SearchStore from '../../stores/searchStore';
import { AgentClaimTable } from './components/agentClaimTable';
import AgentClaimStore from '../../stores/agentClaimStore';
import { additionalTheme } from '../../styles/theme';
import { mergeStyleSets } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import { FocusZone, FocusZoneDirection, FocusZoneTabbableElements, IDropdownOption } from '@fluentui/react';
import { DropdownBase } from '../BaseComponents/dropdownBase';
import { CrudConsts } from '../../stores/crudStoreBase';
import { DatePickerBase } from '../BaseComponents/datePickerBase';
import moment from 'moment';

const classnames = mergeStyleSets( {
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  },
});

export interface IProps {
  searchStore: SearchStore;
  agentClaimStore: AgentClaimStore;
  history: any;
}

export interface IState {
  filterByClaimStatus: string;
  filterByClaimStatusOptions: any[];
  filterByDateStart: string;
  filterByDateEnd: string;
  reloadingItems: boolean;
  gotNewItems: boolean;
  items: any[];
  customRequest: any;
}

@inject(Stores.SearchStore)
@inject(Stores.AgentClaimStore)
@observer
class Index extends AppComponentBase<IProps, IState> {
  formRef: any;

  constructor(props: IProps) {
    super(props);

    this.state = {
      ...this.state,
      items: [],
      filterByClaimStatus: "ALL",
      filterByClaimStatusOptions: [],
      filterByDateStart: "",
      filterByDateEnd: "",
      reloadingItems: false,
      gotNewItems: true,
      customRequest: {...this.props.agentClaimStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '', maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE},
    };
  }

  private async setCustomRequest(newfilterByClaimStatus: string | undefined, newFilterByDateStart: string | undefined, newFilterByDateEnd: string | undefined) {
    const requestPayload: any = {...this.props.agentClaimStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '',
      maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE};

    if((typeof newfilterByClaimStatus === 'string' && newfilterByClaimStatus.length > 0 && newfilterByClaimStatus !== "ALL")) {
      requestPayload['claimStatus'] = newfilterByClaimStatus;
    } else if((typeof newfilterByClaimStatus === 'undefined' && typeof this.state.filterByClaimStatus === 'string' && 
      this.state.filterByClaimStatus.length > 0 && this.state.filterByClaimStatus !== 'ALL')
    ) {
      requestPayload['claimStatus'] = this.state.filterByClaimStatus;
    }
    
    if(typeof newFilterByDateStart === 'string' && newFilterByDateStart.length > 0) {
      requestPayload['updateDateFilterStart'] = newFilterByDateStart;
    } else if(typeof this.state.filterByDateStart === 'string' && this.state.filterByDateStart.length > 0) {
      requestPayload['updateDateFilterStart'] = this.state.filterByDateStart;
    }

    if(typeof newFilterByDateEnd === 'string' && newFilterByDateEnd.length > 0) {
      requestPayload['updateDateFilterEnd'] = newFilterByDateEnd;
    } else if(typeof this.state.filterByDateEnd === 'string' && this.state.filterByDateEnd.length > 0) {
      requestPayload['updateDateFilterEnd'] = this.state.filterByDateEnd;
    }

    this.setState((prevState) => ({...prevState, customRequest: requestPayload, 
                                      filterByClaimStatus: typeof newfilterByClaimStatus === 'string' ? newfilterByClaimStatus : this.state.filterByClaimStatus,
                                      filterByDateStart: typeof newFilterByDateStart === 'string' ? newFilterByDateStart : this.state.filterByDateStart,
                                      filterByDateEnd: typeof newFilterByDateEnd === 'string' ? newFilterByDateEnd : this.state.filterByDateEnd,
                                  }));
  }

  private async refreshItems(forcedKeyword?: string) {
    this.setState((prevState) => ({...prevState, items: [], reloadingItems: true }));

    await this.props.agentClaimStore.getAll({...this.props.agentClaimStore.defaultRequest, keyword: forcedKeyword ? forcedKeyword : 
        (!!this.state.filterByClaimStatus ? this.state.filterByClaimStatus : this.props.searchStore.searchText), 
        maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE})
    .then(() => {
      this.setState((prevState) => ({...prevState, reloadingItems: false, gotNewItems: true }));
    });
  }

  public render() {
    // let items = this.props.agentClaimStore.dataSet ? this.props.agentClaimStore.dataSet.items : [];

    if (!this.state.reloadingItems && this.state.gotNewItems) {
      const items = this.props.agentClaimStore.dataSet ? this.props.agentClaimStore.dataSet.items : [];

      if (items.length > 0) {
        // this.setState((prevState) => ({ ...prevState, items: this.sortItems(items), gotNewItems: false }));
        this.setState((prevState) => ({ ...prevState, items: items, gotNewItems: false }));
      }
    }

    if (this.state.filterByClaimStatusOptions.length === 0) {
      let filterByClaimStatusDropdownOptions: IDropdownOption[] = [
        { key: "ALL", text: L('All2') },
        { key: "active", text: L('Active') },
        { key: "ended", text: L('Ended') },
      ];

      this.setState((prevState) => ({ ...prevState, filterByClaimStatusOptions: filterByClaimStatusDropdownOptions }));
    }

    return (
      <>
        <div className={classnames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Agent claim list')}</h2>
        </div>

        <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} style={{display: 'flex', flexDirection: 'row', flexWrap: 'nowrap'}}>
          <DropdownBase key={'filterByClaimStatusDropdown'} required={false} label={L('Filter by claim status')} options={this.state.filterByClaimStatusOptions}
                    value={this.state.filterByClaimStatus} disabled={false} isDataLoaded={true} customLabelStyles={{maxWidth: 150, whiteSpace: 'nowrap'}}
                    customDropdownWidth={'180px'}
                    onChange={(e: string | number | undefined) => {
                      if(e && e !== this.state.filterByClaimStatus) {
                        this.setCustomRequest(e.toString(), undefined, undefined);
                      }
                    }} />

          <DatePickerBase key={'filterByDateStart'} required={false} label={L('Filter by date (start)')}
                    value={!!this.state.filterByDateStart ? moment(this.state.filterByDateStart) : this.state.filterByDateStart} 
                    disabled={false} isDataLoaded={true} customLabelStyles={{ minWidth: '200px !important', width: '200px !important', whiteSpace: 'nowrap', marginLeft: '20px', fontSize: '12px'}}
                    customInputWidth={'180px'} validationData={{maxDate: this.state.filterByDateEnd}}
                    onChange={(value: string | undefined) => {
                      if(value && value !== this.state.filterByDateStart) {
                        this.setCustomRequest(undefined, value, undefined);
                      }
                    }}
                  />

          <DatePickerBase key={'filterByDateEnd'} required={false} label={L('Filter by date (end)')}
                    value={!!this.state.filterByDateEnd ? moment(this.state.filterByDateEnd) : this.state.filterByDateEnd}
                    disabled={false} isDataLoaded={true} customLabelStyles={{ minWidth: '200px !important', width: '200px !important', whiteSpace: 'nowrap', marginLeft: '20px', fontSize: '12px'}}
                    customInputWidth={'180px'} validationData={{minDate: this.state.filterByDateStart}}
                    onChange={(value: string | undefined) => {
                      if(value && value !== this.state.filterByDateEnd) {
                        this.setCustomRequest(undefined, undefined, value);
                      }
                    }}
                  />
        </FocusZone>
        
        <AgentClaimTable
          searchText={this.props.searchStore.searchText}
          items={this.state.items}
          store={this.props.agentClaimStore}
          history={this.props.history}
          refreshItems={() => this.refreshItems()}
          scrollablePanelMarginTop={220}
          customData={{
            customRequest: this.state.customRequest,
          }}
          showFilters={true}
          hideColumnsInFilters={['id', 'note', 'policyId', 'daysSinceStatusChanged', 'daysSinceClaimReport']}
          hideColumnsInFiltersByName={['DOZS']}
        />
      </>
    );
  }
}

export default Index;