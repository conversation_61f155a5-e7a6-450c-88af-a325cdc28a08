import { OcTerminationReason } from "../../calculation/dto/ocTerminationReasonEnums";
import { ClientDto } from "../../client/dto/clientDto";
import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";
import { UserDto } from "../../user/dto/userDto";
import { OcTerminationCreationWay } from "./ocTerminationCreationWayEnums";

export interface OcTerminationDto extends BaseApiEntityModel {
    agent: UserDto,
    agentId: number,
    client: ClientDto,
    clientId: number,
    registrationNumber: string,
    status: string,
    linkToFile: string,
    isSended: boolean,
    creationWay: OcTerminationCreationWay,
    policyId: number,
    clientFullName: string,
    address: string,
    peselRegon: string,
    insurerName: string,
    policyNumber: string,
    terminationGround: OcTerminationReason,
    terminationDate: string,
    creationTime: string,
    oldInsurerName: string,
    oldPolicyNumber: string,
}