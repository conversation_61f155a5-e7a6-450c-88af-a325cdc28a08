import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>vot, PivotItem } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { UserDto } from '../../../services/user/dto/userDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { defaultUser } from '../../../stores/userCrudStore';

export class ProfileContentView extends GenericContentView {
    private profile: UserDto = defaultUser;

    async componentDidMount() {
        this.checkIfDataIsLoaded("profile");
    }

    renderContent() {
        this.profile = this.props.payload.model ? this.props.payload.model : this.props.payload;
        const pivotStyles = {
            root: {
              marginLeft: '-8px'
            },
            linkIsSelected: {
              color: myTheme.palette.red,
              selectors: {
                ':before': {
                  height: '5px',
                  backgroundColor: additionalTheme.darkerRed
                }
              }
            }
          };

        return <Pivot theme={myTheme} styles={pivotStyles}>
            <PivotItem headerText={L('My profile')} key={'MyProfile'}>
                {this.renderElement(new ContentViewModelProperty('emailAddress', L('E-mail'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'emailAddress': this.profile.emailAddress})}
                {this.renderElement(new ContentViewModelProperty('name', L('First name'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'name': this.profile.name})}
                {this.renderElement(new ContentViewModelProperty('surname', L('Surname'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'surname': this.profile.surname})}
                {this.renderElement(new ContentViewModelProperty('phoneNumber', L('Phone'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'phoneNumber': this.profile.phoneNumber})}
            </PivotItem>
        </Pivot>
    }
    
    renderBack = () => {
        let backButton = <DefaultButton text={L('Close without save')} iconProps={{ iconName: 'Cancel' }} onClick={this._onBack} allowDisabledFocus />;
        return backButton;
    }
}