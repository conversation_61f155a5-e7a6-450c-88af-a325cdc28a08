// import { mergeStyleSets } from '@uifabric/merge-styles';
// import * as React from 'react';
 //import { AzureFileUpload } from './AzureFileUpload';
// import { LabelComponent } from './labelComponent';
 //import { LabelContainerComponent } from './labelContainerComponent';

// export interface ICustomFileUploader {
//     label: string;
//     value?: string;
//     onChange?: (value: string) => void;
// }

export const CustomFileUploader = () => {}
// export const CustomFileUploader = ({label, onChange, value} : ICustomFileUploader) => {
//     return <LabelContainerComponent>
//         <LabelComponent label={label}/>
//         <div className={classNames.azureFileUploaderWrapper}>
//             <AzureFileUpload onChange={onChange} 
//                              value={value} 
//                              maxImageSizeInKB={1024} 
//                              width={180}/>
//         </div>
//     </LabelContainerComponent>
// }

// const classNames = mergeStyleSets({
//     azureFileUploaderWrapper: {
//         marginTop: -10,
//         selectors: {
//             '.ms-Button': {
//                 width: 180,
//                 padding: 0,
//                 margin: 0
//             },
//             '.ant-upload-list-item-name': {
//                 width: 170,
//                 padding: 0
//             },
//             '.ms-Label': {
//                 width: 0,
//             }
//         }
//     }
// });