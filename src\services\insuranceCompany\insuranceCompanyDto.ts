import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";

export interface InsuranceCompanyDto extends BaseApiEntityModel {
  Id: string;
  Name: string;
  // SeName: string;
  Description: string;
  BottomDescription: string;
  // BrandLayoutId: string;
  // MetaKeywords: string;
  // MetaDescription: string;
  // MetaTitle: string;
  // PictureId: string;
  // PageSize: number;
  // AllowCustomersToSelectPageSize: boolean;
  // PageSizeOptions: string;
  // ShowOnHomePage: boolean;
  // IncludeInMenu: boolean;
  // Icon: string;
  Published: boolean;
  // DisplayOrder: number;
}