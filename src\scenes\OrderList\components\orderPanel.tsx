import { OrderDto } from '../../../services/order/dto/orderDto';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { OrderContentView } from '../../Order/components/orderContentView';

export class OrderPanel extends GenericPanel {
    getPanelTitle(): string {
        return "Order"
    }

    renderConfirm = () => {
        return <></>;
    };

    renderContent() {
        return <OrderContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as OrderDto } />;
    }
}