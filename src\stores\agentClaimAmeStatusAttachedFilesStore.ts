import { CrudStoreBase } from './crudStoreBase';
import { defaultAgentClaimAmeStatus } from './agentClaimAmeStatusStore';
import { AgentClaimAmeStatusAttachedFilesDto } from '../services/agentClaimAmeStatus/dto/agentClaimAmeStatusAttachedFilesDto';
import agentClaimAmeStatusAttachedFilesDto from '../services/agentClaimAmeStatus/agentClaimAmeStatusAttachedFilesService';
import agentClaimAmeStatusAttachedFilesService from '../services/agentClaimAmeStatus/agentClaimAmeStatusAttachedFilesService';

class AgentClaimAmeStatusAttachedFilesStore extends CrudStoreBase<AgentClaimAmeStatusAttachedFilesDto>{
    constructor() {
        super(agentClaimAmeStatusAttachedFilesDto, defaultAgentClaimAmeStatusAttachedFile)
    }

    public async getByAmeStatus(ameStatusId: number) {
        await agentClaimAmeStatusAttachedFilesService.getByAmeStatus(ameStatusId).then((result: any) => {
            this.dataSet = result;
        }).catch((error: any) => {
            console.error(error);
        });
    }

    public async getOpinionsByAmeStatus(ameStatusId: number) {
        await agentClaimAmeStatusAttachedFilesService.getOpinionsByAmeStatus(ameStatusId).then((result: any) => {
            this.dataSet = result;
        }).catch((error: any) => {
            console.error(error);
        });
    }

    public async getQuotationByAmeStatus(ameStatusId: number) {
        await agentClaimAmeStatusAttachedFilesService.getQuotationByAmeStatus(ameStatusId).then((result: any) => {
            this.dataSet = result;
        }).catch((error: any) => {
            console.error(error);
        });
    }

    public async getFvByAmeStatus(ameStatusId: number) {
        await agentClaimAmeStatusAttachedFilesService.getFvByAmeStatus(ameStatusId).then((result: any) => {
            this.dataSet = result;
        }).catch((error: any) => {
            console.error(error);
        });
    }

    public async getCostEstimateForeignByAmeStatus(ameStatusId: number) {
        await agentClaimAmeStatusAttachedFilesService.getCostEstimateForeignByAmeStatus(ameStatusId).then((result: any) => {
            this.dataSet = result;
        }).catch((error: any) => {
            console.error(error);
        });
    }

    public async getCostEstimatePlByAmeStatus(ameStatusId: number) {
        await agentClaimAmeStatusAttachedFilesService.getCostEstimatePlByAmeStatus(ameStatusId).then((result: any) => {
            this.dataSet = result;
        }).catch((error: any) => {
            console.error(error);
        });
    }
  
    public async getPaymentToClientByAmeStatus(ameStatusId: number) {
        await agentClaimAmeStatusAttachedFilesService.getPaymentToClientByAmeStatus(ameStatusId).then((result: any) => {
            this.dataSet = result;
        }).catch((error: any) => {
            console.error(error);
        });
    }
}

export const defaultAgentClaimAmeStatusAttachedFile: AgentClaimAmeStatusAttachedFilesDto = {
  id: "",
  ameStatusId: 0,
  ameStatus: defaultAgentClaimAmeStatus,
  agentClaimAmeStatusFileType: '',
  fileUrl: '',
  originalFileName: '',
  blobFileName: '',
  description: '',
}

export default AgentClaimAmeStatusAttachedFilesStore;