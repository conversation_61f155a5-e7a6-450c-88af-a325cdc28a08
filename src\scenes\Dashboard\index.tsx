import * as React from 'react';
import DashboardBtn from './components/DashboardBtn/dashboardBtn';
import { observer } from 'mobx-react';
import { isGranted, L } from '../../lib/abpUtility';
import { Stack } from '@fluentui/react';
import { RouterPath } from '../../components/Router/router.config';
import AppConsts from '../../lib/appconst';
import { hasPermissionsToPage, isConfigForAG, isConfigForProduction } from '../../utils/authUtils';

export interface IProps {
}

export interface IState {
  orderAfterDeadlineCount: number;
  newOrderCount: number;
  newCustomerCount: number;
}

@observer
export class Dashboard extends React.Component<IProps, IState> {
  state: IState = {
    orderAfterDeadlineCount: 0,
    newCustomerCount: 0,
    newOrderCount: 0,
  }

  render() {
    return (
      <Stack horizontal horizontalAlign="center" wrap tokens={{childrenGap: 'l1'}} style={{height: '100%'}}>
        {isGranted("Management.User") &&
          (<Stack.Item align="center">
            <DashboardBtn title={L("Users")} link="/users" />
          </Stack.Item>)
        }

        {hasPermissionsToPage(`/${RouterPath.CustomerList}`) && 
          <Stack.Item align="center">
            <DashboardBtn title={L("Customer list")} link={`/${RouterPath.CustomerList}`} />
          </Stack.Item>
        }

        {hasPermissionsToPage(`/${RouterPath.CalculationList}`) && 
          <Stack.Item align="center">
            <DashboardBtn title={L("Calculation list")} link={`/${RouterPath.CalculationList}`} />
          </Stack.Item>
        }

        {hasPermissionsToPage(`/${RouterPath.ContestList}`) && 
          <Stack.Item align="center">
            <DashboardBtn title={L("Contest list")} link={`/${RouterPath.ContestList}`} />
          </Stack.Item>
        }

        {hasPermissionsToPage(`/${RouterPath.ApkList}`) && 
          <Stack.Item align="center">
            <DashboardBtn title={L("APK list")} link={`/${RouterPath.ApkList}`} />
          </Stack.Item>
        }

        {AppConsts.allowedContent === 'ALL' && <>
          {(hasPermissionsToPage(`/${RouterPath.LoyaltyPointsHistoryList}`) && !isConfigForAG() && !isConfigForProduction()) && 
            <Stack.Item align="center">
              <DashboardBtn title={L("Loyalty points history list")} link={`/${RouterPath.LoyaltyPointsHistoryList}`} />
            </Stack.Item>
          }

          {hasPermissionsToPage(`/${RouterPath.ProductList}`) && 
            <Stack.Item align="center">
              <DashboardBtn title={L('Product list')} link={`/${RouterPath.ProductList}`} />
            </Stack.Item>
          }

          {hasPermissionsToPage(`/${RouterPath.InsuranceCompanyList}`) && 
            <Stack.Item align="center">
              <DashboardBtn title={L("Insurance company list")} link={`/${RouterPath.InsuranceCompanyList}`} />
            </Stack.Item>
          }
        </>}

        {hasPermissionsToPage(`/${RouterPath.InsurancePolicyList}`) && 
          <Stack.Item align="center">
            <DashboardBtn title={L("Insurance policy list")} link={`/${RouterPath.InsurancePolicyList}`} />
          </Stack.Item>
        }

        {hasPermissionsToPage(`/${RouterPath.OrderList}`) && 
          <Stack.Item align="center">
            <DashboardBtn title={L("Orders")} link={`/${RouterPath.OrderList}`} />
          </Stack.Item>
        }

        {AppConsts.allowedContent === 'ALL' && <>
          {hasPermissionsToPage(`/${RouterPath.VehicleList}`) && 
            <Stack.Item align="center">
              <DashboardBtn title={L("Vehicle list")} link={`/${RouterPath.VehicleList}`} />
            </Stack.Item>
          }

          {hasPermissionsToPage(`/${RouterPath.ClaimList}`) && 
            <Stack.Item align="center">
              <DashboardBtn title={L("Claim list")} link={`/${RouterPath.ClaimList}`} />
            </Stack.Item>
          }

          <Stack.Item align="center">
            <DashboardBtn title={L("Agent claim list")} link={`/${RouterPath.AgentClaimList}`} />
          </Stack.Item>
        </>}

        {hasPermissionsToPage(`/${RouterPath.UserList}`) && 
          <Stack.Item align="center">
            <DashboardBtn title={L("User list")} link={`/${RouterPath.UserList}`} />
          </Stack.Item>
        }

        {AppConsts.allowedContent === 'ALL' && <>
          {hasPermissionsToPage(`/${RouterPath.RoleList}`) && 
            <Stack.Item align="center">
              <DashboardBtn title={L("Role list")} link={`/${RouterPath.RoleList}`} />
            </Stack.Item>
          }

          {(hasPermissionsToPage(`/${RouterPath.RatingList}`) && !isConfigForAG() && !isConfigForProduction()) && 
            <Stack.Item align="center">
              <DashboardBtn title={L("Rating list")} link={`/${RouterPath.RatingList}`} />
            </Stack.Item>
          }

          {(hasPermissionsToPage(`/${RouterPath.CommunityList}`) && !isConfigForAG() && !isConfigForProduction()) && 
            <Stack.Item align="center">
              <DashboardBtn title={L("Community list")} link={`/${RouterPath.CommunityList}`} />
            </Stack.Item>
          }

          {hasPermissionsToPage(`/${RouterPath.TestSetList}`) && 
            <Stack.Item align="center">
              <DashboardBtn title={L("TestSet list")} link={`/${RouterPath.TestSetList}`} />
            </Stack.Item>
          }
        </>}

        <Stack.Item align="center">
          <DashboardBtn title={L("About")} link="/about" />
        </Stack.Item>

        {(AppConsts.allowedContent === 'ALL' && hasPermissionsToPage(`/${RouterPath.Settings}`)) && <>
          <Stack.Item align="center">
            <DashboardBtn title={L("Settings")} link="/settings" />
          </Stack.Item>
        </>}
      </Stack>
    );
  }
}

export default Dashboard;