import { isUserLoggedIn } from "../../utils/authUtils";
import { CrudServiceBase } from "../base/crudServiceBase";
import Endpoint from "../endpoint";
import { httpApi } from "../httpService";
import { VehicleConfigDto } from "./vehicleConfigDto";

export class VehicleConfigService extends CrudServiceBase<VehicleConfigDto> {
    constructor() {
        super(Endpoint.VehicleConfig);
        this.internalHttp = httpApi;
    }

    public async saveByVin(createUserInput: VehicleConfigDto) {
        isUserLoggedIn();
        let result = await this.internalHttp.post(this.endpoint.Custom(`SaveByVin`), createUserInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByVin(vin: string) {
        isUserLoggedIn();
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetByVin?vin=${vin}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportVehicleConfigService: VehicleConfigService = new VehicleConfigService();
export default exportVehicleConfigService;