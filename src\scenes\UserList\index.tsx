import { inject, observer } from "mobx-react";
import AppComponentBase from "../../components/AppComponentBase";
import Stores from "../../stores/storeIdentifier";
import "../BaseComponents/index.less";
import SearchStore from "../../stores/searchStore";
import UserCrudStore from "../../stores/userCrudStore";
import { UserTable } from "./components/userTable";
import {IDropdownOption, mergeStyleSets, Pivot, PivotItem, SelectionMode, Stack, FocusZone, FocusZoneDirection, FocusZoneTabbableElements} from "@fluentui/react";
import { L } from "../../lib/abpUtility";
import { UserRoles } from "../../services/user/userRolesEnums";
import { CrudConsts } from "../../stores/crudStoreBase";
import { DropdownBase } from "../BaseComponents";
import AppConsts from "../../lib/appconst";
import {additionalTheme, myTheme} from "../../styles/theme";
import userService from "../../services/user/userService";
import '../BaseComponents/index.less';
import { ArchivedUserTable } from './components/archivedUserTable';
import configurationService from "../../services/configuration/configurationService";

const classNames = mergeStyleSets({
  titleContainer: {
    color: myTheme.palette.neutralDark,
    fontWeight: 700,
    fontSize: 26,
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  },
});

export interface IProps {
  searchStore: SearchStore;
  userCrudStore: UserCrudStore;
  history: any;
}

export interface IState {
  filterByRolesOptions: any[];
  filterByRole: string;
  customRequest: any;
}

@inject(Stores.SearchStore)
@inject(Stores.UserCrudStore)
@observer
class Index extends AppComponentBase<IProps, IState> {
  formRef: any;
  private archivedItems: any[] = [];
  private getItemsProceded: boolean = false;
  private checkedEnvironment: string = "";
  private envArrToHideFilters: string[] = ['Top', 'AG'];

  constructor(props: IProps) {
    super(props);

    this.state = {
      ...this.state,
      filterByRolesOptions: [
        {key: UserRoles.Other, text: L("Other")},
        {key: UserRoles.ActiveUser, text: L("Mobile user + Client")},
        {key: UserRoles.MobileUser, text: L("Mobile user")},
      ],
      filterByRole: UserRoles.Other,
      customRequest: {
        ...this.props.userCrudStore.defaultRequest,
        keyword: this.props.searchStore.searchText
          ? this.props.searchStore.searchText
          : "",
        maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE,
        RoleGroup: UserRoles.Other,
      },
    };
  }

  async componentDidMount() {
    let tempCheckedEnvironment: string = "";

    await configurationService.checkEnvironment().then((response: any) => {
      if(!!response) {
        tempCheckedEnvironment = response;
        this.checkedEnvironment = response;

        this.setState((prevState) => ({
          ...prevState,
          filterByRole: this.envArrToHideFilters.includes(tempCheckedEnvironment) ? UserRoles.Other : 
                          (AppConsts.showFilterByRoleOnUserList === true ? UserRoles.ActiveUser : UserRoles.Other),
          customRequest: {
            ...this.state.customRequest,
            RoleGroup: this.envArrToHideFilters.includes(tempCheckedEnvironment) ? UserRoles.Other : 
                        (AppConsts.showFilterByRoleOnUserList === true ? UserRoles.ActiveUser : UserRoles.Other),
          }
        }));
      }
    }).catch((error: any) => {
      console.error(error);
    });

    if (AppConsts.showArchivedTabOnUserList === true) {
      userService.getAllDeletedInLast30Days().then((response: any) => {
        if (response && response.items) {
          this.archivedItems = response.items;
        }
        this.getItemsProceded = true;
      });
    }

    this.forceUpdate();
  }

  private async refreshItems() {
    setTimeout(async () => {
      const { userCrudStore, searchStore } = this.props;
      const { filterByRole } = this.state;

      const requestPayload = {
        ...userCrudStore.defaultRequest,
        maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE,
        RoleGroup: filterByRole,
      };

      await userCrudStore.getAll(requestPayload).then(() => {
        searchStore.setText(" ");
        setTimeout(() => {
          searchStore.setText("");
        }, 400);
      });
    }, 200);
  }

  private async refreshArchivedItems() {
    if (AppConsts.showArchivedTabOnUserList === true) {
      await userService.getAllDeletedInLast30Days().then((response: any) => {
        if (response && response.items) {
          this.archivedItems = response.items;
        }
        this.getItemsProceded = true;
        this.forceUpdate();
      });
    }
  }

  private async setCustomRequest(newFilterByRoles: string | undefined) {
    const requestPayload: any = {
      ...this.props.userCrudStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '',
      maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE
    };

    if ((typeof newFilterByRoles === 'string' && newFilterByRoles.length > 0 && newFilterByRoles !== "ALL")) {
      requestPayload['RoleGroup'] = newFilterByRoles;
    } else if ((typeof newFilterByRoles === 'undefined' && typeof this.state.filterByRole === 'string' &&
      this.state.filterByRole.length > 0 && this.state.filterByRole !== 'ALL')) {
      requestPayload['RoleGroup'] = this.state.filterByRole;
    }

    this.setState((prevState) => ({
      ...prevState, customRequest: requestPayload,
      filterByRole: typeof newFilterByRoles === 'string' ? newFilterByRoles : this.state.filterByRole,
    }));
  }

  public render() {
    let items = this.props.userCrudStore.dataSet ? [...this.props.userCrudStore.dataSet.items] : [];

    if (this.state.filterByRolesOptions.length === 0) {
      let filterByRoleDropdownOptions: IDropdownOption[] = [
        { key: UserRoles.Other, text: L('Other')},
        { key: UserRoles.ActiveUser, text: L('Active User')},
        { key: UserRoles.MobileUser, text: L('Inactive User') },
      ];

      this.setState((prevState) => ({ ...prevState, filterByRolesOptions: filterByRoleDropdownOptions }));
    }
    const pivotStyles = {
      root: {
        marginLeft: '-8px'
      },
      linkIsSelected: {
        color: myTheme.palette.red,
        selectors: {
          ':before': {
            height: '5px',
            backgroundColor: additionalTheme.darkerRed,
          }
        }
      }
    };

    return (
      <>
        <Stack >
          <div className={classNames.titleContainer} >
            <h2 style={{ fontSize: '26px', color: additionalTheme.grey}}>{L('User List')}</h2>
          </div>
        </Stack>
        <Pivot theme={myTheme} styles={pivotStyles}>
          <PivotItem headerText={L('Users')}>
            {(AppConsts.showFilterByRoleOnUserList === true && !!this.checkedEnvironment && !this.envArrToHideFilters.includes(this.checkedEnvironment)) &&
              <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} style={{display: 'flex', flexDirection: 'row', flexWrap: 'nowrap'}}>
                <DropdownBase key={'filterByRolesDropdown'} required={false} label={L('Filter by roles')} options={this.state.filterByRolesOptions}
                  value={this.state.filterByRole} disabled={false} isDataLoaded={true} customLabelStyles={{ maxWidth: 300, whiteSpace: 'nowrap', background: myTheme.palette.white, border: 0, fontSize: '16px', color: additionalTheme.grey }}
                  customDropdownWidth={'300px'}
                  onChange={async (e: string | number | undefined) => {
                    if (e && e !== this.state.filterByRole) {
                      await this.setCustomRequest(e.toString());
                      this.setState({ filterByRole: e.toString() })
                    }
                  }}
                />
              </FocusZone>
            }

            <UserTable
              searchText={this.props.searchStore.searchText}
              items={items}
              store={this.props.userCrudStore}
              history={this.props.history}
              scrollablePanelMarginTop={AppConsts.showFilterByRoleOnUserList === true && !this.envArrToHideFilters.includes(this.checkedEnvironment) ? 270 : 200}
              refreshItems={() => this.refreshItems()}
              customData={{
                customRequest: this.state.customRequest,
              }}
            />
          </PivotItem>

          {AppConsts.showArchivedTabOnUserList === true &&
            <PivotItem headerText={L('Archived users')}>
              <ArchivedUserTable
                searchText={this.props.searchStore.searchText}
                items={this.archivedItems}
                store={this.props.userCrudStore}
                history={this.props.history}
                refreshItems={() => this.refreshArchivedItems()}
                scrollablePanelMarginTop={270}
                selectionMode={SelectionMode.single}
                customData={{
                  useOnlyRefreshItems: true,
                  disableShimmer: this.getItemsProceded,
                }}
              />
            </PivotItem>
          }
        </Pivot>
      </>
    );
  }
}

export default Index;
