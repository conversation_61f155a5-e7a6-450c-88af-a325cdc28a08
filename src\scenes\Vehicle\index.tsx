import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { VehicleContentView } from './components/vehicleContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import { VehicleDto } from '../../services/vehicle/vehicleDto';
import VehicleStore from '../../stores/vehicleStore';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	vehicleStore: VehicleStore;
	match: any,
	history?: any,
}

@inject(Stores.VehicleStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private vehicleId: string = this.props.match.params.id;
	async componentDidMount() {
		await this.props.vehicleStore.get({ id: this.vehicleId } as VehicleDto);		
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<VehicleContentView store={this.props.vehicleStore} payload={ this.props.vehicleStore.model as VehicleDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;