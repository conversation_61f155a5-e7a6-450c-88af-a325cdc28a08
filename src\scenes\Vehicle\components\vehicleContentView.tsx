import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alog, DialogFooter, DialogType, Icon, IDropdownOption, Link, mergeStyleSets, Pivot, PivotItem, PrimaryButton, Selection, SelectionMode, Spinner, SpinnerSize, Stack } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import Stores from '../../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { Controls } from '../../BaseComponents/controls';
import { defaultVehicle } from '../../../stores/vehicleStore';
import policyDictionaryService from '../../../services/policyDictionary/policyDictionaryService';
import vehicleService from '../../../services/vehicle/vehicleService';
import { RouterPath } from '../../../components/Router/router.config';
import { EurotaxExpertInfoMixedBox } from './eurotaxExpertInfoMixedBox';
import { CustomerFluentListBase } from '../../BaseComponents/customerFluentListBase';
import { VehicleDto } from '../../../services/vehicle/vehicleDto';
import { fluentTableClassNames } from '../../../styles/fluentTableStyles';

const classNames = mergeStyleSets({
    inputIcon: {
        cursor: 'pointer',
        marginLeft: '15px !important',
        marginRight: '10px',
        fontSize: '20px',
        marginTop: '26px',
        transition: 'all 120ms',
        selectors: {
            '&:hover': {
                transform: 'scale(1.2)',
            }
        }
    },
    smallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '10px !important',
        marginTop: '22px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
});

@inject(Stores.LanguageStore)
@inject(Stores.VehicleStore)
@inject(Stores.ClientStore)
export class VehicleContentView extends GenericContentView {
    private vehicle: VehicleDto = defaultVehicle;
    private vehicleTypesOptions = {
        dropdown: [] as IDropdownOption[]
    };
    private fuelTypesOptions = {
        dropdown: [] as IDropdownOption[]
    };
    private showVehicleDialog: boolean = false;
    private collapseEurotaxInfoexpertForm: boolean = true;
    private existingVehicleId: number = -1;
    private customInputsData: any = {
        vehicleBrandId: "",
        vehicleModelId: "",
        engineCapacity: "",
        enginePower: 0,
        vehicleConfigurationEurotaxId: "",
        vehicleConfigurationInfoExpertId: "",
        vehicleInfo: "",
    };
    private selectClientSearchText: string = "";
    private _clientListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedClient: any = this._clientListSelection.getSelection();
            if(Array.isArray(selectedClient) && selectedClient.length > 0 && !!selectedClient[0].id) {
                this.vehicle.clientId = selectedClient[0].id;
                this.vehicle.clientName = selectedClient[0].user.fullName;
                this._clientListSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });

    async componentDidMount() {
        await this.props.store?.getAll(this.props.store?.defaultRequest);
        // await this.props.clientStore?.getAll({...this.props.clientStore?.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE});

        this.checkIfDataIsLoaded("vehicle");

        await policyDictionaryService.getVehicleTypes().then((response: any) => {
            if(response.data && response.data.result) {
                this.vehicleTypesOptions.dropdown = response.data.result.map((type: any) => {
                    return { key: type, 
                            text: L(type), 
                            isSelected: type === this.vehicle.vehicleType };
                }) as IDropdownOption[];
            }
        }).catch((error: any) => {
            console.error(error);
        });

        await policyDictionaryService.getFuelTypes().then((response: any) => {
            if(response.data && response.data.result) {
                this.fuelTypesOptions.dropdown = response.data.result.map((type: any) => {
                    return { key: type, 
                            text: L(type), 
                            isSelected: type === this.vehicle.fuelType };
                }) as IDropdownOption[];
            }
        }).catch((error: any) => {
            console.error(error);
        });

        this.forceUpdate();
    }

    private async getVehicleByVin(vin: string) {
        if(!!vin && vin.length === 17) {
            this.asyncActionInProgress = true;
            this.forceUpdate();

            let vehicleByVin: any = await vehicleService.getByVin(vin);
            if(vehicleByVin.items && vehicleByVin.totalCount > 0) {
                this.existingVehicleId = vehicleByVin.items[0].id;
                this.showVehicleDialog = true;
            }
        }

        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    renderContent() {
        this.vehicle = this.props.payload.model ? this.props.payload.model : this.props.payload;
        if(!this.vehicle.id || this.vehicle.id.length === 0) {
            this.vehicle.id = '0';
        }
        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed
                    }
                }
            }
        };

        return <>
            <Dialog
                hidden={!this.showVehicleDialog}
                onDismiss={() => { this.showVehicleDialog = false; this.forceUpdate(); }}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L("Vehicle with this VIN already exist. Do you want to go to existing vehicle?"),
                    closeButtonAriaLabel: L('Close'),
                }}
                modalProps={{
                    // titleAriaId: this._labelId,
                    // subtitleAriaId: this._subTextId,
                    isBlocking: true,
                    styles: { main: { maxWidth: 450 } },
                }}
                theme={myTheme}
            >
                <DialogFooter theme={myTheme}>
                    <DefaultButton theme={myTheme} onClick={() => { this.showVehicleDialog = false; this.forceUpdate(); }} text={L('No')} />
                    <Link theme={myTheme} href={`/${RouterPath.Vehicle}/${this.existingVehicleId}`}>
                        <PrimaryButton
                            text={L('Yes')}
                            theme={myTheme}
                            disabled={false}
                        />
                    </Link>
                </DialogFooter>
            </Dialog>
            
            <Pivot theme={myTheme} styles={pivotStyles}>
                <PivotItem headerText={L('General')} key={'General'}>
                    <div className={fluentTableClassNames.contentContainer}>
                        <CustomerFluentListBase
                            searchText={this.selectClientSearchText}
                            items={this.props.clientStore?.dataSet && this.props.clientStore?.dataSet.items ? this.props.clientStore?.dataSet.items : []}
                            store={this.props.clientStore!}
                            history={this.props.history}
                            scrollablePanelMarginTop={70}
                            customData={{
                                selectedClient: this.vehicle.clientId && this.vehicle.clientName ? `[${this.vehicle.clientId}] ${this.vehicle.clientName}` : undefined,
                            }}
                            customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                            customSelection={this._clientListSelection}
                            customOnSelectionChanged={(selection: any) => {
                                if(typeof selection === 'string' && selection === 'deleteClient') {
                                    this.vehicle.clientId = defaultVehicle.clientId;
                                    this.vehicle.clientName = defaultVehicle.clientName;
                                    this.forceUpdate();
                                }
                            }}
                        />
                    </div>

                    <Stack horizontal={true}>
                        {this.renderElement(new ContentViewModelProperty('vin', L("VIN"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'vin': this.vehicle.vin})}
                        <Icon iconName='SearchAndApps' className={classNames.inputIcon} onClick={() => this.getVehicleByVin(this.vehicle.vin) } title={L('Check if vehicle with this VIN already exist')} />
                    
                        {this.asyncActionInProgress &&
                            <Spinner label={L("Searching vehicle")} className={classNames.smallLoadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                        }
                    </Stack>

                    {/* {this.renderElement(new ContentViewModelProperty('vin', L("VIN"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'vin': this.vehicle.vin})} */}
                    {this.renderElement(new ContentViewModelProperty('registrationNumber', L("Registration number"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'registrationNumber': this.vehicle.registrationNumber})}
                    {this.renderElement(new ContentViewModelProperty('mileage', L("Mileage"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: "number", min: '0'}), [], {'mileage': this.vehicle.mileage})}
                    {this.renderElement(new ContentViewModelProperty('firstRegistrationDate', L("First registration date"), Controls.Date, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'firstRegistrationDate': this.vehicle.firstRegistrationDate})}
                    {/* {this.renderElement(new ContentViewModelProperty('productionYear', L("Production year"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded, textType: "number", min: '1922', max: new Date().getFullYear()}), [], {'productionYear': this.vehicle.productionYear})} */}
                    {/* {this.renderElement(new ContentViewModelProperty('vehicleType', L("Vehicle type"), Controls.Picker, true, this.vehicleTypesOptions, false, {isDataLoaded: this.isDataLoaded}), [], {'vehicleType': this.vehicle.vehicleType})} */}
                    {/* {this.renderElement(new ContentViewModelProperty('fuelType', L("Fuel type"), Controls.Picker, true, this.fuelTypesOptions, false, {isDataLoaded: this.isDataLoaded}), [], {'fuelType': this.vehicle.fuelType})} */}
                    
                    <EurotaxExpertInfoMixedBox 
                        collapseForm={this.collapseEurotaxInfoexpertForm}
                        onFormCollapse={() => { this.collapseEurotaxInfoexpertForm = !this.collapseEurotaxInfoexpertForm; this.forceUpdate(); }}
                        customInputsData={{
                            vehicleTypeId: this.vehicle.vehicleType,
                            vehicleBrandId: this.customInputsData.vehicleBrandId,
                            productionYear: this.vehicle.productionYear,
                            fuelType: this.vehicle.fuelType,
                            vehicleModelId: this.customInputsData.vehicleModelId,
                            engineCapacity: this.customInputsData.engineCapacity,
                            enginePower: this.customInputsData.enginePower,
                            vehicleConfigurationEurotaxId: this.customInputsData.vehicleConfigurationEurotaxId,
                            vehicleConfigurationInfoExpertId: this.customInputsData.vehicleConfigurationInfoExpertId,
                            vehicleInfo: this.customInputsData.vehicleInfo,
                        }}
                        onInputChange={ (id: string, value: any, customInputData: boolean | undefined) => {
                            switch(id) {
                                case 'vehicleType':
                                    this.customInputsData['vehicleBrandId'] = '';
                                    this.vehicle.productionYear = defaultVehicle.productionYear;
                                    this.vehicle.fuelType = defaultVehicle.fuelType;
                                    this.customInputsData['vehicleModelId'] = '';
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                    this.customInputsData['engineCapacity'] = '';
                                    this.customInputsData['vehicleInfo'] = '';
                                break;
                        
                                case 'vehicleBrandId':
                                    if(!!this.customInputsData['vehicleBrandId']) {
                                        this.vehicle.productionYear = defaultVehicle.productionYear;
                                    }
                                    
                                    this.vehicle.productionYear = defaultVehicle.productionYear;
                                    this.vehicle.fuelType = defaultVehicle.fuelType;
                                    this.customInputsData['vehicleModelId'] = '';
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                    this.customInputsData['engineCapacity'] = '';
                                    this.customInputsData['vehicleInfo'] = '';
                                break;
                        
                                case 'productionYear':
                                    this.vehicle.fuelType = defaultVehicle.fuelType;
                                    this.customInputsData['vehicleModelId'] = '';
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                    this.customInputsData['engineCapacity'] = '';
                                    this.customInputsData['vehicleInfo'] = this.customInputsData['vehicleBrandId'];
                                    if (this.customInputsData['vehicleBrandId'] !== '' && this.customInputsData['vehicleBrandId'] !== undefined) {
                                        this.vehicle.vehicleInfo = this.customInputsData['vehicleBrandId'];
                                    }
                        
                                    // this.inputsTypeValuePairs[this.mapAttributeNameToId('productionYear')] = value;
                                break;

                                case 'fuelType':
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['vehicleModelId'] = '';
                                    this.customInputsData['engineCapacity'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                    this.customInputsData['vehicleInfo'] = '';
                                break;
                        
                                case 'engineCapacity':
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['vehicleModelId'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                break;

                                case 'vehicleModelId':
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['enginePower'] = 0;
                                break;
                        
                                case 'enginePower':
                                    this.customInputsData['vehicleConfigurationId'] = '';
                                    this.customInputsData['vehicleConfigurationEurotaxId'] = '';
                                    this.customInputsData['vehicleConfigurationInfoExpertId'] = '';
                                    this.customInputsData['vehicleInfo'] = `${this.customInputsData['vehicleBrandId']}, ${this.customInputsData['vehicleModelId']}`;
                                    if (this.customInputsData['vehicleBrandId'] !== '' && this.customInputsData['vehicleBrandId'] !== undefined) {
                                        this.vehicle.vehicleInfo = `${this.customInputsData['vehicleBrandId']}, ${this.customInputsData['vehicleModelId']}`;
                                    }
                                break;
                            }
                            
                            if(customInputData === true) {
                                this.customInputsData[id] = value;
                            } else {
                                this.vehicle[id] = value;
                            }

                            this.forceUpdate();
                        }}
                    />

                    {this.renderElement(new ContentViewModelProperty('eurotaxCarId', L("Eurotax ID"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'eurotaxCarId': this.vehicle.eurotaxCarId})}
                    {this.renderElement(new ContentViewModelProperty('infoExpertId', L("Infoexpert ID"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'infoExpertId': this.vehicle.infoExpertId})}
                    {this.renderElement(new ContentViewModelProperty('vehicleInfo', L("Vehicle info"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'vehicleInfo': this.vehicle.vehicleInfo})}
                </PivotItem>
            </Pivot>
        </>;
    }
}