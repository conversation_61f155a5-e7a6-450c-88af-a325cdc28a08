import {HubConnection, HubConnectionBuilder, HubConnectionState} from "@microsoft/signalr";
import { observable, runInAction } from "mobx";
import AppConsts from "../../lib/appconst";
import { WebHookMessage } from "./WebHookMessage";

class WebHookStore {
  @observable hooks: WebHookMessage[] = [];
  connection: HubConnection;

  constructor() {
    this.connection = new HubConnectionBuilder()
      .withUrl(`${AppConsts.remoteServiceBaseUrl}webhook`)
      .withAutomaticReconnect()
      .build();

    this.connection.start().then(this.handleSuccess).catch(this.handleError);
  }

  public push = (message: WebHookMessage) => {
    if (this.connection && this.connection.state === HubConnectionState.Connected) {
      this.connection.send("WebHookToServer", message).catch(this.handleError);
    } else {
      console.warn("No connection to server yet.");
    }
  };

  private handleSuccess = () => {
    this.connection.on("WebHookToClient", (message: WebHookMessage) => {
      const newHooks: WebHookMessage[] = [...this.hooks, message];
      runInAction(() => {
        this.hooks = newHooks;
        // TODO: here do with hoooks what you want
      });
    });
  };

  private handleError = (e: any) => {
    console.error("Connection failed: ", e);
  };
}

const webHookStore = new WebHookStore();
export default webHookStore;

declare var window: any;
window.webhook = webHookStore;