import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { RatingDto } from '../../../services/rating/ratingDto';
import { RatingContentView } from '../../Rating/components/ratingContentView';

export class RatingPanel extends GenericPanel {
    getPanelTitle(): string {
        return L("Rating");
    }

    renderContent() {
        return <RatingContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as RatingDto } />;
    }
}