import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import LoyaltyPointsHistoryStore from '../../stores/loyaltyPointsHistoryStore';
import { LoyaltyPointsHistoryTable } from './components/loyaltyPointsHistoryTable';
import CustomerGroupStore from '../../stores/customerGroupStore';
import { LoyaltyPointsHistoryDto } from '../../services/loyaltyPointsHistory/loyaltyPointsHistoryDto';
import ClientStore from '../../stores/clientStore';
import { filterBySome } from '../../utils/utils';
import {mergeStyleSets, SelectionMode} from '@fluentui/react';
import { isConfigForAG, isConfigForProduction } from '../../utils/authUtils';
import { L } from '../../lib/abpUtility';
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
});

export interface IProps {
  searchStore: SearchStore;
  loyaltyPointsHistoryStore: LoyaltyPointsHistoryStore;
  customerGroupStore: CustomerGroupStore;
  clientStore: ClientStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.LoyaltyPointsHistoryStore)
@inject(Stores.ClientStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  async componentDidMount() {
    if(isConfigForAG() || isConfigForProduction()) {
			window.location.href = '/exception';
		}
    
    await this.props.clientStore?.getAll(this.props.clientStore!.defaultRequest);
  }

  private sortItems(items: any[]): any[] {
    return items.slice(0).sort((a, b) => (a.CreatedOnUtc < b.CreatedOnUtc) ? 1 : ((b.CreatedOnUtc < a.CreatedOnUtc) ? -1 : 0));
  }

  public render() {
    let items = this.props.loyaltyPointsHistoryStore.dataSet ? this.props.loyaltyPointsHistoryStore.dataSet.items : [];
    let customers = this.props.clientStore.dataSet ? this.props.clientStore.dataSet.items : [];

    if(items.length > 0 && customers.length > 0) {
      items.forEach((item: LoyaltyPointsHistoryDto) => {
        // const filteredCustomer = customers.filter(x => x.customerId === item.CustomerId);
        const filteredCustomer = filterBySome(customers, 'id', parseInt(item.CustomerId));
        if(filteredCustomer) {
          item['Customer'] = filteredCustomer;
        }
      });
    }

    if(isConfigForAG() || isConfigForProduction()) {
      return <>
        <p style={{marginTop: 50, fontWeight: 'bold', fontSize: '1.5rem'}}>{L('You do not have access to this page.')}</p>
      </>;
    } else {
      return (
        <>
          <div className={classNames.titleContainer}>
            <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Loyalyty Points History')}</h2>
          </div>
          <LoyaltyPointsHistoryTable
            searchText={this.props.searchStore.searchText}
            items={this.sortItems(items)}
            store={this.props.loyaltyPointsHistoryStore}
            history={this.props.history}
            selectionMode={SelectionMode.none}
          />
        </>
      );
    }
  }
}

export default Index;