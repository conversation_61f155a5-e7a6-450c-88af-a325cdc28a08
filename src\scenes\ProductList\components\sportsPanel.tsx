import { ProductDto } from '../../../services/product/productDto';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { SportsContentView } from '../../Product/components/sportsContentView';

export class SportsPanel extends GenericPanel {
    getPanelTitle(): string {
        return `${L("Sports")} - ${this.props.customData.insuranceName}`;
    }

    renderConfirm = () => {
        return <></>;
    };

    renderContent() {
        return <SportsContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ProductDto } customData={this.props.customData} />;
    }
}