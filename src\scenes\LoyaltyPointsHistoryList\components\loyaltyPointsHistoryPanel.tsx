import { LoyaltyPointsHistoryDto } from '../../../services/loyaltyPointsHistory/loyaltyPointsHistoryDto';
import { LoyaltyPointsHistoryContentView } from '../../LoyaltyPointsHistory/components/loyaltyPointsHistoryContentView';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { PrimaryButton } from '@fluentui/react';
import { myTheme } from '../../../styles/theme';

export class LoyaltyPointsHistoryPanel extends GenericPanel {
    private disableConfirmBtn: boolean = true;

    getPanelTitle(): string {
        return L("Loyalty points history");
    }

    toggleConfirm(show: boolean) {
        if(typeof show === 'boolean' && show !== !this.disableConfirmBtn) {
            this.disableConfirmBtn = !show;
            this.renderConfirm();
            this.forceUpdate();
        }
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} onClick={this._onConfirm} text={L('Save')}
                        disabled={this.asyncActionInProgress || this.disableConfirmBtn} />
    };

    renderContent() {
        return <LoyaltyPointsHistoryContentView toggleConfirm={(show: boolean) => this.toggleConfirm(show)} store={this.props.store}
                        createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as LoyaltyPointsHistoryDto } />;
    }
}