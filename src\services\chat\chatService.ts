import { AxiosInstance } from "axios";
import { CrudConsts } from "../../stores/crudStoreBase";
import { PagedResultRequestDto } from "../base/pagedResultRequestDto";
import { ServiceBase } from "../base/serviceBase";
import Endpoint from "../endpoint";
import http from "../httpService";
import { OutputChatMessageResultDto } from "./chatMessageDto";
import { ChatThreadDto, ChatThreadResultDto } from "./chatThreadDto";
import { ChatUserDto } from "./chatUserDto";

export class ChatService extends ServiceBase {
    defaultRequest: any = { keyword: "", maxResultCount: CrudConsts.PAGE_SIZE, skipCount: 0 };
    internalHttp: AxiosInstance;

    constructor() {
        super(new Endpoint("Chat"));
        this.internalHttp = http;
    }

    public async getThreads(pagedFilterAndSortedRequest: PagedResultRequestDto = this.defaultRequest): Promise<ChatThreadResultDto> {
        let result = await this.internalHttp.get(this.endpoint.Custom("GetThreads"), { params: pagedFilterAndSortedRequest });
        return result.data.result;
    }

    public async getThread(threadId: string): Promise<ChatThreadDto> {
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetThread?threadId=${threadId}`));
        return result.data.result;
    }

    public async getWaitingList(pagedFilterAndSortedRequest: PagedResultRequestDto = this.defaultRequest): Promise<ChatThreadDto[]> {
        let result = await this.internalHttp.get(this.endpoint.Custom("GetWaitingList"), { params: pagedFilterAndSortedRequest });
        return result.data.result.items ? result.data.result.items : result.data.result;
    }

    public async getUsers(userId: number, pagedFilterAndSortedRequest: PagedResultRequestDto = this.defaultRequest): Promise<ChatUserDto[]> {
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetUsers?userId=${userId}`), { params: pagedFilterAndSortedRequest });
        return result.data.result;
    }

    public async getMessages(threadId: string, pagedFilterAndSortedRequest: PagedResultRequestDto = this.defaultRequest): Promise<OutputChatMessageResultDto> {
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetMessages?threadId=${threadId}`), { params: pagedFilterAndSortedRequest });
        return result.data.result;
    }

    public async beginChat(userId: number): Promise<string> {
        let result = await this.internalHttp.post(this.endpoint.Custom(`BeginChat?userId=${userId}`));
        return result.data.result;
    }

    public async joinWaitingRoom(userId: number): Promise<string> {
        let result = await this.internalHttp.post(this.endpoint.Custom(`JoinWaitingRoom?id=${userId}`));
        return result.data;
    }

    public async enterWaitingRoom(): Promise<any> {
        let result = await this.internalHttp.post(this.endpoint.Custom(`EnterWaitingRoom`));
        return result.data;
    }

    public async deleteChatThread(threadId: string): Promise<string> {
        let result = await this.internalHttp.delete(this.endpoint.Custom(`DeleteChatThread?threadId=${threadId}`));
        return result.data;
    }

    public async setAsEnded(threadId: string): Promise<any> {
        let result = await this.internalHttp.post(this.endpoint.Custom(`SetAsEnded?threadId=${threadId}`));
        return result;
    }
}

const exportChatService: ChatService = new ChatService();
export default exportChatService;