import { Icon, Layer, mergeStyleSets } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import {additionalTheme, myTheme} from "../../../styles/theme";

const classNames = mergeStyleSets({
    fontBold: {
        fontWeight: '800',
        color: additionalTheme.darkerRed
    },
    layer: {
        position: 'absolute',
        top: '100px',
        left: '5px',
        selectors: {
            '& .ms-Layer-content': {
                color: myTheme.palette.black,
            }
        }
        // display: 'block',
        // width: 'auto',
        // height: 'auto',
        // padding: '0 20px',
        // background: myTheme.palette.themeLight,
        // position: 'absolute',
        // top: '20px',
        // left: '0',
        // borderRadius: '3px',
        // selectors: {
        //     '& .ms-Layer-content': {
        //         color: myTheme.palette.black,
        //     }
        // }
    },
});

export interface IApkStepsLayerProps {
    step: number;
    hostId: string;
    isProductSelected: boolean;
}

export class ApkStepsLayer extends React.Component<IApkStepsLayerProps> {
    render() {
        return <Layer hostId={this.props.hostId} className={classNames.layer}>
            <p>
                <span className={this.props.step === 1 && !this.props.isProductSelected ? classNames.fontBold : ''}>{L('Product')}</span>
                <Icon iconName="Forward" style={{marginRight: '25px', marginLeft: '25px'}} />
                <span className={this.props.step === 1 && this.props.isProductSelected ? classNames.fontBold : ''}>{L('Customer data')}</span>
                <Icon iconName="Forward" style={{marginRight: '25px', marginLeft: '25px'}} />
                <span className={this.props.step === 2 ? classNames.fontBold : ''}>{L('Complete the form')}</span>
            </p>
        </Layer>;
    }
}