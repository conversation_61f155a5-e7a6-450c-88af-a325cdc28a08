import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import { CustomerGroupTable } from './components/customerGroupTable';
import CustomerGroupStore from '../../stores/customerGroupStore';
import {mergeStyleSets} from "@fluentui/react";
import {L} from "../../lib/abpUtility";
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
})

export interface IProps {
  searchStore: SearchStore;
  customerGroupStore: CustomerGroupStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.CustomerGroupStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  public render() {
    let items = this.props.customerGroupStore.dataSet ? this.props.customerGroupStore.dataSet.items : [];

    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Customer Group List')}</h2>
        </div>
        <CustomerGroupTable
          searchText={this.props.searchStore.searchText}
          items={items}
          store={this.props.customerGroupStore}
          history={this.props.history}
        />
      </>
    );
  }
}

export default Index;