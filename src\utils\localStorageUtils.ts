import { isJsonString } from "./utils";

export const hourInMs: number = 3600000;

export function isLocalStorageAvailable(isSessionStorage: boolean = false): boolean {
    if (isSessionStorage === true) {
        if(typeof sessionStorage !== 'undefined') {
            try {
                sessionStorage.setItem('is_local_storage_available', 'yes');
                if (sessionStorage.getItem('is_local_storage_available') === 'yes') {
                    sessionStorage.removeItem('is_local_storage_available');
                    return true;
                } else {
                    return false; // sessionStorage is disabled
                }
            } catch(e) {
                return false; // sessionStorage is disabled
            }
        } else {
            return false; // sessionStorage is not available\
        }
    } else if (typeof localStorage !== 'undefined') {
        try {
            localStorage.setItem('is_local_storage_available', 'yes');
            if (localStorage.getItem('is_local_storage_available') === 'yes') {
                localStorage.removeItem('is_local_storage_available');
                return true;
            } else {
                return false; // localStorage is disabled
            }
        } catch(e) {
            return false; // localStorage is disabled
        }
    } else {
        return false; // localStorage is not available
    }
}

export function checkIfExistInStorage(key: string, expTimeMin?: number): boolean {
    if(isLocalStorageAvailable()) {
        let tempGetItem: any = localStorage.getItem(key);
    
        if(tempGetItem === null) {
            return false;
        } else {
            tempGetItem = isJsonString(tempGetItem) ? JSON.parse(tempGetItem) : tempGetItem;
    
            if(tempGetItem.saveTime && expTimeMin && (new Date().getTime() - tempGetItem.saveTime) * 1000 * 60 >= expTimeMin) {
                removeFromStorage(key);
                return false;
            } else {
                return true;
            }
        }
    } else {
        return false;
    }
}

export function loadFromStorage(key: string): any {
    if(checkIfExistInStorage(key)) {
        let item: any = localStorage.getItem(key);
        return JSON.parse(item);
    } else {
        return null;
    }
}

export function saveInStorage(key: string, value: any, isSessionStorage: boolean = false) {
    if(isLocalStorageAvailable(isSessionStorage)) {
        let valueString: string = JSON.stringify(value);

        if(isSessionStorage === true) {
            try {
                sessionStorage.setItem(key, valueString);
            } catch(e) {
                console.error(e);
            }
        } else {
            try {
                localStorage.setItem(key, valueString);
            } catch(e) {
                console.error(e);
            }
        }
    }
}

export function removeFromStorage(key: string): boolean {
    if(checkIfExistInStorage(key)) {
        localStorage.removeItem(key);
        return true;
    } else {
        return true;
    }
}

export function validateLocalStorageKeyAndTimestamp(key: string, maxTimeDiffInMs: number, isSessionStorage: boolean = false): boolean {
    const tempItem: string | null = isSessionStorage === true ? sessionStorage.getItem(key) : localStorage.getItem(key);
    const now: number = new Date().getTime();

    if(!!tempItem && isJsonString(tempItem)) {
        const parsedItem: any = JSON.parse(tempItem);
        if(parsedItem.data && parsedItem.timestamp && now - parsedItem.timestamp <= maxTimeDiffInMs) {
            return true;
        } else {
            return false;
        }
    } else {
        return false;
    }
}