import { action } from 'mobx';
import { CrudManyServiceBase } from '../services/base/crudManyServiceBase';
import { CrudServiceBase } from '../services/base/crudServiceBase';
import { EntityStringDto } from '../services/dto/entityStringDto';
import { CrudStoreBase } from './crudStoreBase';

export class CrudManyStoreBase<TDto extends EntityStringDto> extends CrudStoreBase<TDto>{
  crudService: CrudManyServiceBase<TDto>

  constructor(service: CrudManyServiceBase<TDto>, defaultValue: TDto) {
    super(service as CrudServiceBase<TDto>, defaultValue);
    this.crudService = service;
  }

  @action
  async createMany(createInputs: TDto[]) {
    await this.crudService.createMany(createInputs);
  }
}