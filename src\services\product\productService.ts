import { isUserLoggedIn } from '../../utils/authUtils';
import { transformPropsFirstLetter } from '../../utils/modelUtils';
import { CrudServiceBaseTransformed } from '../base/crudServiceBaseTransformed';
import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { ProductDto } from './productDto';

export class ProductService extends CrudServiceBaseTransformed<ProductDto> {
    constructor() {
        super(Endpoint.Product);
        this.internalHttp = httpApi;
    }

    async getProductKeysToIdMapper() {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetProductKeysToIdMapper`, true));

        // return transformPropsFirstLetter(!!result.data && !!result.data.result ? result.data.result : result.data);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    async getAllUserFields() {
        isUserLoggedIn();
        let result = await httpApi.get(new Endpoint("Product", "", false).Custom(`GetAllUserFields`));

        // return transformPropsFirstLetter(result.data && result.data.result && result.data.result.items ? result.data.result.items : []);
        return result.data && result.data.result && result.data.result.items ? result.data.result.items : [];
    }

    async getUserFieldsForProductId(productId: string) {
        isUserLoggedIn();
        let result = await httpApi.get(new Endpoint("Product", "", false).Custom(`GetUserFieldsForProductId?productId=${productId}`));

        return result.data && result.data.result && result.data.result.items ? result.data.result.items : [];
    }

    async getAllForDropdown() {
        isUserLoggedIn();
        let result = await httpApi.get(new Endpoint("Product", "", false).Custom(`GetAllForDropdown`));

        return transformPropsFirstLetter(result.data && result.data.result && result.data.result.items ? result.data.result.items : []);
    }

    async getProductWithMappingsForProductId(productId: string) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetProductWithMappingsForProductId?productId=${productId}`));

        const resultToReturn = result.data && result.data.result && result.data.result.items ? result.data.result.items : 
            (result && result.data && result.data.result ? result.data.result : (result && result.data ? result.data : (result ? result : [])));
            
        return transformPropsFirstLetter(resultToReturn);
    }
}

const exportProductService: ProductService = new ProductService();
export default exportProductService;