import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import SearchStore from '../../stores/searchStore';
import { CalculationTable } from './components/calculationTable';
import CalculationStore, { defaultCalculation } from '../../stores/calculationStore';
import { StatusRoles } from '../../services/calculation/dto/statusEnums';
import { CrudConsts } from '../../stores/crudStoreBase';
import { FocusZone, FocusZoneDirection, FocusZoneTabbableElements, IDropdownOption } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import { DropdownBase } from '../BaseComponents';
import { SendedToClientRoles } from '../../services/calculation/dto/sendedToClientEnums';
import { mergeStyleSets } from "@fluentui/react";
import { additionalTheme } from "../../styles/theme";
import { CalculationDto } from '../../services/calculation/dto/calculationDto';
import { IndividualOfferDto } from '../../services/individualOffer/individualOfferDto';
import { defaultIndividualOffer } from '../../stores/individualOfferStore';
import individualOfferService from '../../services/individualOffer/individualOfferService';
import { DatePickerBase } from '../BaseComponents/datePickerBase';

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
});

export interface IProps {
  searchStore: SearchStore;
  calculationStore: CalculationStore;
  history: any;
}

export interface IState {
  filterByStatusOptions: any[];
  filterByStatus: string;
  filterByProductOptions: any[];
  filterByProduct: string;
  filterBySendedToClientOptions: any[];
  filterBySendedToClient: string;
  filterByDateStart: string;
  filterByDateEnd: string;
  reloadingItems: boolean;
  gotNewItems: boolean;
  items: any[];
  customRequest: any;
} 

@inject(Stores.SearchStore)
@inject(Stores.CalculationStore)
@observer
class Index extends AppComponentBase<IProps, IState> {
  private calculation: CalculationDto = defaultCalculation;
  private individualOffer: IndividualOfferDto = defaultIndividualOffer;
  asyncActionInProgress: boolean = false;
  // formRef: any;

  constructor(props: IProps) {
    super(props);

    this.state = {
      ...this.state,
      items: [],
      filterByStatusOptions: [],
      filterByStatus: StatusRoles.All,
      filterBySendedToClientOptions: [],
      filterBySendedToClient: SendedToClientRoles.All,
      filterByProduct: '',
      filterByProductOptions: [],
      filterByDateStart: "",
      filterByDateEnd: "",
      reloadingItems: false,
      gotNewItems: true,
      customRequest: {
        ...this.props.calculationStore.defaultRequest,
        keyword: this.props.searchStore.searchText
          ? this.props.searchStore.searchText
          : '',
        maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE,
        Status: StatusRoles.All,
        SendedToClient: SendedToClientRoles.All,
      },
    };
  }

  private sortItems(items: any[]): any[] {
    return items.slice(0).sort((a, b) => (a.OrderNumber < b.OrderNumber) ? 1 : ((b.OrderNumber < a.OrderNumber) ? -1 : 0));
  }

  private async refreshItems() {
    const { calculationStore } = this.props;
    const { filterByStatus, filterBySendedToClient, filterByProduct, filterByDateStart, filterByDateEnd } = this.state;
    const requestPayload = {
      ...calculationStore.defaultRequest,
      maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE,
      Status: filterByStatus,
      SendedToClient: filterBySendedToClient,
      Segment: filterByProduct,
      CreationTimeStart: filterByDateStart,
      CreationTimeEnd: filterByDateEnd,
    };

    this.setState((prevState) => ({...prevState, items: [], reloadingItems: true }));

    await this.props.calculationStore.getAllLazy(requestPayload).then(() => {
      this.setState((prevState) => ({...prevState, reloadingItems: false, gotNewItems: true }));
    });
  }

  private async setCustomRequest(newFilterByStatus: string | undefined, newFilterBySendedToClient: string | undefined, newFilterByProduct: string | undefined, newFilterByDateStart: string | undefined, newFilterByDateEnd: string | undefined) {
    const requestPayload: any = {
      ...this.props.calculationStore.defaultRequest,
      keyword: this.props.searchStore.searchText
        ? this.props.searchStore.searchText
        : '',
      maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE,
    };
  
    if (typeof newFilterByStatus === 'string' && newFilterByStatus.length > 0 && newFilterByStatus !== "ALL2") {
      requestPayload['Status'] = newFilterByStatus;
    } else if (typeof this.state.filterByStatus === 'string' && this.state.filterByStatus.length > 0 && this.state.filterByStatus !== 'ALL2') {
      requestPayload['Status'] = this.state.filterByStatus;
    }
  
    if (typeof newFilterBySendedToClient === 'string' && newFilterBySendedToClient !== "ALL") {
      requestPayload['SendedToClient'] = newFilterBySendedToClient;
    } else if (typeof this.state.filterBySendedToClient === 'string' && this.state.filterBySendedToClient !== 'ALL') {
      requestPayload['SendedToClient'] = this.state.filterBySendedToClient;
    } 

    if (typeof newFilterByProduct === 'string' && newFilterByProduct !== "ALL") {
      requestPayload['Segment'] = newFilterByProduct;
    } else if (typeof this.state.filterByProduct === 'string' && this.state.filterByProduct !== 'ALL') {
      requestPayload['Segment'] = this.state.filterByProduct;
    }

    if(typeof newFilterByDateStart === 'string' && newFilterByDateStart.length > 0) {
      requestPayload['CreationTimeStart'] = newFilterByDateStart;
    } else if(typeof this.state.filterByDateStart === 'string' && this.state.filterByDateStart.length > 0) {
      requestPayload['CreationTimeStart'] = this.state.filterByDateStart;
    }

    if(typeof newFilterByDateEnd === 'string' && newFilterByDateEnd.length > 0) {
      requestPayload['CreationTimeEnd'] = newFilterByDateEnd;
    } else if(typeof this.state.filterByDateEnd === 'string' && this.state.filterByDateEnd.length > 0) {
      requestPayload['CreationTimeEnd'] = this.state.filterByDateEnd;
    }

    this.setState((prevState) => ({
      ...prevState,
      customRequest: requestPayload,
      filterByStatus: typeof newFilterByStatus === 'string' ? newFilterByStatus : this.state.filterByStatus,
      filterBySendedToClient: typeof newFilterBySendedToClient === 'string' ? newFilterBySendedToClient : this.state.filterBySendedToClient,
      filterByProduct: typeof newFilterByProduct === 'string' ? newFilterByProduct : this.state.filterByProduct,
      filterByDateStart: typeof newFilterByDateStart === 'string' ? newFilterByDateStart : this.state.filterByDateStart,
      filterByDateEnd: typeof newFilterByDateEnd === 'string' ? newFilterByDateEnd : this.state.filterByDateEnd,
    }));
  }
  
  private async createPolicyFromOffer() {
    this.asyncActionInProgress = true;

    await individualOfferService.createPolicyFromOffer({
      "individualOfferId": Number(this.individualOffer.id),
      "policyNumber": this.individualOffer.policyNumber,
      "startDate": this.individualOffer.startDate,
      "endDate": this.individualOffer.endDate,
      "bankAccountNumber": this.individualOffer.bankAccountNumber,
      "fees": this.individualOffer.fees,
      "paymentDates": this.individualOffer.paymentDates,
      "calculationId": 0,
      "status": "",
      "insurerName": "",
      "insuranceCoverage": [""],
      "installments": "",
      "total": 0,
      "id": ""
    });

    this.asyncActionInProgress = false;
    this.forceUpdate();
  }

  public render() {
    let items = this.props.calculationStore.dataSet ? [...this.props.calculationStore.dataSet.items] : [];
    const {filterByDateEnd, filterByDateStart, filterByProduct, filterByProductOptions, filterBySendedToClient, filterBySendedToClientOptions, filterByStatus, filterByStatusOptions} = this.state;

    if((!this.state.reloadingItems && this.state.gotNewItems) || filterByStatusOptions.length === 0) {
      let tempFilterByStatusDropdownOptions: IDropdownOption[] = [
        { key: 'All2', text: L('All2') },
        { key: 'NotApplicated', text: L('Calculation') },
        { key: 'Applicated', text: L('Applicated') },
        { key: 'Finalized', text: L('Finalized') },
      ];
      
      if(items.length > 0) {
        this.setState((prevState) => ({...prevState, items: this.sortItems(items), filterByStatusOptions: tempFilterByStatusDropdownOptions, gotNewItems: false }));
      }
    }

    if((!this.state.reloadingItems && this.state.gotNewItems) || filterBySendedToClientOptions.length === 0) {
      let tempFilterBySendedToClientDropdownOptions: IDropdownOption[] = [
        { key: SendedToClientRoles.All, text: L('All2') },
        { key: SendedToClientRoles.False, text: L('No') },
        { key: SendedToClientRoles.True, text: L('Yes') },
      ];
      
      if(items.length > 0) {
        this.setState((prevState) => ({...prevState, items: this.sortItems(items), filterBySendedToClientOptions: tempFilterBySendedToClientDropdownOptions, gotNewItems: false }));
      }
    }

    if((!this.state.reloadingItems && this.state.gotNewItems) || filterByProductOptions.length === 0) {
      let tempFilterByProductOptions: IDropdownOption[] = [
        { key: '', text: L('All2') },
        { key: 'Vehicle', text: L('Vehicle2') },
        { key: 'Home', text: L('Home2') },
        { key: 'Travel', text: L('Travel2') },
        { key: 'CancelTravel', text: L('CancelTravel2') },
        { key: 'Children', text: L('Children2') },
      ];
      
      if(items.length > 0) {
        this.setState((prevState) => ({...prevState, items: this.sortItems(items), filterByProductOptions: tempFilterByProductOptions, gotNewItems: false }));
      }
    }

    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Calculations')}</h2>
        </div>
        
        <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} style={{display: 'flex', flexDirection: 'row', flexWrap: 'wrap'}}>
          <DropdownBase key={'filterByStatusDropdown'} required={false} label={L('Filter by status')} options={filterByStatusOptions}
            value={filterByStatus} disabled={false} isDataLoaded={true} customLabelStyles={{ width: "120px", minWidth: "120px", whiteSpace: 'nowrap', fontSize: '14px', display: "flex", alignItems: "center" }}
            customDropdownWidth={'150px'}
            onChange={async (e: string | number | undefined) => {
              if (e && e !== filterByStatus) {
                await this.setCustomRequest(e.toString(), filterBySendedToClient, filterByProduct, filterByDateStart, filterByDateEnd);
              }
            }}
          />
          
          <DropdownBase key={'filterBySendedToClientDropdown'} required={false} label={L('Sended to client')} options={filterBySendedToClientOptions}
            value={filterBySendedToClient} disabled={false} isDataLoaded={true} customLabelStyles={{ width: "120px", minWidth: "120px", marginLeft: "50px", whiteSpace: 'nowrap', fontSize: '14px', display: "flex", alignItems: "center" }}
            customDropdownWidth={'150px'}
            onChange={async (e: string | number | undefined) => {
              if (e !== undefined && e !== filterBySendedToClient) {
                await this.setCustomRequest(filterByStatus, e.toString(), filterByProduct, filterByDateStart, filterByDateEnd);
              } 
            }}
          />

          <DropdownBase key={'filterByProductDropdown'} required={false} label={L('Product')} options={filterByProductOptions}
            value={filterByProduct} disabled={false} isDataLoaded={true} customLabelStyles={{ width: "120px", minWidth: "120px", marginLeft: "50px", whiteSpace: 'nowrap', fontSize: '14px', display: "flex", alignItems: "center" }}
            customDropdownWidth={'150px'}
            onChange={async (value: string | number | undefined) => {
              if (value !== undefined && value !== filterByProduct) {
                await this.setCustomRequest(filterByStatus, filterBySendedToClient, value.toString(), filterByDateStart, filterByDateEnd);
              } 
            }}
          />

          <DatePickerBase key={'filterByDateStart'} required={false} label={L('Creation date (from)')}
                    value={filterByDateStart} disabled={false} isDataLoaded={true} customLabelStyles={{ width: "160px", minWidth: "160px", marginLeft: "50px", whiteSpace: 'nowrap', fontSize: '14px', display: "flex", alignItems: "center" }}
                    customInputWidth={'150px'} validationData={{maxDate: filterByDateEnd}}
                    onChange={(value: string | undefined) => {
                      if(typeof value === 'string' && value !== filterByDateStart) {
                        this.setCustomRequest(filterByStatus, filterBySendedToClient, filterByProduct, value, filterByDateEnd);
                      }
                    }}
                  />

          <DatePickerBase key={'filterByDateEnd'} required={false} label={L('Creation date (to)')}
                    value={filterByDateEnd} disabled={false} isDataLoaded={true} customLabelStyles={{ width: "160px", minWidth: "160px", marginLeft: "0px", whiteSpace: 'nowrap', fontSize: '14px', display: "flex", alignItems: "center" }}
                    customInputWidth={'150px'} validationData={{minDate: filterByDateStart}}
                    onChange={(value: string | undefined) => {
                      if(typeof value === 'string' && value !== filterByDateEnd) {
                        this.setCustomRequest(filterByStatus, filterBySendedToClient, filterByProduct, filterByDateStart, value);
                      }
                    }}
                  />
        </FocusZone>
        
        <CalculationTable
          searchText={this.props.searchStore.searchText}
          items={this.state.items}
          store={this.props.calculationStore}
          history={this.props.history}
          refreshItems={() => this.refreshItems()}
          scrollablePanelMarginTop={268}
          customData={{
            customRequest: this.state.customRequest,
            createPolicyFromOffer: () => this.createPolicyFromOffer()
          }}
        />
      </>
    );
  }
}

export default Index;