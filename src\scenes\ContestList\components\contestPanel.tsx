import { PrimaryButton } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { ClientDto } from '../../../services/client/dto/clientDto';
import { myTheme } from '../../../styles/theme';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { ContestContentView } from '../../Contest/components/contestContentView';

export class ContestPanel extends GenericPanel {
    private inputErrorsCount: number = 0;

    getPanelTitle(): string {
        return L('Contest');
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} onClick={this._onConfirm} text={L('Save')} 
                disabled={this.asyncActionInProgress || this.inputErrorsCount > 0} />
    };

    renderContent() {
        return <ContestContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ClientDto }
                    onSetInputErrorsCount={(count: number) => { this.inputErrorsCount = count; this.forceUpdate(); }}
                    productStore={this.props.customData ? this.props.customData.productStore : {}}
                    prizeStore={this.props.customData ? this.props.customData.prizeStore : {}}
                    inputErrorsCount={this.inputErrorsCount} />;
    }
}