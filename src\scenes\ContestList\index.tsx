import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import SearchStore from '../../stores/searchStore';
import { ContestTable } from './components/contestTable';
import { Pivot, PivotItem, SelectionMode, mergeStyleSets } from '@fluentui/react';
import { additionalTheme, myTheme } from '../../styles/theme';
import { L } from '../../lib/abpUtility';
import { PrizeTable } from './components/prizeTable';
import { CrudConsts } from '../../stores/crudStoreBase';
import ContestStore from '../../stores/contestStore';
import PrizeStore from '../../stores/prizeStore';
import prizeService from '../../services/prize/prizeService';
import ProductStore from '../../stores/productStore';
import ClientContestStore from '../../stores/clientContestStore';

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  },
  contests: {
    root: {
      '& ms-FocusZone': {
        marginBottom: '30px',
      },

      '& span': {
        fontWeight: 600,
        transition: 'color .2s ease-out',
      },
      '& span ms-button': {
        color:myTheme.palette.white
      },
      '& span .ms-Pivot-text:hover': {
        color: myTheme.palette.red,
    },

      '& .ms-DetailsHeader-cellTitle .cellTitle:hover': {
        color: myTheme.palette.black,
      }
  }
}});

export interface IProps {
  searchStore: SearchStore;
  contestStore: ContestStore;
  clientContestStore: ClientContestStore;
  prizeStore: PrizeStore;
  productStore: ProductStore;
  history: any;
}

export interface IState {
  filterByStatus: string;
  filterByStatusOptions: any[];
  filterByPaymentStatus: string;
  filterByPaymentStatusOptions: any[];
  reloadingItems: boolean;
  gotNewItems: boolean;
  items: any[];
  customRequest: any;
}

@inject(Stores.SearchStore)
@inject(Stores.ContestStore)
@inject(Stores.ClientContestStore)
@inject(Stores.PrizeStore)
@inject(Stores.ProductStore)
@observer
class Index extends AppComponentBase<IProps, IState> {
  formRef: any;
  private getContestItemsProceded: boolean = false;
  private getPrizeItemsProceded: boolean = false;
  private overridePrizeItemsTrigger: number = 0;
  private overrideContestItemsTrigger: number = 0;

  constructor(props: IProps) {
    super(props);

    this.state = {
      ...this.state,
      items: [],
      filterByStatus: "ALL",
      filterByStatusOptions: [],
      filterByPaymentStatus: "ALL",
      filterByPaymentStatusOptions: [],
      reloadingItems: false,
      gotNewItems: true,
      customRequest: {...this.props.contestStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '', maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE},
    };
  }

  async componentDidMount() {    
    if(!this.props.contestStore!.dataSet || this.props.contestStore!.dataSet.totalCount <= 0) {
      await this.props.contestStore.getAll({...this.props.contestStore.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE}).then(() => {
        this.getContestItemsProceded = true;
        this.forceUpdate();
      });
    }
    
    if(!this.props.productStore!.dataSet || this.props.productStore!.dataSet.totalCount <= 0) {
      await this.props.productStore.getAll();
    }

    this.props.prizeStore.getAll({...prizeService.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE}).then((response: any) => {
      this.getPrizeItemsProceded = true;
      this.forceUpdate();
    });
  }

  private filterCustomers(value: any) {
    return !value.Deleted;
  }

  private async refreshItems() {
    await this.props.contestStore.getAllLazy().then(() => {
      this.getContestItemsProceded = true;
      this.overrideContestItemsTrigger = this.overrideContestItemsTrigger + 1;
      this.forceUpdate();
    });
  }

  private async refreshPrizeItems(resetDataSet?: boolean) {
    await this.props.prizeStore.getAll({...prizeService.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE}).then((response: any) => {
      this.getPrizeItemsProceded = true;
      this.overridePrizeItemsTrigger = this.overridePrizeItemsTrigger + 1;
      this.forceUpdate();
    });
  }

  private async setCustomRequest(newFilterByStatus: string | undefined, newFilterByPaymentStatus: string | undefined, newFilterByApkStatus: string | undefined) {
    const requestPayload: any = {...this.props.contestStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '',
      maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE};

    if((typeof newFilterByPaymentStatus === 'string' && newFilterByPaymentStatus.length > 0 && newFilterByPaymentStatus !== "ALL")) {
      requestPayload['paymentStatus'] = newFilterByPaymentStatus;
    } else if((typeof newFilterByPaymentStatus === 'undefined' && typeof this.state.filterByPaymentStatus === 'string' && 
      this.state.filterByPaymentStatus.length > 0 && this.state.filterByPaymentStatus !== 'ALL')
    ) {
      requestPayload['paymentStatus'] = this.state.filterByPaymentStatus;
    }

    if((typeof newFilterByStatus === 'string' && newFilterByStatus.length > 0 && newFilterByStatus !== "ALL")) {
      requestPayload['status'] = newFilterByStatus;
    } else if((typeof newFilterByStatus === 'undefined' && typeof this.state.filterByStatus === 'string' && 
      this.state.filterByStatus.length > 0 && this.state.filterByStatus !== 'ALL')
    ) {
      requestPayload['status'] = this.state.filterByStatus;
    }

    this.setState((prevState) => ({...prevState, customRequest: requestPayload, 
                                      filterByPaymentStatus: typeof newFilterByPaymentStatus === 'string' ? newFilterByPaymentStatus : this.state.filterByPaymentStatus,
                                      filterByStatus: typeof newFilterByStatus === 'string' ? newFilterByStatus : this.state.filterByStatus,
                                  }));
  }

  public render() {
    let items: any[] = [];

    if(this.props.contestStore.dataSet) {
      items = [...this.props.contestStore.dataSet.items].filter(this.filterCustomers);
    }

    const pivotStyles = {
      root: {
        marginLeft: '-8px'
      },
      linkIsSelected: {
        color: myTheme.palette.red,
        selectors: {
          ':before': {
            height: '5px',
            backgroundColor: additionalTheme.darkerRed,
          }
        }
      }
    };

    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Contest List')}</h2>
        </div>
        <Pivot className={classNames.contests} styles={pivotStyles} theme={myTheme}>
          <PivotItem headerText={L('Contests')}>
            {/* <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} style={{display: 'flex', flexDirection: 'row', flexWrap: 'nowrap'}}>
              <DropdownBase key={'filterByStatusDropdown'} required={false} label={L('Filter by X')} options={this.state.filterByStatusOptions}
                        value={this.state.filterByStatus} disabled={true} isDataLoaded={true} customLabelStyles={{maxWidth: 150, whiteSpace: 'nowrap'}}
                        customDropdownWidth={'180px'}
                        onChange={(e: string | number | undefined) => {
                          if(e && e !== this.state.filterByStatus) {
                            this.setCustomRequest(e.toString(), undefined, undefined);
                          }
                        }} />

              <DropdownBase key={'filterByPaymentStatusDropdown'} required={false} label={L('Filter by Y')} options={this.state.filterByPaymentStatusOptions}
                        value={this.state.filterByPaymentStatus} disabled={true} isDataLoaded={true} customLabelStyles={{marginLeft: 20, maxWidth: 180, whiteSpace: 'nowrap'}}
                        customDropdownWidth={'180px'}
                        onChange={(e: string | number | undefined) => {
                          if(e && e !== this.state.filterByPaymentStatus) {
                            this.setCustomRequest(undefined, e.toString(), undefined);
                          }
                        }}
                      />
            </FocusZone> */}

            <ContestTable
              searchText={this.props.searchStore.searchText}
              items={items}
              store={this.props.contestStore}
              history={this.props.history}
              refreshItems={() => this.refreshItems()}
              scrollablePanelMarginTop={200}
              customData={{
                productStore: this.props.productStore,
                clientContestStore: this.props.clientContestStore,
                prizeStore: this.props.prizeStore,
                disableShimmer: this.getContestItemsProceded,
                overrideAllItemsTrigger: this.overrideContestItemsTrigger,
              }}
            />
          </PivotItem>

          <PivotItem headerText={L('Prizes')}>
            <PrizeTable
              searchText={this.props.searchStore.searchText}
              items={this.props.prizeStore.dataSet && this.props.prizeStore.dataSet.items ? this.props.prizeStore.dataSet.items : []}
              store={this.props.prizeStore}
              history={this.props.history}
              refreshItems={(customData?: any) => this.refreshPrizeItems(customData.resetDataSet)}
              scrollablePanelMarginTop={200}
              selectionMode={SelectionMode.single}
              customData={{
                disableShimmer: this.getPrizeItemsProceded,
                overrideAllItemsTrigger: this.overridePrizeItemsTrigger,
              }}
            />
          </PivotItem>
        </Pivot>
      </>
    );
  }
}

export default Index;