import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { ApkPanel } from './apkPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { RouterPath } from "../../../components/Router/router.config";
import { ClientDto } from "../../../services/client/dto/clientDto";
import { ClientTypeEnum } from "../../../services/client/clientTypeEnums";
import { Dialog, DialogType, Icon, mergeStyleSets } from "@fluentui/react";
import { catchErrorMessage, dateFormat } from "../../../utils/utils";
import {additionalTheme, myTheme} from "../../../styles/theme";
import { ApkAttachedFilesDto } from "../../../services/apkAttachedFiles/apkAttachedFilesDto";

export const classNames = mergeStyleSets({
  buttonPrimary: {
    width: 'auto !important',
    padding: '0 10px',
    marginRight: '30px',
    selectors: {
      '& .ms-Button-flexContainer': {
        flexDirection: 'row-reverse'
      },
      '.ms-Button-textContainer': {
        flexGrow: 'unset'
      },
      ':hover .ms-Icon': {
        color: myTheme.palette.white
      }
    }
  },
});

export class ApkTable extends FluentTableBase<any> { // APKDto
  private shouldReloadItems: boolean = false;
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";
  
  disableGetAllOnMount = true;

  getItemDisplayNameOf(item: ClientDto): string {
    if(!!item.company) {
      return item.company;
    } else if(!!item.user) {
      return `${item.user.name} ${item.user.surname}`; 
    } else {
      return item.id;
    }
  }

  getColumns(): ITableColumn[] {
    return ApkTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('APK number'),
        fieldName: 'displayedFileName',
        minWidth: 180,
        maxWidth: 180,
      },
      // {
      //   name: L('Client name'),
      //   fieldName: 'client.user.fullName',
      //   minWidth: 250,
      //   maxWidth: 250,
      //   onRender: (item: any): any => {
      //     return item && item.client && item.client.user ? item.client.user.fullName : (item && item.client && !!item.client.company ? item.client.company : "");
      //   }
      // },
      {
        name: L('Agent'),
        fieldName: 'item.agent.fullName',
        minWidth: 250,
        maxWidth: 250,
        onRender: (item: any): any => {
          return item && item.agent ? item.agent.fullName : "";
        }
      },
      {
        name: L('Client name'),
        fieldName: 'client.user.fullName',
        onRender: (item: any): any => {
          return item && item.client && item.client.user ? item.client.user.fullName : "";
        }
      },
      {
        name: L('Product'),
        fieldName: 'productName',
        minWidth: 100,
        maxWidth: 100,
        onRender: (item: any): any => {
          return L(`${item.productName}2`);
        }
      },
      {
        name: L('Status'),
        fieldName: 'status',
        minWidth: 85,
        maxWidth: 85,
        onRender: (item: any): any => {
          let textColor = additionalTheme.white;
          let background = myTheme.palette.orange;

          if (item.status === 'Prepared') {
            textColor = additionalTheme.white;
            background = myTheme.palette.green;
          }  if (item.status === 'Expired') {
            textColor = additionalTheme.white;
            background = additionalTheme.lighterRed;
          } if (item.status === 'Sended') {
            textColor = additionalTheme.white;
            background = myTheme.palette.green;
          } if (item.status === 'Signed') {
            textColor = additionalTheme.white;
            background = myTheme.palette.green;
          }
          return (
              <span style={{ color:textColor, backgroundColor: background, padding: '2px 5px', borderRadius: '2px' }}>
            {L(item.status)}
          </span>
        );

        }
      },
      {
        minWidth: 95,
        maxWidth: 95,
        name: L('Policy issued?'),
        fieldName: 'policyIssued',
        onRender: (item: any) => {
          return <Icon style={{ color: !!item.policyIssued && item.policyIssued === true ? "green" : "red", margin: '0 auto', display: 'block', width: 'min-content'}} 
                      iconName={ !!item.policyIssued && item.policyIssued === true ? "SkypeCheck" : "StatusCircleErrorX" } />
        }
      },
      {
        name: L('Created by'),
        fieldName: 'creationWay',
        minWidth: 80,
        maxWidth: 80,
        onRender: (item: any): any => {
          return <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.orange, padding: '2px 5px', borderRadius: '2px' }}>
            {L(item.creationWay)}
          </span>;
        }
      },
      {
        name: L('Creation time'),
        fieldName: 'creationTime',
        minWidth: 130,
        maxWidth: 130,
        onRender: (item: any): any => {
          return dateFormat(item.creationTime, undefined, true);
        }
      },
      {
        name: L('Download file'),
        fieldName: 'downloadFile',
        onRender: (item: any): any => {
          return <a style={{color: myTheme.palette.neutralDark}} href={item.fileUrl} download={item.displayedFileName} title={`${L("Download file")} ${item.displayedFileName}`}>{L("Download")} <Icon iconName="Download" style={{marginLeft: '5px'}} /></a>;
        }
      },
    ];
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: false,
      delete: false,
      customActions: true,
    };
  }

  private reloadListOnDialogClose() {
    this.showPopUpDialog = false;

    if(this.shouldReloadItems) {
      this.reloadItems();
    }

    this.forceUpdate();
  }

  private async reloadItems(that?: any) {
    this.selectionSetAllSelected(false);
    if(typeof this.props.refreshItems !== 'undefined') {
      await this.props.refreshItems!();
      if(that && typeof that.toggleCustomActionButton !== 'undefined') {
        that.toggleCustomActionButton(false, true);
      }
    } else {
      this.togglePopUpDialog("Error", "The list could not be refreshed automatically, please refresh the page manually.");
      if(that && typeof that.toggleCustomActionButton !== 'undefined') {
        that.toggleCustomActionButton(false, true);
      }
    }
  }

  private togglePopUpDialog(title: string, text: string, visibility?: boolean) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.forceUpdate();
  }

  private async updatePolicyIssuedStatus(apkAttachedFiles: ApkAttachedFilesDto, that: any) {
    await this.props.store.update({
      ...apkAttachedFiles, 
        policyIssued: !apkAttachedFiles.policyIssued, 
        customerId: !!apkAttachedFiles.customerId ? apkAttachedFiles.customerId : 
                                                    (apkAttachedFiles['client'] && !!apkAttachedFiles['client'].customerId ? apkAttachedFiles['client'].customerId : '')
    }).then((response: any) => {}).catch((error: any) => {
      console.error(catchErrorMessage(error));
      this.togglePopUpDialog("Error", catchErrorMessage(error));
    });

    this.reloadItems(that);
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    let isDisplayChangeButton = true;
  
    if (props.selection.getSelection()[0]) {
      const selectedItem = props.selection.getSelection()[0] as any;
  
      if (selectedItem.creationWay === 'Calculation' || selectedItem.status === 'Expired') {
        isDisplayChangeButton = false;
      }
    }

      let customActionsProps = [{
          displayFor: 'none',
          buttonText: L("New APK form"),
          buttonIcon: "Add",
          buttonClassNames: `ms-Button ms-Button--commandBar ms-CommandBarItem-link ${classNames.buttonPrimary}`,
          buttonColor: myTheme.palette.white,
          buttonIconColor: myTheme.palette.white,
          buttonBackground: myTheme.palette.themePrimary,
          buttonBorder: myTheme.palette.themePrimary,
        },
        {
          displayFor: 'single',
          buttonText: L('Change "Policy Issued" status'),
          buttonIcon: "none",
          buttonColor: myTheme.palette.black,
          buttonIconColor: myTheme.palette.white,
          buttonBackground: additionalTheme.darkerWhite,
        },
      ]

      let customActionsPropsWithoutButton = [{
        displayFor: 'none',
        buttonText: L("New APK form"),
        buttonIcon: "Add",
        buttonClassNames: `ms-Button ms-Button--commandBar ms-CommandBarItem-link ${classNames.buttonPrimary}`,
        buttonColor: myTheme.palette.white,
        buttonIconColor: myTheme.palette.white,
        buttonBackground: myTheme.palette.themePrimary,
        buttonBorder: myTheme.palette.themePrimary,
      },
    ]
  
    return {
      ...props,
      customActionsProps: isDisplayChangeButton ? customActionsProps : customActionsPropsWithoutButton,
      customActions: [
        () => {
          this.props.history.push(`/${RouterPath.ApkForm}`);
        },
        async (apkAttachedFiles: ApkAttachedFilesDto, that: any) => {
          that.toggleCustomActionButton(true, true);
          await this.updatePolicyIssuedStatus(apkAttachedFiles, that);
        },
      ]
    }
  }

  getTitle(): string {
    return L('APK list');
  }
  
  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
        <Dialog
          hidden={!this.showPopUpDialog}
          onDismiss={() => this.reloadListOnDialogClose()}
          dialogContentProps={{
              type: DialogType.normal,
              title: L(this.popUpDialogTitle),
              subText: L(this.popUpDialogText),
          }}
          modalProps={{
              isBlocking: true
          }}
        >
      </Dialog>
      
      <ApkPanel
        {...props}
      />
    </>;
  }
  
  copyAndSort<T>(items: T[], columnKey: string, isSortedDescending?: boolean): T[] {
    const key = columnKey as keyof T;
    if(key === 'company') {
      let concatColumn: any[] = [];
      items.forEach((item: any, index: number) => {
        if(item.clientType === ClientTypeEnum.Individual) {
          concatColumn.push({index: index, name: `${item.user.name} ${item.user.surname}`});
        } else {
          concatColumn.push({index: index, name: `${item.company}`});
        }
      });
      concatColumn.sort((a: any, b: any) => { 
        if(a.name < b.name)
          return isSortedDescending ? -1 : 1;
        if(a.name > b.name)
          return isSortedDescending ? 1 : -1;
        return 0;
      });

      let sortedItems: any[] = [];
      concatColumn.forEach((col: any) => {
        sortedItems.push(items[col.index]);
      });
      return sortedItems;
    } else {
      return items.slice(0).sort((a: any, b: any) => { 
        return (isSortedDescending ? a[key] < b[key] : a[key] > b[key]) ? 1 : -1;
      });
    }
  }
}