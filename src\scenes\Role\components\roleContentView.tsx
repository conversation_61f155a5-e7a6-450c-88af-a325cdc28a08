import { Callout, mergeStyleSets, Pivot, PivotItem, Selection, SelectionMode } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { RoleDto } from '../../../services/role/dto/roleDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView, IGenericContentViewProps } from '../../BaseComponents/genericContentView';
import { defaultRole } from '../../../stores/roleStore';
import { CreateRoleInput } from '../../../services/role/dto/createRoleInput';
import { IContentViewState } from '../../BaseComponents/IContentViewState';
import abpUserConfigurationService from "../../../services/abpUserConfigurationService";
import { getPatternText } from '../../../utils/utils';
import { PatternTypeEnum } from '../../../services/pattern/patternTypeEnums';
import { fluentTableClassNames } from '../../../styles/fluentTableStyles';
import { RoleFluentListBase } from '../../BaseComponents/roleFluentListBase';

const classNames = mergeStyleSets({
    callout: {
        width: 320,
        maxWidth: '90%',
        padding: '20px',
        cursor: 'pointer',
    },
    calloutText: {
        whiteSpace: 'pre-line',
    },
});

export class RoleContentView extends GenericContentView {
    private role: RoleDto | CreateRoleInput = defaultRole;
    private allPermissions: any[] = [];
    private allGrantedPermissions: any = {};
    private initSelection: boolean = true;
    private _selection: Selection;
    private initSelectionIndexesSet: boolean = false;
    private selectRoleMappedInputId: string = "";
    private tempInputIdUserFieldPairs: any = {};
    private selectPermissionSearchText: string = "";
    private _roleListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedRole: any = this._roleListSelection.getSelection();
            
            if (selectedRole && Array.isArray(selectedRole) && selectedRole.length > 0 &&
                selectedRole[0].key && !this.role.grantedPermissions.includes(selectedRole[0].key)
            ) {
                this.role.grantedPermissions.push(selectedRole[0].key);
            }

            this._roleListSelection.setAllSelected(false);
            this.forceUpdate();
        }, 
        selectionMode: SelectionMode.single,
    });
    private showCallout: boolean = false;
    private permissionsLegendFromPattern: string = "";

    constructor(props: IGenericContentViewProps) {
        super(props);

        this._selection = new Selection({
            onSelectionChanged: () => {
                if(!this.initSelection) {
                    const selectedPermissionIds: string[] = [];
                    this._selection.getSelection().forEach((element: any, index: number) => {
                        if(typeof element['key'] !== 'undefined') {
                            selectedPermissionIds.push(element.id);
                        } else {
                            this._selection.setIndexSelected(index, false, false);
                        }
                    });

                    this.role.grantedPermissions = selectedPermissionIds;
                } else {
                    this.initSelection = false;
                    this.forceUpdate();
                }
            },
        });
    }

    async componentDidMount() {
        this.checkIfDataIsLoaded("role");

        const permissionsLegendFromPattern = await getPatternText(PatternTypeEnum.PermissionsLegend);
        this.permissionsLegendFromPattern = !!permissionsLegendFromPattern ? permissionsLegendFromPattern : L('Something went wrong. Try again later or contact with administrator.');

        this.getAllPermissions();
    }

    componentDidUpdate(prevProps: Readonly<IGenericContentViewProps>, prevState: Readonly<IContentViewState>, snapshot?: any): void {
        if(this.allGrantedPermissions && this.allGrantedPermissions.items && this.allGrantedPermissions.items.length > 0 && this.initSelectionIndexesSet === false) {
            this.role.grantedPermissions.forEach((permission: string) => {
                this.role.grantedPermissions.forEach((permission: string) => { this._selection.setIndexSelected(this.getIndexByItemId(permission), true, false )});
            this.initSelectionIndexesSet = true;
            this.forceUpdate();
            })
        }
    }

    async getAllPermissions() {
        abpUserConfigurationService.getAll().then((data: any) => {
            for (let key in data.data.result.auth.allPermissions) {
                if (data.data.result.auth.allPermissions.hasOwnProperty(key)) {
                    this.allPermissions.push({ key, text: key });
                }
            }
            
            this.forceUpdate();
        }).catch((error: any) => {
            console.error(error);
        });
    }

    private getIndexByItemId(itemId: string): number {
        let indexToReturn = -1;
        
        if(this.allGrantedPermissions.items.length > 0) {
            this.allGrantedPermissions.items.some((item: any, index: number) => {
                if(item.id === itemId || item.id === itemId) {
                    indexToReturn = index;
                    return true;
                }
                return false;
            })
        }
    
        return indexToReturn;
    }

    private handleSelectionChanged(selection: any) {
        if (selection.action === "deletePermission" && !!selection.key) {
            const cloneItems: any[] = [];
            if (this.role.grantedPermissions && (Array.isArray(this.role.grantedPermissions))) {
                this.role.grantedPermissions.forEach((element: any) => {
                    if (element !== selection.key) {
                        cloneItems.push(element);
                    }
                });
                this.role.grantedPermissions = cloneItems;
                this.forceUpdate();
            }
        }
    }

    renderContent() {
        this.role = this.props.payload.model
            ? this.props.payload.model
            : this.props.payload;
        let preSelectedPermissions: string[] = [];
        
        this.allPermissions.forEach((permission: any) => {
            if (this.role.grantedPermissions.includes(permission.key as string)) {
                permission.selected = true;
                preSelectedPermissions.push(permission.key as string);
            }
        });

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed,
                    }
                }
            }
        };

        return (
            <Pivot theme={myTheme} styles={pivotStyles}>
                <PivotItem headerText={L("General")} key={"General"}>
                    {this.renderElement(new ContentViewModelProperty("name", L("Name"), Controls.Text, false, [], false, { isDataLoaded: this.isDataLoaded }), [], { name: this.role.name })}
                    {this.renderElement(new ContentViewModelProperty("displayName", L("Display name"), Controls.Text, false, [], false, { isDataLoaded: this.isDataLoaded }), [], { displayName: this.role.displayName })}
                    {this.renderElement(new ContentViewModelProperty("normalizedName", L("Normalized name"), Controls.Text, false, [], false, { isDataLoaded: this.isDataLoaded }), [], { normalizedName: this.role.normalizedName })}
                    {this.renderElement(new ContentViewModelProperty("description", L("Description"), Controls.Text, false, [], false, { isDataLoaded: this.isDataLoaded, rows: 5 }), [], { description: this.role.description })}
                    
                    <div className={fluentTableClassNames.contentContainer} style={{minHeight: '400px', marginRight:20}}>
                        <RoleFluentListBase
                            id={`permissionList`}
                            items={this.allPermissions}
                            // searchText={this.selectPermissionSearchText}
                            searchText={undefined}
                            scrollablePanelMarginTop={Math.min(180, (this.role.grantedPermissions && Array.isArray(this.role.grantedPermissions)) ?
                                45 + (Math.floor(this.role.grantedPermissions.length / 3) * 30) + 70 : 45)}
                            // customOnSearchTextChanged={(text: string) => { this.selectPermissionSearchText = text; this.forceUpdate() }}
                            customData={{
                                lowerSpinnerWithLabel: true,
                                selectedItems: this.role.grantedPermissions,
                                iconName: 'Help',
                                iconTitle: L('Legend'),
                                iconOnClick: (iconIndex?: number) => {
                                    this.showCallout = true; 
                                    this.forceUpdate();
                                },
                            }}
                            customOnSelectionChanged={(selection: any) => this.handleSelectionChanged(selection)}
                            customSelection={this._roleListSelection}
                        />
                    </div>

                    {(this.showCallout && this.showCallout === true) && (
                        <Callout className={classNames.callout} gapSpace={0} target={`#permissionList_Help`} setInitialFocus
                            onDismiss={ () => { this.showCallout = false; this.forceUpdate(); }}
                        >
                            <div className={classNames.calloutText} dangerouslySetInnerHTML={{ __html: this.permissionsLegendFromPattern }} />
                        </Callout>
                    )}
                </PivotItem>
            </Pivot>
        );
    }
}