import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { ProductTypeDto } from '../../../services/productType/productTypeDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { ProductTypePanel } from './productTypePanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { RouterPath } from "../../../components/Router/router.config";
import { Link } from '@fluentui/react';

export class ProductTypeTable extends FluentTableBase<ProductTypeDto> {
  getItemDisplayNameOf(item: ProductTypeDto): string {
    return item.Name
  }

  getColumns(): ITableColumn[] {
    return ProductTypeTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Name'),
        fieldName: 'Name',
        onRender: (item: any): any => {
          // if (!isGranted("Management.User")) {
          //   return item.Name;
          // }
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.ProductType}/${item.Id}`);
                      }} 
                        href={`/${RouterPath.ProductType}/${item.Id}`}>
                  {item.Name}
                </Link>
        }
      },
      // {
      //   name: L('Bottom description'),
      //   fieldName: 'BottomDescription',
      // },
      // {
      //   name: L('Description'),
      //   fieldName: 'Description',
      // },
      {
        name: L('Published'),
        fieldName: 'Published',
        onRender: (item: ProductTypeDto) => {
          return item.Published ? (
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
          ) : (
            <span>{L('No')}</span>
          );
        }
      },
      {
        name: L('Display order'),
        fieldName: 'DisplayOrder',
      },
    ];
  }

  getTitle(): string {
    return L('Product types');
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <ProductTypePanel
      {...props}
    />
  }
}