import { IDropdownOption } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import { PagedResultDto } from './pagedResultDto';

export class EntityDto<T = number> {
  id!: T;
}

export interface NamedEntityDto extends EntityDto {
  name: string;
}

export class EntityUtils {

  static resultToOptions(result: PagedResultDto<NamedEntityDto>): IDropdownOption[] {
    if (result && result.items)
      return result.items.map(this.mapToOption);
    return [];
  }

  static mapToOption(namedItem: NamedEntityDto): IDropdownOption {
    return {
      key: namedItem.id,
      text: L(namedItem.name),
    }
  }

  static renderEnumName(namedItem?: NamedEntityDto): string {
    return L(namedItem?.name ?? "");
  }
}