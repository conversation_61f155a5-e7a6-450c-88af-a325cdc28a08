import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { ClientTypeEnum } from "../../../services/client/clientTypeEnums";
import { DefaultButton, Dialog, DialogFooter, DialogType, Icon, PrimaryButton } from "@fluentui/react";
import { PrizeDto } from "../../../services/prize/dto/prizeDto";
import { PrizePanel } from "./prizePanel";
import { myTheme } from "../../../styles/theme";
import { defaultPrize } from "../../../stores/prizeStore";

export class PrizeTable extends FluentTableBase<PrizeDto> {
  private asyncActionInProgress: boolean = false;
  private shouldReloadItems: boolean = false;
  private showPopUpDialog: boolean = false;
  private showDeletePopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";
  private selectedItemToDelete: PrizeDto = defaultPrize;
  
  disableGetAllOnMount = true;

  getItemDisplayNameOf(item: PrizeDto): string {
    return item.name;
  }

  getColumns(): ITableColumn[] {
    return PrizeTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Name'),
        fieldName: 'name',
        minWidth: 200,
        maxWidth: 250,
      },
      {
        minWidth: 25,
        maxWidth: 25,
        name: L('Is enabled'),
        fieldName: 'isEnabled',
        onRender: (item: any) => {
          return <Icon style={item.isEnabled === true ? {color: "green"} : {color: "red"}} 
                      iconName={item.isEnabled === true ? "SkypeCheck" : "StatusCircleErrorX"} />
        }
      },
    ];
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: false,
      customActions: true,
    };
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();

    let customActionsPropsWithoutButton = [{
      displayFor: 'single',
      buttonText: L(`Delete`),
      buttonIcon: "none",
      buttonColor: myTheme.palette.white,
      buttonIconColor: myTheme.palette.white,
      buttonBackground: myTheme.palette.red,
    },
    ]
    return {
      ...props,
      customActionsProps: customActionsPropsWithoutButton,
      customActions: [
          (item: PrizeDto) => {
            this.selectedItemToDelete = item;
            this.showDeletePopUpDialog = true;
            this.forceUpdate();
          },
      ]
    }
  }

  private reloadListOnDialogClose() {
    this.showPopUpDialog = false;

    if(this.shouldReloadItems) {
      this.reloadItems();
    }

    this.forceUpdate();
  }

  private async reloadItems() {
    this.selectionSetAllSelected(false);
    if(typeof this.props.refreshItems !== 'undefined') {
      await this.props.refreshItems!();
    } else {
      this.togglePopUpDialog("Error", "The list could not be refreshed automatically, please refresh the page manually.");
    }
  }

  private togglePopUpDialog(title: string, text: string, visibility?: boolean) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.forceUpdate();
  }

  private closeDeletePopUpDialog() {
    this.showDeletePopUpDialog = false;
    this.forceUpdate();
    
    setTimeout(() => {
      this.asyncActionInProgress = false;
      this.selectedItemToDelete = defaultPrize;
      this.forceUpdate();
    }, 600);
  }

  getTitle(): string {
    return L('Prize list');
  }
  
  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
        <Dialog
          hidden={!this.showDeletePopUpDialog}
          onDismiss={() => { this.closeDeletePopUpDialog(); }}
          dialogContentProps={{
              type: DialogType.normal,
              title: `${L("The voucher takes part in an active Promotion.")} ${L("Are you sure you want to delete")} ${this.selectedItemToDelete.name}?`,
              closeButtonAriaLabel: L('Close'),
          }}
          modalProps={{isBlocking: true, styles: {main: { maxWidth: 450 }},}}
          theme={myTheme}
        >
          <DialogFooter theme={myTheme}>
            <DefaultButton theme={myTheme} onClick={() => { this.closeDeletePopUpDialog(); }} text={L('No')} disabled={this.asyncActionInProgress} />
            <PrimaryButton text={L('Yes')} theme={myTheme} disabled={this.asyncActionInProgress}
                onClick={async () => {
                  if(!!this.selectedItemToDelete.id && parseInt(this.selectedItemToDelete.id) > 0) {
                    this.asyncActionInProgress = true;
                    this.forceUpdate(); 

                    await this.props.store.delete(this.selectedItemToDelete).then(async () => {
                      if(this.props.refreshItems) {
                        await this.props.refreshItems({resetDataSet: true});
                      } else {
                        await this.props.store.getAll();
                      }
                    }).catch((error: any) => {
                      console.error(error);
                      this.closeDeletePopUpDialog();
                    });
                  }

                  this.closeDeletePopUpDialog();
                }}
            />
          </DialogFooter>
        </Dialog>

        <Dialog
          hidden={!this.showPopUpDialog}
          onDismiss={() => this.reloadListOnDialogClose()}
          dialogContentProps={{
              type: DialogType.normal,
              title: L(this.popUpDialogTitle),
              subText: L(this.popUpDialogText),
          }}
          modalProps={{
              isBlocking: true
          }}
        >
      </Dialog>
      
      <PrizePanel
        {...props}
      />
    </>;
  }
  
  copyAndSort<T>(items: T[], columnKey: string, isSortedDescending?: boolean): T[] {
    const key = columnKey as keyof T;
    if(key === 'company') {
      let concatColumn: any[] = [];
      items.forEach((item: any, index: number) => {
        if(item.clientType === ClientTypeEnum.Individual) {
          concatColumn.push({index: index, name: `${item.user.name} ${item.user.surname}`});
        } else {
          concatColumn.push({index: index, name: `${item.company}`});
        }
      });
      concatColumn.sort((a: any, b: any) => { 
        if(a.name < b.name)
          return isSortedDescending ? -1 : 1;
        if(a.name > b.name)
          return isSortedDescending ? 1 : -1;
        return 0;
      });

      let sortedItems: any[] = [];
      concatColumn.forEach((col: any) => {
        sortedItems.push(items[col.index]);
      });
      return sortedItems;
    } else {
      return items.slice(0).sort((a: any, b: any) => { 
        return (isSortedDescending ? a[key] < b[key] : a[key] > b[key]) ? 1 : -1;
      });
    }
  }
}