import { CrudServiceBase } from '../base/crudServiceBase';
import Endpoint from '../endpoint';
import { PolicyCalculationDto } from './policyCalculationDto';
import { httpApi } from '../httpService';
import { isUserLoggedIn } from '../../utils/authUtils';

export class PolicyCalculationService extends CrudServiceBase<PolicyCalculationDto> {
    constructor() {
        super(Endpoint.PolicyCalculation);
        this.internalHttp = httpApi;
    }

    async getFillExample(type: string = "Vehicle") {
        return await this.internalHttp.get(this.endpoint.Custom(`FillExample?type=${type}`, true));
    }

    async getCalculations(requestBody: any) {
        return await this.internalHttp.post(this.endpoint.Custom("CalculateAll", true), requestBody);
    }

    async getCalculation(requestBody: any, insurerName: string, calculationsId: number) {
        const requestBodyMerged = {
            ...requestBody,
            insurerName: insurerName,
            parentCalculationId: calculationsId
        }
        return await this.internalHttp.post(this.endpoint.Custom(`CalculateForInsurer`, true), requestBodyMerged);
    }

    async ExportAsExcel(requestBody: any) {
        return await this.internalHttp.post(this.endpoint.Custom(`ExportAsExcel`, true), requestBody);
    }

    async getInsurerCommunicationData(policyCalculationId: number) {
        isUserLoggedIn();
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetInsurerCommunicationData?policyCalculationId=${policyCalculationId}`, true));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportPolicyCalculationService: PolicyCalculationService = new PolicyCalculationService();
export default exportPolicyCalculationService;