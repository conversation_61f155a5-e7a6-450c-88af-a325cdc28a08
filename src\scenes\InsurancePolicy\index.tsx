import { inject, observer } from 'mobx-react';

import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import InsurancePolicyStore from '../../stores/insurancePolicyStore';
import { InsurancePolicyDto } from '../../services/insurancePolicy/insurancePolicyDto';
import { InsurancePolicyContentView } from './components/insurancePolicyContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	insurancePolicyStore: InsurancePolicyStore;
	match: any
}

@inject(Stores.InsurancePolicyStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private insurancePolicyId = this.props.match.params.id;

	async componentDidMount() {
		await this.props.insurancePolicyStore.get({ id: this.insurancePolicyId } as InsurancePolicyDto);
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<InsurancePolicyContentView store={this.props.insurancePolicyStore} payload={ this.props.insurancePolicyStore.model as InsurancePolicyDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;