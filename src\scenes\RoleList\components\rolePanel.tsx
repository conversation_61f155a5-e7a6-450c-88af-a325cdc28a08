import { RoleDto } from '../../../services/role/dto/roleDto';
import { RoleContentView } from '../../Role/components/roleContentView';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';

export class RolePanel extends GenericPanel {
    getPanelTitle(): string {
        return L("Role");
    }

    renderContent() {
        return <RoleContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as RoleDto } />;
    }
}