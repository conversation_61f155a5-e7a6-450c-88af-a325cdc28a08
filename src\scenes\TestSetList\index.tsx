import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import { TestSetTable } from './components/testSetTable';
import TestSetStore from '../../stores/testSetStore';
import { CrudConsts } from '../../stores/crudStoreBase';
import {mergeStyleSets} from "@fluentui/react";
import {L} from "../../lib/abpUtility";
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
})

export interface IProps {
  searchStore: SearchStore;
  testSetStore: TestSetStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.TestSetStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  private async refreshItems() {
    setTimeout(async () => {
      await this.props.testSetStore.getAll({...this.props.testSetStore.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE}).then(() => {
        this.props.searchStore.setText(' ');
        setTimeout(() => {
          this.props.searchStore.setText('');
        }, 400);
      });

      this.forceUpdate();
    }, 200);
  }

  public render() {
    const items = this.props.testSetStore.dataSet ? this.props.testSetStore.dataSet.items : [];
    
    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Test Set List')}</h2>
        </div>

        <TestSetTable
          searchText={this.props.searchStore.searchText}
          items={items}
          store={this.props.testSetStore}
          history={this.props.history}
          refreshItems={() => this.refreshItems()}
        />
      </>
    );
  }
}

export default Index;