import * as React from 'react';
import AccountStore from '../../../stores/accountStore';
import HelpPanelForm from "./helpPanelForm"
import { RequestResetPassword } from '../../../services/account/dto/requestResetPassword';

export interface IHelpPanelProps {
    isOpen: boolean
    onCancel: () => void;
    accountStore: AccountStore;
}

export interface IHelpPanelState {
}

export interface IHelp extends RequestResetPassword {
}

export default class HelpPanel extends React.Component<IHelpPanelProps, IHelpPanelState> {
    async onSuccess(err: any, values: IHelp) {
        await this.props.accountStore.requestResetPassword(values);
    }

    async onFinished() {
        this.props.onCancel();
    }

    public render() {
        return <HelpPanelForm
            isOpen={this.props.isOpen}
            onCancel={this.props.onCancel}
        />;
    }
}
