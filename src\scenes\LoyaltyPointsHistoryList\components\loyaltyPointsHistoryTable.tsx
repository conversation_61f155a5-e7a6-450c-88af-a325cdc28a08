import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { LoyaltyPointsHistoryDto } from '../../../services/loyaltyPointsHistory/loyaltyPointsHistoryDto';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { LoyaltyPointsHistoryPanel } from './loyaltyPointsHistoryPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { dateFormat } from "../../../utils/utils";
import { getCustomerName } from "../../../utils/storeUtils";
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";

export class LoyaltyPointsHistoryTable extends FluentTableBase<LoyaltyPointsHistoryDto> {
  getItemDisplayNameOf(item: any): string {
    const customerName = item.Customer ? getCustomerName(item.Customer) : null;
    if(customerName && customerName.FirstName) {
      return `${item.Points} ${L('points')} | ${customerName.FirstName} ${customerName.LastName && customerName.LastName}`;
    } else {
      return `${item.Points} ${L('points')}`;
    }
  }

  getColumns(): ITableColumn[] {
    return LoyaltyPointsHistoryTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Customer name'),
        fieldName: 'CustomerName',
        onRender: (item: any): any => {
          let customerUser: any = item.Customer && item.Customer.user ? item.Customer.user : null;
          return customerUser === null ? "-" : `${customerUser.name} ${customerUser.surname}`;
        }
      },
      {
        name: L('Points'),
        fieldName: 'Points',
      },
      {
        name: L('Points balance'),
        fieldName: 'PointsBalance',
      },
      {
        name: L('Message'),
        fieldName: 'Message',
      },
      {
        name: L('Created on'),
        fieldName: 'CreatedOnUtc',
        onRender: (item: any): any => {
          return dateFormat(item.CreatedOnUtc);
        }
      },
    ];
  }

  getTitle(): string {
    return L('Loyalty points history list');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: false,
      delete: false,
      customActions: false,
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <LoyaltyPointsHistoryPanel
      {...props}
    />
  }
}