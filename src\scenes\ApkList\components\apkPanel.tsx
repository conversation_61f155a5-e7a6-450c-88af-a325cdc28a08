import { PrimaryButton } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { myTheme } from '../../../styles/theme';
import { GenericPanel } from '../../Fluent/base/genericPanel';

export class ApkPanel extends GenericPanel {
    private inputErrorsCount: number = 0;

    getPanelTitle(): string {
        return L('APK');
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} onClick={this._onConfirm} text={L('Save')} iconProps={{ iconName: 'Save' }}
                disabled={this.asyncActionInProgress || this.inputErrorsCount > 0} />
    };

    renderContent() {
        // return <CustomerContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ClientDto }
        //         onSetInputErrorsCount={(count: number) => { this.inputErrorsCount = count; this.forceUpdate(); }} />;
        return <></>;
    }
}