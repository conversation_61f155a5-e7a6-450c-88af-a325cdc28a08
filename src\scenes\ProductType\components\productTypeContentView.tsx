import { Pivot, PivotItem } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { ProductTypeDto } from '../../../services/productType/productTypeDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { defaultProductType } from '../../../stores/productTypeStore';

export class ProductTypeContentView extends GenericContentView {
    private productType: ProductTypeDto = defaultProductType;

    componentDidMount() {
        this.checkIfDataIsLoaded("productType");
    }

    renderContent() {
        this.productType = this.props.payload.model ? this.props.payload.model : this.props.payload;
        const pivotStyles = {
            root: {
              marginLeft: '-8px'
            },
            linkIsSelected: {
              color: myTheme.palette.red,
              selectors: {
                ':before': {
                  height: '5px',
                  backgroundColor: additionalTheme.darkerRed
                }
              }
            }
          };


        return <Pivot theme={myTheme} styles={pivotStyles}>
            <PivotItem headerText={L('General')} key={'General'}>
                {this.renderElement(new ContentViewModelProperty('Name', "Name", Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'Name': this.productType.Name})}
                {this.renderElement(new ContentViewModelProperty('DisplayOrder', "Display order", Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: "number"}), [], {'DisplayOrder': this.productType.DisplayOrder})}
                {this.renderElement(new ContentViewModelProperty('Published', "Published", Controls.CheckBox, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'Published': this.productType.Published})}
            </PivotItem>
        </Pivot>
    }
}