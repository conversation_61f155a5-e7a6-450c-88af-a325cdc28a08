import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";

export interface ProductTypeDto extends BaseApiEntityModel {
  Id: string,
  Name: string;
  // Description: string;
  // BottomDescription: string;
  // CategoryLayoutId: string;
  // MetaKeywords: string;
  // MetaDescription: string;
  // MetaTitle: string;
  // SeName: string;
  // ParentCategoryId: string;
  // PictureId: string;
  // PageSize: number;
  // AllowCustomersToSelectPageSize: boolean;
  // PageSizeOptions: string;
  // ShowOnHomePage: boolean;
  // FeaturedProductsOnHomePage: boolean;
  // IncludeInMenu: boolean;
  Published: boolean;
  DisplayOrder: number;
  // Flag: string;
  // FlagStyle: string;
  // Icon: string;
  // HideOnCatalog: boolean;
  // ShowOnSearchBox: boolean;
  // SearchBoxDisplayOrder: number;
}