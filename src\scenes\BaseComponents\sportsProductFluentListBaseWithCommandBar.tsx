import {FluentTableBase} from "../Fluent/base/fluentTableBase";
import {ITableColumn} from "./ITableColumn";
import {myTheme} from "../../styles/theme";
import {L} from "../../lib/abpUtility";
import {ICrudPermissons} from "./commandBarBase";
import {ThemeProvider} from "@fluentui/react";
import { InsurerDto } from "../../services/insurer/dto/insurerDto";
import { IGenericPanelProps } from "../Fluent/base/genericPanel";
import { SportsPanel } from "../ProductList/components/sportsPanel";
import { AddSportsPanel } from "../ProductList/components/addSportsPanel";
import { GeneralSportsListPanel } from "../ProductList/components/generalSportsListPanel";

export class SportsProductFluentListBaseWithCommandBar extends FluentTableBase<InsurerDto> {
    disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;
    private panelSwitch: string = 'none';

    getColumns(): ITableColumn[] {
        return this.getTableColumns(this.props);
    }

    private getTableColumns(props: any): ITableColumn[] {
        return [
            {
                name: L('Insurer name'),
                fieldName: 'insurerName',
                onRender: (item: InsurerDto) => {
                    return item.name;
                }
            }
        ];
    }

    getCrudPermission(): ICrudPermissons {
        return {
            create: false,
            update: false,
            delete: false,
            customActions: true,
        };
    }

    getCommandBarBaseProps() {
        let props = super.getCommandBarBaseProps();
        return {
            ...props,
            customActionsProps: [
                {
                    displayFor: 'none',
                    buttonText: L("General sports list"),
                    buttonIcon: "none",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                {
                    displayFor: 'single',
                    buttonText: L("General sports list"),
                    buttonIcon: "none",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                {
                    displayFor: 'single',
                    buttonText: L("Edit range"),
                    buttonIcon: "Edit",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                {
                    displayFor: 'single',
                    buttonText: L("Add range"),
                    buttonIcon: "add",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                {
                    displayFor: 'single',
                    buttonText: L("Export"),
                    buttonIcon: "none",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
            ],
            customActions: [
                async () => {
                    await this.props.customData.fetchAllSports().then((result: any) => {
                        this.panelSwitch = 'generalSportsList';
                        this.setState({ modalVisible: true });
                    });
                },
                async () => {
                    await this.props.customData.fetchAllSports().then((result: any) => {
                        this.panelSwitch = 'generalSportsList';
                        this.setState({ modalVisible: true });
                    });
                },
                async () => {
                    await this.props.customData.fetchSportsByInsurerId().then((result: any) => {
                        this.panelSwitch = 'sportsPanel';
                        this.setState({ modalVisible: true });
                    });
                },
                async () => {
                    await this.props.customData.fetchAllSports().then((result: any) => {
                        this.panelSwitch = 'addSportsPanel';
                        this.setState({ modalVisible: true });
                    });
                },
                () => {
                    this.props.customData.getSportCoverageExcelByInsurerId();
                },
            ]
        }
    }

    renderPanelView(props: IGenericPanelProps): JSX.Element {
        switch(this.panelSwitch) {
            case 'generalSportsList':
                return <GeneralSportsListPanel
                    {...props}
                />  
            case 'addSportsPanel': 
                return <AddSportsPanel
                    {...props}
                />
            case 'sportsPanel':
                return <SportsPanel
                    {...props}
                /> 
            default: 
                return <></>
        }
    }

    renderAll(pageInfo: string, values: any, columns: any) {
        return <>
            <ThemeProvider theme={myTheme}>
                { this.renderAnnounced(pageInfo) }
                { this.renderCommandBarBase() }

                { this.renderListScrollablePane(values, columns) }
            </ThemeProvider>

            {this.renderPanel()}
        </>;
    }

}