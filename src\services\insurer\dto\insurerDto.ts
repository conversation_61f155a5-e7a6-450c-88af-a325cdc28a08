import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";

export interface InsurerDto extends BaseApiEntityModel {
    name: string;
    fullName: string;
    address: string;
    logoLink: string;
    mobilePaymentSettings: MobilePaymentSettingsDto[];
    phoneNumber: string;
    emailAddress: string;
    isAgentSales: boolean;
    isDirectSales: boolean;
    isSendProposal: boolean;
    isSendPdfFromCalculation: boolean;
    isAgencySales: boolean;
    zipPostalCode: string;
    city: string;
}

export interface MobilePaymentSettingsDto {
    paymentMethod: string;
    paymentPlan: string;
    paymentType: string;
    policyType: string;
}