import ContestService from '../services/contest/contestService';
import { ContestDto } from '../services/contest/dto/contestDto';
import { ContestTypeEnum } from '../services/contest/dto/contestTypeEnum';
import { CrudStoreBase } from './crudStoreBase';

class ContestStore extends CrudStoreBase<ContestDto>{
  constructor() {
    super(ContestService, defaultContest)
  }

  public async create(createUserInput: ContestDto) {
    return ContestService.create(createUserInput);
  }

  public async update(updateUserInput: ContestDto) {
    return ContestService.create(updateUserInput);
  }
}

export const defaultContest = {
  id: '',
  name: '',
  productType: '',
  prizesAmount: 0,
  prizeItems: [],
  infinitePrizes: false,
  conditions: '',
  promotionRules: '',
  isEnabled: false,
  startDate: new Date().toUTCString(),
  endDate: new Date().toUTCString(),
  marketingAgreementsContent: '',
  contestDescription: '',
  contestPrizeDescription: '',
  contestType: ContestTypeEnum.Common,
  imageUrl: '',
  scale: 1,
  section1Name: '',
  section1Description: '',
  section2Name: '',
  section2Description: '',
  description: '',
  descriptionTitle: '',
  receivedPromotionDescription: '',
}

export default ContestStore;