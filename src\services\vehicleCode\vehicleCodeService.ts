import { ODataServiceBase } from '../base/oDataServiceBase';
import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { VehicleCodeDto } from './vehicleCodeDto';

export class VehicleCodeService extends ODataServiceBase<VehicleCodeDto> {
    constructor() {
        super(Endpoint.VehicleCode);
        this.internalHttp = httpApi;
    }

    private encodeForURI(valueToEncode: string) {
        return encodeURIComponent(valueToEncode);
    }

    async getVehicleTypes() {
        let result = await this.internalHttp.get(this.endpoint.Custom(`VehicleTypes`, false));
        let toReturn = this.createDataModel(result);

        return {
            totalCount: toReturn.length,
            items: toReturn
        };
    }

    async getVehicleBrands(vehicleType: string) {
        let result = await this.internalHttp.get(this.endpoint.Custom(`Brands/${this.encodeForURI(vehicleType)}`, false));
        let toReturn = this.createDataModel(result);

        return {
            totalCount: toReturn.length,
            items: toReturn
        };
    }

    async getVehicleFuelTypes(vehicleType: string, brand: string, productionYear: string) {
        let result = await this.internalHttp.get(this.endpoint.Custom(`FuelTypes/${this.encodeForURI(vehicleType)}/${this.encodeForURI(this.encodeForURI(brand))}/${productionYear}`, false)); 
        let toReturn = this.createDataModel(result);

        return {
            totalCount: toReturn.length,
            items: toReturn
        };
    }
    
    async getVehicleEngineCapacities(vehicleType: string, brand: string, productionYear: string, fuelType: string) {
        let result = await this.internalHttp.get(this.endpoint.Custom(`EngineCapacities/${this.encodeForURI(vehicleType)}/${this.encodeForURI(this.encodeForURI(brand))}/${productionYear}/${fuelType}`, false));
        let toReturn = this.createDataModel(result);

        return {
            totalCount: toReturn.length,
            items: toReturn
        };
    }

    async getVehicleModels(vehicleType: string, brand: string, productionYear: string, fuelType: string, engineCapacity: string) {
        let result = await this.internalHttp.get(this.endpoint.Custom(`Models/${this.encodeForURI(vehicleType)}/${this.encodeForURI(this.encodeForURI(brand))}/${productionYear}/${fuelType}/${engineCapacity}`, false));
        let toReturn = this.createDataModel(result);

        return {
            totalCount: toReturn.length,
            items: toReturn
        };
    }

    async getVehicleEnginePowers(vehicleType: string, brand: string, productionYear: string, fuelType: string, engineCapacity: string, model: string) {
        let result = await this.internalHttp.get(this.endpoint.Custom(`EnginePowers/${this.encodeForURI(vehicleType)}/${this.encodeForURI(this.encodeForURI(brand))}/${productionYear}/${fuelType}/${engineCapacity}/${this.encodeForURI(this.encodeForURI(model))}`, false));
        let toReturn = this.createDataModel(result);

        return {
            totalCount: toReturn.length,
            items: toReturn
        };
    }

    async getVehicleCodes(vehicleType?: string, brand?: string, productionYear?: string, fuelType?: string, engineCapacity?: string, model?: string, enginePower?: number) {
        // let result = await this.internalHttp.get(this.endpoint.Custom(`VehicleCodes/${vehicleType}/${brand}/${productionYear}/${fuelType}/${model}/${engineCapacity}/${enginePower}`, false));
        let requestBody = {};
        if(!!vehicleType) requestBody['vehicleType'] = this.encodeForURI(vehicleType);
        if(!!brand) requestBody['brand'] = this.encodeForURI(brand);
        if(!!productionYear) requestBody['year'] = productionYear;
        if(!!fuelType) requestBody['fuelType'] = fuelType;
        if(!!engineCapacity) requestBody['engineCapacity'] = engineCapacity;
        if(!!model) requestBody['model'] = this.encodeForURI(model);
        if(!!enginePower) requestBody['enginePower'] = enginePower;

        let result = await this.internalHttp.post(this.endpoint.Custom(`VehicleCodes`, false), requestBody);
        // let toReturn = this.createDataModel(result);

        // return {
        //     totalCount: toReturn.length,
        //     items: toReturn
        // };
        return result && result.data && result.data.result ? result.data.result : (result && result.data ? result.data : result);
    }

    async getInfoEkspertCode(euroTaxId: number, productionYear: number) {
        let result = await this.internalHttp.get(this.endpoint.Custom(`InfoEkspertCode/${euroTaxId}/${productionYear}`, false));
        return result && result.data && result.data.result ? result.data.result : (result && result.data ? result.data : result);
    }
}

const exportVehicleCodeService: VehicleCodeService = new VehicleCodeService();
export default exportVehicleCodeService;