import { Text, FontWeights, IChoiceGroupOption, Pivot, PivotItem, Selection, SelectionMode, ShimmeredDetailsList, IColumn, mergeStyleSets } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { OrderDto } from '../../../services/order/dto/orderDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { defaultOrder } from '../../../stores/orderStore';
import { dateFormat, enumToDropdownOptions, isJsonString } from '../../../utils/utils';
import { ITableColumn } from '../../BaseComponents/ITableColumn';
import { OrderStatus } from '../../../services/order/enums/orderStatusEnums';
import Stores from '../../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import { utilMapToColumn } from '../../../utils/tableUtils';
import { KeysToIdMapper } from '../../../services/product/productDto';
import { defaultKeysToIdMapper } from '../../../stores/productStore';
import productService from '../../../services/product/productService';
import { mapAttributeNameToId } from '../../../utils/policyCalculationUtils';

const classNames = mergeStyleSets({
  fontBold: {
      fontWeight: '800',
  },
  summaryAttributesWrapper: {
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
      borderRadius: '12px',
      padding: '15px',
      maxWidth: '90%',
      marginBottom: '15px',
      marginTop: '15px',
  },
  summaryAttribute: {
      padding: '5px 10px',
      marginRight: '10px',
      marginBottom: '5px',
      background: myTheme.palette.themeDarker,
      color: `${additionalTheme.white} !important`,
      borderRadius: '10px',
      fontSize: '0.8rem',
      whiteSpace: 'pre-line',
      selectors: {
          '&:last-child': {
              borderRight: 'none',
          }
      }
  }, 
  shimmeredDetailsListStyle: {
      marginTop: '30px',
        '& .ms-DetailsHeader': {
          color: myTheme.palette.neutralDark,
          paddingTop: 0,
          background: myTheme.palette.neutralLighter,
          textTransform: 'uppercase',
        },
        '& .ms-DetailsHeader-cell.ms-DetailsHeader-cellIsCheck .is-checked.ms-Check:before': {
          background: myTheme.palette.themePrimary,
        },
  }
});

@inject(Stores.ProductStore)
@inject(Stores.CountryStore)
@inject(Stores.InsurancePolicyStore)
export class OrderContentView extends GenericContentView {
    private order: OrderDto = defaultOrder;
    private orderItemsParsed: any[] = [];
    private selectedProduct: any = {};
    private _selection: Selection = new Selection({
        onSelectionChanged: () => {
          this.selectedProduct = this._selection.getSelection()[0];

          if(this.selectedProduct) {
            this.parseOrderItem(this.selectedProduct);
          } else {
            this.orderItemsParsed = [];
            this.forceUpdate();
          }
        }
    });
    private keysToIdMapper: KeysToIdMapper = defaultKeysToIdMapper;

    async componentDidMount() {
      this.checkIfDataIsLoaded("order");
      this.keysToIdMapper = await productService.getProductKeysToIdMapper();

      if(!this.props.productStore!.dataSet || this.props.productStore!.dataSet.totalCount <= 0) {
        await this.props.productStore!.getAll(this.props.productStore!.defaultRequest);
      }

      if(!this.props.countryStore?.dataSet || (this.props.countryStore.dataSet && this.props.countryStore.dataSet.totalCount <= 0)) {
        await this.props.countryStore!.getAll(this.props.countryStore!.defaultRequest);
      }

      if(this.props && this.props.insurancePolicyStore) {
        this.props.insurancePolicyStore.clearModel();
      }

      if(this.order && this.order.id && this.order.id.length > 0 && this.order.orderStatusId !== OrderStatus.Saved) {
        await this.props.insurancePolicyStore!.getPolicyByOrderId(this.order.id).then((response: any) => {
          this.props.insurancePolicyStore!.model = response;
          this.forceUpdate();
        });
      }
    }

    renderConfirm = () => {
      return <></>;
    };

    private parseOrderItem(item: any) {
      if(item.attributes && item.attributes.length > 0) {
        const filteredAttr: any[] = item.attributes.filter((attr: any) => attr.key === mapAttributeNameToId("apiCall", item.productId, this.keysToIdMapper));
        const filteredAttrData = item.attributes.filter((attr: any) => attr.key === mapAttributeNameToId("data", item.productId, this.keysToIdMapper));

        if(filteredAttr.length > 0) {
          this.orderItemsParsed.push(JSON.parse(filteredAttr[0].value));
        } else if(filteredAttrData.length > 0) {
          filteredAttrData.some((attr: any) => {
            if(!!attr.value && isJsonString(attr.value)) {
              const parsedJsonAttr: any = JSON.parse(attr.value);
              if(parsedJsonAttr[0] && parsedJsonAttr[0].core_path) {
                this.orderItemsParsed = parsedJsonAttr;
                return true;
              }
            }
            return false;
          });
        } else {
          item.attributes.forEach((attr: any) => {
            this.orderItemsParsed.push(attr.value);
          });
        }
        this.forceUpdate();
      }
    }

    private mapToColumn(tableColumns: ITableColumn[]) {
      return utilMapToColumn(tableColumns);
    }
      
    private getTableColumns(): ITableColumn[] {
      return [          
        {
          name: L('Product'),
          fieldName: 'productId',
          onRender: (item: any): any => {
            return this.renderProductId(item.productId)
          }
        },
        {
          name: L('Total'),
          fieldName: 'priceExclTax',
        },
        {
          name: L('Quantity'),
          fieldName: 'quantity',
        },
        {
          name: L('Attributes count'),
          fieldName: 'attributes',
          onRender: (item: any): any => {
            return <><span className={classNames.fontBold}>{item.attributes.length}</span> ({L('including hidden')})</>
          }
        },
      ];
    }

    private renderProductId(productId: string): any {
      let productStore = this.props.productStore;
      let products = productStore && productStore.dataSet ? productStore.dataSet : null;
      let productName: string = productId;
      if(products && products.items) {
        productName = products.items.filter(product => product.Id === productId).map(product => product.Name)[0];
      };

      return productName;
    }

    renderContent() {
      this.order = this.props.payload.model ? this.props.payload.model : this.props.payload;
      
      let tableColumns = this.getTableColumns();
      let defaultColumns: IColumn[] = this.mapToColumn(tableColumns);

      const orderStatusOptions: any = {
          dropdown: enumToDropdownOptions(OrderStatus, true, true),
          choicegroup: [] as IChoiceGroupOption[]
      };

      let paidDate = '';
      if(this.order.paidDateUtc){
          paidDate = this.order.paidDateUtc;
      };

      let attrNames: string[] = [];
      let attrValues: string[] = [];

      if(this.orderItemsParsed && this.orderItemsParsed.length > 0) {
        this.orderItemsParsed.forEach((item: any, index: number) => {
          if(item.translatedKeyValue) {
            for(let key in item.translatedKeyValue) {
              if(item.translatedKeyValue.hasOwnProperty(key)) {
                attrNames.push(key);
                attrValues.push(item.translatedKeyValue[key]);
              }
            }
          } else if(item.core_path) {
            attrNames.push(item.core_path);
            attrValues.push(item.value);
          } else {
            attrNames.push((index + 1).toString());
            attrValues.push(item);
          }
        });
      }

      let attributes = attrNames.map((name: string, index: number) => {
        return attrValues[index] && attrValues[index].length > 0 ? 
          <p key={index} className={classNames.summaryAttribute}>
              <span className={classNames.fontBold}>{ name }:</span> { attrValues[index] }
          </p> : '';
      });

      let insurancePolicyModel = this.props.insurancePolicyStore?.model;
      let policyPivot = (insurancePolicyModel && typeof insurancePolicyModel.id === 'number' && insurancePolicyModel.id > 0) && 
        <PivotItem headerText={L('Insurance policy')} key={'InsurancePolicy'}>
          {this.renderElement(new ContentViewModelProperty('offerNumber', L("Offer number"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'offerNumber': insurancePolicyModel.offerNumber})}
          {this.renderElement(new ContentViewModelProperty('status', L("Status"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'status': L(insurancePolicyModel.status)})}
          {this.renderElement(new ContentViewModelProperty('segment', L("Segment"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'segment': insurancePolicyModel.segment})}
          {this.renderElement(new ContentViewModelProperty('orderNumber', L("Order number"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'orderNumber': insurancePolicyModel.orderNumber})}
          {this.renderElement(new ContentViewModelProperty('orderDate', L("Order date"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'orderDate': dateFormat(insurancePolicyModel.orderDate)})}
          {this.renderElement(new ContentViewModelProperty('customerName', L("Customer name"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'customerName': insurancePolicyModel.customerName})}
          {this.renderElement(new ContentViewModelProperty('customerSurname', L("Customer surname"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'customerSurname': insurancePolicyModel.customerSurname})}
          {this.renderElement(new ContentViewModelProperty('customerEmail', L("Customer email"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'customerEmail': insurancePolicyModel.customerEmail})}
          {this.renderElement(new ContentViewModelProperty('creationTime', L("Creation date"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'creationTime': dateFormat(insurancePolicyModel.creationTime)})}
          {this.renderElement(new ContentViewModelProperty('startDate', L("Policy start date"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'startDate': dateFormat(insurancePolicyModel.startDate)})}
          {this.renderElement(new ContentViewModelProperty('endDate', L("Policy end date"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'endDate': dateFormat(insurancePolicyModel.endDate)})}
          {this.renderElement(new ContentViewModelProperty('insurer', L("Insurer"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'insurer': insurancePolicyModel.insurer})}
          {this.renderElement(new ContentViewModelProperty('comment', L("Comment"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'comment': insurancePolicyModel.comment})}
        </PivotItem>;

      const pivotStyles = {
        root: {
          marginLeft: '-8px'
        },
        linkIsSelected: {
          color: myTheme.palette.red,
          selectors: {
            ':before': {
              height: '5px',
              backgroundColor: additionalTheme.darkerRed
            }
          }
        }
      };
      return <Pivot theme={myTheme} styles={pivotStyles}>
          <PivotItem headerText={L('General')} key={'General'}>
              {this.renderElement(new ContentViewModelProperty('OrderNumber', L('Number'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'OrderNumber': this.order.orderNumber})}
              {this.renderElement(new ContentViewModelProperty('Code', L('Code'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'Code': this.order.code})}
              {this.renderElement(new ContentViewModelProperty('OrderGuid', L('Order GUID'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'OrderGuid': this.order.orderGuid})}
              {this.renderElement(new ContentViewModelProperty('CustomerEmail', L('Customer'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'CustomerEmail': this.order.customerEmail})}
              {this.renderElement(new ContentViewModelProperty('CustomerId', L('Customer id'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'CustomerId': this.order.customerId})}                
              {this.order.createdOnUtc.length > 0 ? <Text styles={{ root: { fontWeight: FontWeights.bold, marginTop: '15px', color: additionalTheme.grey }}} variant="medium" block>
                  {L('Created on')}: <Text styles={{ root: { fontWeight: FontWeights.regular, color: additionalTheme.grey }}}>{dateFormat(this.order.createdOnUtc)}</Text>
              </Text> : ''}
              {this.renderElement(new ContentViewModelProperty('PaymentMethodSystemName', L('Payment method'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'PaymentMethodSystemName': this.order.paymentMethodSystemName})}
              {paidDate.length > 0 ? <Text styles={{ root: { fontWeight: FontWeights.bold, marginTop: '15px' }}} variant="medium" block>
                  {L('Paid date')}: <Text styles={{ root: { fontWeight: FontWeights.regular }}}>{dateFormat(paidDate)}</Text>
              </Text> : ''}
              {this.renderElement(new ContentViewModelProperty('OrderStatusId', L('Status'), Controls.Picker, false, orderStatusOptions, true, {isDataLoaded: this.isDataLoaded}), [], {'OrderStatusId': this.order.orderStatusId})}
              {this.renderElement(new ContentViewModelProperty('OrderTotal', L('Total'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'OrderTotal': this.order.orderTotal})}
          </PivotItem>

          <PivotItem headerText={L('Products')} key={'OrderItems'}>
              <>
                <ShimmeredDetailsList
                    columns={defaultColumns}
                    items={this.order.orderItems}
                    selectionMode={SelectionMode.single}
                    selection={this._selection}
                    selectionPreservedOnEmptyClick={true}
                    className={classNames.shimmeredDetailsListStyle}
                    theme={myTheme}
                />

                { this.orderItemsParsed && this.orderItemsParsed.length > 0 ?
                  <div className={classNames.summaryAttributesWrapper}>{ attributes.length > 0 ? attributes : L('There are no details for this product') }</div>
                  : <></> 
                }
              </>
          </PivotItem>

          {policyPivot}
      </Pivot>
    }
}