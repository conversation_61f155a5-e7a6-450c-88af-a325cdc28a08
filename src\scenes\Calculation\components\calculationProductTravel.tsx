import { mergeStyleSets, Pivot, PivotItem } from "@fluentui/react";
import React from "react";
import { ContentViewModelProperty } from "../../BaseComponents/contentViewBase";
import { Controls } from "../../BaseComponents/controls";
import { L } from "../../../lib/abpUtility";
import { additionalTheme, myTheme } from "../../../styles/theme";
import { dateFormat } from "../../../utils/utils";
import { LabelComponent } from "../../BaseComponents/labelComponent";
import { checkUsedKeysForDataSet } from "./calculationProductProcessData";

const pivotStyles = {
	root: {
		marginLeft: '-8px'
	},
	linkIsSelected: {
		color: myTheme.palette.red,
		selectors: {
			':before': {
				height: '5px',
				backgroundColor: additionalTheme.darkerRed
			}
		}
	}
};

const classNames = mergeStyleSets({
	tableCell: {
		padding: '3px 10px', 
		border: '1px solid #000',
		textAlign: 'center'
	},
	tableWrapper: {
		maxWidth: '100%',
		overflow: 'auto',
	}
});

export interface ICalculationProductTravelProps {
	calculation: any;
	calculationData: any;
	apkData: any;
	isEditMode: boolean;
	isDataLoaded: boolean;
	activitiesPivotItem: JSX.Element;
	productAttributeResultItems: any[];
	gnLanguage: any;
	renderElement: (element: ContentViewModelProperty, error: any, value: any, tableInputData?: any) => JSX.Element;
}

export class CalculationProductTravel extends React.Component<ICalculationProductTravelProps> {
	private usedAPKInputsKeys: string[] = ['Travel.APK.MedicalInsuranse', 'Travel.APK.CompensationForHealth', 'Travel.APK.ProtectionDamageByChild', 'Travel.APK.CompensationForLuggage', 'Travel.APK.HighRiskSport', 'Travel.APK.SportEquipment', 'Travel.APK.ChronicDiseases'];
	private usedInputsKeys: string[] = ['TravelInsurance.EqSportInsuranceSum', 'TravelInsurance.ActivePackage', 'TravelInsurance.EqSport', 'TravelInsurance.TypeOfCalculation', 'TravelInsurance.Length', 'TravelInsurance.StartDate', 'TravelInsurance.EndDate', 'TravelInfo.TravelCountries', 'TravelInsurance.PaymentMethod', 'TravelInsurance.AdditionalOptions', 'TravelInsurance.LuggageInsuranceSum', 'TravelInsurance.OwnShareInRentCar', 'TravelInsurance.GroupType', 'TravelInsurance.InsuredOnPolandTerritory', 'TravelInsurance.InsurerIsTravelParticipant', 'TravelInsurance.Participants'];
	private usedInputsKeysChecked: boolean = false;

	render() {
		const { calculation, isDataLoaded, calculationData, apkData, renderElement } = this.props;

		// some saved calculations doesn't have full data causing errors
		if(Object.keys(calculationData).length > 0 && !this.usedInputsKeysChecked) {
			checkUsedKeysForDataSet(apkData, this.usedAPKInputsKeys);
			checkUsedKeysForDataSet(calculationData, this.usedInputsKeys, this.props.productAttributeResultItems, this.props.gnLanguage);

			this.usedInputsKeysChecked = true;
			this.forceUpdate();
		}

		const parsedTravelersDataParticipants: any = calculationData['TravelInsurance.Participants'] ? JSON.parse(calculationData['TravelInsurance.Participants'].valueLocale) : {};
		let participantsTableRows: JSX.Element[] = [];
		for(let i = 0; i < Object.keys(parsedTravelersDataParticipants).length; i++) {
			participantsTableRows.push(<tr>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerNumber}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerFirstName}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerSurname}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].date ? dateFormat(parsedTravelersDataParticipants[i].date, "DD.MM.YYYY") : '-'}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerPesel ? parsedTravelersDataParticipants[i].travelerPesel : '-'}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerCity ? parsedTravelersDataParticipants[i].travelerCity : '-'}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerStreet ? parsedTravelersDataParticipants[i].travelerStreet : '-'}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerHouseNumber ? parsedTravelersDataParticipants[i].travelerHouseNumber : '-'}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerPostCode ? parsedTravelersDataParticipants[i].travelerPostCode : '-'}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerCounty ? parsedTravelersDataParticipants[i].travelerCounty : '-'}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerCountry ? parsedTravelersDataParticipants[i].travelerCountry : '-'}</td>
				<td className={classNames.tableCell}>{parsedTravelersDataParticipants[i].travelerPhoneNumber ? parsedTravelersDataParticipants[i].travelerPhoneNumber : '-'}</td>
			</tr>);
		}
		const participantsTable: JSX.Element = <table style={{marginTop: 20}}>
			<tr>
				<th className={classNames.tableCell}>{L('Traveler number')}</th>
				<th className={classNames.tableCell}>{L('Traveler first name')}</th>
				<th className={classNames.tableCell}>{L('Traveler surname')}</th>
				<th className={classNames.tableCell}>{L('Traveler birth date')}</th>
				<th className={classNames.tableCell}>{L('Traveler pesel')}</th>
				<th className={classNames.tableCell}>{L('Traveler city')}</th>
				<th className={classNames.tableCell}>{L('Traveler street')}</th>
				<th className={classNames.tableCell}>{L('Traveler house number')}</th>
				<th className={classNames.tableCell}>{L('Traveler post code')}</th>
				<th className={classNames.tableCell}>{L('Traveler county')}</th>
				<th className={classNames.tableCell}>{L('Traveler country')}</th>
				<th className={classNames.tableCell}>{L('Traveler phone number')}</th>
			</tr>
			{participantsTableRows.map((row) => {
				return row;
			})}
		</table>;

		const parsedTravelCountries: any = calculationData['TravelInfo.TravelCountries'] ? JSON.parse(calculationData['TravelInfo.TravelCountries'].valueLocale) : {};
		
		let travelCountriesNames: string = "";
		if(Array.isArray(parsedTravelCountries) && parsedTravelCountries.length > 0) {
			parsedTravelCountries.forEach((country: any) => {
				if(country && !!country.name) {
					travelCountriesNames += travelCountriesNames.length === 0 ? `${country.name}` : `, ${country.name}`;
				}
			});
		}

		return (
			<>
				<Pivot theme={myTheme} styles={pivotStyles}>		
					<PivotItem headerText={L('General')} key={'General'}>
						{renderElement(new ContentViewModelProperty('calculationId', L("Calculation ID"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'calculationId': calculation.id})}
						{renderElement(new ContentViewModelProperty('translatedSegment', L("Product"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'translatedSegment': L(calculation.segment)})}
						{renderElement(new ContentViewModelProperty('status', L("Status"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'status': L(calculation.lastCreatedPolicyStatus)})}
						{renderElement(new ContentViewModelProperty('creationTime', L("Creation date"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'creationTime': calculation.creationTime})}
						{renderElement(new ContentViewModelProperty('startDate', L("Policy start date"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'startDate': calculation.startDate})}
						{renderElement(new ContentViewModelProperty('customerName', L("Customer name"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerName': calculation.client.user.name})}
						{renderElement(new ContentViewModelProperty('customerSurname', L("Customer surname"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerSurname': calculation.client.user.surname})}
						{renderElement(new ContentViewModelProperty('pesel', L("Pesel"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'pesel': calculation.client.pesel})}
						{renderElement(new ContentViewModelProperty('customerEmail', L("Customer email"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerEmail': calculation.client.user.emailAddress})}
						{renderElement(new ContentViewModelProperty('phoneNumber', L("Customer phone number"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'phoneNumber': calculation.client.phone})}
					</PivotItem>
					
					{Object.keys(apkData).length > 0 &&
						<PivotItem headerText={L('APK')} key={'APK'}>
							{renderElement(new ContentViewModelProperty('Travel.APK.MedicalInsuranse', apkData['Travel.APK.MedicalInsuranse'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'Travel.APK.MedicalInsuranse': apkData['Travel.APK.MedicalInsuranse'].value})}
							{renderElement(new ContentViewModelProperty('Travel.APK.CompensationForHealth', apkData['Travel.APK.CompensationForHealth'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'Travel.APK.CompensationForHealth': apkData['Travel.APK.CompensationForHealth'].value})}
							{renderElement(new ContentViewModelProperty('Travel.APK.ProtectionDamageByChild', apkData['Travel.APK.ProtectionDamageByChild'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'Travel.APK.ProtectionDamageByChild': apkData['Travel.APK.ProtectionDamageByChild'].value})}
							{renderElement(new ContentViewModelProperty('Travel.APK.CompensationForLuggage', apkData['Travel.APK.CompensationForLuggage'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'Travel.APK.CompensationForLuggage': apkData['Travel.APK.CompensationForLuggage'].value})}
							{renderElement(new ContentViewModelProperty('Travel.APK.HighRiskSport', apkData['Travel.APK.HighRiskSport'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'Travel.APK.HighRiskSport': apkData['Travel.APK.HighRiskSport'].value})}
							{renderElement(new ContentViewModelProperty('Travel.APK.SportEquipment', apkData['Travel.APK.SportEquipment'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'Travel.APK.SportEquipment': apkData['Travel.APK.SportEquipment'].value})}
							{renderElement(new ContentViewModelProperty('Travel.APK.ChronicDiseases', apkData['Travel.APK.ChronicDiseases'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'Travel.APK.ChronicDiseases': apkData['Travel.APK.ChronicDiseases'].value})}
						</PivotItem>
					}

					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('Period of protection and direction of departure')} key={'Period of protection and direction of departure'}>
							{renderElement(new ContentViewModelProperty('TravelInsurance.Length', calculationData['TravelInsurance.Length'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.Length': calculationData['TravelInsurance.Length'].valueLocale})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.StartDate', calculationData['TravelInsurance.StartDate'].label, Controls.Date, false, [], true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.StartDate': calculationData['TravelInsurance.StartDate'].value})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.EndDate', calculationData['TravelInsurance.EndDate'].label, Controls.Date, false, [], true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.EndDate': calculationData['TravelInsurance.EndDate'].value})}
							{renderElement(new ContentViewModelProperty('TravelInfo.TravelCountries', calculationData['TravelInfo.TravelCountries'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded, rows: 5}), [], {'TravelInfo.TravelCountries': travelCountriesNames})}
						</PivotItem>
					}

					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('Policy details')} key={'Policy details'}>
							{renderElement(new ContentViewModelProperty('TravelInsurance.PaymentMethod', calculationData['TravelInsurance.PaymentMethod'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.PaymentMethod': calculationData['TravelInsurance.PaymentMethod'].valueLocale})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.AdditionalOptions', calculationData['TravelInsurance.AdditionalOptions'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded, rows: 5}), [], {'TravelInsurance.AdditionalOptions': calculationData['TravelInsurance.AdditionalOptions'].valueLocale})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.LuggageInsuranceSum', calculationData['TravelInsurance.LuggageInsuranceSum'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.LuggageInsuranceSum': calculationData['TravelInsurance.LuggageInsuranceSum'].valueLocale})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.EqSport', calculationData['TravelInsurance.EqSport'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.EqSport': calculationData['TravelInsurance.EqSport'].valueLocale})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.EqSportInsuranceSum', calculationData['TravelInsurance.EqSportInsuranceSum'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.EqSportInsuranceSum': calculationData['TravelInsurance.EqSportInsuranceSum'].valueLocale})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.ActivePackage', calculationData['TravelInsurance.ActivePackage'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.ActivePackage': calculationData['TravelInsurance.ActivePackage'].valueLocale})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.OwnShareInRentCar', calculationData['TravelInsurance.OwnShareInRentCar'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.OwnShareInRentCar': calculationData['TravelInsurance.OwnShareInRentCar'].valueLocale})}
						</PivotItem>
					}

					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('Travelers data')} key={'Travelers data'}>
							{renderElement(new ContentViewModelProperty('TravelInsurance.GroupType', calculationData['TravelInsurance.GroupType'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.GroupType': calculationData['TravelInsurance.GroupType'].valueLocale})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.InsuredOnPolandTerritory', calculationData['TravelInsurance.InsuredOnPolandTerritory'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.InsuredOnPolandTerritory': calculationData['TravelInsurance.InsuredOnPolandTerritory'].valueLocale})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.InsurerIsTravelParticipant', calculationData['TravelInsurance.InsurerIsTravelParticipant'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.InsurerIsTravelParticipant': calculationData['TravelInsurance.InsurerIsTravelParticipant'].valueLocale})}
							{renderElement(new ContentViewModelProperty('TravelInsurance.TypeOfCalculation', calculationData['TravelInsurance.TypeOfCalculation'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'TravelInsurance.TypeOfCalculation': calculationData['TravelInsurance.TypeOfCalculation'].valueLocale})}
							<LabelComponent label={calculationData['TravelInsurance.Participants'].label} customStyles={{marginTop: '20px', marginBottom: '-20px'}} />
							{participantsTable}
						</PivotItem>
					}

					{this.props.activitiesPivotItem}
				</Pivot>
			</>
		);
	}
}