import {
	Default<PERSON><PERSON><PERSON>, Dialog, Di<PERSON>Footer, DialogType, IStackStyles, IStackTokens, mergeStyleSets, MessageBar, Label, PrimaryButton, Spinner, SpinnerSize, Stack
} from "@fluentui/react";
import React from "react";
import { LabeledTextField } from "../../../components/LabeledTextField";
import { L } from "../../../lib/abpUtility";
import { AgreementDto } from "../../../services/agreements/dto/agreementDto";
import {additionalTheme, myTheme} from "../../../styles/theme";
import { getAttributeNameAndValue, getSelectedUserFields } from "../../../utils/policyCalculationUtils";
import { isJsonString } from "../../../utils/utils";
import { CheckBoxBase } from "../../BaseComponents/CheckBoxBase";
import { OcTerminationGeneratorDialog } from "./ocTerminationGeneratorDialog";

const stackStyles: IStackStyles = {
	root: {
		// background: DefaultPalette.themeTertiary,
		// width: 300,
	},
};

const columnTextSpacingStackTokens: IStackTokens = {
	childrenGap: "1%",
	padding: "25px 0",
};


const classNames = mergeStyleSets({
	fontBold: {
		fontWeight: "600",
	},
	confirmCalculationButton: {
		width: "fit-content",
		padding: "6px 20px",
		marginTop: "0 !important",
		marginRight: "25px",
		marginBottom: "25px",
	},
	additionalActionButton: {
		width: "fit-content",
		padding: "20px 20px",
		marginBottom: "5px",
		marginRight: "25px",
	},
	summaryTextContainer: {
		display: 'flex',
		justifyContent: 'space-between',
		alignItems: 'center',
		maxWidth: '90%',
		'& img': {
			maxHeight: '80px',
		}
	},
	summaryText: {
		color: additionalTheme.grey,
		fontWeight: "600",
		selectors: {
			"& span": {
				fontWeight: "500",
				color: additionalTheme.grey
			},
		},
	},
	sectionTitle: {
		color: additionalTheme.grey,
		fontWeight: "800",
		fontSize: '16px',
	},
	//   summaryAttributesWrapper: {
	//     display: "flex",
	//     flexDirection: "row",
	//     flexWrap: "wrap",
	//     justifyContent: "flex-start",
	//     alignItems: "flex-start",
	//     border: `1px solid ${myTheme.palette.themeLight}`,
	//     padding: "15px",
	//     maxWidth: "90%",
	//     marginBottom: "15px",
	//   },
	atributeHorizontal: {
		gridColumn: "span 2",
		gridRow: "span 1",
		boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.25)",
		padding: "10px 20px",
		margin: "10px 1px",
		borderRadius: "12px",
	},
	atributeVertical: {
		gridColumn: "span 1",
		gridRow: "span 2",
		boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.25)",
		width: "100%",
		margin: "10px 20px",
		borderRadius: "12px",
		display: "flex",
		padding: "10px 0",
	},
	atributeVerticalContainer: {
		boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.25)",
		width: "100%",
		margin: "10px 1px",
		borderRadius: "12px",
		display: "flex",
		flexDirection: "column",
		padding: "20px 30px",
		boxSizing: "border-box",
	},
	atributeVerticalElement: {
		width: "50%",
		padding: "0 20px",
	},
	summaryAttributesWrapper: {
		display: "grid" /* 1 */,
		gridTemplateColumns: "repeat(3, auto)",
		gridTemplateRows: "repeat(2, auto)",
		maxWidth: "90%",
	},
	summaryAttribute: {
		color: `${additionalTheme.white} !important`,
		padding: "5px 10px",
		marginRight: "10px",
		marginBottom: "5px",
		background: myTheme.palette.themeLighter,
		fontSize: "0.8rem",
		whiteSpace: "pre-line",
		selectors: {
			"&:last-child": {
				borderRight: "none",
			},
		},
	},
	warningMessageBar: {
		width: "fit-content",
	},
	messageBar: {
		width: "fit-content",
		selectors: {
			"& .ms-MessageBar-innerText": {
				selectors: {
					"& span": {
						whiteSpace: "pre-line",
					},
				},
			},
		},
	},
	confrimButtonWrapper: {
		display: "inline-flex",
		flexDirection: "row",
		flexWrap: "wrap",
		justifyContent: "flex-start",
		alignItems: "center",
		width: "fit-content",
		marginTop: "50px !important",
	},
	loadSpinner: {
		display: "inline-flex",
		selectors: {
			"& .ms-Spinner-label": {
				color: myTheme.palette.themePrimary,
			},
		},
	},
});

export interface IPolicyCalculationStep4Props {
	product: any;
	customer: any;
	selectedCalculation: any;
	filteredCalculations: any;
	orderCreated: boolean;
	orderSent: boolean;
	inspectionLinkSent: boolean;
	policyFinalized: boolean;
	inputsTypeValuePairs: any;
	productAttributes: any;
	gnLanguage: any;
	summaryMessageBoxData: any;
	countryStore: any;
	asyncActionInProgress: boolean | undefined;
	isEditMode: boolean;
	inputsIdUserFieldsPairs: any;
	policyDocumentsDownloaded?: boolean;
	ocTerminationGenerated?: boolean;
	policyMailSended?: boolean;
	payloadType?: string;
	insurancePolicyResponse?: any;
	uwAcceptanceCode: string;
	selectedOfferAgreements: AgreementDto[];
	summaryAgreements: AgreementDto[];
	toggleSummaryAgreement: (agreement: AgreementDto) => void;
	toggleAllSummaryAgreements: (agreements: AgreementDto[]) => void;
	onMessageBarDismiss: (type: string) => void;
	onClickSubmit: () => void;
	goToInsuranceCompany: () => void;
	downloadPolicyDocuments: () => void;
	getMapNameByProduct: (product: any) => string;
	onUwAcceptanceCodeChange: (value: string) => void;
	generateOcTermination: (formData: any) => void;
	sendPolicyMail: () => void;
}

export class PolicyCalculationStep4 extends React.Component<IPolicyCalculationStep4Props> {
	private allRequiredAgreementsAreChecked: boolean = false;
	private showDialog = () => {
		const shouldShow = this.props.insurancePolicyResponse.price < this.props.selectedCalculation.price;
		this.setState({ showDialog: shouldShow });
	}
	private showOcTerminationGeneratorDialog: boolean = false;

	handleButtonClick = async () => {
		if (this.props.onClickSubmit) {
			await this.props.onClickSubmit();
		}
		this.showDialog();
	}

	componentDidMount() {
        this.checkIfAllRequiredAgreementsAreChecked();
    }

	closeDialog = () => {
		this.setState({ showDialog: false });
	}

	state = {
		showDialog: false
	};

	private reduceAttributes(attrIds: string[], attrNames: string[], attrValues: string[], attrMap: string[]): any[] {
		const reducedAttributes: any[] = [];

		attrIds.forEach((productAttributeId: string, index: number) => {
			let doNotPush: boolean = false;

			if (!!this.props.selectedCalculation && !!this.props.inputsIdUserFieldsPairs[productAttributeId]) {
				this.props.inputsIdUserFieldsPairs[productAttributeId].some((userField: any) => {
					if(userField.Key === "allowTuningForInsurer") {
						let splittedValue = userField.Value.split(";;");
						if(!splittedValue.includes(this.props.selectedCalculation.insurerName)) {
							doNotPush = true;
						}
						return true;
					}
					return false;
				});
			} else {
				doNotPush = true;
			}

			if(productAttributeId.length !== 24 || !attrNames[index] || attrNames[index].length === 0) {
				doNotPush = true;
			}

			if(attrNames[index] === 'Wariant ubezpieczenia' || attrNames[index] === 'Insurance option') {
				doNotPush = true;
			}

			if(doNotPush === false) {
				reducedAttributes.push({
					id: productAttributeId,
					name: attrNames[index],
					value: attrValues[index],
					map: attrMap[index],
				});
			}
		});

		return reducedAttributes;
	}

	private checkIfAllRequiredAgreementsAreChecked() {
		let allChecked: boolean = true;

		for(let code in this.props.summaryAgreements) {
			if(this.props.summaryAgreements.hasOwnProperty(code)) {
				if(this.props.summaryAgreements[code].required === true && this.props.summaryAgreements[code].answer !== true) {
					allChecked = false;
				}
			}
		};

		this.allRequiredAgreementsAreChecked = allChecked;
		this.forceUpdate();
	}

	private checkAllRequiredAgreements(summaryAgreements: AgreementDto[]) {
		for(let code in summaryAgreements) {
			if(summaryAgreements.hasOwnProperty(code) && summaryAgreements[code].required === true && !summaryAgreements[code].agreementAsButtons) {
				summaryAgreements[code].answer = true;
			}
		};

		this.props.toggleAllSummaryAgreements(summaryAgreements);
		this.checkIfAllRequiredAgreementsAreChecked();
	}

	render() {
		const { isEditMode, asyncActionInProgress, selectedCalculation, inputsTypeValuePairs, countryStore, product, orderCreated, orderSent, inspectionLinkSent,
			productAttributes, gnLanguage, policyFinalized, summaryMessageBoxData, policyDocumentsDownloaded, payloadType, insurancePolicyResponse, uwAcceptanceCode,
			policyMailSended, ocTerminationGenerated
		} = this.props;

		let saveCalculationButton = (
			<PrimaryButton
				className={classNames.confirmCalculationButton}
				theme={myTheme}
				text={L(isEditMode && payloadType === "order" ? "Update calculation" : "Save calculation")}
				type={"button"}
				onClick={this.props.onClickSubmit}
				disabled={orderCreated || asyncActionInProgress || !this.allRequiredAgreementsAreChecked}
				iconProps={{ iconName: "Save" }}
			/>
		);

		let sendCalculationButton = (
			<PrimaryButton
				className={classNames.confirmCalculationButton}
				theme={myTheme}
				text={L("Send calculation")}
				type={"button"}
				onClick={this.handleButtonClick}
				disabled={!orderCreated || orderSent || asyncActionInProgress || !this.allRequiredAgreementsAreChecked}
				iconProps={{ iconName: "BulkUpload" }}
			/>
		);

		let sendInspectionLinkButton = (
			<PrimaryButton
				className={classNames.confirmCalculationButton}
				theme={myTheme}
				text={L("Send inspection link via SMS")}
				type={"button"}
				onClick={this.props.onClickSubmit}
				disabled={
					!orderCreated ||
					!orderSent ||
					inspectionLinkSent ||
					asyncActionInProgress
				}
				iconProps={{ iconName: "CellPhone" }}
			/>
		);

		let finalizePolicyButton;
		if(insurancePolicyResponse && !!insurancePolicyResponse.clientInspectionLink) {
			finalizePolicyButton = (
				<PrimaryButton
					className={classNames.confirmCalculationButton}
					theme={myTheme}
					text={L("Make policy")}
					type={"button"}
					onClick={this.props.onClickSubmit}
					disabled={!orderCreated || !orderSent || !inspectionLinkSent || policyFinalized || asyncActionInProgress}
					iconProps={{ iconName: "PageAdd" }}
				/>
			);
		} else {
			finalizePolicyButton = (
				<PrimaryButton
					className={classNames.confirmCalculationButton}
					theme={myTheme}
					text={L("Make policy")}
					type={"button"}
					onClick={this.props.onClickSubmit}
					disabled={!orderCreated || !orderSent || policyFinalized || asyncActionInProgress}
					iconProps={{ iconName: "PageAdd" }}
				/>
			);
		}

		let toggleOcTerminationGeneratorDialogButton = (
			<PrimaryButton
				className={classNames.confirmCalculationButton}
				theme={myTheme}
				text={L("Termination of the third party liability insurance contract")}
				type={"button"}
				onClick={() => {
					this.showOcTerminationGeneratorDialog = true;
					this.forceUpdate();
				}}
				disabled={!orderCreated || !orderSent || !policyFinalized || ocTerminationGenerated || asyncActionInProgress}
				iconProps={{ iconName: ocTerminationGenerated === true ? "CheckMark" : 'PageRemove' }}
			/>
		);

		let sendDocumentsToClientButton = (
			<PrimaryButton
				className={classNames.confirmCalculationButton}
				theme={myTheme}
				text={L("Send documents to the client")}
				type={"button"}
				onClick={() => {
					if(this.props.sendPolicyMail) {
						this.props.sendPolicyMail();
					}
				}}
				disabled={!orderCreated || !orderSent || !policyFinalized || policyMailSended || asyncActionInProgress}
				iconProps={{ iconName: policyMailSended === true ? "CheckMark" : 'Send' }}
			/>
		);

		let goToInsuranceCompanyButton = (
			<PrimaryButton
				className={classNames.confirmCalculationButton}
				theme={myTheme}
				text={L("Go to insurance company")}
				type={"button"}
				onClick={this.props.goToInsuranceCompany}
				disabled={!orderCreated || !orderSent || asyncActionInProgress}
			/>
		);

		let downloadPolicyDocumentsButton = (
			<PrimaryButton
				className={classNames.confirmCalculationButton}
				theme={myTheme}
				text={L("Generate and download documents")}
				type={"button"}
				onClick={this.props.downloadPolicyDocuments}
				disabled={
					!orderCreated || !orderSent || !policyFinalized ||
					asyncActionInProgress || policyDocumentsDownloaded === true
				}
				iconProps={{
					iconName: policyDocumentsDownloaded === true ? "CheckMark" : "Download",
				}}
			/>
		);

		let checkAllRequiredAgreementsButton = (
			<DefaultButton
				className={classNames.additionalActionButton}
				theme={myTheme}
				text={L("Check all required requried agreements")}
				type={"button"}
				onClick={() => this.checkAllRequiredAgreements(this.props.summaryAgreements)}
				disabled={asyncActionInProgress || this.allRequiredAgreementsAreChecked === true}
				iconProps={{
					iconName: "ReceiptCheck",
				}}
			/>
		);

		let attrIds: string[] = [];
		let attrNames: string[] = [];
		let attrValues: string[] = [];
		let attrMap: string[] = [];

		if (product && product.ProductAttributeMappings) {
			for (let key in inputsTypeValuePairs) {
				if (inputsTypeValuePairs.hasOwnProperty(key)) {
					let findUserFields = getSelectedUserFields(this.props.inputsIdUserFieldsPairs[key], ['core_path']);
					let findCustomControlType = getSelectedUserFields(this.props.inputsIdUserFieldsPairs[key], ['customControlType']);
					
					if(Object.keys(findUserFields).length > 0) {
						let tempAttrValue = "";
						if((typeof inputsTypeValuePairs[key] === "string" || typeof inputsTypeValuePairs[key] === "number") && (!isJsonString(inputsTypeValuePairs[key]) || !isNaN(parseInt(inputsTypeValuePairs[key])))) {
							tempAttrValue = inputsTypeValuePairs[key];
						} else {
							const objectToIterate: any = isJsonString(inputsTypeValuePairs[key]) ? JSON.parse(inputsTypeValuePairs[key]) : inputsTypeValuePairs[key];

							let multipleValues: string[] = [];
	
							for(let jsonKey in objectToIterate) {
								if(objectToIterate.hasOwnProperty(jsonKey) && objectToIterate[jsonKey]) {
									let multipleValuesPushed: boolean = false;

									if(findCustomControlType && findCustomControlType.customControlType && !!findCustomControlType.customControlType) {
										if(findCustomControlType.customControlType === 'CountrySearchList') {
											multipleValues.push(`${objectToIterate[jsonKey].name}`);
											multipleValuesPushed = true;
										}
										if(findCustomControlType.customControlType === 'SportSearchList') {
											multipleValues.push(`${objectToIterate[jsonKey].name}`);
											multipleValuesPushed = true;
										}
									}

									if(!multipleValuesPushed) {
										multipleValues.push(
											getAttributeNameAndValue(product, productAttributes, jsonKey, objectToIterate[jsonKey], gnLanguage, 
												this.props.getMapNameByProduct(product), { parentKey: key }).translatedValue
										);
									}
								}
							}
	
							tempAttrValue = multipleValues.join(", ");
						}
	
						let attrData = getAttributeNameAndValue(product, productAttributes, key, inputsTypeValuePairs[key], gnLanguage, this.props.getMapNameByProduct(product), 
																{ dataSourceStore: countryStore });
	
						if (attrData.value.length > 0) {
							tempAttrValue = attrData.translatedValue;
						}
	
						attrIds.push(key);
						attrNames.push(attrData.name);
						attrValues.push(tempAttrValue);
						attrMap.push(attrData.map);
					}
				}
			}
		}

		const reducedAttributes: any[] = this.reduceAttributes(attrIds, attrNames, attrValues, attrMap);

		const uniqueReducedAttributes = Array.from(new Set(reducedAttributes.map((a) => a.name))).map((name) => {
			return reducedAttributes.find((a) => a.name === name);
		});

		let client = uniqueReducedAttributes.filter((element: any) =>
			element.name === "First name" ||
			element.name === "Imię" ||
			element.name === "Last name" ||
			element.name === "Nazwisko" ||
			element.name === "Client type" ||
			element.name === "Typ klienta" ||
			element.name === "Phone number" ||
			element.name === "Numer telefonu" ||
			element.name === "PESEL" ||
			element.name === " E-mail adress" ||
			element.name === "Adres e-mail" ||
			element.name === "Nationality" ||
			element.name === "Narodowość"
		);

		let place = uniqueReducedAttributes.filter((element: any) =>
			element.name === "Country" ||
			element.name === "Państwo" ||
			element.name === "City" ||
			element.name === "Miasto" ||
			element.name === "Postal code" ||
			element.name === "Kod pocztowy" ||
			element.name === "Street" ||
			element.name === "Ulica" ||
			element.name === "House / flat number" ||
			element.name === "Nr domu / lokalu" || 
			element.name === "County" || 
			element.name === "Powiat" 
		);

		let other = uniqueReducedAttributes.filter((ar) =>
			!client.find((rm) => rm.name === ar.name && ar.map === rm.map) &&
			!place.find((rm) => rm.name === ar.name && ar.map === rm.map)
		);

		let other1: any[] = [...other];
		other1 = other1.splice(0, other.length / 2);

		let other2: any[] = [...other];
		other2 = other2.splice(other.length / 2, other.length);

		return (
			<>
				{(product && product.SeName && product.SeName === 'ubezpieczenie-auta') &&
					<OcTerminationGeneratorDialog asyncActionInProgress={asyncActionInProgress ? asyncActionInProgress : false}
						showDialog={this.showOcTerminationGeneratorDialog} 
						toggleShowDialog={(value: boolean) => {this.showOcTerminationGeneratorDialog = value; this.forceUpdate(); }}
						generateOcTermination={(formData: any) => {
							this.showOcTerminationGeneratorDialog = false;
							this.props.generateOcTermination(formData);
						}}
					/>
				}

				<Stack styles={stackStyles} tokens={columnTextSpacingStackTokens}>
					<div className={classNames.summaryTextContainer}>
						<div>
							<p className={classNames.summaryText}>
								{L("Product")}: <span>{L(product.Name)}</span>
							</p>
							<p className={classNames.summaryText}>
								{L("Selected offer")}:{" "}
								<span>
									{!!selectedCalculation.gnInsurerId ? selectedCalculation.insurerName : L("none")}
								</span>
							</p>
							<p className={classNames.summaryText}>
								{L("Price")}:{" "}
								<span style={{fontWeight: 800}}>
									{!!selectedCalculation.gnInsurerId ? `${selectedCalculation.price} ${selectedCalculation.currency}` : L("-")}
								</span>
							</p>
						</div>
						<img src={selectedCalculation.insurerLogo} alt="Insurer logo" />
					</div>

					<div className={classNames.summaryAttributesWrapper}>
						{/* {attributes} */}
						<div className={classNames.atributeHorizontal}>
							<h2 className={classNames.sectionTitle}>{L('Client')}</h2>
							{client.map((element: any, index: number) => (
								<p key={index} style={{color: additionalTheme.grey}}>
									<span className={classNames.fontBold}>{element.name}:</span>{" "}
									{element.value}
								</p>
							))}
						</div>

						<div className={classNames.atributeVertical}>
							<div className={classNames.atributeVerticalElement}>
								{other1.map((element: any, index: number) => (
								<p key={index} style={{color: additionalTheme.grey}}>
									<span className={classNames.fontBold}>{element.name}:</span>{" "}
									{element.value}
								</p>
								))}
							</div>
							<div className={classNames.atributeVerticalElement}>
								{other2.map((element: any, index: number) => (
									<p key={index} style={{color: additionalTheme.grey}}>
										<span className={classNames.fontBold}>{element.name}:</span>{" "}
										{element.value}
									</p>
								))}
							</div>
						</div>

						<div className={classNames.atributeHorizontal}>
							<h2 className={classNames.sectionTitle}>{L('Place')}</h2>
							{place.map((element: any, index: number) => (
								<p key={index} style={{color: additionalTheme.grey}}>
									<span className={classNames.fontBold}>{element.name}:</span>{" "}
									{element.value}
								</p>
							))}
						</div>
					</div>

					<div style={{maxWidth: '91%'}}>
						<div className={classNames.atributeVerticalContainer}>
							{this.props.selectedOfferAgreements.slice()  // Create a copy to avoid mutating original array
							.filter((agreement: AgreementDto) => {
								if(agreement.agreementAsButtons !== true) {
									return false;
								}
								return true;
							})
							.sort((a: AgreementDto, b: AgreementDto) => {
									if (a.required && !b.required) return -1; // Move required ones to the top
									if (!a.required && b.required) return 1;
									return 0;  // Keep the original order for those with same 'required' status
							})
							.map((agreement: AgreementDto) => {
								return (
									<div style={{position: 'relative'}}>
										{agreement.required ? <span style={{color: myTheme.palette.red, position: 'absolute', top: 20, left: '-12px'}}>*</span> : <></>}
										<p>{agreement.name}</p>
										
										<div style={{display: 'flex', flexDirection: 'row', flexWrap: 'nowrap', gap: 20, marginBottom: 20}}>
											{this.props.summaryAgreements[agreement.code] && this.props.summaryAgreements[agreement.code].answer === true ?
												<PrimaryButton theme={myTheme} text={L('Yes')} disabled={false} />
												:
												<DefaultButton theme={myTheme} text={L('Yes')} disabled={false} onClick={() => {
													this.props.toggleSummaryAgreement({...agreement, answer: true});
													this.checkIfAllRequiredAgreementsAreChecked();
												}} />
											}

											{this.props.summaryAgreements[agreement.code] && this.props.summaryAgreements[agreement.code].answer === false ?
												<PrimaryButton theme={myTheme} text={L('No')} disabled={false} />
												:
												<DefaultButton theme={myTheme} text={L('No')} disabled={false} onClick={() => {
													this.props.toggleSummaryAgreement({...agreement, answer: false});
													this.checkIfAllRequiredAgreementsAreChecked();
												}} />
											}
										</div>
									</div>
								)
							})}

							{checkAllRequiredAgreementsButton}

							{this.props.selectedOfferAgreements.slice()  // Create a copy to avoid mutating original array
							.filter((agreement: AgreementDto) => {
								if(agreement.agreementAsButtons === true) {
									return false;
								}
								return true;
							})
							.sort((a: AgreementDto, b: AgreementDto) => {
									if (a.required && !b.required) return -1; // Move required ones to the top
									if (!a.required && b.required) return 1;
									return 0;  // Keep the original order for those with same 'required' status
							})
							.map((agreement: AgreementDto) => {
								if(!this.props.summaryAgreements[agreement.code]) {
									this.props.toggleSummaryAgreement({...agreement, answer: agreement.answer ? agreement.answer : false});
								}

								return (
									<div style={{position: 'relative'}}>
										{agreement.required ? <span style={{color: myTheme.palette.red, position: 'absolute', top: 20, left: '-12px'}}>*</span> : <></>}
										<CheckBoxBase label={agreement.name} disabled={false}
											value={this.props.summaryAgreements[agreement.code] ? this.props.summaryAgreements[agreement.code].answer : false}
											onChange={(value: boolean | undefined) => {
												if(typeof value === 'boolean') {
													this.props.toggleSummaryAgreement({...agreement, answer: value});
													this.checkIfAllRequiredAgreementsAreChecked();
												}
											}}
										/>
									</div>
								)
							})}
						</div>
					</div>

					{insurancePolicyResponse && (
						<>
							{!!insurancePolicyResponse.agentInspectionLink && (
								<p className={classNames.summaryText}>
									{L("Agent inspection link")}:&nbsp;&nbsp;
									<a target="_blank" rel="noreferrer" href={insurancePolicyResponse.agentInspectionLink}>
										{insurancePolicyResponse.agentInspectionLink}
									</a>
								</p>
							)}

							{!!insurancePolicyResponse.clientInspectionLink && (
								<p className={classNames.summaryText}>
									{L("Client inspection link")}:&nbsp;&nbsp;
									<a target="_blank" rel="noreferrer" href={insurancePolicyResponse.clientInspectionLink}>
										{insurancePolicyResponse.clientInspectionLink}
									</a>
								</p>
							)}
						</>
					)}

					{orderSent === true &&
						selectedCalculation.insurerName === "Allianz" && 
							(product && product.SeName && product.SeName === 'ubezpieczenie-auta') && 
						(
							<LabeledTextField
								key={"uwAcceptanceCode"}
								required={false}
								label={L("Underwriter acceptance code")}
								// errorMessage={error && error[element.id] ? error[element.id] : ''}
								value={uwAcceptanceCode}
								disabled={false}
								isDataLoaded={true}
								onChange={(e: any) => {
									this.props.onUwAcceptanceCodeChange(e.target && e.target.value ? e.target.value : typeof e === "string" ? e : "");
								}}
							/>
						)
					}

					<div className={classNames.confrimButtonWrapper}>
						{saveCalculationButton}
						{sendCalculationButton}
						{(insurancePolicyResponse && !!insurancePolicyResponse.clientInspectionLink) && sendInspectionLinkButton}
						{finalizePolicyButton}
						{(product && product.SeName && product.SeName === 'ubezpieczenie-auta') && toggleOcTerminationGeneratorDialogButton}
						{downloadPolicyDocumentsButton}
						{sendDocumentsToClientButton}
						{goToInsuranceCompanyButton}

						{asyncActionInProgress && (
							<Spinner
								label={L("Please wait...")}
								className={classNames.loadSpinner}
								size={SpinnerSize.large}
								ariaLive="assertive"
								labelPosition="right"
							/>
						)}
					</div>

					<MessageBar
						messageBarType={summaryMessageBoxData.address.type} isMultiline={false}
						className={`${summaryMessageBoxData.address.hide && "hide"} ${classNames.messageBar}`}
						onDismiss={() => this.props.onMessageBarDismiss("address")}
					>
						{summaryMessageBoxData.address.text}
					</MessageBar>

					<MessageBar
						messageBarType={summaryMessageBoxData.createOrder.type} isMultiline={false}
						className={`${summaryMessageBoxData.createOrder.hide && "hide"} ${classNames.messageBar}`}
						onDismiss={() => this.props.onMessageBarDismiss("createOrder")}
					>
						{summaryMessageBoxData.createOrder.text}
					</MessageBar>

					<MessageBar
						messageBarType={summaryMessageBoxData.policyApplication.type} isMultiline={false}
						className={`${summaryMessageBoxData.policyApplication.hide && "hide"} ${classNames.messageBar}`}
						onDismiss={() =>
							this.props.onMessageBarDismiss("policyApplication")
						}
					>
						{summaryMessageBoxData.policyApplication.text}
					</MessageBar>

					<MessageBar
						messageBarType={summaryMessageBoxData.sendSms.type} isMultiline={false}
						className={`${summaryMessageBoxData.sendSms.hide && "hide"} ${classNames.messageBar}`}
						onDismiss={() => this.props.onMessageBarDismiss("sendSms")}
					>
						{summaryMessageBoxData.sendSms.text}
					</MessageBar>

					<MessageBar
						messageBarType={summaryMessageBoxData.policyFinalization.type} isMultiline={false}
						className={`${summaryMessageBoxData.policyFinalization.hide && "hide"} ${classNames.messageBar}`}
						onDismiss={() =>
							this.props.onMessageBarDismiss("policyFinalization")
						}
					>
						{summaryMessageBoxData.policyFinalization.text}
					</MessageBar>

					<MessageBar
						messageBarType={summaryMessageBoxData.ocTerminationGenerated.type} isMultiline={false}
						className={`${summaryMessageBoxData.ocTerminationGenerated.hide && "hide"} ${classNames.messageBar}`}
						onDismiss={() => this.props.onMessageBarDismiss("ocTerminationGenerated")}
					>
						{summaryMessageBoxData.ocTerminationGenerated.text}
					</MessageBar>

					<MessageBar
						messageBarType={summaryMessageBoxData.sendPolicyMail.type} isMultiline={false}
						className={`${summaryMessageBoxData.sendPolicyMail.hide && "hide"} ${classNames.messageBar}`}
						onDismiss={() => this.props.onMessageBarDismiss("sendPolicyMail")}
					>
						{summaryMessageBoxData.sendPolicyMail.text}
					</MessageBar>
					
					<MessageBar
						messageBarType={summaryMessageBoxData.documentsGeneratedAndDownloaded.type} isMultiline={false}
						className={`${summaryMessageBoxData.documentsGeneratedAndDownloaded.hide && "hide"} ${classNames.messageBar}`}
						onDismiss={() => this.props.onMessageBarDismiss("documentsGeneratedAndDownloaded")}
					>
						{summaryMessageBoxData.documentsGeneratedAndDownloaded.text}
					</MessageBar>
				</Stack>

				<Dialog
					hidden={!this.state.showDialog}
					dialogContentProps={{
						type: DialogType.normal,
						title: L('The offer was recalculated'),
					}}>
					<Label>{L('Due to the marked marketing consents, the price has changed.')}</Label>
					<Label>{L('Old price: ')} {this.props.selectedCalculation.price} PLN</Label>
					<Label>{L('New price: ')} {this.props.insurancePolicyResponse.price} PLN</Label>
					<DialogFooter>
						<PrimaryButton theme={myTheme} text={L('OK')} disabled={false} onClick={this.closeDialog} />
					</DialogFooter>
				</Dialog>
			</>
		);
	}
}
