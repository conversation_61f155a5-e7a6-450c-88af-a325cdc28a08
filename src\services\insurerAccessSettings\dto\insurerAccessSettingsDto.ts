import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";
import { InsurerDto } from "../../insurer/dto/insurerDto";
import { UserDto } from "../../user/dto/userDto";
import { InsurerAccessLoginTypeStringEnum } from "./insurerAccessLoginTypeStringEnums";

export interface InsurerAccessSettingsDto extends BaseApiEntityModel {
    insurerId: number,
    insurer: InsurerDto,
    userId: number,
    user: UserDto,
    loginType: InsurerAccessLoginTypeStringEnum,
    login: string,
    password: string,
    certificatePath: string,
    ofwcaKnfNumber: string,
    agentRef: string,
    branchCode: string,
    token: string,
}