import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { CrudServiceBase } from '../base/crudServiceBase';
import { PolicyCalculationAttachedFilesDto } from './policyCalculationAttachedFilesDto';
import { isUserLoggedIn } from '../../utils/authUtils';

export class PolicyCalculationAttachedFilesService extends CrudServiceBase<PolicyCalculationAttachedFilesDto> {
    constructor() {
        super(Endpoint.PolicyCalculationAttachedFile);
        this.internalHttp = httpApi;
    }

    public async createNew(createPolicyCalculationAttachedFileInput: PolicyCalculationAttachedFilesDto) {
        isUserLoggedIn();
        const copyCreatePolicyCalculationAttachedFileInput = {...createPolicyCalculationAttachedFileInput, id: parseInt(createPolicyCalculationAttachedFileInput.id)};
        let result = await httpApi.post(this.endpoint.Create(), copyCreatePolicyCalculationAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getAllFiles() {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.GetAll());
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByCalculationId(calculationId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetByCalculationId?calculationId=${calculationId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async sendApkAndCalculations(requestBody: any) {
        isUserLoggedIn();
        let result = await this.internalHttp.post(this.endpoint.Custom('SendApkAndCalculations', false), requestBody);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async SendPasswordBySms(uuid: string) {
        let result = await this.internalHttp.post(this.endpoint.Custom(`SendPasswordBySms/${uuid}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getPassword(uuid: string) {
        isUserLoggedIn();
        let result = await this.internalHttp.get(this.endpoint.Custom(`GetPassword/${uuid}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getCalculationDetailsFile(requestBody: any) {
        isUserLoggedIn();
        let result = await this.internalHttp.post(this.endpoint.Custom('GetCalculationDetailsFile', false), requestBody);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportPolicyCalculationAttachedFilesService: PolicyCalculationAttachedFilesService = new PolicyCalculationAttachedFilesService();
export default exportPolicyCalculationAttachedFilesService;