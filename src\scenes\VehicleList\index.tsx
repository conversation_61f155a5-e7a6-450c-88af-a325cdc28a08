import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import { VehicleTable } from './components/vehicleTable';
import VehicleStore from '../../stores/vehicleStore';
import ClientStore from '../../stores/clientStore';
import {mergeStyleSets} from "@fluentui/react";
import {L} from "../../lib/abpUtility";
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
});

export interface IProps {
  searchStore: SearchStore;
  vehicleStore: VehicleStore;
  clientStore: ClientStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.VehicleStore)
@inject(Stores.ClientStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  public render() {
    let items = this.props.vehicleStore.dataSet ? this.props.vehicleStore.dataSet.items : [];
    
    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Vehicle List')}</h2>
        </div>
        <VehicleTable
          searchText={this.props.searchStore.searchText}
          items={items}
          store={this.props.vehicleStore}
          history={this.props.history}
        />
      </>
    );
  }
}

export default Index;