import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import SearchStore from '../../stores/searchStore';
import { ApkTable } from './components/apkTable';
import ApkAttachedFilesStore from '../../stores/apkAttachedFilesStore';
import { CrudConsts } from '../../stores/crudStoreBase';
import { FocusZone, FocusZoneDirection, FocusZoneTabbableElements, IDropdownOption, mergeStyleSets } from '@fluentui/react';
import { DropdownBase } from '../BaseComponents/dropdownBase';
import { L } from '../../lib/abpUtility';
import { enumToDropdownOptions } from '../../utils/utils';
import { ApkAttachedFileStatus } from '../../services/apkAttachedFiles/apkAttachedFilesStatusEnums';
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
})

export interface IProps {
  searchStore: SearchStore;
  apkAttachedFilesStore: ApkAttachedFilesStore;
  history: any;
}

export interface IState {
  filterByStatus: string;
  filterByStatusOptions: any[];
  customRequest: any;
}

@inject(Stores.SearchStore)
@inject(Stores.ApkAttachedFilesStore)
@observer
class Index extends AppComponentBase<IProps, IState> {
  formRef: any;

  constructor(props: IProps) {
    super(props);

    this.state = {
      ...this.state,
      filterByStatus: "ALL",
      filterByStatusOptions: [],
      customRequest: {...this.props.apkAttachedFilesStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '', maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE},
    };
  }

  // private sortItems(a: any, b: any): number {
  //   if (a.name < b.name)
  //     return -1;
  //   if (a.name > b.name)
  //     return 1;
  //   return 0;
  // }

  private async refreshItems() {
    setTimeout(async () => {
      await this.props.apkAttachedFilesStore.getAll({...this.props.apkAttachedFilesStore.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE}).then(() => {
        this.props.searchStore.setText(' ');
        setTimeout(() => {
          this.props.searchStore.setText('');
        }, 400);
      });
    }, 200);
  }

  private async setCustomRequest(newFilterByStatus: string | undefined) {
    const requestPayload: any = {...this.props.apkAttachedFilesStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '',
    maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE};

    if((typeof newFilterByStatus === 'string' && newFilterByStatus.length > 0 && newFilterByStatus !== "ALL")) {
      requestPayload['status'] = newFilterByStatus;
    } else if((typeof newFilterByStatus === 'undefined' && typeof this.state.filterByStatus === 'string' && 
      this.state.filterByStatus.length > 0 && this.state.filterByStatus !== 'ALL')
    ) {
      requestPayload['status'] = this.state.filterByStatus;
    }

    this.setState((prevState) => ({...prevState, customRequest: requestPayload, 
                                      filterByStatus: typeof newFilterByStatus === 'string' ? newFilterByStatus : this.state.filterByStatus,
                                  }));
  }

  public render() {
    let items: any[] = this.props.apkAttachedFilesStore.dataSet ? this.props.apkAttachedFilesStore.dataSet.items : [];

    if(this.state.filterByStatusOptions.length === 0) {
      let filterByStatusDropdownOptions: IDropdownOption[] = [
        { key: "ALL", text: L('All2') },
        ...enumToDropdownOptions(ApkAttachedFileStatus, false, true, "string")
      ];

      this.setState((prevState) => ({...prevState, filterByStatusOptions: filterByStatusDropdownOptions }));
    }

    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('APK List')}</h2>
        </div>
        <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} style={{display: 'flex', flexDirection: 'row', flexWrap: 'nowrap'}}>
          <DropdownBase key={'filterByStatusDropdown'} required={false} label={L('Filter by status')} options={this.state.filterByStatusOptions}
            value={this.state.filterByStatus} disabled={false} isDataLoaded={true} customLabelStyles={{ width: "350px", minWidth: "350px", whiteSpace: 'nowrap', marginRight: '30px'}}
            customDropdownWidth={'300px'}
            onChange={(e: string | number | undefined) => {
              if(e && e !== this.state.filterByStatus) {
                this.setCustomRequest(e.toString());
              }
            }}
          />
        </FocusZone>

        <ApkTable
          searchText={this.props.searchStore.searchText}
          items={items}
          store={this.props.apkAttachedFilesStore}
          history={this.props.history}
          refreshItems={() => this.refreshItems()}
          scrollablePanelMarginTop={220}
          customData={{
            customRequest: this.state.customRequest,
          }}
        />
      </>
    );
  }
}

export default Index;