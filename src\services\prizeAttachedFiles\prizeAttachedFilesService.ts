import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { CrudServiceBase } from '../base/crudServiceBase';
import { PrizeAttachedFilesDto } from './prizeAttachedFilesDto';
import { isUserLoggedIn } from '../../utils/authUtils';
import { getPartialModel } from '../../utils/modelUtils';

export class PrizeAttachedFilesService extends CrudServiceBase<PrizeAttachedFilesDto> {
    constructor() {
        super(Endpoint.PrizeAttachedFiles);
        this.internalHttp = httpApi;
    }

    public async createNew(createPrizeAttachedFileInput: PrizeAttachedFilesDto) {
        isUserLoggedIn();
        const copyCreatePrizeAttachedFileInput = getPartialModel(createPrizeAttachedFileInput, [], ['id', 'prize']);
        let result = await httpApi.post(this.endpoint.Create(), copyCreatePrizeAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getAllFiles() {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.GetAll());
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getByPrizeId(prizeId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetByPrizeId?prizeId=${prizeId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportPrizeAttachedFilesService: PrizeAttachedFilesService = new PrizeAttachedFilesService();
export default exportPrizeAttachedFilesService;