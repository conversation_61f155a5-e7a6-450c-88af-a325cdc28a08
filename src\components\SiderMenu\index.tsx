import './index.less';
import * as React from 'react';
import { L, isGranted } from '../../lib/abpUtility';
import { appRouters } from '../../components/Router/router.config';
import { Nav } from'@fluentui/react';
import { mergeStyleSets} from '@fluentui/merge-styles';
import { myTheme } from '../../styles/theme';
import { mapPathToIcon } from '../../utils/utils';
import { hasPermissionsToPage } from '../../utils/authUtils';

const classNames = mergeStyleSets({
  sidebar: {
    boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
    borderRadius: '12px',
    position: 'relative',
    width: 280,
    minWidth: 280,
    maxWidth: 280,
    flex: '0 0 280px',
    transition: 'all .2s ease-out',
    backgroundColor: myTheme.palette.white,
    height: 'calc(100% - 20px)',
    maxHeight: 'calc(100% - 20px)',
    overflowY: 'auto',
  },
  collapsed: {
    width: 65,
    minWidth: 65,
    maxWidth: 65,
    flex: '0 0 65px',
    backgroundColor: myTheme.palette.neutralQuaternary,
  },
  sidebarNav: {
    root: {
      width: '100%',
      height: 'auto',
      boxSizing: 'border-box',
      overflowY: 'hidden',
      marginTop: 10,
      marginBottom: 10,
    },
    selectors: {
      '& .ms-Button-flexContainer': {
        margin: 2,
      },
      '& a': {
        color: myTheme.palette.black,
        fontWeight: 'normal',
        transition: '.2s',
        backgroundColor: 'transparent',
      },
      '& a:hover': {
        color: myTheme.palette.red,
        fontWeight: 'bold',
        backgroundColor: 'transparent',
        '& .ms-icon, .ms-Button-icon': {
          color: myTheme.palette.red
        },
      },
      '& a:focus': {
        color: myTheme.palette.red,
        fontWeight: 'bold',
        backgroundColor: 'transparent',
        '& .ms-icon, .ms-Button-icon': {
          color: myTheme.palette.red,
        },
      },
      '& div.is-selected a': {
        color: myTheme.palette.red,
        fontWeight: 'bold',
        backgroundColor: 'transparent',
        '& .ms-icon, .ms-Button-icon': {
          color: myTheme.palette.red
        },
      },
      '& a:active': {
        color: myTheme.palette.red,
        fontWeight: 'bold',
        backgroundColor: 'transparent',
        '& .ms-icon, .ms-Button-icon': {
          color: myTheme.palette.red
        },
      },
      '& .ms-Nav-link:after': {
        borderLeft: 0,
        backgroundColor: 'transparent',
        '& .ms-icon, .ms-Button-icon': {
          color: myTheme.palette.red
        },
      },
      '.ms-Nav-link:after span .ms-Nav-linkText': {
        color: myTheme.palette.white
      },
      '& .ms-icon, .ms-Button-icon': {
        color: myTheme.palette.black
      },
      '& .ms-Nav-navItems': {
        padding: '30px',
      },
    },
  },
  links: {},
});

export interface ISiderMenuProps {
  path: any;
  collapsed: boolean;
  toggle: any;
  history: any;
}

class SiderMenu extends React.Component<ISiderMenuProps, {}> {
  handleNavClick = (event: KeyboardEvent, item: any) => {
    event.preventDefault();
    this.props.history.push(item.url);
  };

  render() {
    const { collapsed, history } = this.props;
    let links: any[] = appRouters
      .filter((item: any) => !item.isLayout && item.showInMenu)
      .map((route: any, index: number) => {
        if ((route.permission && !isGranted(route.permission)) || !hasPermissionsToPage(route.path)) {
          return null;
        } else {
          return {
            name: `${L(route.title)}`,
            url: route.path,
            key: route.path,
            onClick: this.handleNavClick,
            className: classNames.links,
            icon: mapPathToIcon(route.path),
          };
        }
      });

    links = links.filter((x) => x);

    return (
      <div style={{ margin: '0 60px 0 0'}}>
        <div className={`${classNames.sidebar} ${collapsed ? classNames.collapsed : ''}`}>
          <Nav
            selectedKey={history.location.pathname}
            className={classNames.sidebarNav}
            groups={[
              {
                links: links,
              },
            ]}
            theme={myTheme}
          />
        </div>
      </div>
    );
  }
}

export default SiderMenu;