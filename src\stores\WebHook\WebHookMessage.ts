// THIS IS EXAMPLE OF TYPES
export enum WebHookType {
  UserText = "USER_TEXT",
  Notification = "NOTIFICATION",
}

interface UserPayload {
  user: string;
  message: string;
  color?: string;
}

export interface UserTextMessage {
  type: WebHookType.UserText;
  payload: UserPayload;
}

interface UserPayload {
  user: string;
  message: string;
  color?: string;
}

export interface NotificationMessage {
  type: WebHookType.Notification;
  payload: string;
}

export type WebHookMessage = NotificationMessage | UserTextMessage;
