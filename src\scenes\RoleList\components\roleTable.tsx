import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { RoleDto } from '../../../services/role/dto/roleDto';
import { ICrudPermissons } from '../../BaseComponents/commandBarBase';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { RolePanel } from './rolePanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { Link } from "@fluentui/react";
import { RouterPath } from "../../../components/Router/router.config";

export class RoleTable extends FluentTableBase<RoleDto> {

  getColumns(): ITableColumn[] {
    return RoleTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Name'),
        fieldName: 'name',
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Role}/${item.id}`);
                      }} 
                        href={`/${RouterPath.Role}/${item.id}`}>
                  {item.name}
                </Link>
        }
      },
      {
        name: L('Display name'),
        fieldName: 'displayName',
        onRender: (item: any): any => {
          return L(item.displayName); 
        }
      },
      {
        name: L('Normalized name'),
        fieldName: 'normalizedName',
      },
      {
        name: L('Description'),
        fieldName: 'description',
      }
    ];
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: true
    };
  }

  getTitle(): string {
    return L('Roles');
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <RolePanel
      {...props}
    />
  }
}