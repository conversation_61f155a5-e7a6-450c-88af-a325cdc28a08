image: atlassian/default-image:3

clone:
  depth: full

pipelines:
  branches:
    master-PROD:
      - step:
          name: Sync
          script:          
            - mkdir -p ~/.ssh
            - (umask 077 && echo "$DEPLOY_KEY" | base64 --decode > ~/.ssh/deploy_key)
            - chmod 600 ~/.ssh/deploy_key       
            - git config --global user.email "<EMAIL>"
            - git config --global user.name "Sync"
            - git clone --single-branch --branch master-PROD *****************:a-softy/top-abp-frontend.git
            - cd top-abp-frontend
            - git remote <NAME_EMAIL>:grupapsj/top-abp-frontend.git
            - git reset $(git commit-tree "HEAD^{tree}" -m "Synchronize")          
            - GIT_SSH_COMMAND="ssh -i ~/.ssh/deploy_key" git push --force target master-PROD:main
    master-DEMO:
      - step:
          name: Sync
          script:          
            - mkdir -p ~/.ssh
            - (umask 077 && echo "$DEPLOY_KEY" | base64 --decode > ~/.ssh/deploy_key)
            - chmod 600 ~/.ssh/deploy_key       
            - git config --global user.email "<EMAIL>"
            - git config --global user.name "Sync"
            - git clone --single-branch --branch master-DEMO *****************:a-softy/top-abp-frontend.git
            - cd top-abp-frontend
            - git remote <NAME_EMAIL>:grupapsj/top-abp-frontend.git
            - git reset $(git commit-tree "HEAD^{tree}" -m "Synchronize")          
            - GIT_SSH_COMMAND="ssh -i ~/.ssh/deploy_key" git push --force target master-DEMO:main-uat