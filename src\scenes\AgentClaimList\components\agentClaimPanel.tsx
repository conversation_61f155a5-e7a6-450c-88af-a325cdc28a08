import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { ClaimDto } from '../../../services/claim/dto/claimDto';
import { AgentClaimContentView } from '../../AgentClaim/components/agentClaimContentView';
import { PrimaryButton } from '@fluentui/react';
import { myTheme } from '../../../styles/theme';

export class AgentClaimPanel extends GenericPanel {
    private disableConfirmButton: boolean = true;

    getPanelTitle(): string {
        return L("Agent claim");
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} onClick={this._onConfirm} text={L('Save')} disabled={this.asyncActionInProgress || this.disableConfirmButton} />
    };

    renderContent() {
        return <AgentClaimContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ClaimDto } toggleConfirm={(show: boolean) => {
            this.disableConfirmButton = !show;
            this.forceUpdate();
        }} />;
    }
}