import { IDropdownOption, IChoiceGroupOption, Pivot, PivotItem, Text, FontWeights, mergeStyleSets, Modal, IconButton, IButtonStyles, IIconProps, Icon, Stack, Selection, SelectionMode } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { ProductDto } from '../../../services/product/productDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { ProductType } from '../../../services/product/productTypeEnums';
import { dateFormat, enumToDropdownOptions, filterBySome, isJsonString } from '../../../utils/utils';
import { defaultProduct } from '../../../stores/productStore';
import Stores from '../../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import { getGrandNodeLanguage, getLocaleName } from '../../../utils/languageUtils';
import { findHiddenAttributes, generateDropdownOptionsIfCountriesInput } from '../../../utils/storeUtils';
import { getDropdownOptionsFromDataSource, isCountriesInput } from '../../../utils/inputUtils';
import userService from '../../../services/user/userService';
import { AdminJsonBuilderTool } from '../../BaseComponents/AdminJsonBuilderTool';
import { SportsProductFluentListBaseWithCommandBar } from '../../BaseComponents/sportsProductFluentListBaseWithCommandBar';
import sportInsuranceCoverageService from '../../../services/sportInsuranceCoverage/sportInsuranceCoverageService';
import { PagedResultRequestDto } from '../../../services/base/pagedResultRequestDto';
import { TravelCountryProductFluentListBaseWithCommandBar } from '../../BaseComponents/travelCountryProductFluentListBaseWithCommandBar';
import { AdminVariantsTableEditTool } from '../../BaseComponents/AdminVariantsTableEditTool';
import { InsuranceCompanyNewOffer } from '../../../services/calculation/dto/insuranceCompanyNewOfferEnum';
import { DropdownBase } from '../../BaseComponents/dropdownBase';

declare var abp: any;

const developersRoleToCheck: string = "GN-ADMIN";
const cancelIcon: IIconProps = { iconName: 'Cancel' };

const iconButtonStyles: Partial<IButtonStyles> = {
    root: {
        color: myTheme.palette.neutralPrimary,
        marginLeft: 'auto',
        marginTop: '4px',
        marginRight: '2px',
    },
    rootHovered: {
        color: myTheme.palette.neutralDark,
    },
};

const classNames = mergeStyleSets({
    adminToolButton: {
        background: myTheme.palette.orange,
        marginTop: '25px',
        marginBottom: '10px',
        transition: 'all 120ms',
        selectors: {
            ':hover': {
                background: myTheme.palette.orangeLighter,
            }
        }
    },
    adminToolIcon: {
        background: myTheme.palette.orange,
        color: myTheme.palette.white,
        borderRadius: '50%',
        width: '36px',
        height: '36px',
        marginTop: '17px',
        display: 'flex',
        alignContent: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        boxSizing: 'border-box',
        fontSize: '21px',
        marginLeft: '20px !important',
        cursor: 'pointer',
        transition: 'all 100ms',
        selectors: {
            ':hover': {
                background: myTheme.palette.orangeLighter,
            }
        }
    },
    adminToolIconAbsolute: {
        position: 'absolute',
        left: 250,
        top: 17,
    },
    container: {
        display: 'flex',
        flexFlow: 'column nowrap',
        alignItems: 'stretch',
        justifyContent: 'center',
    },
    header: [
        myTheme.fonts.large,
        {
            flex: '1 1 auto',
            color: myTheme.palette.neutralPrimary,
            display: 'flex',
            alignItems: 'center',
            fontWeight: FontWeights.semibold,
            padding: '12px 12px 14px 24px',
        },
    ],
    contentContainer: {
        display: 'block',
        width: '100%',
        height: '500px',
        maxHeight: '500px',
        position: 'relative',
        marginTop: '20px',
    },

});

@inject(Stores.LanguageStore)
@inject(Stores.ProductAttributeStore)
@inject(Stores.InsurerStore)
@inject(Stores.SportInsuranceCoverageStore)
@inject(Stores.SportDisciplineStore)
@inject(Stores.TravelCountryCoverageStore)
@inject(Stores.TravelCountryStore)
export class ProductContentView extends GenericContentView {
    private product: ProductDto = defaultProduct;
    private attributes: any = [];
    private attributesWithVariants: any = [];
    private selectedAttribute: any = null;
    private gnLanguage: any = {};
    private userRoles: string[] = [];
    private isModalOpen: boolean = false;
    private insuranceId: number = 0;
    private insuranceName: string = '';
    private _insuranceListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedInsuranceContest: any = this._insuranceListSelection.getSelection();
            if(Array.isArray(selectedInsuranceContest) && selectedInsuranceContest.length > 0 && !!selectedInsuranceContest[0].id) {
                this.insuranceId = selectedInsuranceContest[0].id;
                this.insuranceName = selectedInsuranceContest[0].fullName
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private isVariantsTableEditToolModalOpen: boolean = false;
    private variantsTableData: any = {};
    private selectedInsurerName: string = '';

    async componentDidMount() {
        await this.props.languageStore?.getAll(this.props.languageStore?.defaultRequest);
        this.gnLanguage = getGrandNodeLanguage(this.props.languageStore?.dataSet);

        if(!this.props.productAttributeStore!.dataSet || this.props.productAttributeStore!.dataSet.totalCount <= 0) {
			await this.props.productAttributeStore!.getAll(this.props.productAttributeStore!.defaultRequest);
		}

        if(abp && abp.session && abp.session.userId && abp.session.userId > 0) {
            let user = await userService.get({id: abp.session.userId});
            if(user && user.roleNames) {
                this.userRoles = [...user.roleNames];
            }
        }

        this.checkIfDataIsLoaded("product");

		await this.props.insurerStore?.getInsurersByPolicyType("Travel");
        this.forceUpdate();
    }

    private setAttribute(attr: any, withWariants?: boolean) {
        // if(attr.DisplayOrder < 0) { // attr hidden
        //     return false;
        // }

        if((!!this.variantsTableData[attr.Id] && withWariants !== true) || (!this.variantsTableData[attr.Id] && withWariants === true)) {
            return false;
        }

        let control: any;
        let options: any = {
            dropdown: [] as IDropdownOption[],
            choicegroup: [] as IChoiceGroupOption[]
        };
        let textFieldRows = 1;

        switch(attr.AttributeControlTypeId) {
            case "Datepicker":
                control = Controls.Date;
                break;
            case "TextBox":
                control = Controls.Text;
                break;
            case "MultilineTextbox":
                control = Controls.Text;
                textFieldRows = 5;
                break;
            case "RadioList":
                control = Controls.ChoiceGroup;
                options.choicegroup = attr.ProductAttributeValues.map((attrValue: any) => { 
                    return { key: attrValue.Id,
                        text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, this.gnLanguage) : attrValue.Name, 
                            disabled: false } 
                }) as IChoiceGroupOption[];
                break;
            case "Checkboxes":
                control = Controls.CheckBoxOptions;
                options.dropdown = attr.ProductAttributeValues.map((attrValue: any) => { 
                    return { key: attrValue.Id, 
                        text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, this.gnLanguage) : attrValue.Name, 
                            isSelected: attrValue.IsPreSelected };
                }) as IDropdownOption[];
                break;
            case "DropdownList":
                control = Controls.Picker;
                options.dropdown = attr.ProductAttributeValues.map((attrValue: any) => {
                    return { key: attrValue.Id, 
                            text: attrValue.Locales && attrValue.Locales.length > 0 ? getLocaleName(attrValue.Locales, this.gnLanguage) : attrValue.Name, 
                            isSelected: attrValue.IsPreSelected, 
                            customDropdownWidth: '300px' };
                }) as IDropdownOption[];
                break;
        }

        let attrName: string = " ";
        let userFields: any;
        if(this.props.productAttributeStore!.dataSet && this.props.productAttributeStore!.dataSet.items) {
            this.props.productAttributeStore!.dataSet.items.some((attribute: any) => {
                if(attr.ProductAttributeId === attribute.Id) {
                    if(attribute.UserFields) {
                        userFields = attribute.UserFields;

                        if(!this.variantsTableData[attr.Id] && this.variantsTableData[attr.Id] !== null) {
                            let variantsTableUserField: any;
                            
                            variantsTableUserField = filterBySome(userFields, "Key", "olo_clever_variants_table");
                            if(!variantsTableUserField) {
                                variantsTableUserField = filterBySome(userFields, "Key", "variants_table");
                            }

                            if(variantsTableUserField) {
                                this.variantsTableData[attr.Id] = isJsonString(variantsTableUserField.Value) ? JSON.parse(variantsTableUserField.Value) : null;
                            }
                        }
                    }

                    if(attribute.Locales && getLocaleName(attribute.Locales, this.gnLanguage).length > 0) {
                        attrName = `${attribute.Name} (${getLocaleName(attribute.Locales, this.gnLanguage)})`;
                    } else if(attribute.Name.length > 0) {
                        attrName = attribute.Name;
                    }

                    if(attr.AttributeControlTypeId === "DropdownList") {
                        let dropdownOptions: IDropdownOption[] = [];
                        if(isCountriesInput(attr)) {
                            dropdownOptions = getDropdownOptionsFromDataSource(attribute, this.props, "", this.gnLanguage);
                        } else {
                            dropdownOptions = getDropdownOptionsFromDataSource(attribute, this.props, "Name", this.gnLanguage);
                        }

                        if(dropdownOptions && dropdownOptions.length > 0) {
                            options.dropdown = generateDropdownOptionsIfCountriesInput(attr, dropdownOptions);
                        }
                    }

                    return true;
                }
                return false;
            });
        }
        
        const property = new ContentViewModelProperty(attr.Id, attrName, control, attr.IsRequired, options, false, {isDataLoaded: this.isDataLoaded, rows: textFieldRows});

        if(withWariants && !attrName.includes(this.selectedInsurerName)) {
            return;
        }

        return (this.userRoles.includes(developersRoleToCheck) || this.userRoles.includes("ADMIN")) ?
            <Stack key={`stack-${attr.Id}`} horizontal={control !== Controls.CheckBoxOptions ? true : false} style={{position: 'relative'}}>
                { this.renderElement(property, [], {}) }

                { this.userRoles.includes(developersRoleToCheck) &&
                    <Icon iconName='BuildDefinition' className={`${classNames.adminToolIcon} ${control === Controls.CheckBoxOptions && classNames.adminToolIconAbsolute}`}
                        title={L('Open JSON property / validator builder tool')}
                        onClick={() => { this.selectedAttribute = {...attr, attrName, userFields}; this.isModalOpen = true; this.forceUpdate(); }} />
                } 

                {!!this.variantsTableData[attr.Id] &&
                    <Icon iconName='ColumnOptions' className={`${classNames.adminToolIcon} ${control === Controls.CheckBoxOptions && classNames.adminToolIconAbsolute}`}
                        title={L('Open variants table edit tool')}
                        onClick={() => { this.selectedAttribute = {...attr, attrName, userFields}; this.isVariantsTableEditToolModalOpen = true; this.forceUpdate(); }} /> 
                }
            </Stack>
            :
            this.renderElement(property, [], {});
    }

    private fetchSportsByInsurerId = async () => {
        await this.props.sportInsuranceCoverageStore?.getByInsurerId(this.insuranceId.toString());
        this.forceUpdate();
    }
    
    private fetchTravelCountryByInsurerId = async () => {
        await this.props.travelCountryCoverageStore?.getByInsurerId(this.insuranceId.toString());
        this.forceUpdate();
    }

    private fetchAllSports = async () => {
        const request: PagedResultRequestDto = { keyword: "", sorting: "Name", maxResultCount: 25, skipCount: 0 };
        await this.props.sportDisciplineStore?.getAll(request);
        this.forceUpdate();
    }

    private fetchAllTravelCountry = async () => {
        const request: PagedResultRequestDto = { keyword: "", sorting: "Name", maxResultCount: 25, skipCount: 0 };
        await this.props.travelCountryStore?.getAll(request);
        this.forceUpdate();
    }

    private getSportCoverageExcelByInsurerId = async () => {
        await sportInsuranceCoverageService.getSportCoverageExcelByInsurerId(this.insuranceId.toString()).then(async (response: any) => {
            if(response) {
                const url = response;
                const link = document.createElement('a');
                link.href = url;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        })
    }  

    renderContent() {
        this.product = this.props.payload.model ? this.props.payload.model : this.props.payload;

        const foundHiddenAttributes = findHiddenAttributes(this.product.ProductAttributeMappings);
        
        this.attributes = (this.product && this.product.ProductAttributeMappings && foundHiddenAttributes.itemsCountWithoutHidden > 0 ? this.product.ProductAttributeMappings!.map((attr: any) => {
            return this.setAttribute(attr);
        }) : []);

        this.attributesWithVariants = (this.product && this.product.ProductAttributeMappings && foundHiddenAttributes.itemsCountWithoutHidden > 0 ? this.product.ProductAttributeMappings!.map((attr: any) => {
            return this.setAttribute(attr, true);
        }) : []);
        
        this.attributes = this.attributes.filter((x: any) => x !== false);
        this.attributesWithVariants = this.attributesWithVariants.filter((x: any) => x !== false);

        const pivotItemAttributes =
            <PivotItem className={this.attributes.length <= 0 ? 'hide' : ''} headerText={L('Attributes')} key={'Attributes'}>
                <Text block styles={{ root: { marginTop: '15px' }}}>{L('Attribute list for preview only.')}</Text>
                {this.attributes}
            </PivotItem>;
        
        const pivotItemOnepagers =
            <PivotItem className={this.attributes.length <= 0 ? 'hide' : ''} headerText={L('Onepagers')} key={'Onepagers'}>
                <DropdownBase key={'insurerName'} label={L("Insurance company")} options={ enumToDropdownOptions(InsuranceCompanyNewOffer, true, true, "string")}
                    value={this.selectedInsurerName} disabled={false} isDataLoaded={true} required={true} customDropdownWidth={'300px'} customLabelStyles={{ width: "200px", minWidth: "200px" }}
                    onChange={(value: string | number | undefined) => {
                        if(value && typeof value === 'string') {
                            this.selectedInsurerName = value;
                            this.forceUpdate();
                        }
                    }}
                />

                <Text block styles={{ root: { marginTop: '15px' }}}>{L('Attribute list for preview only.')}</Text>
                {this.attributesWithVariants}
            </PivotItem>;

        const sportsPivotItem =
            <PivotItem className={this.attributes.length <= 0 ? 'hide' : ''} headerText={L('Sports')} key={'Sports'}>
                <Stack>
                    <div className={classNames.contentContainer} >
                        <SportsProductFluentListBaseWithCommandBar
                            store={this.props.insurerStore!}
                            items={
                                this.props.insurerStore?.dataSet && this.props.insurerStore?.dataSet.items
                                    ? this.props.insurerStore?.dataSet.items
                                    : []
                            }
                            customSelection={this._insuranceListSelection}
                            searchText={''}
                            history={this.props.history}
                            customData={{
                                fetchSportsByInsurerId: this.fetchSportsByInsurerId,
                                fetchAllSports: this.fetchAllSports,
                                getSportCoverageExcelByInsurerId: this.getSportCoverageExcelByInsurerId,
                                disableGetAllOnMount: true,
                                useOnlyRefreshItems: true,
                                insurerId: this.insuranceId,
                                insuranceName: this.insuranceName,
                            }}
                            scrollablePanelMarginTop={120}
                        />
                    </div>
                </Stack>
            </PivotItem>;

        const travelPivotItem =
            <PivotItem className={this.attributes.length <= 0 ? 'hide' : ''} headerText={L('Country list')} key={'CountryList'}>
                <Stack>
                    <div className={classNames.contentContainer} >
                        <TravelCountryProductFluentListBaseWithCommandBar
                            store={this.props.insurerStore!}
                            items={
                                this.props.insurerStore?.dataSet && this.props.insurerStore?.dataSet.items
                                    ? this.props.insurerStore?.dataSet.items
                                    : []
                            }
                            customSelection={this._insuranceListSelection}
                            searchText={''}
                            history={this.props.history}
                            customData={{
                                fetchTravelCountryByInsurerId: this.fetchTravelCountryByInsurerId,
                                fetchAllTravelCountry: this.fetchAllTravelCountry,
                                getSportCoverageExcelByInsurerId: this.getSportCoverageExcelByInsurerId,
                                disableGetAllOnMount: true,
                                useOnlyRefreshItems: true,
                                insurerId: this.insuranceId,
                                insuranceName: this.insuranceName
                            }}
                            scrollablePanelMarginTop={120}
                        />
                    </div>
                </Stack>
            </PivotItem>;
        
        const productTypeOptions: any = {
            dropdown: enumToDropdownOptions(ProductType),
            choicegroup: [] as IChoiceGroupOption[]
        };
        
        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed
                    }
                }
            }
        };

        return <>
            { this.userRoles.includes(developersRoleToCheck) && <Modal
                titleAriaId={'adminJsonBuilderToolModal'}
                isOpen={this.isModalOpen}
                onDismiss={() => { this.isModalOpen = false; this.forceUpdate(); }}
                isBlocking={true}
                containerClassName={classNames.container}
            >
                <div className={classNames.header}>
                    {/* <span id={'selectCustomerModal'}>{L("Select customer:")}</span> */}
                    <IconButton
                        styles={iconButtonStyles}
                        iconProps={cancelIcon}
                        ariaLabel={L("Close popup modal")}
                        onClick={() => { this.isModalOpen = false; this.forceUpdate(); }}
                    />
                </div>

                <AdminJsonBuilderTool activeAttribute={this.selectedAttribute} />
            </Modal> }

            { (this.userRoles.includes(developersRoleToCheck) || this.userRoles.includes("ADMIN")) && <Modal
                titleAriaId={'adminVariantsTableEditToolModal'}
                isOpen={this.isVariantsTableEditToolModalOpen}
                onDismiss={() => { this.isVariantsTableEditToolModalOpen = false; this.forceUpdate(); }}
                isBlocking={true}
                containerClassName={classNames.container}
            >
                <div className={classNames.header}>
                    <IconButton
                        styles={iconButtonStyles}
                        iconProps={cancelIcon}
                        ariaLabel={L("Close popup modal")}
                        onClick={() => { this.isVariantsTableEditToolModalOpen = false; this.forceUpdate(); }}
                    />
                </div>

                <AdminVariantsTableEditTool activeAttribute={this.selectedAttribute} gnLanguage={this.gnLanguage}
                    variantsTableData={this.selectedAttribute && !!this.selectedAttribute.Id && this.variantsTableData && this.variantsTableData[this.selectedAttribute.Id] ? 
                                        this.variantsTableData[this.selectedAttribute.Id] : null} 
                />
            </Modal> }

            <Pivot theme={myTheme} styles={pivotStyles}>
                <PivotItem headerText={L('General')} key={'General'}>
                    {this.renderElement(new ContentViewModelProperty('Name', "Name", Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'Name': this.product.Name})}
                    {this.renderElement(new ContentViewModelProperty('ShortDescription', "Short description", Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'ShortDescription': this.product.ShortDescription})}
                    {this.renderElement(new ContentViewModelProperty('FullDescription', "Full description", Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'FullDescription': this.product.FullDescription})}
                    {this.renderElement(new ContentViewModelProperty('ProductTypeId', "Type", Controls.Picker, true, productTypeOptions, false, {isDataLoaded: this.isDataLoaded}), [], {'ProductTypeId': this.product.ProductTypeId})}
                    {this.renderElement(new ContentViewModelProperty('Published', "Published", Controls.CheckBox, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'Published': this.product.Published})}
                    {this.product.CreatedOnUtc && this.product.CreatedOnUtc.length > 0 ? 
                        <Text styles={{ root: { fontWeight: FontWeights.bold, marginTop: '15px' }}} variant="medium" block>
                            {L('Created on')}: <Text styles={{ root: { fontWeight: FontWeights.regular }}}>{dateFormat(this.product.CreatedOnUtc)}</Text>
                        </Text> : ''}
                    {this.product.UpdatedOnUtc && this.product.UpdatedOnUtc.length > 0 ? 
                        <Text styles={{ root: { fontWeight: FontWeights.bold, marginTop: '15px' }}} variant="medium" block>
                            {L('Updated on')}: <Text styles={{ root: { fontWeight: FontWeights.regular }}}>{dateFormat(this.product.UpdatedOnUtc)}</Text>
                        </Text> : ''}
                </PivotItem>

                {this.attributes.length > 0 && this.userRoles.includes(developersRoleToCheck) ? pivotItemAttributes : ''}
                {this.attributesWithVariants.length > 0 ? pivotItemOnepagers : ''}
                {this.product.SeName === "podróż" ? sportsPivotItem : ''}
                {this.product.SeName === "podróż" ? travelPivotItem : ''}
            </Pivot>
        </>
    }
}