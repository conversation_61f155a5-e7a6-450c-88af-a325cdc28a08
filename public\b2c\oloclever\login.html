<!DOCTYPE html>
<html lang="en">
    <head>
        <title></title>

        <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta charset="utf-8" />
        <meta name="locale" content="en-US" />
        <meta name="ROBOTS" content="NONE, NOARCHIVE" />
        <meta name="GOOGLEBOT" content="NOARCHIVE" />
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=yes"
        />
        <style>
            @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");
            :root {
                --black: #000;
                --white: #fff;
                --blue: #097ffc;
                --blue-border: #097ffb;
                --gray: #e7eaf0;
                --color-google: #eb4335;
                --padding-top: 25px;
            }
            *,
            *:after,
            *:before {
                box-sizing: border-box;
            }
            html {
                width: 100%;
                height: 100%;
            }
            body {
                margin: 0;
                padding: 0;
                font-family: "Roboto", sans-serif;
                display: flex;
                justify-content: center;
                min-height: 100vh;
            }
            input,
            button {
                font-family: inherit;
            }
            #verifying_blurb {
                display: none;
            }
            #api {
                min-height: 100%;
                width: 100%;
                max-width: 325px;
                padding-bottom: 30px;
                position: relative;
            }
            #cancel {
                position: absolute;
                top: var(--padding-top);
                background: none;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-self: center;
            }
            .backArrow {
                width: 20px;
                height: 15px;
                color: var(--black);
            }
        </style>
        <style>
            #api {
                display: flex;
                flex-direction: column;
            }
            .divider h2 {
                display: none;
            }
            #localAccountForm {
                margin-top: 15%;
                display: flex;
                flex-direction: column;
                flex: 1;
            }
            .entry {
                display: flex;
                flex-direction: column;
                flex: 1;
                margin-top: 50px;
            }
            .intro h2 {
                font-size: 28px;
                font-weight: 700;
                text-align: left;
                color: var(--blue);
                margin: 0;
            }
            .entry-item {
                display: flex;
                flex-direction: column;
                margin: 10px 0;
                position: relative;
            }
            label {
                color: var(--blue);
                font-size: 15px;
                font-weight: 500;
            }
            input {
                width: 100%;
                border: none;
                font-size: 17px;
                outline: none;
                padding: 5px 0;
                font-weight: 500;
                border-bottom: 2px solid var(--gray);
                transition: 0.2s linear;
                border-radius: 0;
                -webkit-appearance: none;
                -webkit-border-radius: 0;
            }
            #forgotPassword {
                font-size: 14px;
                font-weight: 500;
                color: var(--blue);
                position: absolute;
                bottom: -50%;
                right: 0;
            }
            input:focus {
                border-bottom: 3px solid var(--blue-border);
            }
            #next {
                cursor: pointer;
                width: 100%;
                border: none;
                outline: none;
                background: var(--blue);
                padding: 15px 0;
                border-radius: 50px;
                color: var(--white);
                font-size: 19px;
                font-weight: 500;
                letter-spacing: 1px;
            }
            .create {
                font-size: 15px;
                display: flex;
                align-items: flex-end;
                justify-content: center;
                flex: 1;
            }
            #createAccount {
                padding-left: 10px;
                color: var(--black);
                font-weight: 700;
                text-align: center;
            }
            .social {
                display: flex;
                flex-direction: column;
                align-items: center;
            }
            .social .intro {
                margin: 20px 0 0 0;
            }
            .social .intro h2 {
                color: var(--black-100);
                font-size: 16px;
                font-weight: 500;
                margin: 0;
            }
            .options {
                width: 80%;
                display: flex;
                justify-content: space-around;
                align-items: center;
            }
            #GoogleExchange {
                cursor: pointer;
                height: 50px;
                border: none;
                outline: none;
                color: var(--white);
                background: transparent;
                font-size: 30px;
                margin-top: 22px;
                margin-right: 70px;
            }
            #AppleManagedExchange {
                cursor: pointer;
                height: 50px;
                border: none;
                outline: none;
                background: transparent;
                color: var(--white);
                font-size: 30px;
                margin-top: 20px;
                margin-left: 70px;
            }
            .accountButton {
                margin: 0;
            }
        </style>
    </head>
    <body>
        <div id="api" data-name="Unified"></div>

        <script>
            "use strict";
            $(document).ready(function () {
                if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
                    var t = document.createElement("style");
                    t.appendChild(
                        document.createTextNode(
                            "@-ms-viewport{width:auto!important}"
                        )
                    ),
                        t.appendChild(
                            document.createTextNode(
                                "@-ms-viewport{height:auto!important}"
                            )
                        ),
                        document.getElementsByTagName("head")[0].appendChild(t);
                }
                if (navigator.userAgent.match(/MSIE 10/i)) {
                    var e = $("#footer_links_container");
                    $(e).css("padding-top", "100px");
                }
                var o,
                    i = $("#background_background_image"),
                    n = function () {
                        (document.body.style.overflow = "hidden"),
                            ($(window).width() - 500) / $(window).height() < o
                                ? (i.height($(window).height()),
                                  i.width("auto"))
                                : (i.width($(window).width() - 500),
                                  i.height("auto")),
                            (document.body.style.overflow = "");
                    };
                $("<img>")
                    .attr("src", i.attr("src"))
                    .on("load", function () {
                        (o = this.width / this.height), n();
                    }),
                    $(window).resize(function () {
                        n();
                    }),
                    "undefined" != typeof $("#MicrosoftAccountExchange") &&
                        $("#MicrosoftAccountExchange").text("Microsoft"),
                    $("*").removeAttr("placeholder");
            });
        </script>
        <script>
            const GOOGLE_ICON_SVG = `<svg height="100%" viewBox="0 0 46 46" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:sketch="http://www.bohemiancoding.com/sketch/ns">
    <title>Google</title>
    <defs>
        <filter x="-50%" y="-50%" width="200%" height="200%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.168 0" in="shadowBlurOuter1" type="matrix" result="shadowMatrixOuter1"></feColorMatrix>
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter2"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter2" result="shadowBlurOuter2"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.084 0" in="shadowBlurOuter2" type="matrix" result="shadowMatrixOuter2"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="shadowMatrixOuter2"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <rect id="path-2" x="0" y="0" width="40" height="40" rx="2"></rect>
    </defs>
    <g id="Google-Button" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" sketch:type="MSPage">
        <g id="9-PATCH" sketch:type="MSArtboardGroup" transform="translate(-728.000000, -160.000000)"></g>
        <g id="btn_google_light_pressed" sketch:type="MSArtboardGroup" transform="translate(-1.000000, -1.000000)">
            <g id="button" sketch:type="MSLayerGroup" transform="translate(4.000000, 4.000000)" filter="url(#filter-1)">
                <g id="button-bg">
                    <use fill="#EEEEEE" fill-rule="evenodd" sketch:type="MSShapeGroup" xlink:href="#path-2"></use>
                    <use fill="none" xlink:href="#path-2"></use>
                    <use fill="none" xlink:href="#path-2"></use>
                    <use fill="none" xlink:href="#path-2"></use>
                </g>
            </g>
            <g id="logo_googleg_48dp" sketch:type="MSLayerGroup" transform="translate(15.000000, 15.000000)">
                <path d="M17.64,9.20454545 C17.64,8.56636364 17.5827273,7.95272727 17.4763636,7.36363636 L9,7.36363636 L9,10.845 L13.8436364,10.845 C13.635,11.97 13.0009091,12.9231818 12.0477273,13.5613636 L12.0477273,15.8195455 L14.9563636,15.8195455 C16.6581818,14.2527273 17.64,11.9454545 17.64,9.20454545 L17.64,9.20454545 Z" id="Shape" fill="#4285F4" sketch:type="MSShapeGroup"></path>
                <path d="M9,18 C11.43,18 13.4672727,17.1940909 14.9563636,15.8195455 L12.0477273,13.5613636 C11.2418182,14.1013636 10.2109091,14.4204545 9,14.4204545 C6.65590909,14.4204545 4.67181818,12.8372727 3.96409091,10.71 L0.957272727,10.71 L0.957272727,13.0418182 C2.43818182,15.9831818 5.48181818,18 9,18 L9,18 Z" id="Shape" fill="#34A853" sketch:type="MSShapeGroup"></path>
                <path d="M3.96409091,10.71 C3.78409091,10.17 3.68181818,9.59318182 3.68181818,9 C3.68181818,8.40681818 3.78409091,7.83 3.96409091,7.29 L3.96409091,4.95818182 L0.957272727,4.95818182 C0.347727273,6.17318182 0,7.54772727 0,9 C0,10.4522727 0.347727273,11.8268182 0.957272727,13.0418182 L3.96409091,10.71 L3.96409091,10.71 Z" id="Shape" fill="#FBBC05" sketch:type="MSShapeGroup"></path>
                <path d="M9,3.57954545 C10.3213636,3.57954545 11.5077273,4.03363636 12.4404545,4.92545455 L15.0218182,2.34409091 C13.4631818,0.891818182 11.4259091,0 9,0 C5.48181818,0 2.43818182,2.01681818 0.957272727,4.95818182 L3.96409091,7.29 C4.67181818,5.16272727 6.65590909,3.57954545 9,3.57954545 L9,3.57954545 Z" id="Shape" fill="#EA4335" sketch:type="MSShapeGroup"></path>
                <path d="M0,0 L18,0 L18,18 L0,18 L0,0 Z" id="Shape" sketch:type="MSShapeGroup"></path>
            </g>
            <g id="handles_square" sketch:type="MSLayerGroup"></g>
        </g>
    </g>
</svg>`;

            const APPLE_ICON_SVG = `<div style="font-synthesis: none; -moz-font-feature-settings: kern; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; width: 40px; height: 40px; min-width: 30px; max-width: 64px; min-height: 30px; max-height: 64px; position: relative;" role="button" tabindex="0" aria-label="Sign in with Apple">
    <div style=" position: absolute; box-sizing: border-box; width: 100%; height: 100%;">
  <svg xmlns="http://www.w3.org/2000/svg" style="overflow:visible" width="100%" height="100%" viewBox="6 6 44 44">
      <g fill="none" fill-rule="evenodd">
          <path fill="#fff" fill-rule="nonzero" d="M28.2226562,20.3846154 C29.0546875,20.3846154 30.0976562,19.8048315 30.71875,19.0317864 C31.28125,18.3312142 31.6914062,17.352829 31.6914062,16.3744437 C31.6914062,16.2415766 31.6796875,16.1087095 31.65625,16 C30.7304687,16.0362365 29.6171875,16.640178 28.9492187,17.4494596 C28.421875,18.06548 27.9414062,19.0317864 27.9414062,20.0222505 C27.9414062,20.1671964 27.9648438,20.3121424 27.9765625,20.3604577 C28.0351562,20.3725366 28.1289062,20.3846154 28.2226562,20.3846154 Z M25.2929688,35 C26.4296875,35 26.9335938,34.214876 28.3515625,34.214876 C29.7929688,34.214876 30.109375,34.9758423 31.375,34.9758423 C32.6171875,34.9758423 33.4492188,33.792117 34.234375,32.6325493 C35.1132812,31.3038779 35.4765625,29.9993643 35.5,29.9389701 C35.4179688,29.9148125 33.0390625,28.9122695 33.0390625,26.0979021 C33.0390625,23.6579784 34.9140625,22.5588048 35.0195312,22.474253 C33.7773438,20.6382708 31.890625,20.5899555 31.375,20.5899555 C29.9804688,20.5899555 28.84375,21.4596313 28.1289062,21.4596313 C27.3554688,21.4596313 26.3359375,20.6382708 25.1289062,20.6382708 C22.8320312,20.6382708 20.5,22.5950413 20.5,26.2911634 C20.5,28.5861411 21.3671875,31.013986 22.4335938,32.5842339 C23.3476562,33.9129053 24.1445312,35 25.2929688,35 Z"></path>
      </g>
  </svg></div>
    <div style=" width: 100%; height: 100%; box-sizing: border-box;">
    <svg xmlns="http://www.w3.org/2000/svg" style=" overflow: visible;" width="100%" height="100%" viewBox="0 0 50 50" preserveAspectRatio="xMidYMin meet"><rect width="100%" height="100%" ry="15%" fill="#000"></rect></svg></div>
    </div>`;

            const apiContainer = document.querySelector("#api");
            const socialClone = document.querySelector(".social");
            const googleProviderButton = document.querySelector("#GoogleExchange");
            const appleProviderButton = document.querySelector('#AppleManagedExchange')

            // remove social block
            document.querySelector(".social").remove();

            // add social block in new place
            apiContainer.appendChild(socialClone);

            // clear the button
            googleProviderButton.innerHTML = GOOGLE_ICON_SVG;
            appleProviderButton.innerHTML = APPLE_ICON_SVG;

            const entry = document.querySelector(".entry");
            const buttonsInEntry = entry.querySelector(".buttons");
            const createAccountBlock = document.querySelector(".create");

            entry.insertBefore(createAccountBlock, buttonsInEntry);

            // remove second divider block
            document.querySelectorAll(".divider")[1].remove();
        </script>
    </body>
</html>
