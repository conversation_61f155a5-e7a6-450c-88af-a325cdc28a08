import React, { useState, useEffect, useCallback } from 'react';
import { HubConnection, HubConnectionState } from '@microsoft/signalr';
import ChatWindow from './ChatWindow';
import ChatInput from './ChatInput';
import './Chat.css'
import { Conditional } from './Conditional';
import { ChatThreadDto } from '../../services/chat/chatThreadDto';
import { ChatUserDto } from '../../services/chat/chatUserDto';
import { ChatModel } from '../../services/chat/chatModel';
import { ChatMessageDto } from '../../services/chat/chatMessageDto';
import { DefaultButton, Dialog, DialogFooter, DialogType, Icon, PrimaryButton, Spinner, SpinnerSize } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import ChatGuest from './ChatGuest';
import ChatWaitingList from './ChatWaitingList';
import ChatThreadList from './ChatThreadList';
import chatStore from '../../stores/chatStore';
import { HubMessageTypeEnum } from '../../services/chat/hubMessageTypeEnums';
import { isJsonString } from '../../utils/utils';
import { myTheme } from '../../styles/theme';

interface IChat {
    userId: number;
    users: ChatUserDto[];
    threads: ChatThreadDto[];
    waitingList: any;
    current?: ChatModel;
    asyncActionInProgress?: boolean;
    connection: HubConnection | null;
    connectionError: string | null;
    chat: ChatMessageDto[];
    receivedMessagesData: any;
    extensionRole: string;
    isChatEnded: boolean;
    visible: boolean;
    setAsyncActionInProgress: (value: boolean) => void;
    setIsChatEnded: (value: boolean) => void;
    setVisible: (value: boolean) => void;
    setChat: (chat: ChatMessageDto[]) => void;
    sendMessage: (message: string, time: string) => void;
    sendActionNotification: (actionType: HubMessageTypeEnum) => void;
    select: (threadId?: string, userId?: number, guestId?: number, sendChatMessage?: string, sendActionNotification?: HubMessageTypeEnum) => void;
    deleteChatThread: (threadId: string) => void;
    enterWaitingRoom: () => void;
    refreshChatStore?: () => void;
    joinWaitingRoom: (waitingRoomId: number, waitingRoomThreadId: string, guestUserId?: number) => void;
    setReceivedMessagesData: any;
}

const Chat: React.FC<IChat> = (props: IChat) => {
    const { users, waitingList, current, chat, receivedMessagesData, select, enterWaitingRoom, joinWaitingRoom, threads, extensionRole, visible, setVisible, connectionError } = props;
    const findUserByIdentity = useCallback((identityId?: string, userId?: number): string => {
        const foundUserName: string = users.find(x => x.communicationIdentityId === identityId)?.name || '';
        return !!foundUserName ? foundUserName : (!!userId ? userId.toString() : (!!identityId ? identityId : ''));
    }, [users]);
    const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
    const [prevThreadId, setPrevThreadId] = useState<string>('');
    const [isCurrentUndefined, setIsCurrentUndefined] = useState<boolean>(false);

    const oldMessages = current ? current.messages : [];
    const connectionState: HubConnectionState = props.connection && props.connection.state === HubConnectionState.Connected ? HubConnectionState.Connected : HubConnectionState.Disconnected;

    useEffect(() => {
        if(!current) {
            setIsCurrentUndefined(true);
        } else {
            setIsCurrentUndefined(false);
        }

        if(current && current.thread && !!current.thread.id && (prevThreadId !== current.thread.id || isCurrentUndefined)) {
            const newMessages = oldMessages.filter(x => {
                const parsedMessage = !!x.content.message && isJsonString(x.content.message) ? JSON.parse(x.content.message)[0] : {};
                return parsedMessage.type === HubMessageTypeEnum.ChatMessage;
            });

            setPrevThreadId(current.thread.id);
            const tempChat: any[] = [];

            newMessages
                .sort(x => x.createdOn > new Date() ? 1 : -1)
                .filter(x => !x.content?.initiator)
                .forEach(x => {
                    tempChat.push({
                        authToken: '',
                        user: findUserByIdentity(x.sender?.id, x.userId),
                        message: x.content?.message || ''
                    });
                });

            props.setChat(tempChat);
        }

        // eslint-disable-next-line
    }, [current]);

    return (
        <div className={`chat__container ${!visible && 'chat__container--not-visible'}`} style={{overflowY: 'visible'}}>
            <Conditional condition={visible}>
                {props.asyncActionInProgress && <Spinner label={''} className={`chat__load-spinner`} size={SpinnerSize.small} ariaLive="assertive" labelPosition="bottom" />}

                <Conditional condition={!current}>
                    <div className='chat__navbar'>
                        <p>{L('Chat')}</p>
                        <div className="chat__button--circle" title={`${L('Minimize')}`} onClick={() => { setVisible(false); }}>
                            <Icon iconName='CalculatorSubtract' />
                        </div>
                    </div>
                    {extensionRole.includes('Consultant') ?
                        <div className='chat__window'>
                            <ChatWaitingList joinWaitingRoom={joinWaitingRoom} waitingList={waitingList} asyncActionInProgress={props.asyncActionInProgress} 
                                receivedMessagesData={receivedMessagesData} refreshChatStore={props.refreshChatStore} />
                            <ChatThreadList users={users} threads={threads} select={select} receivedMessagesData={receivedMessagesData} asyncActionInProgress={props.asyncActionInProgress}
                                refreshChatStore={props.refreshChatStore} /> 
                        </div>
                        : extensionRole.includes('Guest' || "Client") ? 
                            <ChatGuest enterWaitingRoom={enterWaitingRoom} />
                            : 
                            null
                    }
                </Conditional>

                <Conditional condition={current}>
                    <div className='chat__navbar'>
                        {extensionRole.includes('Consultant') ? <p>{chatStore.username}</p> : <p>{L('Chat')}</p>}
                        
                        <div className='chat__button--wrapper'>
                            {extensionRole.includes('Consultant') &&
                                <div className="chat__button--circle" title={`${L('Go back to thread list')}`} onClick={() => { select() }}>
                                    <Icon iconName='ChevronLeftMed' />

                                    {(receivedMessagesData.counterOfReceivedMessages && receivedMessagesData.counterOfReceivedMessages > 0) ?
                                        <span className="chat__button--notification-counter" title={L('Number of unread messages')}>
                                            <p>{receivedMessagesData.counterOfReceivedMessages}</p>
                                        </span> : <></>
                                    }
                                    {(receivedMessagesData.counterOfOpenedWaitingRooms && receivedMessagesData.counterOfOpenedWaitingRooms > 0) ?
                                        <span className="chat__button--notification-counter-type-2" title={L('Number of open waiting rooms')}>
                                            <p>{receivedMessagesData.counterOfOpenedWaitingRooms}</p>
                                        </span> : <></>
                                    }
                                </div>
                            }

                            <div className="chat__button--circle" title={`${L('Minimize')}`} onClick={() => { setVisible(false) }}>
                                <Icon iconName='CalculatorSubtract' />
                            </div>

                            {!props.isChatEnded &&
                                <div className="chat__button--circle" title={`${L('End the chat')}`} onClick={() => {
                                    setIsDialogOpen(true);
                                }}>
                                    <Icon iconName='CalculatorMultiply' />
                                </div>
                            }
                        </div>
                    </div>

                    <ChatWindow chat={chat} isChatEnded={props.isChatEnded} setChat={props.setChat} threadId={current?.thread.id!} users={users} userId={props.userId}
                        prevThreadId={prevThreadId} setIsChatEnded={(value: boolean) => props.setIsChatEnded(value)} />
                        
                    {!props.isChatEnded ?
                        (extensionRole.includes('Consultant') || 
                            (extensionRole.includes('Guest') && receivedMessagesData.consultantJoinedWaitingRoom === true &&
                                receivedMessagesData.guestActiveThreadId === current?.thread.id)) ?
                            <ChatInput sendMessage={props.sendMessage} sendActionNotification={props.sendActionNotification} />
                            :
                            <p className='chat__waiting-message'>{L('Please wait for consultant to join...')}</p>
                        : <></>
                    }
                </Conditional>
            </Conditional>

            <Conditional condition={!visible}>
                <div className="chat__open" 
                    title={connectionState === HubConnectionState.Connected ? 
                            extensionRole.includes('Consultant') ? L('Chat') : L('Chat with consultant') : 
                                (!!connectionError ? L(connectionError) : L('Connecting to chat, please wait'))} 
                    onClick={() => { 
                        if(connectionState === HubConnectionState.Connected) {
                            setVisible(true); 
                        }
                    }}
                    style={{cursor: !!connectionError || connectionState !== HubConnectionState.Connected ? 'default' : 'pointer'}}
                >
                    <Icon iconName={'chat'} className="chat__icon" />

                    {(connectionState !== HubConnectionState.Connected && props.connectionError === null) &&
                        <span className="chat__icon--load-spinner">
                            <Spinner label={''} className={`chat__load-spinner`} size={SpinnerSize.small} ariaLive="assertive" labelPosition="bottom" />
                        </span>
                    }

                    {(connectionState === HubConnectionState.Connected && connectionError === null &&
                        receivedMessagesData.counterOfReceivedMessages && receivedMessagesData.counterOfReceivedMessages > 0) ?
                        <span className="chat__icon--notification-counter" title={L('Number of unread messages')}>
                            <p>{receivedMessagesData.counterOfReceivedMessages}</p>
                        </span> : <></>
                    }
                    {connectionState === HubConnectionState.Connected && connectionError === null &&
                        extensionRole.includes('Consultant') && (receivedMessagesData.counterOfOpenedWaitingRooms && receivedMessagesData.counterOfOpenedWaitingRooms > 0) ?
                        <span className="chat__icon--notification-counter-type-2" title={L('Number of open waiting rooms')}>
                            <p>{receivedMessagesData.counterOfOpenedWaitingRooms}</p>
                        </span> : <></>
                    }

                    {!!connectionError &&
                        <span className="chat__icon--notification-counter">
                            <p>X</p>
                        </span>
                    }
                </div>
            </Conditional>

            <Dialog
                hidden={!isDialogOpen}
                onDismiss={() => setIsDialogOpen(false)}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L("Are you sure you want to end this chat thread?"),
                    // subText: chatThreadToDelete.userName && (!asyncChatActionError || asyncChatActionError.length === 0) ? chatThreadToDelete.userName : '',
                    closeButtonAriaLabel: L('Close'),
                }}
                modalProps={{ isBlocking: false, styles: { main: { maxWidth: 450 } }, }}
                theme={myTheme}
            >
                <DialogFooter theme={myTheme}>
                    <DefaultButton theme={myTheme} onClick={() => setIsDialogOpen(false)} text={L('No')} />
                    <PrimaryButton
                        onClick={async () => {
                            if(current && current.thread && !!current.thread.id) {
                                await chatStore.setAsEnded(current.thread.id).then((response: any) => {
                                    props.setIsChatEnded(true);
                                    props.sendMessage(`#%#%${L('The chat has ended.')}#%#%`, new Date().toString());
                                }).catch((error: any) => {
                                    console.error(error);
                                });
                            }
                            setIsDialogOpen(false);
                        }}
                        text={L('Yes')}
                        theme={myTheme}
                    />
                </DialogFooter>
            </Dialog>
        </div>
    );
};

export default Chat;