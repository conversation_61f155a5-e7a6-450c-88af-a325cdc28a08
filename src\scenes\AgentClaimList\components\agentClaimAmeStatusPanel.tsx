import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { ClaimDto } from '../../../services/claim/dto/claimDto';
import { AgentClaimAmeStatusContentView } from '../../AgentClaim/components/agentClaimAmeStatusContentView';
import { AgentClaimAmeStatusTypeEnum } from '../../../services/agentClaimAmeStatus/agentClaimAmeStatusTypeEnums';
import { PrimaryButton } from '@fluentui/react';
import { myTheme } from '../../../styles/theme';
import agentClaimAmeStatusAttachedFilesService from '../../../services/agentClaimAmeStatus/agentClaimAmeStatusAttachedFilesService';

export class AgentClainAmeStatusPanel extends GenericPanel {
    private uploadedFilesPayload: any = {
        [AgentClaimAmeStatusTypeEnum.Opinion]: {},
        [AgentClaimAmeStatusTypeEnum.Quotation]: {},
        [AgentClaimAmeStatusTypeEnum.FileApprovedQuotation]: {},
        [AgentClaimAmeStatusTypeEnum.FV]: {},
    };

    getPanelTitle(): string {
        return L("Agent claim");
    }

    private async delayedUpload(ameStatusId: number) {
        if(ameStatusId && ameStatusId > 0) {
            for(let key in this.uploadedFilesPayload) {
                if(this.uploadedFilesPayload.hasOwnProperty(key) && Object.keys(this.uploadedFilesPayload[key]).length > 0) {
                    await agentClaimAmeStatusAttachedFilesService.createNew({...this.uploadedFilesPayload[key], ameStatusId: ameStatusId})
                        .then(async () => {})
                        .catch((error: any) => {
                            console.error(error);
                        }
                    );
                }
            }
        }
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} onClick={async () => {
            await this._onConfirm().then((response: any) => {
                this.delayedUpload(
                    (this.props.store && this.props.store.dataSet && this.props.store.dataSet.items && this.props.store.dataSet.items.length > 0) ? 
                        this.props.store.dataSet.items[0].id : 0
                );
            }).catch((error: any) => {
                console.error(error);
            });
        }} text={L('Save')} disabled={this.asyncActionInProgress} />
    };

    renderContent() {
        return <AgentClaimAmeStatusContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ClaimDto }
            customData={{
                claimId: this.props.customData.claimId,
                passUploadedFilesResults: (type: string, payload: any) => {
                    if(!!type && typeof this.uploadedFilesPayload[type] !== 'undefined') {
                        this.uploadedFilesPayload[type] = payload;
                        this.forceUpdate();
                    }
                }
            }}
        />;
    }
}