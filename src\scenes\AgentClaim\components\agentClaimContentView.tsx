import { <PERSON><PERSON><PERSON>O<PERSON>, Pivot, PivotItem, PrimaryButton, Selection, SelectionMode, Spinner, SpinnerSize } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { additionalTheme, myTheme } from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import Stores from '../../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { Controls } from '../../BaseComponents/controls';
import { PolicyType } from '../../../services/policy/policyTypeEnums';
import insurancePolicyService from '../../../services/insurancePolicy/insurancePolicyService';
import { enumToDropdownOptions } from '../../../utils/utils';
import { fluentTableClassNames } from '../../../styles/fluentTableStyles';
import { CustomerFluentListBase } from '../../BaseComponents/customerFluentListBase';
import { spinnerClassNames } from '../../../styles/spinnerStyles';
import { CrudConsts } from '../../../stores/crudStoreBase';
import { defaultAgentClaim } from '../../../stores/agentClaimStore';
import { DropdownBase } from '../../BaseComponents/dropdownBase';
import { UserFluentListBase } from '../../BaseComponents/userFluentListBase';
import { CheckBoxBase } from '../../BaseComponents/CheckBoxBase';
import { UserDto } from '../../../services/user/dto/userDto';
import { AgentClaimDocumentsTab } from './agentClaimDocumentsTab';
import { LabeledTextField } from '../../../components/LabeledTextField';
import { AgentClaimPhotosTab } from './agentClaimPhotosTab';
import { ClientDto } from '../../../services/client/dto/clientDto';
import { AMEStatusFluentListBaseWithCommandBar } from '../../BaseComponents/ameStatusFluentListBaseWithCommandBar';
import { AgentClaimHistoryFluentListBaseWithCommandBar } from '../../BaseComponents/agentClaimHistoryFluentListBaseWithCommandBar';
import { validateEmail } from '../../../utils/inputUtils';
import userService from '../../../services/user/userCrudService';
import { InsuranceClaimTypeEnum } from '../../../services/claim/claimTypeEnum';
import { AgentClaimDto } from '../../../services/agentClaim/dto/agentClaimDto';

declare var abp: any;

const pivotStyles = {
    root: {
        marginLeft: '-8px'
    },
    linkIsSelected: {
        color: myTheme.palette.red,
        selectors: {
            ':before': {
                height: '5px',
                backgroundColor: additionalTheme.darkerRed,
            }
        }
    }
};

@inject(Stores.AgentClaimStore)
@inject(Stores.UserCrudStore)
@inject(Stores.ClientStore)
@inject(Stores.AgentClaimHistoryStore)
@inject(Stores.AgentClaimAmeStatusStore)
export class AgentClaimContentView extends GenericContentView {
    private agentClaim: AgentClaimDto = defaultAgentClaim;
    private isEditMode: boolean | null = null;
    private policyTypeOptions: any = {
        dropdown: enumToDropdownOptions(PolicyType, false, true),
    };
    private insurancePolicyOptions: any = {
        dropdown: [] as IDropdownOption[],
    };
    private selectedClientFullname: string = "";
    private _clientListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedClient: any = this._clientListSelection.getSelection();
            if(Array.isArray(selectedClient) && selectedClient.length > 0 && !!selectedClient[0].id) {
                this.agentClaim.clientId = selectedClient[0].id;
                this.selectedClientFullname = selectedClient[0].user.fullName;
                this._clientListSelection.setAllSelected(false);
                this.onCustomerSelect(selectedClient[0]);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private selectedAssistFullName: string = "";
    private _userListAssistSelection: Selection = new Selection({
        onSelectionChanged: async () => {
            const selectedUser: any = this._userListAssistSelection.getSelection();
            if(Array.isArray(selectedUser) && selectedUser.length > 0 && !!selectedUser[0].id) {
                this.selectedAssistFullName = `[${selectedUser[0].id}] ${selectedUser[0].name} ${selectedUser[0].surname}`;
                this.agentClaim.agentId = selectedUser[0].id;
                this._userListAssistSelection.setAllSelected(false);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private emailAddressErrorMessage: string = '';
    private insurancePolicies: any = {};
    private disableConfirmButton: boolean = true;

    async componentDidMount() {
        await this.props.clientStore?.getAll({...this.props.clientStore?.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE});

        this.checkIfDataIsLoaded("claim");

        const tempClaim: AgentClaimDto = this.props.payload.model ? this.props.payload.model : this.props.payload;
        let tempClientUser: ClientDto | undefined = undefined;

        if(tempClaim && ((typeof tempClaim.clientId === 'string' && parseInt(tempClaim.clientId) > 0) || tempClaim.clientId > 0) && this.props.clientStore) {
            tempClientUser = await this.props.clientStore.get({ id: tempClaim.clientId.toString() } as ClientDto);
            this.selectedClientFullname = !!tempClientUser['fullName'] ? tempClientUser['fullName'] : (!!tempClientUser.user.fullName ? tempClientUser.user.fullName : tempClientUser.userId.toString());
            if(tempClientUser && !!tempClientUser.id) {
                this.onCustomerSelect(undefined, parseInt(tempClientUser.id), tempClientUser.user['objectId']);
            }
        }

        if(tempClaim && ((!!tempClaim.policyId && tempClaim.policyId > 0) || ((typeof tempClaim.clientId === 'string' && parseInt(tempClaim.clientId) > 0) || tempClaim.clientId > 0))) {
            this.isEditMode = true;
        } else {
            this.isEditMode = false;
        }

        if(!!tempClaim.agentId && this.props.userCrudStore) {
            let tempAgentUser: UserDto = await this.props.userCrudStore.get({ id: tempClaim.agentId.toString() } as UserDto);
            this.selectedAssistFullName = !!tempAgentUser.fullName ? tempAgentUser.fullName : `${tempAgentUser.name} ${tempAgentUser.surname}`;
        }

        if(this.agentClaim.agentId) {
            await this.props.agentClaimAmeStatusStore?.getByClaimId(this.agentClaim.id)
        }

        if(!this.agentClaim.agentId || this.agentClaim.agentId <= 0) {
            this.agentClaim.agentId = abp.session.userId;
            await userService.get({ id: abp.session.userId }).then((response: any) => {
                if(response) {
                    this.selectedAssistFullName = response.fullName;
                }
            }).catch((error: any) => {
                console.error(error);
            });
        }

        this.forceUpdate();
    }

    private async onCustomerSelect(customer: any, clientId?: number, customerId?: string) {
        if(this.asyncActionInProgress) return;
        let newModel = this.state.model;

        if((customer && typeof customer !== 'undefined') || !!customerId) {
            newModel.value["clientId"] = !!clientId ? clientId : customer.id;

            this.asyncActionInProgress = true;
            this.forceUpdate();

            await insurancePolicyService.GetPolicyByCustomerId(customer ? customer.customerId : customerId).then((response: any) => {
                if(response.totalCount > 0) {
                    this.insurancePolicies = {};
                    this.insurancePolicyOptions.dropdown = response.items.map((item: any) => {
                        this.insurancePolicies[item.id] = item;
                        return { key: item.id, 
                                text: `${item.insurer} | ${item.segment} | ${item.policyNumber}`, 
                                isSelected: this.agentClaim.policyId === item.id };
                    }) as IDropdownOption[];
                } else {
                    this.insurancePolicies = {};
                    this.insurancePolicyOptions.dropdown = [];
                }
            });

            this.asyncActionInProgress = false;
            this.forceUpdate();
        } else {
            newModel.value["clientId"] = "";
            this.insurancePolicies = {};
            this.insurancePolicyOptions.dropdown = [];
        }
        this.setState({ model: newModel });
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} onClick={this._onConfirm} text={L('Save')} disabled={this.asyncActionInProgress || this.disableConfirmButton} />
    };

    renderContent() {
        this.agentClaim = this.props.payload.model ? this.props.payload.model : this.props.payload;

        let errorFound: boolean = false;
        const keysToCheck: string[] = ['agentId', 'policyId','insuranceClaimType', 'applicationDate', 'claimNumber', 'claimDate', 'claimSubject', 'claimType', 'clientId', 'emailAddress'];

        for(let key in this.agentClaim) {
            if(keysToCheck.includes(key) && this.agentClaim.hasOwnProperty(key) && 
                (!this.agentClaim[key] || this.agentClaim[key].length === 0 || (typeof this.agentClaim[key] === 'number' && this.agentClaim[key] === 0) ||
                    (key === 'emailAddress' && !validateEmail(this.agentClaim[key])))) {
                errorFound = true;
            }
        }

        if(errorFound !== false  && this.disableConfirmButton === false) {
            this.disableConfirmButton = true;
            if(this.props.toggleConfirm) {
                this.props.toggleConfirm(false);
            }
        } else if(errorFound === false && this.disableConfirmButton === true) {
            this.disableConfirmButton = false;
            if(this.props.toggleConfirm) {
                this.props.toggleConfirm(true);
            }
        }

        return typeof this.isEditMode === 'boolean' ?
            <Pivot styles={pivotStyles} theme={myTheme}>
                <PivotItem headerText={L('General')} key={'General'}>
                    {(this.isEditMode === false && this.props.clientStore) && 
                        <div className={fluentTableClassNames.contentContainer}>
                            <CustomerFluentListBase
                                searchText={undefined}
                                items={this.props.clientStore.dataSet && this.props.clientStore.dataSet.items ? this.props.clientStore.dataSet.items : []}
                                store={this.props.clientStore}
                                history={this.props.history}
                                scrollablePanelMarginTop={70}
                                customData={{ 
                                    selectedClient: !!this.selectedClientFullname ? `${this.selectedClientFullname}` : undefined,
                                }}
                                customSelection={this._clientListSelection}
                                customOnSelectionChanged={(selection: any) => {
                                    if(typeof selection === 'string' && selection === 'deleteClient') {
                                        this.agentClaim.clientId = defaultAgentClaim.clientId;
                                        this.selectedClientFullname = "";
                                        this.forceUpdate();
                                    }
                                }}
                            />
                        </div>
                    }

                    {this.isEditMode === true &&
                        <LabeledTextField key={'selectedClient'} required={false} label={L('Selected client')} 
                            value={!!this.selectedClientFullname ? `${this.selectedClientFullname}` : ''} 
                            disabled={true} isDataLoaded={true}
                            onChange={(e) => { return; }} 
                        />
                    }
                    
                    {this.asyncActionInProgress &&
                        <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                    }

                    <DropdownBase key={'policyId'} required={true} label={L("Policy")} options={this.insurancePolicyOptions.dropdown} value={!!this.agentClaim.policyId ? this.agentClaim.policyId : ''}
                        disabled={this.insurancePolicyOptions.dropdown.length === 0 || this.isEditMode === true}
                        isDataLoaded={this.isDataLoaded} 
                        onChange={(value: string | number | undefined) => {
                            if(value && typeof value === 'number') {
                                this.agentClaim.policyId = value;
                                this.agentClaim.claimType = this.insurancePolicies[value].segment;

                                this.forceUpdate();
                            }
                        }}
                    />
                    
                    {this.renderElement(new ContentViewModelProperty('claimType', '', Controls.Picker, false, this.policyTypeOptions, true, {isDataLoaded: this.isDataLoaded, hide: true}), [], {'claimType': this.agentClaim.claimType})}
                
                    {!!this.agentClaim.policyId && <>
                        {this.renderElement(new ContentViewModelProperty('insuranceClaimType', L("Claim type"), Controls.Picker, true, {dropdown: enumToDropdownOptions(InsuranceClaimTypeEnum, true, true, "string")}, false, {isDataLoaded: this.isDataLoaded}), [], {'insuranceClaimType': this.agentClaim.insuranceClaimType})}
                        {this.renderElement(new ContentViewModelProperty('claimSubject', L("Claim subject"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'claimSubject': this.agentClaim.claimSubject})}
                        {this.renderElement(new ContentViewModelProperty('claimDate', L("Claim date"), Controls.Date, true, [], false, {isDataLoaded: this.isDataLoaded, validationData: {'maxDate': 'TODAY'}}), [], {'claimDate': this.agentClaim.claimDate})}
                        {this.renderElement(new ContentViewModelProperty('applicationDate', L("Application date"), Controls.Date, true, [], false, {isDataLoaded: this.isDataLoaded, validationData: {'maxDate': 'TODAY'}}), [], {'applicationDate': this.agentClaim.applicationDate})}
                        {this.renderElement(new ContentViewModelProperty('note', L("Note"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'note': this.agentClaim.note})}
                        {this.renderElement(new ContentViewModelProperty('claimNumber', L("Claim number"), Controls.Text, true, [], false, {isDataLoaded: this.isDataLoaded}), [], {'claimNumber': this.agentClaim.claimNumber})}
                        {/* {this.renderElement(new ContentViewModelProperty('emailAddress', L("E-mail"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'emailAddress': this.agentClaim.emailAddress})} */}
                        
                        <LabeledTextField key={'emailAddress'} required={true} label={L('E-mail')} errorMessage={this.emailAddressErrorMessage}
                            value={this.agentClaim.emailAddress}
                            disabled={!this.isDataLoaded} isDataLoaded={this.isDataLoaded}
                            onChange={(e: any, value: string | undefined) => {
                                
                                    this.agentClaim.emailAddress = value || '';
                                    this.emailAddressErrorMessage = '';

                                    if(value && !validateEmail(value)) {
                                        this.emailAddressErrorMessage = L('Incorrect e-mail address.');
                                    }

                                    this.forceUpdate();
                                
                            }}
                        />

                        {/* {this.renderElement(new ContentViewModelProperty('compensationPaid', L("Compensation paid"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, validationData: {
                            mask: "**********", maskFormat: {'*': /[0-9]/}, maskChar: "", suffix: 'PLN'
                        }}), [], {'compensationPaid': this.agentClaim.compensationPaid})} */}

                        <CheckBoxBase key={'compensationPaid'} label={L("Is compensation paid")} 
                            value={this.agentClaim.compensationPaid} disabled={false}
                            onChange={(value: boolean | undefined) => {
                                if(typeof value === 'boolean') {
                                    this.agentClaim.compensationPaid = value;
                                    this.forceUpdate();
                                }
                            }}
                        />

                        <CheckBoxBase key={'invoicePaid'} label={L("Is invoice paid")} 
                            value={this.agentClaim.invoicePaid} disabled={false}
                            onChange={(value: boolean | undefined) => {
                                if(typeof value === 'boolean') {
                                    this.agentClaim.invoicePaid = value;
                                    this.forceUpdate();
                                }
                            }}
                        />

                        <div className={fluentTableClassNames.contentContainer}>
                            <UserFluentListBase
                                searchText={undefined}
                                items={this.props.userCrudStore?.dataSet && this.props.userCrudStore?.dataSet.items ? this.props.userCrudStore?.dataSet.items : []}
                                store={this.props.userCrudStore!}
                                history={this.props.history}
                                scrollablePanelMarginTop={70}
                                customData={{
                                    required: true,
                                    customLabel: L('Assist'),
                                    selectedUser: !!this.selectedAssistFullName ? `${this.selectedAssistFullName}` : undefined,
                                }}
                                customSelection={this._userListAssistSelection}
                                customOnSelectionChanged={(selection: any) => {
                                    if(typeof selection === 'string' && selection === 'deleteUser') {
                                        this.agentClaim.agentId = defaultAgentClaim.agentId;
                                        this.selectedAssistFullName = "";
                                        this.forceUpdate();
                                    }
                                }}
                            />
                        </div>
                    </>}
                </PivotItem>

                <PivotItem headerText={this.isEditMode === true ? L('Claim history') : ''} key={'claimHistory'} headerButtonProps={{disabled: !this.isEditMode, style: {display: this.isEditMode ? 'initial' : 'none'}}} style={{minHeight: '60vh'}}>
                    <AgentClaimHistoryFluentListBaseWithCommandBar
                        scrollablePanelMarginTop={200}
                        searchText={undefined}
                        items={this.props.agentClaimHistoryStore?.dataSet && this.props.agentClaimHistoryStore?.dataSet.items ? this.props.agentClaimHistoryStore?.dataSet.items : []}
                        store={this.props.agentClaimHistoryStore!}
                        history={this.props.history}
                        customData={{
                            disableGetAllOnMount: true,
                            useOnlyRefreshItems: true,
                            agentClaim: this.agentClaim,
                            customRequest: {claimId: this.agentClaim.id},
                            clientStore: this.props.clientStore,
                            selectedAssistFullName: this.selectedAssistFullName
                        }}
                    />
                </PivotItem>

                <PivotItem headerText={this.isEditMode === true ? L('Foreign claims') : ''} key={'ameStatus'} headerButtonProps={{disabled: !this.isEditMode, style: {display: this.isEditMode ? 'initial' : 'none'}}} style={{minHeight: '60vh'}}>
                    <AMEStatusFluentListBaseWithCommandBar
                        scrollablePanelMarginTop={200}
                        searchText={undefined}
                        items={this.props.agentClaimAmeStatusStore?.dataSet && this.props.agentClaimAmeStatusStore?.dataSet.items ? this.props.agentClaimAmeStatusStore?.dataSet.items : []}
                        store={this.props.agentClaimAmeStatusStore!}
                        history={this.props.history}
                        customData={{
                            claimId: this.agentClaim.id,
                            disableGetAllOnMount: true,
                            useOnlyRefreshItems: true,
                            customRequest: {claimId: this.agentClaim.id},
                            agentClaim: this.agentClaim
                        }}
                    />
                </PivotItem>

                <PivotItem headerText={this.isEditMode === true ? L('Documents') : ''} key={'documents'} headerButtonProps={{disabled: !this.isEditMode, style: {display: this.isEditMode ? 'initial' : 'none'}}}>
                    {this.isEditMode === true &&
                        <AgentClaimDocumentsTab agentClaim={this.agentClaim} />
                    }
                </PivotItem>

                <PivotItem headerText={this.isEditMode === true ? L('Photos') : ''} key={'photos'} headerButtonProps={{disabled: !this.isEditMode, style: {display: this.isEditMode ? 'initial' : 'none'}}}>
                    {this.isEditMode === true &&
                        <AgentClaimPhotosTab agentClaim={this.agentClaim} />
                    }
                </PivotItem>
            </Pivot>
        :
        <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
    }
}