import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { VehicleContentView } from '../../Vehicle/components/vehicleContentView';
import { VehicleDto } from '../../../services/vehicle/vehicleDto';

export class VehiclePanel extends GenericPanel {
    getPanelTitle(): string {
        return L("Vehicle");
    }

    renderContent() {
        return <VehicleContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as VehicleDto } />;
    }
}