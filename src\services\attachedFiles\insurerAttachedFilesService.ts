import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { CrudServiceBase } from '../base/crudServiceBase';
import { InsurerAttachedFilesDto } from './insurerAttachedFilesDto';

export class InsurerAttachedFilesService extends CrudServiceBase<InsurerAttachedFilesDto> {
    constructor() {
        super(Endpoint.InsurerAttachedFile);
        this.internalHttp = httpApi;
    }

    public async createNew(createInsurerAttachedFileInput: InsurerAttachedFilesDto) {
        const copyCreateInsurerAttachedFileInput = {...createInsurerAttachedFileInput, id: parseInt(createInsurerAttachedFileInput.id)};
        let result = await httpApi.post(this.endpoint.Create(), copyCreateInsurerAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getAllFiles() {
        let result = await httpApi.get(this.endpoint.GetAll());
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportInsurerAttachedFilesService: InsurerAttachedFilesService = new InsurerAttachedFilesService();
export default exportInsurerAttachedFilesService;