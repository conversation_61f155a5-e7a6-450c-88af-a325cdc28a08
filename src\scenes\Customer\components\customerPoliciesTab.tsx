import React from "react";
import { mergeStyleSets, MessageBar, MessageBarType, Spinner, SpinnerSize, Stack } from "@fluentui/react";
import { L } from "../../../lib/abpUtility";
import { myTheme } from "../../../styles/theme";

const classNames = mergeStyleSets({
    smallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '10px !important',
        marginTop: '22px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    fileLabelSmallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '30px !important',
        marginTop: '0',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
                textDecoration: 'none',
            }
        },
    },
    messageBar: {
        width: 'fit-content',
        marginLeft: '25px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
    fileListLabel: {
        fontSize: 16,
        fontWeight: 'bold',
        textDecoration: 'underline',
    },
});

export interface ICustomerPoliciesTabProps {
    asyncActionInProgress: boolean;
    clientFilePasswordError: string;
    policyAttachedFiles: JSX.Element[];
    onMessageBarDismiss: () => void;
}

export class CustomerPoliciesTab extends React.Component<ICustomerPoliciesTabProps> {
    render() {
        const {policyAttachedFiles, clientFilePasswordError, asyncActionInProgress} = this.props;

        return <Stack>
            {!!clientFilePasswordError &&
                <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`}
                    onDismiss={() => { this.props.onMessageBarDismiss(); }}
                >
                    {clientFilePasswordError}
                </MessageBar>
            }

            <ul style={{padding: 0}}>
                <p className={classNames.fileListLabel}>
                    {L('Customer insurance policies')}:
                    {asyncActionInProgress && (
                        <Spinner className={`${classNames.fileLabelSmallLoadSpinner}`} size={SpinnerSize.small} ariaLive="assertive" labelPosition="right" />
                    )}
                </p>
                {
                    policyAttachedFiles.length > 0 ?
                        policyAttachedFiles.map((element: any) => (
                            element.element
                        ))
                        : 
                        L('There are no files to display')
                }
            </ul>
        </Stack>;
    }
}