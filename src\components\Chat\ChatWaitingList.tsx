import React from 'react';
import { dateFormatChat } from '../../utils/utils';
import { L } from '../../lib/abpUtility';
import { Stack } from '@fluentui/react';

interface IChatWaitingListProps {
    waitingList: any;
    asyncActionInProgress?: boolean;
    receivedMessagesData?: any;
    joinWaitingRoom: (waitingRoomId: number, waitingRoomThreadId: string, guestUserId?: number) => void;
    refreshChatStore?: () => void;
}

const ChatWaitingList: React.FC<IChatWaitingListProps> = (props) => {
    const { waitingList, receivedMessagesData, joinWaitingRoom } = props;
    const mappedArray: { key: string; value: number }[] = [];

    for (const key in receivedMessagesData.counterOfReceivedMessagesPerThread) {
        mappedArray.push({
            key,
            value: receivedMessagesData.counterOfReceivedMessagesPerThread[key]
        });
    }
    
    let waitingListComponents: any[] = [];

    if (waitingList && Array.isArray(waitingList)) {
        let filteredWaitings = [...waitingList];

        filteredWaitings.sort((a: any, b: any) => {
            const timeA = new Date(a.creationTime).getTime();
            const timeB = new Date(b.creationTime).getTime();
            return timeB > timeA ? 1 : timeB < timeA ? -1 : 0;
        });

        waitingListComponents = filteredWaitings.map((waitingList: any) =>
            <div key={waitingList.id} className={`chat__option chat__option--thread ${props.asyncActionInProgress && 'chat__option--disabled'}`}
                onClick={() => {
                    joinWaitingRoom(waitingList.waitingRoomId, waitingList.threadId, waitingList.guestUserId);
                }}
            >
                <p className='chat__option-name'>{waitingList.guestName + ' ' + waitingList.guestSurname}</p>
                <div style={{minWidth: '102px'}}>
                    <div className='chat__option-circle'></div>
                    <Stack>
                        <p className='chat__option-date'>{dateFormatChat(waitingList.creationTime, undefined, true)}</p>
                        <p className={`chat__option-status chat__option-status--orange`}>{L(`ChatWaiting`)}</p>
                    </Stack>
                </div>
                {(receivedMessagesData && receivedMessagesData.counterOfReceivedMessagesPerThread && receivedMessagesData.counterOfReceivedMessagesPerThread[waitingList.id] && receivedMessagesData.counterOfReceivedMessagesPerThread.counterOfReceivedMessages) &&
                    <span className={`chat__option--notification`}>
                        <p>{receivedMessagesData.counterOfReceivedMessagesPerThread[waitingList.id]}</p>
                    </span>
                }
            </div>
        )
    }

    return (
        <div>
            {waitingListComponents}
        </div>
    );
};

export default ChatWaitingList;