import * as React from 'react';

import AuthenticationStore from '../../stores/authenticationStore';
import Stores from '../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import { msalConfig } from '../../authConfig';
import { MsalContext } from '@azure/msal-react';

export interface ILogoutProps {
  authenticationStore?: AuthenticationStore;
}

const createLogoutUrl = (): string => {
  const logoutUrl = new URL(msalConfig.auth.logoutUriBase);
  logoutUrl.searchParams.append('post_logout_redirect_uri', `${msalConfig.auth.redirectUri}`)

  return logoutUrl.toString();
}

@inject(Stores.AuthenticationStore)
class Logout extends React.Component<ILogoutProps> {
  static contextType = MsalContext;

  componentDidMount() {
    const url = createLogoutUrl();
    this.props.authenticationStore!.logout();
    window.location.href = url;
  }

  render() {
    return null;
  }
}

export default Logout;