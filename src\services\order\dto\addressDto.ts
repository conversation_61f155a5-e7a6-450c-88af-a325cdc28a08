import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";

export interface AddressDto extends BaseApiEntityModel {
    Id: string;
    FirstName: string;
    LastName: string;
    Email: string;
    Company: string;
    VatNumber: string;
    CountryId: string;
    StateProvinceId: string;
    City: string;
    Address1: string;
    Address2: string;
    ZipPostalCode: string;
    PhoneNumber: string;
    FaxNumber: string;
    CustomAttributes: string;
    CreatedOnUtc: string;
}