import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";
import { CustomAttributeDto } from "./customAttributeDto";
import { OrderItemDto } from "./orderItemDto";
//import { OrderStatus } from "./orderStatusEnums";
import { OrderTaxDto } from "./orderTaxDto";
import { PaymentStatus } from "../enums/paymentStatusEnums";
import { ShippingStatus } from "../enums/shippingStatusEnums";
import { TaxDisplayType } from "../enums/taxDisplayTypeEnums";
//import { AddressDto } from "./addressDto";

export interface OrderDto extends BaseApiEntityModel {
    orderNumber: number;
    seName: string;
    code: string;
    companyName: string;
    customerEmail: string;
    firstName: string;
    lastName: string;
    orderTotal: number;
    paymentMethodSystemName: string;
    //billingAddress: AddressDto;
    shippingMethod: string;
    //shippingAddress: AddressDto;
    orderStatusId: number;
    orderItems: OrderItemDto[];
    checkoutAttributes: CustomAttributeDto[];
    orderGuid: string;
    storeId: string;
    customerId: string;
    ownerId: string;
    seId: string;
    pickUpInStore: boolean;
    paymentOptionAttribute: string;
    customerCurrencyCode: string;
    primaryCurrencyCode: string;
    currencyRate: number;
    rate: number;
    vatNumber: string;
    vatNumberStatusId: number;
    orderSubtotalInclTax: number;
    orderSubtotalExclTax: number;
    orderSubTotalDiscountInclTax: number;
    orderSubTotalDiscountExclTax: number;
    orderShippingInclTax: number;
    orderShippingExclTax: number;
    paymentMethodAdditionalFeeInclTax: number;
    paymentMethodAdditionalFeeExclTax: number;
    orderTax: number;
    orderDiscount: number;
    paidAmount: number;
    refundedAmount: number;
    loyaltyPointsWereAdded: boolean;
    redeemedLoyaltyPoints: number;
    redeemedLoyaltyPointsAmount: number;
    calcLoyaltyPoints: number;
    checkoutAttributeDescription: string;
    customerLanguageId: string;
    affiliateId: string;
    customerIp: string;
    shippingRateProviderSystemName: string;
    deleted: boolean;
    createdOnUtc: string;
    imported: boolean;
    urlReferrer: string;
    shippingOptionAttributeDescription: string;
    shippingOptionAttribute: string;
    orderTags: string[];
    paidDateUtc: string | null;
    shippingStatusId: ShippingStatus;
    paymentStatusId: PaymentStatus;
    customerTaxDisplayTypeId: TaxDisplayType;    
    orderTaxes: OrderTaxDto[];
    //pickupPoint: PickupPoint;
}