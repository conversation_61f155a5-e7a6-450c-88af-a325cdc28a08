import { mergeStyleSets, Stack, SelectionMode, Selection, Spinner, SpinnerSize } from '@fluentui/react';
import { myTheme } from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import Stores from '../../../stores/storeIdentifier';
import { inject, observer } from 'mobx-react';
import SportDisciplineStore from '../../../stores/sportDisciplineStore';
import { GeneralSportsFluentListBaseWithCommandBar } from '../../BaseComponents/generalSportsFluentListBaseWithCommandBar';

const classNames = mergeStyleSets({
    contentContainer: {
        display: 'block',
        width: '100%',
        height: '500px',
        maxHeight: '500px',
        position: 'relative',
        marginTop: '20px',
    },
    smallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '10px !important',
        marginTop: '22px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    }
});

export interface IProps {
	sportDisciplineStore: SportDisciplineStore;
}

@inject(Stores.LanguageStore)
@inject(Stores.ProductAttributeStore)
@inject(Stores.SportDisciplineStore)
@observer
export class GeneralSportsListContentView extends GenericContentView {
    private selectedSport: any;
    private _sportListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedSport: any = this._sportListSelection.getSelection();
            if(Array.isArray(selectedSport) && selectedSport.length > 0 && !!selectedSport[0].id) {
                this.selectedSport = selectedSport[0];
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private overrideAllItemsTrigger: number = 0;

    private async reloadItems() {
        if(this.props.customData && this.props.customData.fetchAllSports) {
            await this.props.customData.fetchAllSports();
            this.overrideAllItemsTrigger = this.overrideAllItemsTrigger + 1;
            this.forceUpdate();
        }
    }

    renderContent() {
        return <>
            <Stack>
                <div className={classNames.contentContainer}>
                    <GeneralSportsFluentListBaseWithCommandBar 
                        store={this.props.sportDisciplineStore!}
                        items={
                            this.props.sportDisciplineStore?.dataSet && this.props.sportDisciplineStore?.dataSet.items
                                ? this.props.sportDisciplineStore?.dataSet.items
                                : []
                        }
                        customSelection={this._sportListSelection}
                        searchText={''}
                        history={this.props.history}
                        customData={{
                            selectedSport: this.selectedSport,
                            overrideAllItemsTrigger: this.overrideAllItemsTrigger,
                            insurerId: this.props.customData.insurerId,
                            disableGetAllOnMount: true,
                        }}
                        scrollablePanelMarginTop={120}
                        refreshItems={() => this.reloadItems()}
                    />
                    <Stack horizontal={true}>
                        {this.asyncActionInProgress &&
                            <Spinner className={classNames.smallLoadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                        }
                    </Stack>
                </div>
            </Stack>
        </>
    }
}