import { BlobServiceClient, BlobUploadCommonResponse } from '@azure/storage-blob';
import { L } from '../lib/abpUtility';
import apkAttachedFilesService from './apkAttachedFiles/apkAttachedFilesService';
import blobStorageService from './blobStorage/blobStorageService';

export interface SasResult {
  blobUri: string;
  sasKey: string;
  container: string;
  blobName: string;
}

export interface UploadedFile {
  name: string;
  url: string;
}

export const uploadSignedAPKFileToAzure = async (file: File, policyId: number): Promise<UploadedFile> => {
  const sasResult: SasResult = await apkAttachedFilesService.GenerateSasTokenForSignedAPK(policyId);

  if(sasResult) {
    const blobServiceClient = new BlobServiceClient(
      `${sasResult.blobUri}/?${sasResult.sasKey}`
    );
  
    const containerClient = blobServiceClient.getContainerClient(sasResult.container);
    const blockBlobClient = containerClient.getBlockBlobClient(sasResult.blobName);
    
    let blobUploadCommonResponse: BlobUploadCommonResponse = await blockBlobClient.uploadData(file);
  
    if(blobUploadCommonResponse._response.status === 201) {
      let url: string = await blobStorageService.getLinkToFile(sasResult.blobName);
  
      return {
        name: sasResult.blobName,
        url: url,
      }
    } else {
      throw new Error(`${L('File upload failed, upload error code:')} ${blobUploadCommonResponse.errorCode}`);
    }
  } else {
    throw new Error(`${L('File upload failed, possibly the signed apk file is already added! Otherwise try again later or contact administration.')}`);
  }
}

export const uploadFileToAzure = async (file: File, isTokenForRodo?: boolean): Promise<UploadedFile> => {
  const sasResult: SasResult = isTokenForRodo === true ? await blobStorageService.GenerateSasTokenForRodo(file.name) : await blobStorageService.GenerateSasTokenForFile(file.name);
  const blobServiceClient = new BlobServiceClient(
    `${sasResult.blobUri}/?${sasResult.sasKey}`
  );

  const containerClient = blobServiceClient.getContainerClient(sasResult.container);
  const blockBlobClient = containerClient.getBlockBlobClient(sasResult.blobName);
  
  let blobUploadCommonResponse: BlobUploadCommonResponse = await blockBlobClient.uploadData(file);

  if(blobUploadCommonResponse._response.status === 201) {
    let url: string = await blobStorageService.getLinkToFile(sasResult.blobName);

    return {
      name: sasResult.blobName,
      url: url,
    }
  } else {
    throw new Error(`${L('File upload failed, upload error code:')} ${blobUploadCommonResponse.errorCode}`);
  }
}