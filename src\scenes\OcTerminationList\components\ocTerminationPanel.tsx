import { PrimaryButton } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { OcTerminationDto } from '../../../services/ocTermination/dto/ocTerminationDto';
import { myTheme } from '../../../styles/theme';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { OcTerminationContentView } from '../../OcTermination/components/ocTerminationContentView';

export class OcTerminationPanel extends GenericPanel {
    private inputErrorsCount: number = 0;

    getPanelTitle(): string {
        return L('OC termination');
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} onClick={this._onConfirm} text={L('Save')}
                disabled={this.asyncActionInProgress || this.inputErrorsCount > 0} />
    };

    renderContent() {
        return <OcTerminationContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as OcTerminationDto }
                onSetInputErrorsCount={(count: number) => { this.inputErrorsCount = count; this.forceUpdate(); }} 
                customData={{ inputErrorsCount: this.inputErrorsCount }} />;
    }
}