import { InsurancePolicyDto } from '../../../services/insurancePolicy/insurancePolicyDto';
import { InsurancePolicyContentView } from '../../InsurancePolicy/components/insurancePolicyContentView';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { DefaultButton, PrimaryButton } from '@fluentui/react';
import { myTheme } from '../../../styles/theme';

export class InsurancePolicyPanel extends GenericPanel {
    private isEditMode: boolean = false;

    getPanelTitle(): string {
        return L("Insurance policy");
    }

    renderConfirm = () => {
        if(this.isEditMode === true) {
            return <PrimaryButton theme={myTheme} onClick={this._onConfirm} text={L('Save')} disabled={this.asyncActionInProgress} />;
        } else {
            return <></>;
        }
    };
    
    renderCancel = () => {
        return <DefaultButton theme={myTheme} onClick={this._onCancel} text={L('Back')} />
    };
    
    renderContent() {
        return <InsurancePolicyContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={this.props.payload as InsurancePolicyDto}
            toggleConfirm={(show: boolean) => { this.isEditMode = show; this.forceUpdate(); }} />;
    }
}