import { mergeStyleSets} from '@fluentui/merge-styles';
import { myTheme } from './theme';

export const spinnerClassNames = mergeStyleSets({
    loadSpinner: {
        display: 'inline-flex',
        marginTop: 15,
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    smallLoadSpinner: {
        display: 'inline-flex',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
});