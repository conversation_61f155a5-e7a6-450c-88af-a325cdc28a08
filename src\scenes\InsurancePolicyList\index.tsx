import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import SearchStore from '../../stores/searchStore';
import InsurancePolicyStore from '../../stores/insurancePolicyStore';
import { InsurancePolicyTable } from './components/insurancePolicyTable';
import { FocusZone, FocusZoneDirection, FocusZoneTabbableElements, IDropdownOption, mergeStyleSets } from '@fluentui/react';
import { DropdownBase } from '../BaseComponents';
import { L } from '../../lib/abpUtility';
import { InsurancePolicyStatus } from '../../services/insurancePolicy/insurancePolicyStatusEnums';
import { enumToDropdownOptions } from '../../utils/utils';
import { CrudConsts } from '../../stores/crudStoreBase';
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  },
});

export interface IProps {
  searchStore: SearchStore;
  insurancePolicyStore: InsurancePolicyStore;
  history: any;
}

export interface IState {
  filterByStatus: string;
  filterByStatusOptions: any[];
  filterByPaymentStatus: string;
  filterByPaymentStatusOptions: any[];
  filterByApkStatus: string;
  filterByApkStatusOptions: any[];
  reloadingItems: boolean;
  gotNewItems: boolean;
  items: any[];
  customRequest: any;
}


@inject(Stores.SearchStore)
@inject(Stores.InsurancePolicyStore)
@observer
class Index extends AppComponentBase<IProps, IState> {
  formRef: any;

  constructor(props: IProps) {
    super(props);

    this.state = {
      ...this.state,
      items: [],
      filterByStatus: "PolicyIssued",
      filterByStatusOptions: [],
      filterByPaymentStatus: "ALL",
      filterByPaymentStatusOptions: [],
      filterByApkStatus: "ALL",
      filterByApkStatusOptions: [],
      reloadingItems: false,
      gotNewItems: true,
      customRequest: {...this.props.insurancePolicyStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '', maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE, status: "PolicyIssued"},
    };
  }

  private handleChange = (value: string, option: any, index: any) => {
    if (index === 0) {
      this.setState({ filterByStatus: value });
    } else {
      this.setState({ filterByPaymentStatus: value });
    }
    this.refreshItems();
  };


  private sortItems(items: any[]): any[] {
    return items.slice(0).sort((a, b) => (a.OrderNumber < b.OrderNumber) ? 1 : ((b.OrderNumber < a.OrderNumber) ? -1 : 0));
  }

  private async setCustomRequest(newFilterByStatus: string | undefined, newFilterByPaymentStatus: string | undefined, newFilterByApkStatus: string | undefined) {
    const requestPayload: any = {...this.props.insurancePolicyStore.defaultRequest, keyword: this.props.searchStore.searchText ? this.props.searchStore.searchText : '',
    maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE};

    if((typeof newFilterByPaymentStatus === 'string' && newFilterByPaymentStatus.length > 0 && newFilterByPaymentStatus !== "ALL")) {
      requestPayload['paymentStatus'] = newFilterByPaymentStatus;
    } else if((typeof newFilterByPaymentStatus === 'undefined' && typeof this.state.filterByPaymentStatus === 'string' && 
      this.state.filterByPaymentStatus.length > 0 && this.state.filterByPaymentStatus !== 'ALL')
    ) {
      requestPayload['paymentStatus'] = this.state.filterByPaymentStatus;
    }

    if((typeof newFilterByStatus === 'string' && newFilterByStatus.length > 0 && newFilterByStatus !== "ALL")) {
      requestPayload['status'] = newFilterByStatus;
    } else if((typeof newFilterByStatus === 'undefined' && typeof this.state.filterByStatus === 'string' && 
      this.state.filterByStatus.length > 0 && this.state.filterByStatus !== 'ALL')
    ) {
      requestPayload['status'] = this.state.filterByStatus;
    }
    
    if((typeof newFilterByApkStatus === 'string' && newFilterByApkStatus.length > 0 && newFilterByApkStatus !== "ALL")) {
      requestPayload['isApkSinged'] = newFilterByApkStatus;
    } else if((typeof newFilterByApkStatus === 'undefined' && typeof this.state.filterByApkStatus === 'string' && 
      this.state.filterByApkStatus.length > 0 && this.state.filterByApkStatus !== 'ALL')
    ) {
      requestPayload['isApkSinged'] = this.state.filterByApkStatus;
    }

    this.setState((prevState) => ({...prevState, customRequest: requestPayload, 
                                      filterByPaymentStatus: typeof newFilterByPaymentStatus === 'string' ? newFilterByPaymentStatus : this.state.filterByPaymentStatus,
                                      filterByStatus: typeof newFilterByStatus === 'string' ? newFilterByStatus : this.state.filterByStatus,
                                      filterByApkStatus: typeof newFilterByApkStatus === 'string' ? newFilterByApkStatus : this.state.filterByApkStatus,
                                  }));
  }

  private async refreshItems(forcedKeyword?: string) {
    this.setState((prevState) => ({...prevState, items: [], reloadingItems: true }));

    await this.props.insurancePolicyStore.getAll({...this.props.insurancePolicyStore.defaultRequest, keyword: forcedKeyword ? forcedKeyword : this.props.searchStore.searchText, 
        maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE})
    .then(() => {
      this.setState((prevState) => ({...prevState, reloadingItems: false, gotNewItems: true }));
    });
  }

  public render() {
    if (!this.state.reloadingItems && this.state.gotNewItems) {
      const items = this.props.insurancePolicyStore.dataSet ? this.props.insurancePolicyStore.dataSet.items : [];

      if (items.length > 0) {
        this.setState((prevState) => ({ ...prevState, items: this.sortItems(items), gotNewItems: false }));
      }
    }

    if (this.state.filterByStatusOptions.length === 0) {
      let filterByStatusDropdownOptions: IDropdownOption[] = [
        { key: "ALL", text: L('All2') },
        ...enumToDropdownOptions(InsurancePolicyStatus, false, true, "string")
      ];

      this.setState((prevState) => ({ ...prevState, filterByStatusOptions: filterByStatusDropdownOptions }));
    }

    if (this.state.filterByPaymentStatusOptions.length === 0) {
      let filterByPaymentStatusDropdownOptions: IDropdownOption[] = [
        { key: "ALL", text: L('All2') },
        { key: "UnPaid", text: L('UnPaid')},
        { key: "PartiallyPaid", text: L('PartiallyPaid')},
        { key: "Paid", text: L('Paid')},
      ];

      this.setState((prevState) => ({ ...prevState, filterByPaymentStatusOptions: filterByPaymentStatusDropdownOptions }));
    }

    if(this.state.filterByApkStatusOptions.length === 0) {
      let filterByApkStatusDropdownOptions: IDropdownOption[] = [
        { key: "ALL", text: L('All2') },
        { key: "true", text: L('Signed')},
        { key: "false", text: L('Not signed')},
      ];

      this.setState((prevState) => ({...prevState, filterByApkStatusOptions: filterByApkStatusDropdownOptions }));
    }
    
    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Insurance Policy List')}</h2>
        </div>
        <FocusZone direction={FocusZoneDirection.horizontal} handleTabKey={FocusZoneTabbableElements.all} style={{display: 'flex', flexDirection: 'row', flexWrap: 'nowrap', alignItems:'center'}}>
          <DropdownBase key={'filterByStatusDropdown'} required={false} label={L('Filter by status')} options={this.state.filterByStatusOptions}
                    value={this.state.filterByStatus} disabled={false} isDataLoaded={true} customLabelStyles={{minWidth: '200px !important', width: '200px !important', whiteSpace: 'nowrap', fontSize: '12px'}}
                    customDropdownWidth={'180px'}
                    onChange={(e: string | number | undefined) => {
                      if(e && e !== this.state.filterByStatus) {
                        this.setCustomRequest(e.toString(), undefined, undefined);
                      }
                    }} />

          <DropdownBase key={'filterByPaymentStatusDropdown'} required={false} label={L('Filter by payment status')} options={this.state.filterByPaymentStatusOptions}
                    value={this.state.filterByPaymentStatus} disabled={false} isDataLoaded={true} customLabelStyles={{minWidth: '200px !important', width: '200px !important', marginRight:'30px',marginLeft:'20px', whiteSpace: 'nowrap', fontSize: '12px'}}
                    customDropdownWidth={'180px'}
                    onChange={(e: string | number | undefined) => {
                      if(e && e !== this.state.filterByPaymentStatus) {
                        this.setCustomRequest(undefined, e.toString(), undefined);
                      }
                    }}
                  />

          <DropdownBase key={'filterByApkStatusDropdown'} required={false} label={L('Filter by APK status')} options={this.state.filterByApkStatusOptions}
                    value={this.state.filterByApkStatus} disabled={false} isDataLoaded={true} customLabelStyles={{ minWidth: '200px !important', width: '200px !important', whiteSpace: 'nowrap', marginLeft: '20px', fontSize: '12px'}}
                    customDropdownWidth={'180px'}
                    onChange={(e: string | number | undefined) => {
                      if(e && e !== this.state.filterByApkStatus) {
                        this.setCustomRequest(undefined, undefined, e.toString());
                      }
                    }}
                  />
        </FocusZone>

        <InsurancePolicyTable
          searchText={this.props.searchStore.searchText}
          items={this.state.items}
          store={this.props.insurancePolicyStore}
          history={this.props.history}
          refreshItems={() => this.refreshItems()}
          scrollablePanelMarginTop={220}
          customData={{
            customRequest: this.state.customRequest,
          }}
        />
      </>
    );
  }
}

export default Index;