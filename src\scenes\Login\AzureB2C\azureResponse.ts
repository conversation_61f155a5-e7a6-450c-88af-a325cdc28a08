import { GetAllUserOutput } from "../../../services/user/dto/getAllUserOutput";

export interface IdTokenClaims {
    exp: number;
    nbf: number;
    ver: string;
    iss: string;
    sub: string;
    aud: string;
    nonce: string;
    iat: number;
    auth_time: number;
    oid: string;
    family_name: string;
    given_name: string;
    name: string;
    emails: string[];
    extension_Role: string;
    tfp: string;
}

export interface Account {
    homeAccountId: string;
    environment: string;
    tenantId: string;
    username: string;
    localAccountId: string;
    name: string;
    idTokenClaims: IdTokenClaims;
}

export interface IdTokenClaims2 {
    exp: number;
    nbf: number;
    ver: string;
    iss: string;
    sub: string;
    aud: string;
    nonce: string;
    iat: number;
    auth_time: number;
    oid: string;
    family_name: string;
    given_name: string;
    name: string;
    emails: string[];
    extension_Role: string;
    tfp: string;
}

export interface AzureResponse {
    authority: string;
    uniqueId: string;
    tenantId: string;
    scopes: any[];
    account: Account;
    idToken: string;
    idTokenClaims: IdTokenClaims2;
    accessToken: string;
    fromCache: boolean;
    expiresOn?: any;
    familyId: string;
    tokenType: string;
    state: string;
    cloudGraphHostName: string;
    msGraphHost: string;
}


export interface  AzureB2CStorage{
    user : GetAllUserOutput,
    b2c: AzureResponse
}