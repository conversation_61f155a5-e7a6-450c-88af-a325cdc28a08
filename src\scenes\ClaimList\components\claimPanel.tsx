import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { ClaimContentView } from '../../Claim/components/claimContentView';
import { ClaimDto } from '../../../services/claim/dto/claimDto';
import claimService from '../../../services/claim/claimService';
import { PolicyType } from '../../../services/policy/policyTypeEnums';
import { ClaimChildrenDto } from '../../../services/claim/dto/claimChildrenDto';
import { ClaimHomeDto } from '../../../services/claim/dto/claimHomeDto';
import { ClaimLifeDto } from '../../../services/claim/dto/claimLifeDto';
import { ClaimTravelDto } from '../../../services/claim/dto/claimTravelDto';
import { ClaimVehicleDto } from '../../../services/claim/dto/claimVehicleDto';
import { mergeStyleSets, MessageBar, MessageBarType } from '@fluentui/react';
import { catchErrorMessage } from '../../../utils/utils';

const classNames = mergeStyleSets({
    messageBar: {
        width: 'fit-content',
        marginTop: '20px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
});

export class ClaimPanel extends GenericPanel {
    private prevEntityId: number = 0;
    private dataDownloaded: boolean = false;
    private entityError: string = '';

    getPanelTitle(): string {
        return L("Claim");
    }

    private async getEntityModel(entityId: string) {
        let newModel: any = {};
        let policyType = undefined;

        this.entityError = "";

        if(this.props.payload && this.props.payload.model && this.props.payload.model.id > 0) {
            policyType = PolicyType[this.props.payload.model.policyType] as unknown as PolicyType;
        } else {
            await this.props.store.get({ id: entityId } as ClaimDto).then(async (claim: ClaimDto) => {
                policyType = PolicyType[claim.policyType] as unknown as PolicyType;
            }).catch((error: any) => {
                console.error(error);
                this.entityError = catchErrorMessage(error);
            });
        }

        switch(policyType) {
            case PolicyType.Children:
                await claimService.getChildren({ id: entityId } as ClaimChildrenDto).then((response: any) => {
                    if(response && response.id) {
                        newModel = response;
                    } else {
                        this.entityError = L('Something went wrong. Try again later or contact with administrator.');
                    }
                }).catch((error: any) => {
                    console.error(error);
                    this.entityError = catchErrorMessage(error);
                });
                break;
            case PolicyType.Home:
                await claimService.getHome({ id: entityId } as ClaimHomeDto).then((response: any) => {
                    if(response && response.id) {
                        newModel = response;
                    } else {
                        this.entityError = L('Something went wrong. Try again later or contact with administrator.');
                    }
                }).catch((error: any) => {
                    console.error(error);
                    this.entityError = catchErrorMessage(error);
                });
            break;
            case PolicyType.Life:
                await claimService.getLife({ id: entityId } as ClaimLifeDto).then((response: any) => {
                    if(response && response.id) {
                        newModel = response;
                    } else {
                        this.entityError = L('Something went wrong. Try again later or contact with administrator.');
                    }
                }).catch((error: any) => {
                    console.error(error);
                    this.entityError = catchErrorMessage(error);
                });
            break;
            case PolicyType.Travel:
                await claimService.getTravel({ id: entityId } as ClaimTravelDto).then((response: any) => {
                    if(response && response.id) {
                        newModel = response;
                    } else {
                        this.entityError = L('Something went wrong. Try again later or contact with administrator.');
                    }
                }).catch((error: any) => {
                    console.error(error);
                    this.entityError = catchErrorMessage(error);
                });
            break;
            case PolicyType.Vehicle:
                await claimService.getVehicle({ id: entityId } as ClaimVehicleDto).then((response: any) => {
                    if(response && response.id) {
                        newModel = response;
                    } else {
                        this.entityError = L('Something went wrong. Try again later or contact with administrator.');
                    }
                }).catch((error: any) => {
                    console.error(error);
                    this.entityError = catchErrorMessage(error);
                });
            break;
            default:
                await this.props.store.get({ id: entityId } as ClaimDto).then((response: any) => {
                    if(response && response.id) {
                        newModel = response;
                    } else {
                        this.entityError = L('Something went wrong. Try again later or contact with administrator.');
                    }
                }).catch((error: any) => {
                    console.error(error);
                    this.entityError = catchErrorMessage(error);
                });
        }
        this.props.payload.model = newModel;
        this.dataDownloaded = true;
        this.forceUpdate();
    }

    _onCancel = () => {
        this.props.onClose(true);
        this.setState({ model: { error: {}, value: {} } });
        
        setTimeout(() => {
            this.prevEntityId = 0;
        }, 500);
    }

    _onConfirm = async () => {
        this.props.onClose(true);
        this.setState({ model: { error: {}, value: {} } });
        
        setTimeout(() => {
            this.prevEntityId = 0;
        }, 500);
    }

    renderContent() {
        if(typeof this.props.payload.entityId === 'number' && this.props.payload.entityId > 0 && this.props.payload.entityId !== this.prevEntityId) {
            this.prevEntityId = this.props.payload.entityId;
            this.getEntityModel(this.prevEntityId.toString());
        } else if(this.dataDownloaded && (this.state.model.value.id === 0 || this.state.model.value.id === "")) {
            this.prevEntityId = 0;
            this.dataDownloaded = false;
        }

        return (!!this.entityError ? 
            <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`}>
                {this.entityError}
            </MessageBar>
            : 
            <ClaimContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ClaimDto } />);
    }
}