import { inject, observer } from 'mobx-react';

import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { InsuranceCompanyContentView } from './components/insuranceCompanyContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import InsurerStore from '../../stores/insurerStore';
import { InsurerDto } from '../../services/insurer/dto/insurerDto';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0',
	},
});

export interface IProps {
	insurerStore: InsurerStore;
	match: any;
}

@inject(Stores.InsurerStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private insuranceCompanyId = this.props.match.params.id;

	async componentDidMount() {
		await this.props.insurerStore.get({ id: this.insuranceCompanyId } as InsurerDto);
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<InsuranceCompanyContentView store={this.props.insurerStore} payload={ this.props.insurerStore.model as InsurerDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;