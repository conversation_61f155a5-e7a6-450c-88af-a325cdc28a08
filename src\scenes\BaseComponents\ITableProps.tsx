import { SelectionMode, Selection } from '@fluentui/react';
import { EntityStringDto } from '../../services/dto/entityStringDto';
import { CrudStoreBase } from '../../stores/crudStoreBase';

export interface ITableProps<TDto extends EntityStringDto> {
  store: CrudStoreBase<TDto>;
  items: TDto[];
  searchText: string | undefined;
  history?: any;
  scrollablePanelMarginTop?: number,
  selectionMode?: SelectionMode,
  customSelection?: Selection,
  customData?: any,
  showFilters?: boolean,
  hideColumnsInFilters?: string[],
  hideColumnsInFiltersByName?: string[],
  showFiltersInSingleDialog?: boolean,
  getAll?: () => void;
  getAllAsync?: () => Promise<void>;
  refreshItems?: (customData?: any) => Promise<void>;
  onFillDataButtonClick?: () => void;
  customOnSelectionChanged?: (selection: any) => void;
  customOnSearchTextChanged?: (text: string) => void;
}
