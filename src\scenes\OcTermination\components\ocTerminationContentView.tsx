import { IChoiceGroupOption, IDropdownOption, Pivot, PivotItem, Spinner, SpinnerSize, mergeStyleSets, Selection, SelectionMode, ComboBox, IComboBox, IComboBoxOption } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { myTheme } from '../../../styles/theme';
import { DropdownBase } from '../../BaseComponents/dropdownBase';
import { LabeledTextField } from '../../../components/LabeledTextField';
import { enumToDropdownOptions } from '../../../utils/utils';
import { defaultOcTermination } from '../../../stores/ocTerminationStore';
import { OcTerminationDto } from '../../../services/ocTermination/dto/ocTerminationDto';
import { DatePickerBase } from '../../BaseComponents/datePickerBase';
import { InsuranceCompanyNewOffer } from '../../../services/calculation/dto/insuranceCompanyNewOfferEnum';
import { OcTerminationReason } from '../../../services/calculation/dto/ocTerminationReasonEnums';
import { ChoiceGroupBase } from '../../BaseComponents/ChoiceGroupBase';
import { CustomerFluentListBase } from '../../BaseComponents/customerFluentListBase';
import { spinnerClassNames } from '../../../styles/spinnerStyles';
import { fluentTableClassNames } from '../../../styles/fluentTableStyles';
import { inject } from 'mobx-react';
import Stores from '../../../stores/storeIdentifier';
import { CrudConsts } from '../../../stores/crudStoreBase';
import vehicleService from '../../../services/vehicle/vehicleService';
import { LabelContainerComponent } from '../../BaseComponents/labelContainerComponent';
import { LabelComponent } from '../../BaseComponents/labelComponent';

const classNames = mergeStyleSets({
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    customChoiceGroupContainer: {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
    },
    comboBoxStyles: {
        width: '300px',
        position: 'relative',
    },
    comboBoxRequiredMark: {
        selectors: {
            '::before': {
                content: "'*'",
                position: 'absolute',
                top: '-5px',
                right: '-10px',
                color: myTheme.palette.redDark,
            },
        }
    }
});

@inject(Stores.ClientStore)
export class OcTerminationContentView extends GenericContentView {
    private ocTermination: OcTerminationDto = defaultOcTermination;
    private formData: any = {
        policyNumber: '' as string,
        insuranceCompanyName: '' as string,
        terminationDate: '' as string,
        terminationReason: '' as string,
        oldInsurerName: '' as string,
        oldPolicyNumber: '' as string,
    };
    private terminationGroundValues: any = {
        [OcTerminationReason.Last_period_day]: L('Wypowiedzenie z końcem okresu ubezpieczenia art. 28'),
        [OcTerminationReason.Another_insurer]: L('Wypowiedzenie podwójnego ubezpieczenia art. 28a'),
        [OcTerminationReason.After_vehicle_acquiring]: L('Wypowiedzenie przez nabywcę pojazdu art. 31'),
    };
    private isEditMode: boolean | null = null;
    private isClientPreselected: boolean | null = null;
    private vehicleOptions: any = {
        dropdown: [] as IDropdownOption[],
    };
    private selectedClientFullName: string = "";
    private selectClientSearchText: string = "";
    private _clientListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedClient: any = this._clientListSelection.getSelection();
            if(Array.isArray(selectedClient) && selectedClient.length > 0 && !!selectedClient[0].id) {
                this.ocTermination.clientId = selectedClient[0].id.toString();
                this.ocTermination.clientFullName = selectedClient[0].user.fullName;
                this.ocTermination.address = `${selectedClient[0].stateProvinceId}, ${selectedClient[0].county}, ${selectedClient[0].city}, ${selectedClient[0].streetAddress} ${selectedClient[0].streetAddress2}`;
                this.selectedClientFullName = selectedClient[0].user.fullName;
                this._clientListSelection.setAllSelected(false);
                this.onCustomerSelect(selectedClient[0]);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });

    async componentDidMount() {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        await this.props.clientStore?.getAll({...this.props.clientStore?.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE});

        this.asyncActionInProgress = false;
        this.forceUpdate();

        const tempOcTermination: OcTerminationDto = this.props.payload.model ? this.props.payload.model : this.props.payload;
        if(tempOcTermination && parseInt(tempOcTermination.id) > 0) {
            this.isEditMode = true;
            if(!!tempOcTermination.clientId && this.vehicleOptions.dropdown.length === 0) {
                this.onCustomerSelect(undefined, tempOcTermination.clientId.toString());
            }

            this.formData.policyNumber = tempOcTermination.policyNumber;
            this.formData.insuranceCompanyName = tempOcTermination.insurerName;
            this.formData.terminationDate = new Date(tempOcTermination.terminationDate).toDateString();
            this.formData.terminationReason = tempOcTermination.terminationGround as string;
            this.formData.oldInsurerName = tempOcTermination.oldInsurerName;
            this.formData.oldPolicyNumber = tempOcTermination.oldPolicyNumber;
        } else {
            this.isEditMode = false;

            if(this.props.additionalPayload?.payloadType === "customer" && this.props.additionalPayload?.payloadId && this.props.additionalPayload?.payloadId?.length > 0) {
                this.onCustomerSelect(undefined, this.props.additionalPayload?.payloadId);
                this.isClientPreselected = true;
            }
        }

        this.forceUpdate();
    }

    private async onCustomerSelect(customer: any, clientId?: string) {
        if(this.asyncActionInProgress) return;
        let newModel = this.state.model;

        if((customer && typeof customer !== 'undefined') || !!clientId) {
            let choosenClientId: string = !!clientId ? clientId : customer.id;
            if(!!clientId) {
                const splittedClientId: string[] = clientId.split('||');
                if(clientId.length > 1) {
                    choosenClientId = splittedClientId[0];
                    newModel.value["clientFullName"] = !!splittedClientId[1] ? splittedClientId[1] : 
                                                            (customer && customer.user && !!customer.user.fullName ? customer.user.fullName : this.ocTermination.clientFullName);
                }
            }

            newModel.value["clientId"] = choosenClientId;

            this.asyncActionInProgress = true;
            this.forceUpdate();

            await vehicleService.getByClientId(newModel.value["clientId"]).then((response: any) => {
                if(response.totalCount > 0) {
                    // this.insurancePolicies = response.items;
                    this.vehicleOptions.dropdown = response.items.map((item: any) => {
                        return { key: item.registrationNumber, 
                                text: `${item.registrationNumber} | ${item.vehicleInfo} | ${item.productionYear}`, 
                                isSelected: this.ocTermination.registrationNumber === item.registrationNumber };
                    }) as IDropdownOption[];
                } else {
                    this.vehicleOptions.dropdown = [];
                }
            });

            this.asyncActionInProgress = false;
            this.forceUpdate();
        } else {
            newModel.value["clientId"] = "";
            this.vehicleOptions.dropdown = [];
        }
        this.setState({ model: newModel });
    }

    private validateFormData(): boolean {
        let hasAnyError: boolean = false;

        for(let key in this.formData) {
            if(this.formData.hasOwnProperty(key) && (!this.formData[key] || this.formData[key].length === 0)) {
                if(['insuranceCompanyName', 'policyNumber'].includes(key) && this.formData.terminationReason !== OcTerminationReason.Another_insurer) {
                    continue;
                }
                hasAnyError = true;
            }
        }

        if(!this.ocTermination || !this.ocTermination.clientId || this.ocTermination.clientId <= 0) {
            hasAnyError = true;
        }

        return hasAnyError;
    }

    renderContent() {
        this.ocTermination = this.props.payload.model ? this.props.payload.model : this.props.payload; 

        const currentInputErrorsCount: number = this.props.customData && this.props.customData.inputErrorsCount ? this.props.customData.inputErrorsCount : 0;
        if(this.validateFormData() && typeof this.props.onSetInputErrorsCount === 'function' && currentInputErrorsCount <= 0) {
            this.props.onSetInputErrorsCount(1);
        } else if(!this.validateFormData() && typeof this.props.onSetInputErrorsCount === 'function' && currentInputErrorsCount > 0) {
            this.props.onSetInputErrorsCount(0);
        }

        return <Pivot theme={myTheme}>
            <PivotItem headerText={L('General')} key={'General'}>
                {(this.isEditMode === false && this.props.clientStore && !this.isClientPreselected) && 
                    <div className={fluentTableClassNames.contentContainer}>
                        <CustomerFluentListBase
                            // searchText={this.selectClientSearchText}
                            searchText={undefined}
                            items={this.props.clientStore.dataSet && this.props.clientStore.dataSet.items ? this.props.clientStore.dataSet.items : []}
                            store={this.props.clientStore}
                            history={this.props.history}
                            scrollablePanelMarginTop={70}
                            customData={{ 
                                selectedClient: !!this.selectedClientFullName ? `${this.selectedClientFullName}` : undefined,
                            }}
                            // customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                            customSelection={this._clientListSelection}
                            customOnSelectionChanged={(selection: any) => {
                                if(typeof selection === 'string' && selection === 'deleteClient') {
                                    this.ocTermination.clientId = defaultOcTermination.clientId;
                                    this.selectedClientFullName = "";
                                    this.forceUpdate();
                                }
                            }}
                        />
                    </div>
                }
                
                {this.asyncActionInProgress &&
                    <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                }

                {(this.isEditMode === true || this.isClientPreselected === true) && 
                    <LabeledTextField
                        isDataLoaded={true}
                        label={L("Customer")}
                        disabled={this.isEditMode || this.isClientPreselected === true || this.asyncActionInProgress}
                        value={this.ocTermination.clientFullName}
                        required={!this.isEditMode}
                    />
                }

                <LabelContainerComponent>
                    <LabelComponent label={L("Registration number")} required={true} />  
                    
                    <ComboBox
                        label={''}
                        text={this.ocTermination.registrationNumber}
                        required={true}
                        allowFreeform={true}
                        autoComplete={'on'}
                        options={this.vehicleOptions.dropdown}
                        className={`${classNames.comboBoxStyles} ${!this.isEditMode ? classNames.comboBoxRequiredMark : ''}`}
                        key={`RegistrationNumberComboBox`}
                        disabled={this.asyncActionInProgress}
                        onChange={(event: React.FormEvent<IComboBox>, option?: IComboBoxOption, index?: number, value?: string) => {
                            if(option && !!option.key && !!option.text) {
                                this.ocTermination.registrationNumber = typeof option.key === 'number' ? option.key.toString() : option.key;
                                this.forceUpdate();
                            } else if(typeof value !== 'undefined') {
                                const splitedValue = value.split(' | ');

                                this.ocTermination.registrationNumber = splitedValue[0];
                                this.forceUpdate();
                            }
                        }}
                        onPendingValueChanged={(option?: IComboBoxOption, index?: number, value?: string) => {
                            if(typeof value !== 'undefined') {
                                const splitedValue = value.split(' | ');

                                this.ocTermination.registrationNumber = splitedValue[0];
                                this.forceUpdate();
                            }
                        }} 
                    />
                </LabelContainerComponent>

                <LabeledTextField
                    isDataLoaded={true}
                    label={L("Terminated policy number")}
                    disabled={this.asyncActionInProgress}
                    value={this.formData.oldPolicyNumber}
                    required={true}
                    onChange={(event: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string | undefined) => {
                        if(typeof newValue !== 'undefined') {
                            this.formData.oldPolicyNumber = newValue;
                            this.ocTermination.oldPolicyNumber = newValue;
                            this.forceUpdate();
                        }
                    }}
                />

                <DatePickerBase
                    isDataLoaded={true}
                    label={L("Termination date")}
                    disabled={this.asyncActionInProgress}
                    value={this.formData.terminationDate}
                    required={true}
                    validationData={{'minDate': 'SUBTRACT_7_DAYS'}}
                    onChange={(value: string | undefined) => {
                        if(!!value) {
                            this.formData.terminationDate = value;
                            this.ocTermination.terminationDate = value;
                            this.forceUpdate();
                        }
                    }}
                />

                <DropdownBase 
                    isDataLoaded={true}
                    label={L("Terminated policy insurance company name")}
                    disabled={this.asyncActionInProgress}
                    options={enumToDropdownOptions(InsuranceCompanyNewOffer, true, false, 'string')} 
                    value={this.formData.oldInsurerName}
                    required={true}
                    onChange={(value: string | number | undefined) => {
                        if(typeof value !== 'undefined') {
                            this.formData.oldInsurerName = value;
                            this.ocTermination.oldInsurerName = typeof value === 'number' ? value.toString() : value;
                            this.forceUpdate();
                        }
                    }}
                />

                <ChoiceGroupBase
                    label={L("The article with which the insurance contract is terminated")}
                    disabled={this.asyncActionInProgress}
                    options={[
                        {key: OcTerminationReason.Last_period_day as string, text: this.terminationGroundValues[OcTerminationReason.Last_period_day]},
                        {key: OcTerminationReason.Another_insurer as string, text: this.terminationGroundValues[OcTerminationReason.Another_insurer]},
                        {key: OcTerminationReason.After_vehicle_acquiring as string, text: this.terminationGroundValues[OcTerminationReason.After_vehicle_acquiring]},
                    ]}
                    value={this.formData.terminationReason}
                    required={true}
                    customContainerClassNames={classNames.customChoiceGroupContainer}
                    onChange={(option: IChoiceGroupOption | undefined) => {
                        if(option && !!option.key) {
                            this.formData.terminationReason = option.key as string;
                            this.ocTermination.terminationGround = option.key as OcTerminationReason;
                            this.forceUpdate();
                        }
                    }}
                />

                {this.formData.terminationReason === OcTerminationReason.Another_insurer &&
                    <>
                        <DropdownBase 
                            isDataLoaded={true}
                            label={L("Insurance company name")}
                            disabled={this.asyncActionInProgress}
                            options={enumToDropdownOptions(InsuranceCompanyNewOffer, true, false, 'string')} 
                            value={this.formData.insuranceCompanyName}
                            required={true}
                            onChange={(value: string | number | undefined) => {
                                if(typeof value !== 'undefined') {
                                    this.formData.insuranceCompanyName = value;
                                    this.ocTermination.insurerName = typeof value === 'number' ? value.toString() : value;
                                    this.forceUpdate();
                                }
                            }}
                        />

                        <LabeledTextField
                            isDataLoaded={true}
                            label={L("Policy number")}
                            disabled={this.asyncActionInProgress}
                            value={this.formData.policyNumber}
                            required={true}
                            onChange={(event: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>, newValue?: string | undefined) => {
                                if(typeof newValue !== 'undefined') {
                                    this.formData.policyNumber = newValue;
                                    this.ocTermination.policyNumber = newValue;
                                    this.forceUpdate();
                                }
                            }}
                        />
                    </>
                }
            </PivotItem>
        </Pivot>
    }
}