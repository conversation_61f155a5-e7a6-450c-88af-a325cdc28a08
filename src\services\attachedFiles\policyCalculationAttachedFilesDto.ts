import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";

export interface PolicyCalculationAttachedFilesDto extends BaseApiEntityModel {
  // id: number;
  fileUrl?: string,
  originalFileName?: string,
  blobFileName?: string,
  policyCalculationId: number,
  status: string,
  type: string,
  recommended: boolean,
  insurer: string | null,
  customerId: string,
  // lastModificationTime: string,
  // lastModifierUserId: number,
  // creationTime: string,
  // creatorUserId: number,
}