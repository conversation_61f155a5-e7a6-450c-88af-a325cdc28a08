import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { OcTerminationContentView } from './components/ocTerminationContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import { OcTerminationDto } from '../../services/ocTermination/dto/ocTerminationDto';
import OcTerminationStore from '../../stores/ocTerminationStore';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	ocTerminationStore: OcTerminationStore;
	match: any,
	location: any,
}

@inject(Stores.OcTerminationStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private ocTerminationId: string = this.props.match.params.id;

	private query = new URLSearchParams(this.props.location.search);
	private entityId = this.query.get('entityId');
	private entityType = this.query.get('entityType');

	async componentDidMount() {
		if(!!this.ocTerminationId && parseInt(this.ocTerminationId) > 0) {
			await this.props.ocTerminationStore.get({ id: this.ocTerminationId } as OcTerminationDto);		
		}
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<OcTerminationContentView store={this.props.ocTerminationStore} payload={ this.props.ocTerminationStore.model as OcTerminationDto } 
					additionalPayload={{ payloadType: this.entityType, payloadId: this.entityId }} renderFooter={{show: true}}
				/>
			</FocusZone>
		);
	}
}

export default Index;