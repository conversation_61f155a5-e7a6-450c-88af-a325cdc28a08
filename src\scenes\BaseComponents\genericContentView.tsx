import { MessageBarType } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import AppConfig from '../../lib/appconfig';
import CalculationStore from '../../stores/calculationStore';
import VehicleStore from '../../stores/vehicleStore';
import ClaimStore from '../../stores/claimStore';
import ClientStore from '../../stores/clientStore';
import CountryStore from '../../stores/countryStore';
import CustomerGroupStore from '../../stores/customerGroupStore';
import InsurancePolicyStore from '../../stores/insurancePolicyStore';
import LanguageStore from '../../stores/languageStore';
import OrderStore from '../../stores/orderStore';
import ProductAttributeStore from '../../stores/productAttributeStore';
import ProductStore from '../../stores/productStore';
import ContentViewBase from './contentViewBase';
import { IContentViewProps } from './IContentViewProps';
import UserCrudStore from '../../stores/userCrudStore';
import CountryItemsStore from '../../stores/countryItemsStore';
import SportDisciplineStore from '../../stores/sportDisciplineStore';
import SportInsuranceCoverageStore from '../../stores/sportInsuranceCoverageStore';
import RoleStore from '../../stores/roleStore';
import InsurerStore from '../../stores/insurerStore';
import ApkAttachedFilesStore from '../../stores/apkAttachedFilesStore';
import AgentClaimStore from '../../stores/agentClaimStore';
import AgentClaimHistoryStore from '../../stores/agentClaimHistoryStore';
import AgentClaimAmeStatusStore from '../../stores/agentClaimAmeStatusStore';
import TravelCountryCoverageStore from '../../stores/travelCountryCoverageStore';
import TravelCountryStore from '../../stores/travelCountryStore';
import PrizeStore from '../../stores/prizeStore';
import ClientLeasingStore from '../../stores/clientLeasingStore';
import ContestStore from '../../stores/contestStore';
import IndividualOfferStore from '../../stores/individualOfferStore';
import OcTerminationStore from '../../stores/ocTerminationStore';
import UserAgencyLoginStore from '../../stores/userAgencyLoginStore';

export interface IGenericContentViewProps extends IContentViewProps {
    apkAttachedFilesStore?: ApkAttachedFilesStore;
    calculationStore?: CalculationStore;
    productAttributeStore?: ProductAttributeStore;
    claimStore?: ClaimStore;
    agentClaimStore?: AgentClaimStore;
    agentClaimHistoryStore?: AgentClaimHistoryStore;
    agentClaimAmeStatusStore?: AgentClaimAmeStatusStore;
    productStore?: ProductStore;
    prizeStore?: PrizeStore;
    clientStore?: ClientStore;
    clientLeasingStore?: ClientLeasingStore;
    contestStore?: ContestStore;
    languageStore?: LanguageStore;
    countryStore?: CountryStore;
    countryItemsStore?: CountryItemsStore;
    sportDisciplineStore?: SportDisciplineStore;
    sportInsuranceCoverageStore?: SportInsuranceCoverageStore;
    travelCountryCoverageStore?: TravelCountryCoverageStore;
    vehicleStore?: VehicleStore;
    roleStore?: RoleStore;
    insurancePolicyStore?: InsurancePolicyStore;
    customerGroupStore?: CustomerGroupStore;
    userCrudStore?: UserCrudStore;
    insurerStore?: InsurerStore;
    userAgencyLoginStore?: UserAgencyLoginStore;
    individualOfferStore?: IndividualOfferStore;
    orderStore?: OrderStore;
    travelCountryStore?: TravelCountryStore;
    ocTerminationStore?: OcTerminationStore;
    isBulkOperation?: boolean;
    policyCalculation?: {
        payloadType: string | null;
        payloadId: string | null;
    },
    additionalPayload?: {
        payloadType: string | null;
        payloadId: string | null;
    },
    history?: any;
    customData?: any;
    inputErrorsCount?: number;
    createOrUpdate?: (values: any) => Promise<void>;
    toggleConfirm?: (show: boolean) => void;
    toggleHideConfirm?: (hide: boolean) => void;
    onSetInputErrorsCount?: (count: number) => void;
}

export class GenericContentView extends ContentViewBase<IGenericContentViewProps> {
    protected isDataLoaded = false;

    checkIfDataIsLoaded(entityType: string) {
        if((this.props.payload.entityId && this.props.payload.entityId.length > 0) || typeof this.props.payload.entityId === 'undefined') {
            let intervalCounter = 0;
            let waitForDataInterval = setInterval(() => { // interval to prevent UI blinking when data is loaded to early 
                intervalCounter++;

                this.isDataLoaded = this[entityType] && this[entityType].Id && this[entityType].Id.length > 0 ? true : false;
                if((this[entityType] && typeof this[entityType].id === 'number' && this[entityType].id > 0) || (this[entityType] && typeof this[entityType].id === 'string' && this[entityType].id.length > 0)) {
                    this.isDataLoaded = true;
                }

                if(this.isDataLoaded === true) {
                    clearInterval(waitForDataInterval);
                    this.forceUpdate();
                } else if(intervalCounter === AppConfig.defaultLoadDataTimeout) {
                    this.setState((prevState) => ({ ...prevState, message: { text: L('Connection timeout.'), type: MessageBarType.error } }));
                    this.isDataLoaded = true;
                    clearInterval(waitForDataInterval);
                    this.forceUpdate();
                }
            }, AppConfig.defaultLoadDataInterval);
        } else {
            this.isDataLoaded = true;
            this.forceUpdate();
        }
    }

    async onConfirm(): Promise<Boolean> {
        let confirmed = await super.onConfirm();
        if (!confirmed) return false;
        // todo handle http errors
        if (this.props.createOrUpdate) {
            await this.props.createOrUpdate(this.state.model.value);
            this.toggleAsyncActionInProgressFlag(false, false);
        } else {
            await this.createOrUpdate(this.state.model.value);
            this.toggleAsyncActionInProgressFlag(false, true);
        }

        return true;
    }

    async createOrUpdate(values: any) {
        if (values && values.id) {
            // update
            await this.props.store.update(values);
            if (this.props.store.dataSet) {
                await this.props.store.getUpdated([values.id]);
            }
        } else{
            // create
            let result = await this.props.store.create(values);
            if (result && result.id)
                await this.props.store.getUpdated([result.id]);
        }
        this.setState({ message: { text: L('Saved sucessfully.'), type: MessageBarType.success } });
    }
}

export class DefinitionParser {
    static generateDefinition(object: any): ITypeDefinition[] {
        let array: ITypeDefinition[] = [];
        for (let prop in object) {
            if (!object.hasOwnProperty(prop)) continue;
            array.push({
                name: prop,
                type: typeof (object[prop]),
                members: []
            });
        }
        return array;
    }
}

export interface ITypeDefinition {
    // Name of type which will be referenced everywhere - defautl endpoint Path it is ! YODA
    name: string;
    // Type like DateTime, String, Number, Boolean, Reference Type One(Object), Reference Type Many(Array)
    // if it's reference ask it's many or one
    type: string;
    // Controls mean, that you want to change control type to some custome one
    control?: string;
    // Endpoint override path - if null name property is path
    endpoint?: string;
    members: ITypeDefinition[];
}