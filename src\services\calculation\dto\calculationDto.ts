import { ClientDto } from "../../client/dto/clientDto";
import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";
import { CalculationStatusEnum } from "../calculationStatusEnums";

export interface CalculationDto extends BaseApiEntityModel {
    client: ClientDto,
    clientId: number,
    creationTime: string,
    creatorUserId: number,
    deleterUserId: number | null,
    deletionTime: number | null,
    isDeleted: boolean,
    lastModificationTime: number | null,
    lastModifierUserId: number | null,
    payload: string,
    vehicleRegistrationNumber: string,
    lastCreatedPolicyStatus?: CalculationStatusEnum,
}