import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { TestSetContentView } from '../../TestSet/components/testSetContentView';
import { TestSetDto } from '../../../services/testSet/dto/testSetDto';

export class TestSetPanel extends GenericPanel {
    getPanelTitle(): string {
        return L("TestSet");
    }

    renderContent() {
        return <TestSetContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as TestSetDto } />;
    }
}