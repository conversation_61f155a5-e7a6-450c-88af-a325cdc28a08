import { ProductTypeDto } from '../../../services/productType/productTypeDto';
import { ProductTypeContentView } from '../../ProductType/components/productTypeContentView';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';

export class ProductTypePanel extends GenericPanel {
    getPanelTitle(): string {
        return L("Product type");
    }

    renderContent() {
        return <ProductTypeContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ProductTypeDto } />;
    }
}