import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { CommunityDto } from '../../../services/community/dto/communityDto';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { CommunityPanel } from './communityPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { dateFormat } from "../../../utils/utils";

export class CommunityTable extends FluentTableBase<CommunityDto> {
  
  getColumns(): ITableColumn[] {
    return CommunityTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('ID'),
        fieldName: 'id',
      },
      {
        name: L('Number of users'),
        fieldName: 'numberOfUsers',
      },
      {
        name: L('Number of purchased policies'),
        fieldName: 'numberOfPurchasedPolicies',
      },
      {
        name: L('Number of claims'),
        fieldName: 'numberOfClaims',
      },
      {
        name: L('Average time of concluding policy agreement'),
        fieldName: 'averageTimeOfConcludingPolicyAgreement',
      },
      {
        name: L('App rank'),
        fieldName: 'appRank',
      },
      {
        name: L('Creation time'),
        fieldName: 'creationTime',
        onRender: (item: any): any => {
          return dateFormat(item.creationTime, "DD.MM.YYYY HH:mm", true);
        }
      },
    ];
  }

  getTitle(): string {
    return L('Communities');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: false,
      delete: false,
      customActions: false,
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <CommunityPanel
        {...props}
      />
    </>
  }
}