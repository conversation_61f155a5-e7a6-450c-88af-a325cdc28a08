import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { UserDto } from '../../services/user/dto/userDto';
import { ProfileContentView } from './components/profileContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import userService from '../../services/user/userCrudService';
import UserCrudStore from '../../stores/userCrudStore';

declare var abp: any;

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	userCrudStore: UserCrudStore;
	match: any
}

@inject(Stores.UserCrudStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private userId: string = abp.session.userId;
	private response: any = {};
	
	async componentDidMount() {
		this.response = await userService.get({ id: this.userId } as UserDto);
		this.props.userCrudStore.model = this.response;
		this.forceUpdate();
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<ProfileContentView store={this.props.userCrudStore} payload={ this.props.userCrudStore.model as UserDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;