window._app_ = {
    "LOAD_DATA_TIMEOUT": 10, // seconds, after this time stop waiting for data and display timeout error message
    "LOAD_DATA_INTERVAL": 1000, // miliseconds, check if data is loaded after initial X miliseconds and keep shimmer animation for at least that time
    "PAGE_PATH_TO_ICON_PAIRS": { // route path as keys and Fluent UI icon name as value
        '/dashboard': 'Home',
        '/about': 'Info',
        '/calculation-list': 'ReportDocument',
        '/customer-list': 'Reminder<PERSON>erson',
        '/product-list': 'ProductList',
        '/product-type-list': 'BacklogList',
        '/order-list': 'WaitlistConfirm',
        '/user-list': 'People',
        '/insurance-company-list': 'Teamwork',
        '/insurance-policy-list': 'EntitlementPolicy',
        '/role-list': 'ContactInfo',
        '/claim-list': 'ReportWarning',
        '/loyalty-points-history-list': 'FavoriteList',
        '/oc-termination-list': 'PageRemove',
        '/pattern-list': 'ViewOriginal',
        '/rating-list': 'FavoriteStar',
        '/community-list': 'JoinOnlineMeeting',
        '/customer-group-list': 'Group',
        '/test-set-list': 'TestCase',
        '/vehicle-list': 'ParkingLocation',
        '/apk-list': 'FormLibrary',
        '/contest-list': 'Savings',
        '/settings': 'Settings',
        '/agent-claim-list': 'Admin',
    },
    "CALCULATION_ALL_STEPS": 4,
    "APK_FORM_ALL_STEPS": 2,
    "POLICY_FORM_ALL_STEPS": 3,
    "POLICY_FORM_INSURER_DATA_TAB_INDEX": 4,
    "TAB_NUMBER_FOR_CUSTOM_INPUT_IN_MOTOR_INSURANCE": 2,
    "EUROTAX_COLLECT_DATA_MAX_CONNECTION_ERRORS_COUNT": 5,
    "COUNT_OF_SUGGESTED_COUNTRIES_IN_DROPDOWN": 1,
    "TESTS_ADMIN_PASSWORD": '123qwe',
    "UNIQA_NNW_STEP_3_INSURANCE_OPTIONS_PDF_URL": "https://toptmp.blob.core.windows.net/public-files/NNW%20Dziecka_UNIQA.pdf",
    "GENERALI_NNW_STEP_3_INSURANCE_OPTIONS_PDF_URL": "https://toptmp.blob.core.windows.net/public-files/NNW%20Dziecka_Generali.pdf",
}