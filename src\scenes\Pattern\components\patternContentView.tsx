import { Pivot, PivotItem } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { Controls } from '../../BaseComponents/controls';
import { PatternDto } from '../../../services/pattern/dto/patternDto';
import { defaultPattern } from '../../../stores/patternStore';

export class PatternContentView extends GenericContentView {
    private pattern: PatternDto = defaultPattern;

    componentDidMount() {
        this.checkIfDataIsLoaded("pattern");
    }

    renderContent() {
        this.pattern = this.props.payload.model ? this.props.payload.model : this.props.payload;

        const pivotStyles = {
            root: {
              marginLeft: '-8px'
            },
            linkIsSelected: {
              color: myTheme.palette.red,
              selectors: {
                ':before': {
                  height: '5px',
                  backgroundColor: additionalTheme.darkerRed
                }
              }
            }
          };

        return <Pivot theme={myTheme} styles={pivotStyles}>
            <PivotItem headerText={L('General')} key={'General'}>
                {this.renderElement(new ContentViewModelProperty('patternType', L('Pattern type'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'patternType': this.pattern.patternType})}
                {this.renderElement(new ContentViewModelProperty('value', L('Value'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 20}), [], {'value': this.pattern.value})}
                {this.renderElement(new ContentViewModelProperty('description', L('Description'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'description': this.pattern.description})}
            </PivotItem>
        </Pivot>
    }
}