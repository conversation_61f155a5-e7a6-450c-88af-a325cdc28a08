import { IDropdownOption, Pivot, PivotItem } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { myTheme } from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { defaultAgentClaimHistory } from '../../../stores/agentClaimHistoryStore';
import { AgentClaimHistoryDto } from '../../../services/agentClaim/dto/agentClaimHistoryDto';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { Controls } from '../../BaseComponents/controls';
import { DropdownBase } from '../../BaseComponents';
import Stores from '../../../stores/storeIdentifier';
import { inject } from 'mobx-react';

@inject(Stores.ClientStore)
export class AgentClaimHistoryContentView extends GenericContentView {
    private agentClaimHistory: AgentClaimHistoryDto = defaultAgentClaimHistory;
    private productOptions: any = {
        dropdown: [{key: 'DECYZJA_TU_ODMOWA', text: 'DECYZJA TU ODMOWA'},
        {key: 'DECYZJA_TU_ODMOWA_BRAK_DOKUMENTOW', text: 'DECYZJA TU ODMOWA BRAK DOKUMENTOW'},
        {key: 'DECYZJA_TU_ODMOWA_PO_REKLAMACJI', text: 'DECYZJA TU ODMOWA PO REKLAMACJI'},
        {key: 'DECYZJA_TU_ODMOWA_PRAWNIK_PO_REKLAMACJI', text: 'DECYZJA TU ODMOWA PRAWNIK PO REKLAMACJI'},
        {key: 'DECYZJA_TU_ODMOWA_PRAWNIK_REKLAMACJA_WYPŁATA', text: 'DECYZJA TU ODMOWA PRAWNIK REKLAMACJA WYPŁATA'},
        {key: 'DECYZJA_TU_ODMOWA_REKLAMACJA_WYPŁATA', text: 'DECYZJA TU ODMOWA REKLAMACJA WYPŁATA'},
        {key: 'DECYZJA_TU_ODMOWA_WYPŁATA', text: 'DECYZJA TU ODMOWA WYPŁATA'},
        {key: 'MONIT_DO_KLIENTA_NR_1', text: 'MONIT DO KLIENTA NR 1'},
        {key: 'MONIT_DO_KLIENTA_NR_2', text: 'MONIT DO KLIENTA NR 2'},
        {key: 'MONIT_DO_TU_14_DNI', text: 'MONIT DO TU 14 DNI'},
        {key: 'MONIT_DO_TU_7_DNI', text: 'MONIT DO TU 7 DNI'},
        {key: 'PRAWNIK_POSTEPOWANIE_SADOWE', text: 'PRAWNIK POSTEPOWANIE SADOWE'},
        {key: 'PRAWNIK_REKLAMACJA', text: 'PRAWNIK REKLAMACJA'},
        {key: 'REKLAMACJA_W_TRAKCIE_PRZYGOTOWYWANIA', text: 'REKLAMACJA W TRAKCIE PRZYGOTOWYWANIA'},
        {key: 'REKLAMACJA_WYSLANA', text: 'REKLAMACJA WYSLANA'},
        {key: 'W_TRAKCIE_LIKWIDACJI', text: 'W TRAKCIE LIKWIDACJI'},
        {key: 'WPROWADZONA', text: 'WPROWADZONA'},
        {key: 'ZAKONCZONA_ODMOWA_PO_REKLAMACJI', text: 'ZAKONCZONA ODMOWA PO REKLAMACJI'},
        {key: 'ZAKONCZONA_PRAWNIK_ODMOWA_PO_REKLAMACJI', text: 'ZAKONCZONA PRAWNIK ODMOWA PO REKLAMACJI'},
        {key: 'ZAKONCZONA_WYCOFANA_PRZEZ_KLIENTA', text: 'ZAKONCZONA WYCOFANA PRZEZ KLIENTA'},] as IDropdownOption[],
    };

    async componentDidMount() {
        this.checkIfDataIsLoaded("agentClaimHistory");

        if(this.props.customData.agentClaim.agentId) {
            this.agentClaimHistory.userId = this.props.customData.agentClaim.agentId
        }

        this.forceUpdate();
    }

    renderContent() {   
        this.agentClaimHistory = this.props.payload.model ? this.props.payload.model : this.props.payload;
        this.agentClaimHistory.claimId = this.props.customData.agentClaim.id;
        
        return (
            <Pivot theme={myTheme}>
                <PivotItem>
                    {this.renderElement(new ContentViewModelProperty("claimId", L("Claim Id"), Controls.Text, false, [], true, { isDataLoaded: this.isDataLoaded }), [], { claimId: this.agentClaimHistory.claimId })}
                    {this.renderElement(new ContentViewModelProperty("noteDate", L("Note Date"), Controls.Date, true, [], false, { isDataLoaded: this.isDataLoaded }), [], { noteDate: this.agentClaimHistory.noteDate })}

                    {(this.props.customData.selectedAssistFullName) && 
                        <DropdownBase label={L('Assist')} options={[{key: this.props.customData.selectedAssistFullName, text: this.props.customData.selectedAssistFullName}]}
                            value={this.props.customData.selectedAssistFullName} disabled={true} isDataLoaded={true} required={false}
                        />
                    }

                    {this.renderElement(new ContentViewModelProperty("note", L("Note"), Controls.Text, false, [], false, { isDataLoaded: this.isDataLoaded, rows: 5 }), [], { note: this.agentClaimHistory.note })}
                    <DropdownBase label={L('Claim status')} options={this.productOptions.dropdown}
                        value={this.agentClaimHistory.status} disabled={false} isDataLoaded={true}
                        onChange={(e: string | number | undefined) => {
                            if(e) {
                                this.agentClaimHistory.status = e.toString();
                                this.forceUpdate();
                            }
                        }} 
                    />
                </PivotItem>
            </Pivot>
        );
    }
}