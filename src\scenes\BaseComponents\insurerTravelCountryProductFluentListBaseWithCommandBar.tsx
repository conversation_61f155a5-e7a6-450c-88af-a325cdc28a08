import {FluentTableBase} from "../Fluent/base/fluentTableBase";
import {ITableColumn} from "./ITableColumn";
import {myTheme} from "../../styles/theme";
import {L} from "../../lib/abpUtility";
import {ICrudPermissons} from "./commandBarBase";
import {<PERSON>fault<PERSON><PERSON>on, <PERSON><PERSON>, <PERSON><PERSON>Footer, PrimaryButton, TextField, ThemeProvider} from "@fluentui/react";
import { DropdownBase } from "./dropdownBase";
import { TravelCountryCoverageDto } from "../../services/travelCountryCoverage/dto/travelCountryCoverageDto";
import { TravelCountryDto } from "../../services/travelCountry/dto/travelCountryDto";
import { GeographicalZoneType } from "../../services/travelCountryCoverage/geographicalZoneEnums";
import travelCountryCoverageService from "../../services/travelCountryCoverage/travelCountryCoverageService";

const dialogStyles = {
    main: {
        selectors: {
            '@media (min-width: 0px)': {
                maxWidth: 580,
                width: 580
            }
        }
    }
};

export class InsurerTravelCountryFluentListBaseWithCommandBar extends FluentTableBase<TravelCountryCoverageDto | TravelCountryDto> {
    private shouldReloadItems: boolean = false;
    private showPopUpDialog: boolean = false;
    private insurerCountryId: string = "";
    private selectedGeographicalZone: string = "";
    private geographicalZoneTypeOptions = [{key: "Poland", text: L(GeographicalZoneType.Poland)}, {key: "Europe", text: L(GeographicalZoneType.Europe)}, {key: "RestOfTheWorldWithoutUSA", text: L(GeographicalZoneType.RestOfTheWorldWithoutUSA)}, {key: "RestOfTheWorld", text: L(GeographicalZoneType.RestOfTheWorld)}]
    
    disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;

    getColumns(): ITableColumn[] {
        return this.getTableColumns(this.props);
    }

    private getTableColumns(props: any): ITableColumn[] {
        return [
            {
                name: L('Country'),
                fieldName: 'country',
                onRender: (item: TravelCountryCoverageDto) => {
                    return L(item.country.name);
                }
            },
            {
                name: L('Country ID'),
                fieldName: 'countryId',
                onRender: (item: TravelCountryCoverageDto) => {
                    return L(item.insurerCountryId);
                }
            },
            {
                name: L('Geographical zone'),
                fieldName: 'geographicalZone',
                onRender: (item: TravelCountryCoverageDto) => {
                    return L(GeographicalZoneType[item.geographicalZone]);
                }
            }
        ];
    }

    getCrudPermission(): ICrudPermissons {
        return {
            create: false,
            update: false,
            delete: true,
            customActions: true,
        };
    }

    getCommandBarBaseProps() {
        let props = super.getCommandBarBaseProps();
        return {
            ...props,
            customActionsProps: [
                {
                    displayFor: 'single',
                    buttonText: L("Edit"),
                    buttonIcon: "Edit",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                }
            ],
            customActions: [
                () => {
                    this.insurerCountryId = ''
                    this.selectedGeographicalZone = ''
                    this.getTravelCountryById();
                    this.handleShowPopUpDialog();
                }
            ]
        }
    }

    delete = async () => {
        this.setState({ asyncActionInProgress: true });
        await this.props.store.delete(this.props.customData.selectedTravelCountry);
        await this.reloadItems();
        this.setState({ asyncActionInProgress: false });
    };

    private updateTravelCountryInsuranceCoverage = async () => {
        await travelCountryCoverageService.update({
            id: this.props.customData.selectedTravelCountry.id,
            countryId: this.props.customData.selectedTravelCountry.countryId,
            insurerId: this.props.customData.selectedTravelCountry.insurerId,
            insurerCountryId: this.insurerCountryId,
            geographicalZone: this.selectedGeographicalZone
        }).then((response: any) => {
            if(response) {
                this.showPopUpDialog = false;
                this.reloadItems();
            }
        }).catch((error: any) => {
            console.error(error);
        })
        this.forceUpdate();
    }

    private getTravelCountryById = async () => {
        await  travelCountryCoverageService.get({id: this.props.customData.selectedTravelCountry.id}).then((response: any) => {
            if(response) {
                this.insurerCountryId = response.insurerCountryId
                this.selectedGeographicalZone = response.geographicalZone
                this.showPopUpDialog = true;
             }
        })
        this.forceUpdate();
    }

    private handleShowPopUpDialog() {
        this.showPopUpDialog = true;
        this.forceUpdate();
    }

    private reloadListOnDialogClose() {
        this.showPopUpDialog = false;
    
        if(this.shouldReloadItems) {
            this.reloadItems();
        }
    
        this.forceUpdate();
    }

    private async reloadItems() {
        this.selectionSetAllSelected(false);
        if(typeof this.props.refreshItems !== 'undefined') {
            await this.props.refreshItems!();
        }
    }  

    renderAll(pageInfo: string, values: any, columns: any) {
        return <>
            <Dialog
                hidden={!this.showPopUpDialog}
                onDismiss={() => this.reloadListOnDialogClose()}
                modalProps={{
                    isBlocking: true,
                    styles: dialogStyles
                }}
            >
                <TextField 
                    label={L("Enter the country ID")}
                    value={this.insurerCountryId}
                    onChange={(e: any, newValue?: string) => {
                        this.insurerCountryId = newValue || '';
                        this.forceUpdate();
                    }}
                />
                <DropdownBase key="GeographicalZoneType" required={true} label={L("Select geographical zone")} options={this.geographicalZoneTypeOptions} value={this.selectedGeographicalZone ? this.selectedGeographicalZone : this.props.customData.selectedSport?.coverageType} 
                    isDataLoaded={true} 
                    customLabelStyles={{width: "200px", minWidth: "200px"}}
                    onChange={(value) => {
                        if (typeof value === 'string') {
                            this.selectedGeographicalZone = value;
                            this.forceUpdate();
                        } else {
                        }
                    }}
                />
                <DialogFooter theme={myTheme}>
                    <PrimaryButton
                        onClick={() => {
                            this.updateTravelCountryInsuranceCoverage();
                        }}
                        text={L('Save')}
                        theme={myTheme}
                    />
                    <DefaultButton theme={myTheme} onClick={() => this.reloadListOnDialogClose()} text={L('Cancel')} />
                </DialogFooter>
            </Dialog>
            
            <ThemeProvider theme={myTheme}>
                { this.renderAnnounced(pageInfo) }
                { this.renderCommandBarBase() }

                { this.renderListScrollablePane(values, columns) }
            </ThemeProvider>

            {this.renderDialog()}
        </>;
    }
}