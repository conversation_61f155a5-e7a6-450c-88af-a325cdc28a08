import { mergeStyleSets, Stack, SelectionMode, Selection } from '@fluentui/react';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import Stores from '../../../stores/storeIdentifier';
import { inject, observer } from 'mobx-react';
import TravelCountryCoverageStore from '../../../stores/travelCountryCoverageStore';
import { InsurerTravelCountryFluentListBaseWithCommandBar } from '../../BaseComponents/insurerTravelCountryProductFluentListBaseWithCommandBar';

const classNames = mergeStyleSets({
    contentContainer: {
        display: 'block',
        width: '100%',
        height: '500px',
        maxHeight: '500px',
        position: 'relative',
        marginTop: '20px',
    },

});

export interface IProps {
	travelCountryCoverageStore: TravelCountryCoverageStore;
}

@inject(Stores.LanguageStore)
@inject(Stores.ProductAttributeStore)
@inject(Stores.TravelCountryCoverageStore)
@observer
export class TravelCountryContentView extends GenericContentView {
    private selectedTravelCountry: any;
    private _travelCountryListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedTravelCountry: any = this._travelCountryListSelection.getSelection();
            if(Array.isArray(selectedTravelCountry) && selectedTravelCountry.length > 0 && !!selectedTravelCountry[0].id) {
                this.selectedTravelCountry = selectedTravelCountry[0];
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });
    private overrideAllItemsTrigger: number = 0;

    private async reloadItems() {
        if(this.props.customData && this.props.customData.fetchTravelCountryByInsurerId) {
            await this.props.customData.fetchTravelCountryByInsurerId();
            this.overrideAllItemsTrigger = this.overrideAllItemsTrigger + 1;
            this.forceUpdate();
        }
    }

    renderContent() {
        return <>
            <Stack>
                <div className={classNames.contentContainer}>
                    <InsurerTravelCountryFluentListBaseWithCommandBar 
                        store={this.props.travelCountryCoverageStore!}
                        items={
                            this.props.travelCountryCoverageStore?.dataSet && this.props.travelCountryCoverageStore?.dataSet.items
                                ? this.props.travelCountryCoverageStore?.dataSet.items
                                : []
                        }
                        customSelection={this._travelCountryListSelection}
                        searchText={''}
                        history={this.props.history}
                        customData={{
                            selectedTravelCountry: this.selectedTravelCountry,
                            disableGetAllOnMount: true,
                            useOnlyRefreshItems: true,
                            overrideAllItemsTrigger: this.overrideAllItemsTrigger,
                        }}
                        scrollablePanelMarginTop={120}
                        refreshItems={() => this.reloadItems()}
                    />
                </div>
            </Stack>
        </>
    }
}