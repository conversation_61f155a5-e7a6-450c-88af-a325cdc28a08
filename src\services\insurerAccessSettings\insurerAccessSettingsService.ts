import { isUserLoggedIn } from '../../utils/authUtils';
import { CrudServiceBase } from '../base/crudServiceBase';
import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { InsurerAccessSettingsDto } from './dto/insurerAccessSettingsDto';

export class InsurerAccessSettingsService extends CrudServiceBase<InsurerAccessSettingsDto> {
    constructor() {
        super(Endpoint.InsurerAccessSettings);
        this.internalHttp = httpApi;
    }

    public async getByUserId(userId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetByUserId?userId=${userId}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportInsurerAccessSettingsService: InsurerAccessSettingsService = new InsurerAccessSettingsService();
export default exportInsurerAccessSettingsService;