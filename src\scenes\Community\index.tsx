import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { CommunityDto } from '../../services/community/dto/communityDto';
import { CommunityContentView } from './components/communityContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import CommunityStore from '../../stores/communityStore';
import communityService from '../../services/community/communityService';
import { L } from '../../lib/abpUtility';
import { isConfigForAG, isConfigForProduction } from '../../utils/authUtils';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	communityStore: CommunityStore;
	match: any
}

@inject(Stores.CommunityStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private communityId: string = this.props.match.params.id;

	async componentDidMount() {
		if(isConfigForAG() || isConfigForProduction()) {
			window.location.href = '/exception';
		}
		
		this.props.communityStore.model = await communityService.get({ id: this.communityId } as CommunityDto);;
		this.forceUpdate();
	}

	public render() {
		if(isConfigForAG() || isConfigForProduction()) {
			return <>
				<p style={{marginTop: 50, fontWeight: 'bold', fontSize: '1.5rem'}}>{L('You do not have access to this page.')}</p>
			</>;
		} else {
			return (
				<FocusZone as="div" className={classNames.subpageContentWrapper}>
					<CommunityContentView store={this.props.communityStore} payload={ this.props.communityStore.model as CommunityDto } renderFooter={{show: true}} />
				</FocusZone>
			);
		}
	}
}

export default Index;