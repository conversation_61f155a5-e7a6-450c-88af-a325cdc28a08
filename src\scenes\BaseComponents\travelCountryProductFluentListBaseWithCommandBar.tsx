import {FluentTableBase} from "../Fluent/base/fluentTableBase";
import {ITableColumn} from "./ITableColumn";
import {myTheme} from "../../styles/theme";
import {L} from "../../lib/abpUtility";
import {ICrudPermissons} from "./commandBarBase";
import {ThemeProvider} from "@fluentui/react";
import { InsurerDto } from "../../services/insurer/dto/insurerDto";
import { IGenericPanelProps } from "../Fluent/base/genericPanel";
import { TravelCountryPanel } from "../ProductList/components/travelCountryPanel";
import { AddTravelCountryPanel } from "../ProductList/components/addTravelCountryPanel";
import { GeneralCountryListPanel } from "../ProductList/components/generalCountryListPanel";

export class TravelCountryProductFluentListBaseWithCommandBar extends FluentTableBase<InsurerDto> {
    disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;
    private panelSwitch: string = 'none';

    getColumns(): ITableColumn[] {
        return this.getTableColumns(this.props);
    }

    private getTableColumns(props: any): ITableColumn[] {
        return [
            {
                name: L('Insurer name'),
                fieldName: 'insurerName',
                onRender: (item: InsurerDto) => {
                    return item.name;
                }
            }
        ];
    }

    getCrudPermission(): ICrudPermissons {
        return {
            create: false,
            update: false,
            delete: false,
            customActions: true,
        };
    }

    getCommandBarBaseProps() {
        let props = super.getCommandBarBaseProps();
        return {
            ...props,
            customActionsProps: [
                {
                    displayFor: 'none',
                    buttonText: L("General country list"),
                    buttonIcon: "none",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                {
                    displayFor: 'single',
                    buttonText: L("General country list"),
                    buttonIcon: "none",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                {
                    displayFor: 'single',
                    buttonText: L("Edit range"),
                    buttonIcon: "Edit",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                // {
                //     displayFor: 'single',
                //     buttonText: L("Export"),
                //     buttonIcon: "none",
                //     buttonColor: myTheme.palette.white,
                //     buttonIconColor: myTheme.palette.white,
                //     buttonBackground: myTheme.palette.themeTertiary,
                // },
                {
                    displayFor: 'single',
                    buttonText: L("Add range"),
                    buttonIcon: "add",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
            ],
            customActions: [
                async () => {
                    await this.props.customData.fetchAllTravelCountry().then((result: any) => {
                        this.panelSwitch = 'generalCountryList';
                        this.setState({ modalVisible: true });
                    });
                },
                async () => {
                    await this.props.customData.fetchAllTravelCountry().then((result: any) => {
                        this.panelSwitch = 'generalCountryList';
                        this.setState({ modalVisible: true });
                    });
                },
                async () => {
                    await this.props.customData.fetchTravelCountryByInsurerId().then((result: any) => {
                        this.panelSwitch = 'editRange';
                        this.setState({ modalVisible: true });
                    });
                },
                // () => {
                //     this.props.customData.getSportCoverageExcelByInsurerId();
                // },
                async () => {
                    await this.props.customData.fetchAllTravelCountry().then((result: any) => {
                        this.panelSwitch = 'addRange';
                        this.setState({ modalVisible: true });
                    });
                },
            ]
        }
    }

    renderPanelView(props: IGenericPanelProps): JSX.Element {
        switch(this.panelSwitch) {
            case 'generalCountryList':
                return <GeneralCountryListPanel
                    {...props}
                />  
            case 'editRange': 
                return <TravelCountryPanel
                    {...props}
                />
            case 'addRange':
                return <AddTravelCountryPanel
                    {...props}
                /> 
            default: 
                return <></>
        }
    }

    renderAll(pageInfo: string, values: any, columns: any) {
        return <>
            <ThemeProvider theme={myTheme}>
                { this.renderAnnounced(pageInfo) }
                { this.renderCommandBarBase() }
                { this.renderListScrollablePane(values, columns) }
            </ThemeProvider>
            {this.renderPanel()}
        </>;
    }
}