import React from 'react';
import { HubMessageTypeEnum } from '../../services/chat/hubMessageTypeEnums';
import { dateFormatChat, isJsonString } from '../../utils/utils';
import { L } from '../../lib/abpUtility';

interface IMessage {
    user: string;
    message: string;
    isMyUser: boolean;
    setIsChatEnded: (value: boolean) => void;
}

const Message: React.FC<IMessage> = (props: IMessage) => {
    let message: string | null = null;
    let time: string | null = null;
    let isChatEnded: boolean = false;
    let isChatStarted: boolean = false;

    if(typeof props.message === 'string' && isJsonString(props.message)) {
        const parsedMessage: any = JSON.parse(props.message)[0];
        if(parsedMessage.type === HubMessageTypeEnum.ChatMessage && parsedMessage.value) message = parsedMessage.value;
        if(parsedMessage.type === HubMessageTypeEnum.ChatMessage && parsedMessage.time) time = dateFormatChat(parsedMessage.time);
    } else {
        message = props.message;
        if(!!time) {
            time = dateFormatChat(time);
        }
    }

    if(!!message && message.substring(0, 4) === '#%#%' && message.substring(message.length - 4) === '#%#%') {
        isChatEnded = true;
        props.setIsChatEnded(true);
    }
    if(!!message && message.substring(0, 4) === '%#%#' && message.substring(message.length - 4) === '%#%#') {
        isChatStarted = true;
    }

    return (!!message ? 
        <div className={`chat__message ${isChatStarted ? 
                                        'chat__message--chat-started' : 
                                            (isChatEnded ? 'chat__message--chat-ended' : 
                                                (props.isMyUser ? 'chat__message--my-messages' : 
                                                    'chat__message--other-messages'))}`}
        >
            <div className='chat__message--text'>
                {isChatStarted || isChatEnded ?
                    <p>{isChatStarted ? L('The chat has started.') : L('The chat has ended.')}</p>
                    :
                    <p>{message}</p>
                }
            </div>
            {!isChatStarted &&
                <div className='chat__message--time-container'>
                    <p>{time}</p>
                </div>
            }
        </div> 
        : 
        <></>
    );
};

export default Message;