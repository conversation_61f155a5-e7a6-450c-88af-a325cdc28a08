import * as React from 'react';
import { SearchBox } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import SearchStore from '../../../stores/searchStore';
import { mergeStyleSets} from '@fluentui/merge-styles';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { observer } from 'mobx-react';
import AppConsts from '../../../lib/appconst';

var _ = require('lodash');

interface ISearchProps {
  searchStore: SearchStore;
}

const classNames = mergeStyleSets({
  searchCol: {
    height: '100%',
    selectors: {
      '& .ms-SearchBox': {
        selectors: {
          ':focus': {
            outline: 'none',
            border: 'none',
            borderRadius: '2px',
          },
          ':hover': {
            borderColor: 'transparent !important',
          },
        },
      },
      '& .ms-SearchBox-field': {
        lineHeight: 3,
      },
    },
  },
  headerCol: {
    flex: 1,
  },
});

@observer
class Search extends React.Component<ISearchProps, {}> {
  private debouncedOnInputChange: any = _.debounce((event: React.ChangeEvent<HTMLInputElement> | undefined, newValue: string | undefined) => {
    this.props.searchStore.setText(newValue ? newValue.trim() : ' ');
  }, AppConsts.defaultSerachBarDelay, []);

  render() {
    return (
      <form className={`${classNames.headerCol} ${classNames.searchCol}`} autoComplete="off">
        <SearchBox
          key={Math.random()}
          defaultValue={this.props.searchStore.searchText}
          autoComplete="off"
          theme={myTheme}
          styles={{
            root: { 
              flex: 1, 
              borderRadius: '2px',
              width: 'auto',
              height: '32px',
              borderColor: 'transparent', 
              backgroundColor:additionalTheme.white,
              borderBottom:'1px solid black',


            },
            field: { borderRadius: '2px' },

          }}
          placeholder={L('Search')}
          onChange={(event, newValue) => this.debouncedOnInputChange(event, newValue)}
          onSearch={(newValue) => this.props.searchStore.setText(newValue ? newValue : ' ')}
        />
      </form>
    );
  }
}

export default Search;
