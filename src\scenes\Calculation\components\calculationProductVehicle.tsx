import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Footer, DialogType, mergeStyleSets, Pivot, PivotItem, PrimaryButton, Spinner, SpinnerSize } from "@fluentui/react";
import React from "react";
import { LabelContainerComponent } from "../../BaseComponents/labelContainerComponent";
import { LabelComponent } from "../../BaseComponents/labelComponent";
import { ContentViewModelProperty } from "../../BaseComponents/contentViewBase";
import { Controls } from "../../BaseComponents/controls";
import { L } from "../../../lib/abpUtility";
import { enumToDropdownOptions, isJsonString } from "../../../utils/utils";
import { DropdownBase } from "../../BaseComponents/dropdownBase";
import { NewOfferStatus } from "../../../services/calculation/dto/newOfferStatusEnums";
import { LabeledTextField } from "../../../components/LabeledTextField";
import { additionalTheme, myTheme } from "../../../styles/theme";
import { CheckBoxBase } from "../../BaseComponents/CheckBoxBase";
import { InsuranceCompanyNewOffer } from "../../../services/calculation/dto/insuranceCompanyNewOfferEnum";
import { NumberOfInstallments } from "../../../services/calculation/dto/numberOfInstallmentsEnum";
import { IndividualOfferDto } from "../../../services/individualOffer/individualOfferDto";
import { checkUsedKeysForDataSet } from "./calculationProductProcessData";

const pivotStyles = {
	root: {
		marginLeft: '-8px'
	},
	linkIsSelected: {
		color: myTheme.palette.red,
		selectors: {
			':before': {
				height: '5px',
				backgroundColor: additionalTheme.darkerRed
			}
		}
	}
};

const classNames = mergeStyleSets({
	loadSpinner: {
		display: 'inline-flex',
		marginLeft: '45px !important',
		marginTop: '20px',
		selectors: {
			'& .ms-Spinner-label': {
				color: myTheme.palette.themePrimary,
			}
		}
	},
	comboBoxStyles: {
		width: '300px',
		position: 'relative',
	},
	comboBoxRequiredMark: {
		selectors: {
			'::before': {
				content: "'*'",
				position: 'absolute',
				top: '-5px',
				right: '-10px',
				color: myTheme.palette.redDark,
			},
		}
	}
});

export interface ICalculationProductVehicleProps {
	calculation: any;
	calculationData: any;
	apkData: any;
	isEditMode: boolean;
	showMakeIndividualOfferDialog: boolean;
	asyncActionInProgress: boolean;
	individualOffer: IndividualOfferDto;
	isDataLoaded: boolean;
	activitiesPivotItem: JSX.Element;
	productAttributeResultItems: any[];
	gnLanguage: any;
	createIndividualOffer: () => void;
	toggleShowMakeIndividualOfferDialog: (value: boolean) => void;
	onUploadPdf: () => void;
	validateBankAccountNumber: (bankAccountNumber: string) => boolean;
	renderDataPickersAndFee: () => void;
	renderElement: (element: ContentViewModelProperty, error: any, value: any, tableInputData?: any) => JSX.Element;
	updateIndividualOfferValue: (newIndividualOffer: IndividualOfferDto) => void;
}

export class CalculationProductVehicle extends React.Component<ICalculationProductVehicleProps> {
	private usedAPKInputsKeys: string[] = ['Auto.APK.HaveCoOwner', 'Auto.APK.UsageType', 'Auto.APK.RightHandDrive', 'Auto.APK.OC', 'Auto.APK.AC', 'Auto.APK.ScopeAC', 'Auto.APK.OwnContributionAc', 'Auto.APK.YoungerAnotherDriver', 'Auto.APK.AdditionalProtection', 'Auto.APK.ScopeASS'];
	private usedInputsKeys: string[] = ['VehicleInfo.Damaged', 'VehicleInsurance.AcType', 'VehicleInsurance.DrivingLicenceIssueYear', 'VehicleInsurance.YearOfPurchaseOfTheVehicle', 'VehicleInsurance.YearOfBirthOldestChild', 'VehicleInsurance.ChildrenUnder26', 'VehicleOwner.MaritalStatus', 'VehicleInfo.ImportedInLast12Months', 'VehicleInfo.MadeNorthAmerica', 'VehicleInsurance.AdditionalInformation', 'VehicleInsurance.AdditionalInformation', 'VehicleInfo.NumberOfKeys', 'VehicleInfo.SeatsCount', 'VehicleInfo.VehicleGuards', 'VehicleInsurance.InstallmentCount', 'VehicleInsurance.PaymentMethod', 'VehicleInsurance.InsuranceSumType', 'VehicleInsurance.AssTypes', 'VehicleInsurance.StartDate', 'VehicleInsurance.PolicyTypes', 'VehicleInfo.RegistrationNumber', 'VehicleInfo.VIN', 'VehicleInfo.Mileage', 'VehicleInfo.AverageAnnualMileage', 'VehicleInfo.ParkingPlace', 'VehicleInfo.UsageType', 'VehicleInfo.FirstRegistrationDate', 'VehicleInfo.EurotaxCarId', 'VehicleInfo.InfoExpertId'];
	private usedInputsKeysChecked: boolean = false;

	render() {
		const { calculation, showMakeIndividualOfferDialog, asyncActionInProgress, individualOffer, isDataLoaded, apkData, calculationData, renderElement } = this.props;

		// some saved calculations doesn't have full data causing errors
		if(Object.keys(calculationData).length > 0 && !this.usedInputsKeysChecked) {
			checkUsedKeysForDataSet(apkData, this.usedAPKInputsKeys);
			checkUsedKeysForDataSet(calculationData, this.usedInputsKeys, this.props.productAttributeResultItems, this.props.gnLanguage);

			this.usedInputsKeysChecked = true;
			this.forceUpdate();
		}

		const vehicleConfiguration: string = `${calculationData['VehicleInfo.EurotaxCarId'] && calculationData['VehicleInfo.EurotaxCarId'].label}: ${calculationData['VehicleInfo.EurotaxCarId'] && calculationData['VehicleInfo.EurotaxCarId'].value}\n\r${calculationData['VehicleInfo.InfoExpertId'] && calculationData['VehicleInfo.InfoExpertId'].label}: ${calculationData['VehicleInfo.InfoExpertId'] && calculationData['VehicleInfo.InfoExpertId'].value}`;

		const vehicleInsuranceAdditionalInformation = calculationData['VehicleInsurance.AdditionalInformation'];
		const parsedVehicleInsuranceAdditionalInformation: any = vehicleInsuranceAdditionalInformation ? (!!vehicleInsuranceAdditionalInformation.valueLocale ? 
																	(isJsonString(vehicleInsuranceAdditionalInformation.valueLocale) ? JSON.parse(vehicleInsuranceAdditionalInformation.valueLocale) : vehicleInsuranceAdditionalInformation.valueLocale)
																		: (!!vehicleInsuranceAdditionalInformation.value && isJsonString(vehicleInsuranceAdditionalInformation.value) ? JSON.parse(vehicleInsuranceAdditionalInformation.value) : vehicleInsuranceAdditionalInformation.value))
																			: {};
		let vehicleInsuranceAdditionalInformationText: string = typeof parsedVehicleInsuranceAdditionalInformation === 'string' ? parsedVehicleInsuranceAdditionalInformation : "";

		if(parsedVehicleInsuranceAdditionalInformation && Object.keys(parsedVehicleInsuranceAdditionalInformation).length > 0) {
			for(let key in parsedVehicleInsuranceAdditionalInformation) {
				if(key && parsedVehicleInsuranceAdditionalInformation.hasOwnProperty(key) && parsedVehicleInsuranceAdditionalInformation[key] === true) {
					switch(key) {
						case '64abed486648597eb34a87df':
							vehicleInsuranceAdditionalInformationText += `${vehicleInsuranceAdditionalInformationText.length > 0 ? '\n' : ''}- ${L('The vehicle is driven by a young driver under 26 years of age')}`;
						break;
						case '64abed486648597eb34a87e2':
							vehicleInsuranceAdditionalInformationText += `${vehicleInsuranceAdditionalInformationText.length > 0 ? '\n' : ''}- ${L('Vehicle imported from abroad')}`;
						break;
						case '64abed486648597eb34a87e5':
							vehicleInsuranceAdditionalInformationText += `${vehicleInsuranceAdditionalInformationText.length > 0 ? '\n' : ''}- ${L('The vehicle has a steering wheel on the right side')}`;
						break;
						case '64abed486648597eb34a87e8':
							vehicleInsuranceAdditionalInformationText += `${vehicleInsuranceAdditionalInformationText.length > 0 ? '\n' : ''}- ${L('The vehicle is damaged')}`;
						break;
					}
				}
			}
		}

		return (
			<>
				<Dialog
					hidden={!showMakeIndividualOfferDialog}
					onDismiss={() => { this.props.toggleShowMakeIndividualOfferDialog(false); this.forceUpdate(); }}
					dialogContentProps={{
						type: DialogType.normal,
						title: L('Add individual offer'),
					}}
					modalProps={{
						isBlocking: true,
					}}
					minWidth={595}
				>
					<DialogContent>
						<DropdownBase key={'status'} label={L("Status")} options={ enumToDropdownOptions(NewOfferStatus, true, true, "string")}
							value={individualOffer.status} disabled={false} isDataLoaded={true} required={true} customDropdownWidth={'300px'} customLabelStyles={{ width: "150px", minWidth: "150px" }}
							onChange={(value: string | number | undefined) => {
								if(typeof value === 'string' && individualOffer.status !== value) {
									this.props.updateIndividualOfferValue({...individualOffer, status: value});
								}
							}}
						/>

						<DropdownBase key={'insurerName'} label={L("Insurance company")} options={ enumToDropdownOptions(InsuranceCompanyNewOffer, true, true, "string")}
							value={individualOffer.insurerName} disabled={false} isDataLoaded={true} required={true} customDropdownWidth={'300px'} customLabelStyles={{ width: "150px", minWidth: "150px" }}
							onChange={(value: string | number | undefined) => {
								if(typeof value === 'string' && individualOffer.insurerName !== value) {
									this.props.updateIndividualOfferValue({...individualOffer, insurerName: value});
								}
							}}
						/>

						<LabelContainerComponent customStyles={{display: 'flex', flexDirection: 'column'}}>
							<LabelComponent label={L('Insurance coverage')} customStyles={{minHeight: 'unset'}} />
						
							<div style={{display: "flex"}}>
								<CheckBoxBase key={'insuranceCoverage OC'} label={L("OC")} 
									value={individualOffer.insuranceCoverage.includes('OC')} disabled={false} required={true} containerCustomStyles={{ width: "70px", minWidth: "70px" }}
									onChange={(isChecked) => {
										const coverage = [...individualOffer.insuranceCoverage];
										if (isChecked && !coverage.includes('OC')) {
											coverage.push('OC');
										} else if (!isChecked && coverage.includes('OC')) {
											const index = coverage.indexOf('OC');
											coverage.splice(index, 1);
										}
										this.props.updateIndividualOfferValue({...individualOffer, insuranceCoverage: coverage});
									}}
								/>

								<CheckBoxBase key={'insuranceCoverage AC'} label={L("AC")} 
									value={individualOffer.insuranceCoverage.includes('AC')} disabled={false} required={true} containerCustomStyles={{ width: "70px", minWidth: "70px" }}
									onChange={(isChecked) => {
										const coverage = [...individualOffer.insuranceCoverage];
										if (isChecked && !coverage.includes('AC')) {
											coverage.push('AC');
										} else if (!isChecked && coverage.includes('AC')) {
											const index = coverage.indexOf('AC');
											coverage.splice(index, 1);
										}
										this.props.updateIndividualOfferValue({...individualOffer, insuranceCoverage: coverage});
									}}
								/>

								<CheckBoxBase key={'insuranceCoverage NNW'} label={L("NNW")} 
									value={individualOffer.insuranceCoverage.includes('NNW')} disabled={false} required={true} containerCustomStyles={{ width: "80px", minWidth: "80px" }}
									onChange={(isChecked) => {
										const coverage = [...individualOffer.insuranceCoverage];
										if (isChecked && !coverage.includes('NNW')) {
											coverage.push('NNW');
										} else if (!isChecked && coverage.includes('NNW')) {
											const index = coverage.indexOf('NNW');
											coverage.splice(index, 1);
										}
										this.props.updateIndividualOfferValue({...individualOffer, insuranceCoverage: coverage});
									}}
								/>

								<CheckBoxBase key={'insuranceCoverage Assistance'} label={L("Assistance")} 
									value={individualOffer.insuranceCoverage.includes('Assistance')} disabled={false} required={true} containerCustomStyles={{ width: "110px", minWidth: "110px" }}
									onChange={(isChecked) => {
										const coverage = [...individualOffer.insuranceCoverage];
										if (isChecked && !coverage.includes('Assistance')) {
											coverage.push('Assistance');
										} else if (!isChecked && coverage.includes('Assistance')) {
											const index = coverage.indexOf('Assistance');
											coverage.splice(index, 1);
										}
										this.props.updateIndividualOfferValue({...individualOffer, insuranceCoverage: coverage});
									}}
								/>

								<CheckBoxBase key={'insuranceCoverage Glass'} label={L("Glass")} 
									value={individualOffer.insuranceCoverage.includes('Glass')} disabled={false} required={true} containerCustomStyles={{ width: "110px", minWidth: "110px" }}
									onChange={(isChecked) => {
										const coverage = [...individualOffer.insuranceCoverage];
										if (isChecked && !coverage.includes('Glass')) {
											coverage.push('Glass');
										} else if (!isChecked && coverage.includes('Glass')) {
											const index = coverage.indexOf('Glass');
											coverage.splice(index, 1);
										}
										this.props.updateIndividualOfferValue({...individualOffer, insuranceCoverage: coverage});
									}}
								/>
							</div>
						</LabelContainerComponent>

						<DropdownBase key={'installments'} label={L("Number of installments")} options={ enumToDropdownOptions(NumberOfInstallments, true, true, "string")}
							value={individualOffer.installments} disabled={false} isDataLoaded={true} required={true} customDropdownWidth={'300px'} customLabelStyles={{ width: "150px", minWidth: "150px" }}
							onChange={(value: string | number | undefined) => {
								if(typeof value === 'string' && individualOffer.installments !== value) {
									this.props.updateIndividualOfferValue({...individualOffer, installments: value});
								}
							}}
						/>

						<LabeledTextField key={'total'} required={true} label={L('Individual offer contribution ')} type='number'
							rows={1} multiline={false} value={individualOffer.total.toString()} disabled={false} isDataLoaded={true} customLabelStyles={{ width: "150px", minWidth: "150px" }}
							onChange={(e: any, newValue: string | undefined) => {
								this.props.updateIndividualOfferValue({...individualOffer, total: !!newValue ? parseInt(newValue) : 0});
							}}
						/>
					</DialogContent>

					<DialogFooter>
						{asyncActionInProgress && (
							<div style={{position: 'absolute', top: '-32px'}}>
								<Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
							</div>
						)}
						
						<PrimaryButton theme={myTheme} text={L('Save')} 
							disabled={asyncActionInProgress === true || !individualOffer.status || !individualOffer.insurerName  || !individualOffer.installments || !individualOffer.total}
							style={{ backgroundColor: myTheme.palette.red }}
							onClick={() => this.props.createIndividualOffer()}
						/>
					</DialogFooter>
				</Dialog>

				<Pivot theme={myTheme} styles={pivotStyles}>		
					<PivotItem headerText={L('General')} key={'General'}>
						{renderElement(new ContentViewModelProperty('calculationId', L("Calculation ID"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'calculationId': calculation.id})}
						{renderElement(new ContentViewModelProperty('translatedSegment', L("Product"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'translatedSegment': L(calculation.segment)})}
						{renderElement(new ContentViewModelProperty('status', L("Status"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'status': L(calculation.lastCreatedPolicyStatus)})}
						{renderElement(new ContentViewModelProperty('creationTime', L("Creation date"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'creationTime': calculation.creationTime})}
						{renderElement(new ContentViewModelProperty('startDate', L("Policy start date"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'startDate': calculation.startDate})}
						{renderElement(new ContentViewModelProperty('customerName', L("Customer name"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerName': calculation.client.user.name})}
						{renderElement(new ContentViewModelProperty('customerSurname', L("Customer surname"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerSurname': calculation.client.user.surname})}
						{renderElement(new ContentViewModelProperty('pesel', L("Pesel"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'pesel': calculation.client.pesel})}
						{renderElement(new ContentViewModelProperty('customerEmail', L("Customer email"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'customerEmail': calculation.client.user.emailAddress})}
						{renderElement(new ContentViewModelProperty('phoneNumber', L("Customer phone number"), Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'phoneNumber': calculation.client.phone})}
					</PivotItem>
					
					{Object.keys(apkData).length > 0 &&
						<PivotItem headerText={L('APK')} key={'APK'}>
							{/* <LabelContainerComponent>
								<LabelComponent label={L("Registration number")} required={true} />  
								
								<ComboBox
									label={''}
									text={this.apkData.registrationNumber}
									required={!isEditMode}
									allowFreeform={true}
									autoComplete={'on'}
									options={this.vehicleOptions.dropdown}
									className={`${classNames.comboBoxStyles} ${!isEditMode ? classNames.comboBoxRequiredMark : ''}`}
									key={`RegistrationNumberComboBox`}
									disabled={isEditMode || asyncActionInProgress || true}
									onChange={(event: React.FormEvent<IComboBox>, option?: IComboBoxOption, index?: number, value?: string) => {
										if(option && !!option.key && !!option.text) {
											this.apkData.registrationNumber = typeof option.key === 'number' ? option.key.toString() : option.key;
											this.forceUpdate();
										} else if(typeof value !== 'undefined') {
											const splitedValue = value.split(' | ');
											this.apkData.registrationNumber = splitedValue[0];
											this.forceUpdate();
										}
									}}
									onPendingValueChanged={(option?: IComboBoxOption, index?: number, value?: string) => {
										if(typeof value !== 'undefined') {
											const splitedValue = value.split(' | ');
											this.apkData.registrationNumber = splitedValue[0];
											this.forceUpdate();
										}
									}} 
								/>
							</LabelContainerComponent> */}
							
							{renderElement(new ContentViewModelProperty('Auto.APK.HaveCoOwner', apkData['Auto.APK.HaveCoOwner'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]},  true, {isDataLoaded: isDataLoaded}), [], {'Auto.APK.HaveCoOwner': apkData['Auto.APK.HaveCoOwner'].value})}
							{renderElement(new ContentViewModelProperty('Auto.APK.UsageType', apkData['Auto.APK.UsageType'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'Auto.APK.UsageType': apkData['Auto.APK.UsageType'].valueLocale})} 
							{renderElement(new ContentViewModelProperty('Auto.APK.RightHandDrive', apkData['Auto.APK.RightHandDrive'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]},  true, {isDataLoaded: isDataLoaded}), [], {'Auto.APK.RightHandDrive': apkData['Auto.APK.RightHandDrive'].value})}
							{renderElement(new ContentViewModelProperty('Auto.APK.OC', apkData['Auto.APK.OC'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]},  true, {isDataLoaded: isDataLoaded}), [], {'Auto.APK.OC': apkData['Auto.APK.OC'].value})}
							{renderElement(new ContentViewModelProperty('Auto.APK.AC', apkData['Auto.APK.AC'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]},  true, {isDataLoaded: isDataLoaded}), [], {'Auto.APK.AC': apkData['Auto.APK.AC'].value})}
							{renderElement(new ContentViewModelProperty('Auto.APK.ScopeAC', apkData['Auto.APK.ScopeAC'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'Auto.APK.ScopeAC': apkData['Auto.APK.ScopeAC'].valueLocale})} 
							{renderElement(new ContentViewModelProperty('Auto.APK.OwnContributionAc', apkData['Auto.APK.OwnContributionAc'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]},  true, {isDataLoaded: isDataLoaded}), [], {'Auto.APK.OwnContributionAc': apkData['Auto.APK.OwnContributionAc'].value})}
							{renderElement(new ContentViewModelProperty('Auto.APK.YoungerAnotherDriver', apkData['Auto.APK.YoungerAnotherDriver'].label, Controls.Picker, false, {dropdown: [{key: "true", text: L("Yes")}, {key: "false", text: L("No")}]},  true, {isDataLoaded: isDataLoaded}), [], {'Auto.APK.YoungerAnotherDriver': apkData['Auto.APK.YoungerAnotherDriver'].value})}
							{renderElement(new ContentViewModelProperty('Auto.APK.AdditionalProtection', apkData['Auto.APK.AdditionalProtection'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded, rows: 5}), [], {'Auto.APK.AdditionalProtection': apkData['Auto.APK.AdditionalProtection'].valueLocale})} 
							{renderElement(new ContentViewModelProperty('Auto.APK.ScopeASS', apkData['Auto.APK.ScopeASS'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded, rows: 5}), [], {'Auto.APK.ScopeASS': apkData['Auto.APK.ScopeASS'].valueLocale})} 
						</PivotItem>
					}

					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('Vehicle data')} key={'Vehicle data'}>
							{renderElement(new ContentViewModelProperty('VehicleInfo.RegistrationNumber', calculationData['VehicleInfo.RegistrationNumber'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.RegistrationNumber': calculationData['VehicleInfo.RegistrationNumber'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.VIN', calculationData['VehicleInfo.VIN'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.VIN': calculationData['VehicleInfo.VIN'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.Mileage', calculationData['VehicleInfo.Mileage'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.Mileage': calculationData['VehicleInfo.Mileage'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.AverageAnnualMileage', calculationData['VehicleInfo.AverageAnnualMileage'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.AverageAnnualMileage': calculationData['VehicleInfo.AverageAnnualMileage'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.ParkingPlace', calculationData['VehicleInfo.ParkingPlace'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.ParkingPlace': calculationData['VehicleInfo.ParkingPlace'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.UsageType', calculationData['VehicleInfo.UsageType'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.UsageType': calculationData['VehicleInfo.UsageType'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.FirstRegistrationDate', calculationData['VehicleInfo.FirstRegistrationDate'].label, Controls.Date, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.FirstRegistrationDate': calculationData['VehicleInfo.FirstRegistrationDate'].valueLocale})}
							{renderElement(new ContentViewModelProperty('vehicleConfiguration', L("Vehicle configuration"), Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded, rows: 5}), [], {'vehicleConfiguration': vehicleConfiguration})}
						</PivotItem>
					}

					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('Policy data')} key={'Policy data'}>
							{renderElement(new ContentViewModelProperty('VehicleInsurance.PolicyTypes', calculationData['VehicleInsurance.PolicyTypes'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded, rows: 5}), [], {'VehicleInsurance.PolicyTypes': calculationData['VehicleInsurance.PolicyTypes'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.StartDate', calculationData['VehicleInsurance.StartDate'].label, Controls.Date, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInsurance.StartDate': calculationData['VehicleInsurance.StartDate'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.AcType', calculationData['VehicleInsurance.AcType'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInsurance.AcType': calculationData['VehicleInsurance.AcType'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.InsuranceSumType', calculationData['VehicleInsurance.InsuranceSumType'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInsurance.InsuranceSumType': calculationData['VehicleInsurance.InsuranceSumType'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.AssTypes', calculationData['VehicleInsurance.AssTypes'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInsurance.AssTypes': calculationData['VehicleInsurance.AssTypes'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.PaymentMethod', calculationData['VehicleInsurance.PaymentMethod'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInsurance.PaymentMethod': calculationData['VehicleInsurance.PaymentMethod'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.InstallmentCount', calculationData['VehicleInsurance.InstallmentCount'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInsurance.InstallmentCount': calculationData['VehicleInsurance.InstallmentCount'].valueLocale})}
						</PivotItem>
					}

					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('Advanced options')} key={'Advanced options'}>
							{renderElement(new ContentViewModelProperty('VehicleInfo.VehicleGuards', calculationData['VehicleInfo.VehicleGuards'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded, rows: 5}), [], {'VehicleInfo.VehicleGuards': calculationData['VehicleInfo.VehicleGuards'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.SeatsCount', calculationData['VehicleInfo.SeatsCount'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.SeatsCount': calculationData['VehicleInfo.SeatsCount'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.Damaged', calculationData['VehicleInfo.Damaged'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]},  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.Damaged': calculationData['VehicleInfo.Damaged'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.NumberOfKeys', calculationData['VehicleInfo.NumberOfKeys'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.NumberOfKeys': calculationData['VehicleInfo.NumberOfKeys'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.AdditionalInformation', calculationData['VehicleInsurance.AdditionalInformation'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded, rows: 10}), [], {'VehicleInsurance.AdditionalInformation': vehicleInsuranceAdditionalInformationText})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.MadeNorthAmerica', calculationData['VehicleInfo.MadeNorthAmerica'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]},  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.MadeNorthAmerica': calculationData['VehicleInfo.MadeNorthAmerica'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInfo.ImportedInLast12Months', calculationData['VehicleInfo.ImportedInLast12Months'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]},  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInfo.ImportedInLast12Months': calculationData['VehicleInfo.ImportedInLast12Months'].valueLocale})}
						</PivotItem>
					}

					{Object.keys(calculationData).length > 0 &&
						<PivotItem headerText={L('Owner data')} key={'Owner data'}>
							{renderElement(new ContentViewModelProperty('VehicleOwner.MaritalStatus', calculationData['VehicleOwner.MaritalStatus'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleOwner.MaritalStatus': calculationData['VehicleOwner.MaritalStatus'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.ChildrenUnder26', calculationData['VehicleInsurance.ChildrenUnder26'].label, Controls.Picker, false, {dropdown: [{key: L("Yes"), text: L("Yes")}, {key: L("No"), text: L("No")}]}, true, {isDataLoaded: isDataLoaded}), [], {'VehicleInsurance.ChildrenUnder26': calculationData['VehicleInsurance.ChildrenUnder26'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.YearOfBirthOldestChild', calculationData['VehicleInsurance.YearOfBirthOldestChild'].label, Controls.Text, false, [], true, {isDataLoaded: isDataLoaded}), [], {'VehicleInsurance.YearOfBirthOldestChild': calculationData['VehicleInsurance.YearOfBirthOldestChild'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.YearOfPurchaseOfTheVehicle', calculationData['VehicleInsurance.YearOfPurchaseOfTheVehicle'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInsurance.YearOfPurchaseOfTheVehicle': calculationData['VehicleInsurance.YearOfPurchaseOfTheVehicle'].valueLocale})}
							{renderElement(new ContentViewModelProperty('VehicleInsurance.DrivingLicenceIssueYear', calculationData['VehicleInsurance.DrivingLicenceIssueYear'].label, Controls.Text, false, [],  true, {isDataLoaded: isDataLoaded}), [], {'VehicleInsurance.DrivingLicenceIssueYear': calculationData['VehicleInsurance.DrivingLicenceIssueYear'].valueLocale})}
						</PivotItem>
					}

					{this.props.activitiesPivotItem}
				</Pivot>
			</>
		);
	}
}