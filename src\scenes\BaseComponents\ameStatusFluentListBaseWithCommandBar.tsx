import { ITableColumn } from "./ITableColumn";
import { L } from "../../lib/abpUtility";
import { FluentTableBase } from "../Fluent/base/fluentTableBase";
import { IGenericPanelProps } from "../Fluent/base/genericPanel";
import { ICrudPermissons } from "./commandBarBase";
import { AgentClainAmeStatusPanel } from "../AgentClaimList/components/agentClaimAmeStatusPanel";
import { AgentClaimAmeStatusDto } from "../../services/agentClaimAmeStatus/dto/agentClaimAmeStatusDto";

export class AMEStatusFluentListBaseWithCommandBar extends FluentTableBase<AgentClaimAmeStatusDto> {
  disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;
  
  getItemDisplayNameOf(item: AgentClaimAmeStatusDto): string {
    return "";
  }

  getColumns(): ITableColumn[] {
    return this.getTableColumns(this.props);
  }

  private getTableColumns(props: any): ITableColumn[] {
    return [          
      {
        name: L('FV appraiser'),
        fieldName: 'appraiserFV',
      },
      {
        name: L('Pricing'),
        fieldName: 'pricing',
      },
      {
        name: L('Approved quotation'),
        fieldName: 'approvedQuotation',
      },
      {
        name: L('Currency'),
        fieldName: 'currency',
      },
    ];
  }

  getTitle(): string {
    return L('Claims');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: true,
      customActions: false,
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <AgentClainAmeStatusPanel
        {...props}
        store={this.props.store}
        customData={{claimId: this.props.customData.claimId}}
      />
    </>
  }
}