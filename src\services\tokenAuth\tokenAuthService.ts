import { AuthenticationModel } from './dto/authenticationModel';
import { AuthenticationResultModel } from './dto/authenticationResultModel';
import http from '../httpService';

class TokenAuthService {
  public async authenticate(authenticationInput: AuthenticationModel): Promise<AuthenticationResultModel> {
    let result = await http.post('api/TokenAuth/Authenticate', authenticationInput);
    return !!result.data && !!result.data.result ? result.data.result : result.data;
  }
}

const exportTokenAuthService: TokenAuthService = new TokenAuthService();
export default exportTokenAuthService;
