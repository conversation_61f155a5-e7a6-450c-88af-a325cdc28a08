import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { AgentClaimContentView } from './components/agentClaimContentView';
import { mergeStyleSets, FocusZone, MessageBar, MessageBarType, PrimaryButton, Link } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import { RouterPath } from '../../components/Router/router.config';
import AgentClaimStore from '../../stores/agentClaimStore';
import { AgentClaimDto } from '../../services/agentClaim/dto/agentClaimDto';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
	messageBar: {
        width: 'fit-content',
        marginTop: '40px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
});

export interface IProps {
	agentClaimStore: AgentClaimStore;
	match: any
}

@inject(Stores.AgentClaimStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private agentClaimId: string = this.props.match.params.id;
	private entityError: string = '';

	async componentDidMount() {
		await this.props.agentClaimStore.get({ id: this.agentClaimId } as AgentClaimDto);		
	}

	public render() {
		return (!!this.entityError ? 
			<>
				<MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`}>
					{this.entityError}
				</MessageBar>

				<Link href={`/${RouterPath.ClaimList}`}>
					<PrimaryButton text={L('Back')} style={{marginTop: 30}} />
				</Link>
			</>
            : 
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<AgentClaimContentView store={this.props.agentClaimStore} payload={ this.props.agentClaimStore.model as AgentClaimDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;