import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { UserPanel } from './userPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { ClientTypeEnum } from "../../../services/client/clientTypeEnums";
import { myTheme } from "../../../styles/theme";
import { Dialog, DialogType } from "@fluentui/react";
import { UserDto } from "../../../services/user/dto/userDto";
import userService from "../../../services/user/userService";
import { formatPhoneNumber } from "../../../utils/utils";

export class ArchivedUserTable extends FluentTableBase<UserDto> {
  private shouldReloadItems: boolean = false;
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";
  
  disableGetAllOnMount = true;

  getColumns(): ITableColumn[] {
    return ArchivedUserTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('UserName'),
        fieldName: 'userName',
      },
      {
        name: L('CustomerName'),
        fieldName: 'name',
      },
      {
        name: L('Surname'),
        fieldName: 'surname',
      },
      {
        name: L('E-mail'),
        fieldName: 'emailAddress',
      },
      {
        name: L('Phone'),
        fieldName: 'phoneNumber',
        onRender: (item: any): any => {
          return (item.phoneNumber && !!item.phoneNumber) ? formatPhoneNumber(item.phoneNumber) : '';
        }
      },
      {
        name: L('Roles'),
        fieldName: 'roleNames',
        onRender: (item: UserDto) => {
          let roleNames = item.roleNames.reduce((role: string, roles: string) => roles + ", " + role, "");
          return roleNames.slice(0, roleNames.length - 2);
        },
      },
    ];
  }
  
  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: false,
      delete: false,
      customActions: true,
    };
  }

  private reloadListOnDialogClose() {
    this.showPopUpDialog = false;

    if(this.shouldReloadItems) {
      this.reloadItems();
    }

    this.forceUpdate();
  }

  private async reloadItems() {
    this.selectionSetAllSelected(false);
    if(typeof this.props.refreshItems !== 'undefined') {
      await this.props.refreshItems!();
    } else {
      this.togglePopUpDialog("Error", "The list could not be refreshed automatically, please refresh the page manually.");
    }
  }

  private togglePopUpDialog(title: string, text: string, visibility?: boolean) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.forceUpdate();
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    return {
      ...props,
      customActionsProps: [
      {
        displayFor: 'single',
        buttonText: L("Restore"),
        buttonIcon: "RemoveFromTrash",
        buttonColor: myTheme.palette.white,
        buttonIconColor: myTheme.palette.white,
        buttonBackground: myTheme.palette.themeTertiary,
      }],
      customActions: [
        async (item: UserDto) => {
          let restoreResult = await userService.restore(parseInt(item.id));

          if(restoreResult && restoreResult.data && restoreResult.data.success) {
            this.reloadItems();
          } else {
            this.togglePopUpDialog("Error", "Error occured during attempt of client duplicate.");
          }
        }
      ]
    }
  }

  getTitle(): string {
    return L('Archived customer list');
  }
  
  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
        <Dialog
          hidden={!this.showPopUpDialog}
          onDismiss={() => this.reloadListOnDialogClose()}
          dialogContentProps={{
              type: DialogType.normal,
              title: L(this.popUpDialogTitle),
              subText: L(this.popUpDialogText),
          }}
          modalProps={{
              isBlocking: true
          }}
        >
      </Dialog>
      
      <UserPanel
        {...props}
      />
    </>;
  }
  
  copyAndSort<T>(items: T[], columnKey: string, isSortedDescending?: boolean): T[] {
    const key = columnKey as keyof T;
    if(key === 'company') {
      let concatColumn: any[] = [];
      items.forEach((item: any, index: number) => {
        if(item.clientType === ClientTypeEnum.Individual) {
          concatColumn.push({index: index, name: `${item.user.name} ${item.user.surname}`});
        } else {
          concatColumn.push({index: index, name: `${item.company}`});
        }
      });
      concatColumn.sort((a: any, b: any) => { 
        if(a.name < b.name)
          return isSortedDescending ? -1 : 1;
        if(a.name > b.name)
          return isSortedDescending ? 1 : -1;
        return 0;
      });

      let sortedItems: any[] = [];
      concatColumn.forEach((col: any) => {
        sortedItems.push(items[col.index]);
      });
      return sortedItems;
    } else {
      return items.slice(0).sort((a: any, b: any) => { 
        return (isSortedDescending ? a[key] < b[key] : a[key] > b[key]) ? 1 : -1;
      });
    }
  }
}