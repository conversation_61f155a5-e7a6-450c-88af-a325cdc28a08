import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";

export interface ProductAttributeDto extends BaseApiEntityModel {
  name: string;
  description: string;
  predefinedProductAttributeValues: PredefinedProductAttributeValueDto[];
}

export interface PredefinedProductAttributeValueDto extends BaseApiEntityModel {
  name: string;
  priceAdjustment: number;
  weightAdjustment: number;
  cost: number;
  isPreSelected: boolean;
  displayOrder: number;
}