<!DOCTYPE html>
<html>
<head>
    <title>Sign up or sign in</title>
</head>
<style>
    :root {
        --blue: #233762;
        --black: #000;
        --white: #fff;
    }
    body {
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        height: 100vh;
    }
    #api {
        display: flex;
        width: 325px;
        height: 100%;
        flex-direction: column;
        justify-content: center;
    }
    #attributeVerification {
        width: 100%;
        height: 80%;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        color: var(--black);
        font-size: 15px;
        font-weight: 500;
    }
    #attributeList {
        width: 100%;
        height: 80%;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        color: var(--black);
        font-size: 15px;
        font-weight: 500;
    }
    #attributeList ul {
        padding: 0;
    }
    #attributeList li {
        list-style: none;
    }
    .buttons {
        width: 100%;
        height: 20%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .buttons button {
        cursor: pointer;
        width: 100%;
        border: none;
        outline: none;
        background: var(--blue);
        padding: 15px 0;
        border-radius: 5px;
        color: var(--white);
        font-size: 19px;
        font-weight: 500;
        letter-spacing: 1px;
    }
</style>
<body>
    <div id="api">
        <div id="content">

        </div>
        <div id="buttons">

        </div>
    </div>
    <script>
        /*var attributeList = document.querySelector("#attributeList");
        var cancelButton = document.querySelector("#cancel");

        attributeList.remove();
        cancelButton.remove();

        document.querySelector("#content").appendChild(attributeList);
        document.querySelector("#buttons").appendChild(cancelButton);*/
    </script>
</body>
</html>