import { Icon, mergeStyleSets } from '@fluentui/react';
import * as React from 'react';
import Loading from '../../components/Loading';
import { L } from '../../lib/abpUtility';
import apkAttachedFilesService from '../../services/apkAttachedFiles/apkAttachedFilesService';
import policyCalculationAttachedFilesService from '../../services/attachedFiles/policyCalculationAttachedFilesService';
import {myTheme} from "../../styles/theme";
import policyAttachedFilesService from '../../services/attachedFiles/policyAttachedFilesService';
import clientAttachedFilesService from '../../services/attachedFiles/clientAttachedFilesService';

const classNames = mergeStyleSets({
    verificate: {
        height: '80vh',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
    },
    icon: {
        fontSize: '2.5rem',
    },
    iconError: {
        color: 'red',
    },
    iconSuccess: {
        color: myTheme.palette.green,
    }
});

export interface IProps {
    match?: any;
}

export interface IState {
    isSuccess: boolean | null;
    text: string;
}

export class Index extends React.Component<IProps, IState>  {
    constructor(props: any){
        super(props);
        this.state = {
            isSuccess: null,
            text : L("Please wait, we are sending you an SMS."),
        }
    }
    
    async componentDidMount() {
        if(this.props && this.props.match && !!this.props.match.path && this.props.match.params && this.props.match.params.uuid) {
            const splittedPath: string[] = this.props.match.path.split('/');
            if(splittedPath[0] === 'policy-calculation-apk' || (splittedPath[0].length === 0 && splittedPath[1] === 'policy-calculation-apk')) {
                await policyCalculationAttachedFilesService.SendPasswordBySms(this.props.match.params.uuid).then((response: any) => {
                    this.handleSuccess(response);
                }).catch((error: any) => {
                    this.handleError(error);
                });
            } else if(splittedPath[0] === 'apk-file' || (splittedPath[0].length === 0 && splittedPath[1] === 'apk-file')) {
                await apkAttachedFilesService.SendPasswordBySms(this.props.match.params.uuid).then((response: any) => {
                    this.handleSuccess(response);
                }).catch((error: any) => {
                    this.handleError(error);
                });
            } else if(splittedPath[0] === 'policy' || (splittedPath[0].length === 0 && splittedPath[1] === 'policy')) {
                await policyAttachedFilesService.SendPasswordBySms(this.props.match.params.uuid).then((response: any) => {
                    this.handleSuccess(response);
                }).catch((error: any) => {
                    this.handleError(error);
                });
            } else if(splittedPath[0] === 'rodo' || (splittedPath[0].length === 0 && splittedPath[1] === 'rodo')) {
                await clientAttachedFilesService.sendSmsCodeByUuid(this.props.match.params.uuid).then((response: any) => {
                    this.handleSuccess(response);
                }).catch((error: any) => {
                    this.handleError(error);
                });
            } else {
                window.location.href = '/exception';
            }
        } else {
            window.location.href = '/exception';
        }
    }

    private handleSuccess(response: any) {
        if(response && response.success) {
            this.setState({text: L("Message has been sent to your phone number, you can now close this page."), isSuccess: true});
        } else {
            this.handleError(response);
        }
    }

    private handleError(error: any) {
        this.setState({text: L("Something went wrong, please try again later or contact with administrator."), isSuccess: false});
    }

    render() {
        return <div className={classNames.verificate}>
            {this.state.isSuccess === null && <Loading />}
            {this.state.isSuccess === false && <Icon iconName={'Error'} className={`${classNames.icon} ${classNames.iconError}`} />}
            {this.state.isSuccess === true && <Icon iconName={'MailCheck'} className={`${classNames.icon} ${classNames.iconSuccess}`} />}
            <h3 style={{color: this.state.isSuccess === null ? '' : (this.state.isSuccess === false ? 'red' : 'green')}}>{this.state.text}</h3>
        </div>;
    }
}

export default Index;
