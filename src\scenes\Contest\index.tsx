import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { ContestContentView } from './components/contestContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import ContestStore from '../../stores/contestStore';
import { ContestDto } from '../../services/contest/dto/contestDto';
import ProductStore from '../../stores/productStore';
import PrizeStore from '../../stores/prizeStore';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	contestStore: ContestStore;
	productStore: ProductStore;
	prizeStore: PrizeStore;
	match: any
}

@inject(Stores.ContestStore)
@inject(Stores.ProductStore)
@inject(Stores.PrizeStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private contestId: string = this.props.match.params.id;

	async componentDidMount() {
		await this.props.contestStore.get({ id: this.contestId } as ContestDto);		
		await this.props.productStore.getAll();
		await this.props.prizeStore.getAll();
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<ContestContentView store={this.props.contestStore} payload={ this.props.contestStore.model as ContestDto } productStore={this.props.productStore}
					prizeStore={this.props.prizeStore} />
			</FocusZone>
		);
	}
}

export default Index;