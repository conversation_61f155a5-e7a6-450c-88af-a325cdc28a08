import { ContestDto } from "../../contest/dto/contestDto";
import {BaseApiEntityModel} from "../../dto/BaseApiEntityModel";
import { InsurancePolicyDto } from "../../insurancePolicy/insurancePolicyDto";
import { PrizeDto } from "../../prize/dto/prizeDto";

export interface ClientContestDto extends BaseApiEntityModel {
    clientId: number,
    agreement: boolean,
    contestId: number | null,
    contest: ContestDto | null,
    isReceived: boolean,
    isPrizeSentToClient: boolean,
    receivedPrizeId: number | null,
    receivedPrize: PrizeDto | null,
    isConditionsMeet: boolean,
    policyId: number | null,
    policy: InsurancePolicyDto | null,
    prizeNumber: string,
    postDate: string,
    isParticipates: boolean,
    oloPoints: number,
}