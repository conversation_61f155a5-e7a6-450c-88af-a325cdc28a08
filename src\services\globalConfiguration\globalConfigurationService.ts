import { isUserLoggedIn } from '../../utils/authUtils';
import { CrudServiceBase } from '../base/crudServiceBase';
import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { GlobalConfigurationDto } from './dto/globalConfigurationDto';

export class GlobalConfigurationService extends CrudServiceBase<GlobalConfigurationDto> {
    constructor() {
        super(Endpoint.GlobalConfiguration);
        this.internalHttp = httpApi;
    }

    public async getConfigurations(): Promise<GlobalConfigurationDto> {
        isUserLoggedIn();
        let result = await this.internalHttp.get(this.endpoint.Custom('GetConfigurations'), {});
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async updateGlobalConfiguration(id: number | string, isEnabled: boolean) {
        isUserLoggedIn();
        let result = await this.internalHttp.put(this.endpoint.Custom(`Update?id=${id}&isEnabled=${isEnabled}`));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportGlobalConfigurationService: GlobalConfigurationService = new GlobalConfigurationService();
export default exportGlobalConfigurationService;