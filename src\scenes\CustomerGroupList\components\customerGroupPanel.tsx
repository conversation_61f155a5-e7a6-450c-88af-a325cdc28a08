import { L } from '../../../lib/abpUtility';
import { CustomerGroupDto } from '../../../services/customerGroup/customerGroupDto';
import { CustomerGroupContentView } from '../../CustomerGroup/components/customerGroupContentView';
import { GenericPanel } from '../../Fluent/base/genericPanel';

export class CustomerGroupPanel extends GenericPanel {
    getPanelTitle(): string {
        return L('Customer group');
    }

    renderContent() {
        return <CustomerGroupContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as CustomerGroupDto } />;
    }
}