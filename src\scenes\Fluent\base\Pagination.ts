import * as React from "react";
import { IPaginationProps, IPaginationState } from "./pagination.types";

export default class Pagination extends React.Component<IPaginationProps, IPaginationState> {
  private _data: any = [];

  footerRef = React.createRef<HTMLDivElement>();

  state = {
    data: [],
    limit: 30,
    total: 0,
    page: 1,
    loading: false,
  };

  componentDidMount() {
    if (this.props.footerRef.current !== null) {
      this._observer.observe(this.props.footerRef.current);
    }
  }

  componentDidUpdate(prevProps: IPaginationProps) {
    const { reload, filter, add } = this.props;

    if (reload) {
      this._reload();
    }

    if (filter) {
      this._reload();
    }

    if (add) {}
  }

  componentWillUnmount() {
    if (this.props.footerRef.current !== null) {
      this._observer.unobserve(this.props.footerRef.current);
    }
  }

  private _handleObserver = (entry: any, observer: any) => {
    if (this.state.data.length < this.state.total) {}
  };

  private _observer = new IntersectionObserver(this._handleObserver, {
    root: null,
    rootMargin: "200px",
    threshold: 1,
  });

  private _reload = async () => {};

  public render() {
    const renderProps = {
      data: this.state.data,
      loading: this.state.loading
    };

    return this.props.render(renderProps);
  }
}