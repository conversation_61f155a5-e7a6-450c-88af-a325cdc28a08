import { transformPropsFirstLetter } from '../../utils/modelUtils';
import { CrudServiceBaseTransformed } from '../base/crudServiceBaseTransformed';
import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { CustomerGroupDto } from './customerGroupDto';

export class CustomerGroupService extends CrudServiceBaseTransformed<CustomerGroupDto> {
    constructor() {
        super(Endpoint.CustomerGroup);
        this.internalHttp = httpApi;
    }

    public async getFiltered(filterKey: string, filterValue: string) {
        let result = await this.internalHttp.get(this.endpoint.Custom(`?$filter=${filterKey} eq '${filterValue}'`, false));

        if(Array.isArray(result.data.value) && result.data.value.length > 0) {
            return transformPropsFirstLetter(result.data.value[0]);
        } else {
            return transformPropsFirstLetter(result.data.value);
        }
    }
}

const exportCustomerGroupService: CustomerGroupService = new CustomerGroupService();
export default exportCustomerGroupService;