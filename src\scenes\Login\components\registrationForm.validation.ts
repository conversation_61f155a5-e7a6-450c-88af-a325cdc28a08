import { L } from '../../../lib/abpUtility';
import { checkTextField } from '../../BaseComponents/createOrUpdate';

const rules = {
  login: [{ required: true, message: L('ThisFieldIsRequired') }],
  email: [{ required: true, message: L('ThisFieldIsRequired') }, { validator: checkTextField }],
  companyName: [{ required: true, message: L('ThisFieldIsRequired') }],
  nip: [{ required: true, message: L('ThisFieldIsRequired') }, { validator: checkTextField }],
  bdo: [{ required: true, message: L('ThisFieldIsRequired') }],
  phone: [{ validator: checkTextField }],
};

export default rules;
