import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import SearchStore from '../../stores/searchStore';
import { OcTerminationTable } from './components/ocTerminationTable';
import OcTerminationStore from '../../stores/ocTerminationStore';
import { additionalTheme } from '../../styles/theme';
import { mergeStyleSets } from '@fluentui/merge-styles';
import { L } from '../../lib/abpUtility';

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
});

export interface IProps {
  searchStore: SearchStore;
  ocTerminationStore: OcTerminationStore;
  history: any;
}

export interface IState {
  filterByStatus: string;
  filterByStatusOptions: any[];
  customRequest: any;
}

@inject(Stores.SearchStore)
@inject(Stores.OcTerminationStore)
@observer
class Index extends AppComponentBase<IProps, IState> {
  formRef: any;

  constructor(props: IProps) {
    super(props);

    this.state = {
      ...this.state,
    };
  }

  private async refreshItems() {
    await this.props.ocTerminationStore.getAllLazy().then(() => {
      this.forceUpdate();
    });
  }

  public render() {
    let items: any[] = this.props.ocTerminationStore.dataSet ? this.props.ocTerminationStore.dataSet.items : [];

    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('OC termination list')}</h2>
        </div>

        <OcTerminationTable
          searchText={this.props.searchStore.searchText}
          items={items}
          store={this.props.ocTerminationStore}
          refreshItems={() => this.refreshItems()}
          history={this.props.history}
        />
      </>
    );
  }
}

export default Index;