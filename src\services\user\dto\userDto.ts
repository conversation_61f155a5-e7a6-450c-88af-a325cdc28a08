export interface UserDto {
  userName: string;
  name: string;
  surname: string;
  fullName?: string;
  emailAddress: string;
  isActive: boolean;
  roleNames: string[];
  password: string;
  phoneNumber: string;
  id: string;
  isEmailConfirmed: boolean;
  organizationUnitIds: number[];
  avatarUrl?: string;
  directorId: number;
  directorFullName?: string;
  managerId: number;
  managerFullName?: string;
}