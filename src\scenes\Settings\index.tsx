import * as React from 'react';
import { L } from '../../lib/abpUtility';
import {CommandBar, ICommandBarItemProps, Sticky, StickyPositionType, ThemeProvider} from '@fluentui/react';
import {additionalTheme, myTheme} from '../../styles/theme';
import { classNames, CustomButton } from '../BaseComponents/commandBarBase';
import { OloCleverSettings } from './components/oloCleverSettings';
import { isConfigForAG } from '../../utils/authUtils';
import { GrandNodeSettings } from './components/grandNodeSettings';

export class Settings extends React.Component<any> {
  state = {
    currentPage: 'oloClever',
  };

  protected getItems(): ICommandBarItemProps[] {
    let array: ICommandBarItemProps[] = [];
    array.push(this.getGrandNode());
    if(!isConfigForAG()) {
      array.push(this.getOloClever());
    }

    return array;
  };

  commandBarClick = (currentItemName: string) => {
    this.setState({ currentPage: currentItemName });
  };

  protected getOloClever(): ICommandBarItemProps {
    let currentItemName = 'oloClever';
    return {
      key: currentItemName,
      text: L('OLO Clever'),
      className: classNames.buttons,
      style: {
        color: currentItemName === this.state.currentPage ? myTheme.palette.red : additionalTheme.grey,
        borderBottom: currentItemName === this.state.currentPage ? `5px solid ${additionalTheme.darkerRed}` : 'none',
      },
      onClick: (key) => {
        this.commandBarClick(currentItemName);
      },
    };
  };

  protected getGrandNode(): ICommandBarItemProps {
    let currentItemName = 'grandNode';
    return {
      key: currentItemName,
      text: L('Products versions'),
      className: classNames.buttons,
      style: {
        color: currentItemName === this.state.currentPage ? myTheme.palette.red : additionalTheme.grey,
        borderBottom: currentItemName === this.state.currentPage ? `5px solid ${additionalTheme.darkerRed}` : 'none',
      },
      onClick: (key) => {
        this.commandBarClick(currentItemName);
      },
    };
  };

  renderCurrentPage(): JSX.Element {
    let currentPage = <></>;

    switch (this.state.currentPage) {
      case 'oloClever':
        if(!isConfigForAG()) {
          currentPage = <OloCleverSettings />;
        }
        break;
      case 'grandNode':
        currentPage = <GrandNodeSettings />;
        break;
      default:
        currentPage = <GrandNodeSettings />;
        break;
    }

    return currentPage;
  };  

  public render() {
    let items = this.getItems();

    return (
      <>
        <ThemeProvider theme={myTheme}>
          <Sticky stickyPosition={StickyPositionType.Header}>
            {items.length === 0 ? 
              <p style={{marginTop: 50, fontWeight: 'bold', fontSize: '1.5rem'}}>{L('No data')}</p>
            :
              <>
                <div style={{textTransform: "uppercase", display: "flex", alignItems: "center",}}>
                  <h2 style={{fontSize: '26px', color: additionalTheme.grey, fontFamily:'sans-serif'}}>{L('Settings')}</h2>
                </div>
                <CommandBar theme={myTheme} items={items} className={classNames.customCommandbarAbout} buttonAs={CustomButton} />
              </>
            }
          </Sticky>

          {this.renderCurrentPage()}
        </ThemeProvider>
      </>
    );
  }
}

export default Settings;