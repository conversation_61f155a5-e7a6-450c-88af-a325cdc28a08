import { ProductDto } from '../../../services/product/productDto';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { AddSportsContentView } from '../../Product/components/addSportsContentView';

export class AddSportsPanel extends GenericPanel {
    getPanelTitle(): string {
        return L("Sports");
    }

    renderConfirm = () => {
        return <></>;
    };

    renderContent() {
        return <AddSportsContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ProductDto } customData={this.props.customData} />;
    }
}