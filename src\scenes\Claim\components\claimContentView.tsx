import { Default<PERSON>utton, IChoiceGroupOption, IDropdownOption, mergeStyleSets, Pivot, PivotItem, Selection, SelectionMode, Spinner, SpinnerSize, Stack, Text } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { ClaimDto } from '../../../services/claim/dto/claimDto';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { defaultChildrenClaim, defaultClaim, defaultHomeClaim, defaultLifeClaim, defaultTravelClaim, defaultVehicleClaim } from '../../../stores/claimStore';
import Stores from '../../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { Controls } from '../../BaseComponents/controls';
import { PolicyType } from '../../../services/policy/policyTypeEnums';
import { ClaimChildrenDto } from '../../../services/claim/dto/claimChildrenDto';
import { ClaimHomeDto } from '../../../services/claim/dto/claimHomeDto';
import { ClaimLifeDto } from '../../../services/claim/dto/claimLifeDto';
import { ClaimTravelDto } from '../../../services/claim/dto/claimTravelDto';
import { ClaimVehicleDto } from '../../../services/claim/dto/claimVehicleDto';
import insurancePolicyService from '../../../services/insurancePolicy/insurancePolicyService';
import { InsurancePolicyDto } from '../../../services/insurancePolicy/insurancePolicyDto';
import { dateFormat, enumToDropdownOptions, isJsonString } from '../../../utils/utils';
import { parseDtoToJsonString, presetTableInputData } from '../../../utils/inputUtils';
import { fluentTableClassNames } from '../../../styles/fluentTableStyles';
import { CustomerFluentListBase } from '../../BaseComponents/customerFluentListBase';
import { spinnerClassNames } from '../../../styles/spinnerStyles';
import { CrudConsts } from '../../../stores/crudStoreBase';
import policyAttachedFilesService from '../../../services/attachedFiles/policyAttachedFilesService';
import { LabeledTextField } from '../../../components/LabeledTextField';

const classNames = mergeStyleSets({
    attachedFilesLabel: {
        fontWeight: 'bold',
        marginTop: '20px',
        marginBottom: '10px',
    },
    attachedFilesListItem: {
        marginBottom: '5px',
    },
});

@inject(Stores.LanguageStore)
@inject(Stores.ClientStore)
export class ClaimContentView extends GenericContentView {
    private claim: ClaimDto = defaultClaim;
    private claimChildren: ClaimChildrenDto = defaultChildrenClaim;
    private claimHome: ClaimHomeDto = defaultHomeClaim;
    private claimLife: ClaimLifeDto = defaultLifeClaim;
    private claimTravel: ClaimTravelDto = defaultTravelClaim;
    private claimVehicle: ClaimVehicleDto = defaultVehicleClaim;
    private isEditMode: boolean | null = null;
    private insurancePolicies: InsurancePolicyDto[] = [];
    private policyTypeOptions: any = {
        dropdown: enumToDropdownOptions(PolicyType, false, true),
    };
    private insurancePolicyOptions: any = {
        dropdown: [] as IDropdownOption[],
    };
    private homeClaimDamage: any = {
        count: 0 as number,
        isSet: false as boolean,
    };
    private selectedClientFullname: string = "";
    private attachedFiles: any = [];
    private selectClientSearchText: string = "";
    private _clientListSelection: Selection = new Selection({
        onSelectionChanged: () => {
            const selectedClient: any = this._clientListSelection.getSelection();
            if(Array.isArray(selectedClient) && selectedClient.length > 0 && !!selectedClient[0].id) {
                this.claim.customerId = selectedClient[0].id.toString();
                this.selectedClientFullname = selectedClient[0].user.fullName;
                this._clientListSelection.setAllSelected(false);
                this.onCustomerSelect(selectedClient[0]);
            }
            this.forceUpdate();
        },
        selectionMode: SelectionMode.single
    });

    async componentDidMount() {
        await this.props.clientStore?.getAll({...this.props.clientStore?.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE});

        this.checkIfDataIsLoaded("claim");

        const tempClaim = this.props.payload.model ? this.props.payload.model : this.props.payload;
        if(tempClaim && (tempClaim.policyId > 0 || !!tempClaim.customerId)) {
            this.isEditMode = true;
            if(!!tempClaim.customerId && this.insurancePolicyOptions.dropdown.length === 0) {
                this.onCustomerSelect(undefined, tempClaim.customerId);
            }
        } else {
            this.isEditMode = false;
        }
        this.forceUpdate();
    }

    private async onCustomerSelect(customer: any, customerId?: string) {
        if(this.asyncActionInProgress) return;
        let newModel = this.state.model;

        if((customer && typeof customer !== 'undefined') || !!customerId) {
            newModel.value["customerId"] = !!customerId ? customerId : customer.customerId;

            this.asyncActionInProgress = true;
            this.forceUpdate();

            await insurancePolicyService.GetPolicyByCustomerId(newModel.value["customerId"]).then((response: any) => {
                if(response.totalCount > 0) {
                    this.insurancePolicies = response.items;
                    this.insurancePolicyOptions.dropdown = response.items.map((item: any) => {
                        return { key: item.id, 
                                text: `${item.insurer} | ${item.segment} | ${item.policyNumber}`, 
                                isSelected: this.claim.policyId === item.id };
                    }) as IDropdownOption[];

                    response.items.forEach(async (item: any) => {
                        if(item.id === this.claim.policyId) {
                            await policyAttachedFilesService.getByOrderId(item.orderId).then((downloadResponse: any) => {
                                if(downloadResponse && downloadResponse.items) {
                                    this.attachedFiles = downloadResponse.items;
                                    this.forceUpdate();
                                }
                            }).catch((error: any) => {
                                console.error(error);
                            });
                        }
                    });
                } else {
                    this.insurancePolicyOptions.dropdown = [];
                }
            });

            this.asyncActionInProgress = false;
            this.forceUpdate();
        } else {
            newModel.value["customerId"] = "";
            this.insurancePolicyOptions.dropdown = [];
        }
        this.setState({ model: newModel });
    }

    private selectClaimModel(policyType: PolicyType): any {
        switch(policyType) {
            case PolicyType.Children:
                this.claimChildren = this.props.payload.model ? this.props.payload.model : this.props.payload;
                return this.claimChildren;
            case PolicyType.Home:
                this.claimHome = this.props.payload.model ? this.props.payload.model : this.props.payload;
                return this.claimHome;
            case PolicyType.Life:
                this.claimLife = this.props.payload.model ? this.props.payload.model : this.props.payload;
                return this.claimLife;
            case PolicyType.Travel:
                this.claimTravel = this.props.payload.model ? this.props.payload.model : this.props.payload;
                return this.claimTravel;
            case PolicyType.Vehicle:
                this.claimVehicle = this.props.payload.model ? this.props.payload.model : this.props.payload;
                return this.claimVehicle;
            default:
                return this.claim;
        }
    }

    renderContent() {
        this.claim = this.props.payload.model ? this.props.payload.model : this.props.payload;

        let options: any = {
            choicegroup: [
                { key: 'true', text: L('Yes'), disabled: false },
                { key: 'false', text: L('No'), disabled: false },
            ] as IChoiceGroupOption[]
        };
        
        const selectedClaimModel = this.selectClaimModel(this.claim.policyType);

        let selectedInsurancePolicy = null;
        let filteredInsurancePolicy = this.insurancePolicies.filter((policy: InsurancePolicyDto) => policy.id === selectedClaimModel.policyId);
        if(filteredInsurancePolicy.length > 0) {
            selectedInsurancePolicy = filteredInsurancePolicy[0];
        }

        if(selectedClaimModel.policyType === PolicyType[PolicyType.Home] && !this.homeClaimDamage.isSet) {
            if(selectedClaimModel.listOfDamages && Object.keys(selectedClaimModel.listOfDamages).length > 0) {
                this.homeClaimDamage.count = Object.keys(selectedClaimModel.listOfDamages).length;
                this.homeClaimDamage.isSet = true;
            }
        } else if(selectedClaimModel.policyType !== PolicyType[PolicyType.Home]) {
            this.homeClaimDamage.count = 0;
            this.homeClaimDamage.isSet = false;
        }

        let clientPayloadInputs: JSX.Element[] = [];
        if(this.claim && !!this.claim.payload && isJsonString(this.claim.payload)) {
            const parsedPayload = JSON.parse(this.claim.payload);

            for(let key in parsedPayload) {
                if(parsedPayload.hasOwnProperty(key)) {
                    if(typeof parsedPayload[key] === 'boolean') {
                        clientPayloadInputs.push(
                            <LabeledTextField key={key} required={false} label={L(`claimClientPayload-${key}`)} value={parsedPayload[key] === true ? L('Yes') : L('No')}
                                disabled={true} isDataLoaded={this.isDataLoaded} onChange={(e) => {return}} />
                        );
                    } else if(typeof parsedPayload[key] === 'string' || typeof parsedPayload[key] === 'number') {
                        clientPayloadInputs.push(
                            <LabeledTextField key={key} required={false} label={L(`claimClientPayload-${key}`)} rows={3} multiline={true} 
                                value={typeof parsedPayload[key] === 'number' ? parsedPayload[key].toString() : parsedPayload[key]} 
                                disabled={true} isDataLoaded={this.isDataLoaded} onChange={(e) => {return}} />    
                        );
                    }
                }
            }
        }

        let attachedFilesList: JSX.Element[] = [];
        if(!!this.attachedFiles && this.attachedFiles.length > 0) {
            this.attachedFiles.forEach((file: any) => {
                attachedFilesList.push(
                    <li key={file.id} className={classNames.attachedFilesListItem}>
                        {dateFormat(file.creationTime, "DD.MM.YYYY HH:mm", true)}{` - `} 
                        <a href={file.fileUrl} title={L("Download file")}>{file.originalFileName}</a>
                    </li>
                );
            });
        }
        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        height: '5px',
                        backgroundColor: additionalTheme.darkerRed,
                    }
                }
            }
        };

        return typeof this.isEditMode === 'boolean' ?
            <Pivot theme={myTheme} styles={pivotStyles}>
                <PivotItem headerText={L('General')} key={'General'}>
                    {(this.isEditMode === false && this.props.clientStore) && 
                        <div className={fluentTableClassNames.contentContainer}>
                            <CustomerFluentListBase
                                // searchText={this.selectClientSearchText}
                                searchText={undefined}
                                items={this.props.clientStore.dataSet && this.props.clientStore.dataSet.items ? this.props.clientStore.dataSet.items : []}
                                store={this.props.clientStore}
                                history={this.props.history}
                                scrollablePanelMarginTop={70}
                                customData={{ 
                                    selectedClient: selectedClaimModel.customerId && !!this.selectedClientFullname ? `[${selectedClaimModel.customerId}] ${this.selectedClientFullname}` : undefined,
                                }}
                                // customOnSearchTextChanged={(text: string) => { this.selectClientSearchText = text; this.forceUpdate(); }}
                                customSelection={this._clientListSelection}
                                customOnSelectionChanged={(selection: any) => {
                                    if(typeof selection === 'string' && selection === 'deleteClient') {
                                        this.claim.customerId = defaultClaim.customerId;
                                        this.selectedClientFullname = "";
                                        this.forceUpdate();
                                    }
                                }}
                            />
                        </div>
                    }
                    
                    {this.asyncActionInProgress &&
                        <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                    }
                    
                    {this.renderElement(new ContentViewModelProperty('policyId', L("Policy"), Controls.Picker, false, this.insurancePolicyOptions, this.insurancePolicyOptions.dropdown.length === 0 || this.isEditMode, {isDataLoaded: this.isDataLoaded}), [], {'policyId': selectedInsurancePolicy !== null ? selectedInsurancePolicy.id : selectedClaimModel.policyId})}
                    {this.renderElement(new ContentViewModelProperty('policyType', '', Controls.Picker, false, this.policyTypeOptions, true, {isDataLoaded: this.isDataLoaded, hide: true}), [], {'policyType': selectedInsurancePolicy !== null ? selectedInsurancePolicy.segment : selectedClaimModel.policyType})}

                    {selectedClaimModel.policyType !== -1 && <>
                        {this.renderElement(new ContentViewModelProperty('claimDate', L("Claim date"), Controls.Date, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'claimDate': selectedClaimModel.claimDate})}
                        {this.renderElement(new ContentViewModelProperty('bankName', L("Bank name"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'bankName': selectedClaimModel.bankName})}
                        {this.renderElement(new ContentViewModelProperty('accountNumber', L("Account number"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'accountNumber': selectedClaimModel.accountNumber})}
                        {this.renderElement(new ContentViewModelProperty('insurerSiteLink', L("Insurer site link"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'insurerSiteLink': selectedClaimModel.insurerSiteLink})}
                        {this.renderElement(new ContentViewModelProperty('additionalInformation', L("Additional information"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'additionalInformation': selectedClaimModel.additionalInformation})}
                        {this.renderElement(new ContentViewModelProperty('comment', L("Comment"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'comment': selectedClaimModel.comment})}
                    </>}
                    {selectedClaimModel.policyType === PolicyType[PolicyType.Children] && <>
                        {this.renderElement(new ContentViewModelProperty('claimCauseList', L("Claim cause list"), Controls.TableInputs, false, {
                            tableInputs: {
                                "tableRowsCountFromInputId": "claimCauseCount",
                                "tableColumnHeaders": "pl=Nazwa zdarzenia;en=Cause name;;pl=Opis zdarzenia;en=Cause description;;",
                                "tableColumnInputs": "claimCauseName;;claimCauseDescription"
                            }
                        }, false, {isDataLoaded: this.isDataLoaded}), [], {
                                'claimCauseList': presetTableInputData(parseDtoToJsonString(selectedClaimModel.claimCauseList, 'ClaimCauseDto'), [
                                    {inputIdToPreset: 'claimCauseName', 
                                        values: [L('Damage to health as a result of an accident'), L('Medical costs'), L('Rehabilitation costs'),
                                                L('Seriously ill'), L('Hospital stay')]
                                    }
                                ]),
                                'claimCauseCount': 5
                            },
                        {'templateInputsForTable': {'claimCauseList': [
                            new ContentViewModelProperty('claimCauseName', L("Cause name"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded, rows: 2}),
                            new ContentViewModelProperty('claimCauseDescription', L("Cause description"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}),
                        ]}})}
                        {this.renderElement(new ContentViewModelProperty('anotherCause', L("Another cause"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'anotherCause': selectedClaimModel.anotherCause})}
                        {this.renderElement(new ContentViewModelProperty('claimCircumtances', L("Claim circumtances"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'claimCircumtances': selectedClaimModel.claimCircumtances})}
                    </>}

                    {selectedClaimModel.policyType === PolicyType[PolicyType.Home] && <> 
                        <div style={{ marginTop: '15px' }}>
                            <DefaultButton style={{ marginRight: '15px' }} theme={myTheme} text={L('Add claim row')} onClick={() => { this.homeClaimDamage.count++; this.forceUpdate(); } } />           
                            <DefaultButton theme={myTheme} text={L('Remove claim row')} onClick={() => { this.homeClaimDamage.count = this.homeClaimDamage.count - 1 < 0 ? 0 : this.homeClaimDamage.count - 1; this.forceUpdate(); } } />           
                        </div>
                        {this.renderElement(new ContentViewModelProperty('listOfDamages', L("List of damages"), Controls.TableInputs, false, {
                            tableInputs: {
                                "tableRowsCountFromInputId": "claimDamageCount",
                                "tableColumnHeaders": "pl=Szkoda;en=Claim;;pl=Wartość;en=Value;;",
                                "tableColumnInputs": "claimDamageClaim;;claimDamageValue"
                            }
                        }, false, {isDataLoaded: this.isDataLoaded}), [], {
                                'listOfDamages': parseDtoToJsonString(selectedClaimModel.listOfDamages, 'ClaimDamageDto'), 
                                'claimDamageCount': this.homeClaimDamage.count
                            },
                        {'templateInputsForTable': {'listOfDamages': [
                            new ContentViewModelProperty('claimDamageClaim', L("Claim"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 2}),
                            new ContentViewModelProperty('claimDamageValue', L("Value"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: 'number'}),
                        ]}})}
                        {this.renderElement(new ContentViewModelProperty('causeOfDamage', L("Cause of damage"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'causeOfDamage': selectedClaimModel.causeOfDamage})}
                    </>}

                    {selectedClaimModel.policyType === PolicyType[PolicyType.Travel] && <div style={{width:'fit-content'}}>
                        {this.renderElement(new ContentViewModelProperty('claimCauseList', L("Claim cause list"), Controls.TableInputs, false, {
                            tableInputs: {
                                "tableRowsCountFromInputId": "claimCauseCount",
                                "tableColumnHeaders": "pl=Nazwa zdarzenia;en=Cause name;;pl=Opis zdarzenia;en=Cause description;;",
                                "tableColumnInputs": "claimCauseName;;claimCauseDescription"
                            }
                        }, false, {isDataLoaded: this.isDataLoaded}), [], {
                                'claimCauseList': presetTableInputData(parseDtoToJsonString(selectedClaimModel.claimCauseList, 'ClaimCauseDto'), [
                                    {inputIdToPreset: 'claimCauseName', 
                                        values: [L('Doctor\'s visit costs'), L('Drug purchase costs'), L('Hospital treatment'),
                                                L('Damaged luggage'), L('Injury due to an accident'), L('An accident (with OC insurance) in private life')]
                                    }
                                ]), 'claimCauseCount': 6
                            },
                        {'templateInputsForTable': {'claimCauseList': [
                            new ContentViewModelProperty('claimCauseName', L("Cause name"), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded, rows: 2}),
                            new ContentViewModelProperty('claimCauseDescription', L("Cause description"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}),
                        ]}})}
                        {this.renderElement(new ContentViewModelProperty('anotherCause', L("Another cause"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'anotherCause': selectedClaimModel.anotherCause})}
                        {this.renderElement(new ContentViewModelProperty('claimCircumtances', L("Claim circumtances"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 5}), [], {'claimCircumtances': selectedClaimModel.claimCircumtances})}
                    </div>}

                    {selectedClaimModel.policyType === PolicyType[PolicyType.Vehicle] && <>
                        {this.renderElement(new ContentViewModelProperty('accidentPlace', L("Accident place"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'accidentPlace': selectedClaimModel.accidentPlace})}
                        {this.renderElement(new ContentViewModelProperty('policeNotified', L("Police notified"), Controls.ChoiceGroup, false, options, false, {isDataLoaded: this.isDataLoaded}), [], {'policeNotified': typeof selectedClaimModel.policeNotified !== 'undefined' && typeof selectedClaimModel.policeNotified !== 'string' ? selectedClaimModel.policeNotified.toString() : selectedClaimModel.policeNotified})}
                        {this.renderElement(new ContentViewModelProperty('policeAddress', L("Police address (if notified)"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'policeAddress': selectedClaimModel.policeAddress})}
                        {this.renderElement(new ContentViewModelProperty('vehicleInspectPlace', L("Vehicle inspect place"), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'vehicleInspectPlace': selectedClaimModel.vehicleInspectPlace})}
                        {this.renderElement(new ContentViewModelProperty('repairedAtASO', L("Repaired at ASO"), Controls.ChoiceGroup, false, options, false, {isDataLoaded: this.isDataLoaded}), [], {'repairedAtASO': typeof selectedClaimModel.repairedAtASO !== 'undefined' && typeof selectedClaimModel.repairedAtASO !== 'string' ? selectedClaimModel.repairedAtASO.toString() : selectedClaimModel.repairedAtASO})}
                    </>}
                </PivotItem>

                {(clientPayloadInputs && clientPayloadInputs.length > 0) &&
                    <PivotItem headerText={L('Data from client')} key={'dataFromClient'}>
                        {this.asyncActionInProgress &&
                            <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                        }

                        {clientPayloadInputs}

                        { attachedFilesList.length > 0 && <Stack>
                            <Text variant="large" className={classNames.attachedFilesLabel}>
                                { L('Attached files:') }
                            </Text>

                            <ul>
                                { attachedFilesList }
                            </ul>
                        </Stack> }

                        {this.asyncActionInProgress &&
                            <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
                        }
                    </PivotItem>
                }
            </Pivot>
        :
        <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
    }
}