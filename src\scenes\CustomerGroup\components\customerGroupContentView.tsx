import { Pivot, PivotItem } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import { Controls } from '../../BaseComponents/controls';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { defaultCustomerGroup } from '../../../stores/customerGroupStore';
import { CustomerGroupDto } from '../../../services/customerGroup/customerGroupDto';

export class CustomerGroupContentView extends GenericContentView {
    private customerGroup: CustomerGroupDto = defaultCustomerGroup;

    async componentDidMount() {
        this.checkIfDataIsLoaded("customerGroup");
    }

    renderContent() {
        this.customerGroup = this.props.payload.model ? this.props.payload.model : this.props.payload;

        const pivotStyles = {
            root: {
              marginLeft: '-8px'
            },
            linkIsSelected: {
              color: myTheme.palette.red,
              selectors: {
                ':before': {
                  height: '5px',
                  backgroundColor: additionalTheme.darkerRed
                }
              }
            }
          };

        return <Pivot theme={myTheme} styles={pivotStyles}>
            <PivotItem headerText={L('General')} key={'General'}>
                {this.renderElement(new ContentViewModelProperty('Name', L('Name'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'Name': this.customerGroup.Name})}
                {this.renderElement(new ContentViewModelProperty('SystemName', L('System name'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'SystemName': this.customerGroup.SystemName})}
                {this.renderElement(new ContentViewModelProperty('Active', L('Active'), Controls.CheckBox, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'Active': this.customerGroup.Active})}
                {this.renderElement(new ContentViewModelProperty('IsSystem', L('Is system'), Controls.CheckBox, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'IsSystem': this.customerGroup.IsSystem})}
                {this.renderElement(new ContentViewModelProperty('EnablePasswordLifetime', L('Enable password lifetime'), Controls.CheckBox, false, [], false, {isDataLoaded: this.isDataLoaded}), [], {'EnablePasswordLifetime': this.customerGroup.EnablePasswordLifetime})}
            </PivotItem>
        </Pivot>
    }
}