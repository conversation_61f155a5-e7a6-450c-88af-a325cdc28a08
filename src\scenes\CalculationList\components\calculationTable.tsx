import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { ICrudPermissons } from '../../BaseComponents/commandBarBase';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { Callout, Dialog, DialogType, Icon, IStackStyles, IStackTokens, Stack, Text, mergeStyleSets, Link } from "@fluentui/react";
import { RouterPath } from "../../../components/Router/router.config";
import { dateFormat, getNumberWithSpaces, isJsonString } from "../../../utils/utils";
import {additionalTheme, myTheme} from "../../../styles/theme";
import { CalculationDto } from "../../../services/calculation/dto/calculationDto";
import { CalculationPanel } from "./calculationPanel";
import calculationService from "../../../services/calculation/calculationService";
import { CalculationStatusEnum } from "../../../services/calculation/calculationStatusEnums";
import { PolicyType } from "../../../services/policy/policyTypeEnums";

const stackStyles: IStackStyles = {
  root: {
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      '& .ms-Icon': {
          display: 'none'
      }
  },
};

const customSpacingStackTokens: IStackTokens = {
  childrenGap: '0',
  padding: '25px 0',
};

const classNames = mergeStyleSets({
  '& .ms-ButtonFlexContainer .ms-Icon': {
    display:'none'
  },
  dialogWithHistoryResults: {
    selectors: {
      '& .ms-Dialog-main': {
        maxWidth: '85%',
        width: 'auto',
        minWidth: '1000px',
      }
    }
  },
  calculationBox: {
    minWidth: '214px',
    boxSizing: 'border-box',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '15px',
    cursor: 'default',
    borderRadius: '12px',
    marginRight: '40px',
    marginBottom: '25px',
    position: 'relative',
    selectors: {
      '& p': {
        color: myTheme.palette.themePrimary,
        fontWeight: 'bold',
        // fontSize: '1rem',
      },
      '& h3': {
        color: myTheme.palette.blue,
        fontSize: '1.2rem',
        selectors: {
          '& span': {
            color: myTheme.palette.black,
            fontSize: '0.8rem',
          }
        }
      },
      '& h5': {
        fontSize: '0.9rem',
      }
    }
  },
  calculationBoxError: {
    border: `2px solid ${myTheme.palette.red} !important`,
    cursor: 'not-allowed',
    selectors: {
      '& p': {
        color: `${myTheme.palette.red} !important`,
      },
      '& h3': {
        color: `${myTheme.palette.red} !important`,
        textDecoration: 'line-through',
      }
    }
  },
  calculationBoxWarning: {
    border: `2px solid ${myTheme.palette.yellow} !important`,
    cursor: 'not-allowed',
    selectors: {
      '& p': {
        color: `${myTheme.palette.yellowDark} !important`,
      },
      '& h3': {
        color: `${myTheme.palette.yellowDark} !important`,
        textDecoration: 'line-through',
      }
    }
  },
  calculationBoxDisabled: {
      border: `2px solid ${myTheme.palette.themeDark} !important`,
      background: `${myTheme.palette.blackTranslucent40} !important`,
      cursor: 'not-allowed',
      selectors: {
          '& p': {
              color: myTheme.palette.themeDark,
          },
          '& h3': {
              color: `${myTheme.palette.themeDark} !important`,
          }
      }
  },
  calculationBoxIcon: {
      position: 'absolute',
      top: '5px',
      right: '5px',
      color: myTheme.palette.themePrimary,
      fontSize: '22px',
      cursor: 'pointer',
  },
  calculationBoxErrorIcon: {
      color: myTheme.palette.red,
      left: '5px',
      right: 'none',
  },
  calculationBoxWarningIcon: {
      color: `${myTheme.palette.yellow} !important`,
  },
  calculationBoxOutputDetailsIcon: {
      bottom: '5px',
      top: 'none',
      left: '5px',
      right: 'none',
      color: myTheme.palette.neutralQuaternary,
  },
  notSelectedCalculationBox: {
      border: `1px solid ${myTheme.palette.themePrimary}`,
      background: myTheme.palette.themeLighterAlt,
      marginTop: '1px',
      marginLeft: '1px',
      marginRight: '51px',
      marginBottom: '26px',
  },
  selectedCalculationBox: {
      border: `2px solid ${myTheme.palette.blue}`,
      background: myTheme.palette.themeLighter,
      selectors: {
          '& p': {
              color: myTheme.palette.blue,
          },
          '& h3': {
              color: `${myTheme.palette.blue} !important`,
          }
      }
  },
  callout: {
      width: 320,
      maxWidth: '90%',
      padding: '20px',
      cursor: 'pointer',
  },
  calloutText: {
      whiteSpace: 'pre-line',
  },
  hide: {
      display: 'none !important',
  },
  messageBar: {
      width: 'fit-content'
  },
  customActionButton: {
      width: 'fit-content',
      padding: '25px 50px',
      marginTop: '0 !important',
      marginRight: '20px',

  },
  fontBold: {
      fontWeight: '800',
  },
  summaryAttributesWrapper: {
      display: 'flex',
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      border: `1px solid ${myTheme.palette.themeLight}`,
      padding: '15px',
      maxWidth: '90%',
      marginBottom: '15px',
      marginTop: '15px',
  },
  summaryAttribute: {
        color: `${additionalTheme.white} !important` ,
      padding: '5px 10px',
      marginRight: '10px',
      marginBottom: '5px',
      background: myTheme.palette.themeLighter,
      fontSize: '0.8rem',
      whiteSpace: 'pre-line',
      selectors: {
          '&:last-child': {
              borderRight: 'none',
          }
      }
  },
  historyResultRowWrapper: {
  },
  activeHistoryResultRowWrapper: {
      background: myTheme.palette.themeLighterAlt,
      padding: '5px 25px 5px',
  },
  historyResultDateLabel: {
      cursor: 'pointer',
      fontWeight: 'bold',
      fontSize: '1.2rem',
      marginTop: 5
  },
  historyResultRow: {
      selectors: {
          '& .ms-Stack': {
              paddingTop: 0,
              paddingBottom: 0,
          }
      }
  },
  statusIcon: {
    fontSize: '1.2rem',
    textAlign: 'center',
    display: 'block',
  }
});

export class CalculationTable extends FluentTableBase<CalculationDto> {
  private shouldReloadItems: boolean = false;
  private showPopUpDialog: boolean = false;
  private popUpDialogTitle: string = "";
  private popUpDialogText: string = "";
  private popUpDialogMode: string = "message";
  private customActionButtonDisabled: boolean = false;
  private calloutStatus: any = {};
  private customInputsForCalculationIndex: number = -1;
  private jsonResponseHistory: any[] = [];
  private historyResultListShowRow: number = -1;

  getColumns(): ITableColumn[] {
    return CalculationTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        minWidth: 45,
        maxWidth: 45,
        name: L('Status'),
        fieldName: 'lastCreatedPolicyStatus',
        onRender: (item: any) => {
          return <Icon className={classNames.statusIcon}
                    style={item.lastCreatedPolicyStatus === CalculationStatusEnum.Finalized ? {color: "green"} : (item.lastCreatedPolicyStatus === CalculationStatusEnum.Applicated ? {color: "orange"} : {color: "red"})} 
                    iconName={ item.lastCreatedPolicyStatus === CalculationStatusEnum.Finalized ? "SkypeCheck" : (item.lastCreatedPolicyStatus === CalculationStatusEnum.Applicated ? "HourGlass" : "StatusCircleErrorX")}
                    title={L(item.lastCreatedPolicyStatus)}
                  />
        }
      },
      {
        minWidth: 90,
        maxWidth: 90,
        name: L('Calculation sent'),
        fieldName: 'sendedToClient',
        onRender: (item: any) => {
          return <Icon className={classNames.statusIcon}
                    style={item.sendedToClient === true ? {color: "green"} : {color: "red"}} 
                    iconName={item.sendedToClient === true ? "SkypeCheck" : "StatusCircleErrorX"}
                  />
        }
      },
      {
        name: L('Calculation ID 2'),
        fieldName: 'id',
        minWidth: 70,
        maxWidth: 70,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Calculation}/${item.id}`);
                      }} 
                        href={`/${RouterPath.Calculation}/${item.id}`}>
                  {item.id}
                </Link>
        }
      },
      {
        name: L('Agent'),
        fieldName: 'item.agent.fullName',
        minWidth: 150,
        maxWidth: 150,
        onRender: (item: any): any => {
          return item && item.agent ? item.agent.fullName : "";
        }
      },
      {
        name: L('Insurance start date'),
        fieldName: 'startDate',
        onRender: (item: any): any => {
          if (item.startDate) {
            return dateFormat(item.startDate, 'DD.MM.YYYY');
          } else {
            return "";
          }
        }
      },
      {
        name: L('Insurer2'),
        fieldName: 'insurer',
        minWidth: 160,
        maxWidth: 160,
        onRender: (item: any): any => {
          return item.insurer;
        }
      },
      {
        name: L('Product'),
        fieldName: 'type',
        onRender: (item: any): any => {
          return item.payload && isJsonString(item.payload) ? L(`${PolicyType[JSON.parse(item.payload).type]}2`) : '-';
        }
      },
      {
        name: L('Client name'),
        fieldName: 'fullName',
        minWidth: 150,
        maxWidth: 150,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Customer}/${item.client.id}`);
                      }} 
                        href={`/${RouterPath.Customer}/${item.client.id}`}>
                  {item.client ? item.client.fullName : ""}
                </Link>
        }
      },
      {
        name: L('Pesel'),
        fieldName: 'pesel',
        minWidth: 130,
        maxWidth: 130,
        onRender: (item: any): any => {
          return item.client.pesel;
        }
      },
      {
        name: L('Vehicle registration number'),
        fieldName: 'vehicleRegistrationNumber',
        minWidth: 300,
        maxWidth: 300,
      },
      {
        name: L('Creation time'),
        fieldName: 'creationTime',
        onRender: (item: any): any => {
          return dateFormat(item.creationTime, undefined, true);
        }
      },
    ];
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: true,
      delete: true,
      customActions: true,
    };
  }

  getTitle(): string {
    return L('Calculations');
  }

  private async reloadItems() {
    this.selectionSetAllSelected(false);
    if(typeof this.props.refreshItems !== 'undefined') {
      await this.props.refreshItems!();
    } else {
      this.togglePopUpDialog("Error", "The list could not be refreshed automatically, please refresh the page manually.");
    }
  }

  private togglePopUpDialog(title: string, text: string, mode?: string) {
    this.popUpDialogTitle = title;
    this.popUpDialogText = text;
    this.showPopUpDialog = true;
    this.popUpDialogMode = "message";
    if(!!mode) {
      this.popUpDialogMode = mode;
    }
    this.forceUpdate();
  }

  private reloadListOnDialogClose() {
    this.showPopUpDialog = false;

    if(this.shouldReloadItems) {
      this.reloadItems();
    }

    this.forceUpdate();
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    return {
      ...props,
      setButtons: () => {
        return {
          'newItem': {text: 'New', icon: 'Add'},
          'delete': {text: 'Delete'},
          'edit': {text: 'Details',},
          'newMsg': {text: 'Msg', icon: ''},
        }
      },
      customActionsProps: [
        {
          displayFor: 'single',
          buttonText: L("Edit calculation"),
          buttonIcon: "none",
          buttonDisabled: this.customActionButtonDisabled,
          buttonColor: myTheme.palette.black,
          buttonIconColor: myTheme.palette.white,
          buttonBackground: myTheme.palette.white,
        },
        {
          displayFor: 'single',
          buttonText: L("Offers history"),
          buttonIcon: "none",
          buttonDisabled: this.customActionButtonDisabled,
          buttonColor: myTheme.palette.black,
          buttonIconColor: myTheme.palette.white,
          buttonBackground: myTheme.palette.white,
        }
      ],
      customActions: [
        (calculation: CalculationDto) => {
          if(calculation && calculation.id && parseInt(calculation.id) > 0 && calculation.lastCreatedPolicyStatus !== CalculationStatusEnum.Finalized) {
            this.props.history.push(`/${RouterPath.PolicyCalculation}/?entityType=calculation&entityId=${calculation.id}`);
          } else if(calculation.lastCreatedPolicyStatus === CalculationStatusEnum.Finalized) {
            this.togglePopUpDialog("Action unavailable", "You cannot edit a finalized calculation (policy has been concluded).");
          } else {
            this.togglePopUpDialog("Action unavailable", "This calculation cannot be edited.");
          }
        },
        async (calculation: CalculationDto) => {
          this.jsonResponseHistory = [];

          await calculationService.getHistory(calculation).then((response: any) => {
              if(response && response.totalCount > 0) {
                response.items.forEach((element: any) => {
                    this.jsonResponseHistory.push({date: dateFormat(element.modificationDate, undefined, true), result: element.result, request: element.request});
                });

                this.forceUpdate();
                this.togglePopUpDialog("Offers history:", "", "history");
              } else {
                this.togglePopUpDialog("", "Offers history is empty.");
              }
            }).catch((error) => {
              console.error(error);
              this.togglePopUpDialog("Error", "There was an error when getting offers history for this calculation.");
          });
        },
      ]
    }
  }

  private getCalculationsFromRawResult(rawResult: any, request: any): any {
    let parsedResult: any = null;
    let calculations: any = {};

    if(typeof rawResult === 'object' || (typeof rawResult === 'string' && isJsonString(rawResult))) {
        parsedResult = JSON.parse(rawResult);

        if(!!parsedResult) {
            calculations['data'] = {};
            calculations['data']['error'] = null;
            calculations['data']['result'] = {};
            calculations['data']['result']['policyCalculations'] = [...parsedResult];
            calculations['data']['result']['request'] = !!request ? JSON.parse(request) : null;
        }
    }

    let rawRequest: any = null;
    let tempCalculations: any = calculations;
    tempCalculations = tempCalculations && tempCalculations.data ? tempCalculations.data : L('No data');
    if(typeof tempCalculations !== 'string' && tempCalculations.error === null) {
        if(tempCalculations.result && tempCalculations.result.policyCalculations) {
            if(tempCalculations.result.request && rawRequest === null) {
                rawRequest = tempCalculations.result.request;
            }
            tempCalculations = tempCalculations.result.policyCalculations;
        } else {
            tempCalculations = L('No data');
        }
    } else {
        tempCalculations = tempCalculations.error;
    }

    return tempCalculations;
  }

  private toggleCallout(index: number, newState: boolean) {
    if(typeof index !== 'undefined' && index >= 0) {
        this.calloutStatus[index] = newState;
        this.forceUpdate();
    }
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <Dialog
        hidden={!this.showPopUpDialog}
        onDismiss={() => this.reloadListOnDialogClose()}
        dialogContentProps={{
            type: DialogType.normal,
            title: L(this.popUpDialogTitle),
            subText: L(this.popUpDialogText),
        }}
        modalProps={{
            isBlocking: true,
            className: this.popUpDialogMode === 'history' ? classNames.dialogWithHistoryResults : undefined
        }}
      >
        {this.popUpDialogMode === 'history' &&
          <>
            {this.jsonResponseHistory.map((historyItem: any, i: number) => {
              let tempCalculations: any = this.getCalculationsFromRawResult(historyItem.result, historyItem.request);

              return <div className={`${classNames.historyResultRowWrapper} ${this.historyResultListShowRow === i && classNames.activeHistoryResultRowWrapper}`}>
                <p className={classNames.historyResultDateLabel} onClick={() => { 
                  if(this.historyResultListShowRow === i) {
                      this.historyResultListShowRow = -1; 
                  } else {
                      this.historyResultListShowRow = i; 
                  }
                  this.forceUpdate();
                }}>{historyItem.date}</p>

                <div className={classNames.historyResultRow} style={{display: this.historyResultListShowRow === i ? 'block' : 'none'}}>
                  { historyItem.result === null || (typeof historyItem.result !== 'object' && typeof historyItem.result === 'string' && !isJsonString(historyItem.result)) ? 
                    <p style={{color: 'orange'}}>{L('Result is not a valid JSON.')}</p>
                    :
                    <Stack horizontal styles={stackStyles} tokens={customSpacingStackTokens}>
                      {Array.isArray(tempCalculations) ?
                        tempCalculations.map((calculation: any, index: number) => {
                          let tempCalloutIndex = (tempCalculations.length + (i + 1)) * (index + 10);
                          
                          return <div key={index}
                                  className={`${classNames.calculationBox} ${calculation.success ? '' : (calculation.rideryErrors && calculation.rideryErrors.length > 0 ? classNames.calculationBoxWarning : classNames.calculationBoxError)} 
                                              ${classNames.notSelectedCalculationBox}
                                              ${this.customInputsForCalculationIndex >= 0 && this.customInputsForCalculationIndex !== index ? classNames.calculationBoxDisabled : ''}`}
                          >
                            <p>{calculation.insurerName}</p> 
                            <h3>{calculation.price} <span>{calculation.currency}</span></h3>
                            { (calculation.insuranceSum) ?
                              <h5 style={{marginTop: 0, marginBottom: 10}}>SU: {getNumberWithSpaces(calculation.insuranceSum)} <span>{calculation.currency}</span></h5>
                              :
                              <span style={{display: 'block', height: 28}}></span>
                            }
                            { (calculation.errors && calculation.errors.length > 0) &&
                              <Icon onClick={ () => this.toggleCallout(tempCalloutIndex, true) } iconName={'WarningSolid'} className={`${classNames.calculationBoxIcon} ${classNames.calculationBoxErrorIcon} ${(calculation.rideryErrors && calculation.rideryErrors.length > 0) && classNames.calculationBoxWarningIcon}`}
                                  id={`calculationOffer${tempCalloutIndex}`} title={L('Errors have occurred')} /> }
                            
                            {(this.calloutStatus[tempCalloutIndex] && this.calloutStatus[tempCalloutIndex] === true) && (
                              <Callout
                                className={classNames.callout}
                                gapSpace={0}
                                target={`#calculationOffer${tempCalloutIndex}`}
                                onDismiss={ () => this.toggleCallout(tempCalloutIndex, false) }
                                setInitialFocus
                              >
                                <Text className={classNames.calloutText} block variant="small">
                                  { calculation.rideryErrors && calculation.rideryErrors.length > 0 ?
                                    calculation.rideryErrors.map((rideryError: any) => {
                                        return `${L(rideryError)} \n\r\n\r`
                                    }) :
                                    (calculation.errors && calculation.errors.length > 0 ?
                                        calculation.errors.map((error: any) => {
                                            return `${L(error)} \n\r\n\r`
                                        }) : '')
                                  }
                                </Text>
                              </Callout>
                            )}
                          </div>;
                        })
                      : <p>{tempCalculations}</p>}
                    </Stack>
                  }
                </div>
              </div>;
            })}
          </>
        }
      </Dialog>

      <CalculationPanel
        {...props}
        customData={{createPolicyFromOffer: () => this.props.customData.createPolicyFromOffer()}}
      />
    </>
  }
}