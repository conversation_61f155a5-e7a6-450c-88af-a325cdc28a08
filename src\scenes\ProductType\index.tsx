import { inject, observer } from 'mobx-react';

import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import ProductTypeStore from '../../stores/productTypeStore';
import { ProductTypeDto } from '../../services/productType/productTypeDto';
import { ProductTypeContentView } from './components/productTypeContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	productTypeStore: ProductTypeStore;
	match: any
}

@inject(Stores.ProductTypeStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private productTypeId = this.props.match.params.id;

	async componentDidMount() {
		await this.props.productTypeStore.get({ id: this.productTypeId } as ProductTypeDto);
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<ProductTypeContentView store={this.props.productTypeStore} payload={ this.props.productTypeStore.model as ProductTypeDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;