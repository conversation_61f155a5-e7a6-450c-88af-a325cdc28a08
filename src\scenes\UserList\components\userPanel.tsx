import { UserDto } from '../../../services/user/dto/userDto';
import { UserContentView } from '../../User/components/userContentView';
import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { DefaultButton, PrimaryButton } from '@fluentui/react';
import { myTheme } from '../../../styles/theme';

export class UserPanel extends GenericPanel {
    private hideConfirmButton: boolean = false;
    
    getPanelTitle(): string {
        return L("User");
    }

    renderConfirm = () => {
        return <PrimaryButton theme={myTheme} 
                    onClick={() => {
                        this.hideConfirmButton = false;
                        this.forceUpdate();
                        this._onConfirm();
                    }} 
                    text={L('Save')}
                    style={{display: this.hideConfirmButton ? 'none' : 'unset'}}
                    disabled={this.asyncActionInProgress}
                />
    };

    renderCancel = () => {
        return <DefaultButton theme={myTheme} text={L('Cancel')}
            onClick={() => {
                this.hideConfirmButton = false;
                this.forceUpdate();
                this._onCancel();
            }}
        />
    };

    renderContent() {
        return <UserContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as UserDto }
            toggleHideConfirm={(hide: boolean) => { this.hideConfirmButton = hide; this.forceUpdate(); }}
        />;
    }
}