import { isUserLoggedIn } from "../../utils/authUtils";
import { getPartialModel } from "../../utils/modelUtils";
import { CrudServiceBase } from "../base/crudServiceBase";
import Endpoint from "../endpoint";
import { httpApi } from "../httpService";
import { IndividualOfferAttachedFileDto } from "./individualOfferAttachedFileDto";

export class IndividualOfferAttachedFileService extends CrudServiceBase<IndividualOfferAttachedFileDto> {
    constructor() {
        super(Endpoint.IndividualOfferAttachedFile);
        this.internalHttp = httpApi;
    }

    public async create(createAttachedIndividualOfferInput: IndividualOfferAttachedFileDto) {
        isUserLoggedIn();
        let newCreateUserInput = getPartialModel(createAttachedIndividualOfferInput, [], ['id']);
        let result = await httpApi.post(this.endpoint.Create(), newCreateUserInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getPdfFiles(id: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`Get?id=${id}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportIndividualOfferAttachedFileService: IndividualOfferAttachedFileService = new IndividualOfferAttachedFileService();
export default exportIndividualOfferAttachedFileService;