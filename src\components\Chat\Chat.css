.chat__load-spinner {
    /* position: absolute;
    top: 10px;
    right: 8px; */

    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;
}

.chat__message {
    display: flex;
    flex-direction: column;
    padding: 10px;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 12px;
    margin: 15px;
    max-width: 70%;
}

.chat__message--text p {
    margin: 0;
}

.chat__message--time-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 10px;
}

.chat__message--time-container p {
    margin: 0 0 0 10px;
    font-size: 12px;
    font-style: italic;
    color: #7A7A7A;
}

/* .chat__message:last-of-type {
    padding: 1px 5px 10px;
} */

.chat__message--name {
    margin-right: 4px;
    font-weight: bold;
    white-space: nowrap;
}

.chat__message--my-messages {
    background-color: #CCD2E3;
    margin-left: auto;
}

.chat__message--my-messages > .chat__message--name {
    text-decoration: underline;
}

.chat__message--other-messages {
    background-color: #F5F5F5;
}

.chat__message--chat-started {
    margin-left: auto;
    margin-right: auto;
    margin-top: 0;
    background-color: #ffffff00;
    text-align: center;
    padding: 10px;
    box-shadow: none;
    border-radius: unset;
    max-width: 60%;
    font-size: 14px;
    font-style: italic;
    color: #7A7A7A;
}

.chat__message--chat-ended {
    margin-left: auto;
    margin-right: auto;
    background-color: #ffe2e2;
    text-align: center;
    font-weight: bold;
    margin-top: 30px;
}

.chat__window {
    background-color: white;
    padding: 1px 0px 1px;
    overflow-y: auto;
    overflow-x: hidden;
    height: 550px;
    width: 400px;
    /* box-shadow: inset 0px 11px 5px -10px rgba(0, 0, 0, 0.25),
                inset 0px -11px 5px -10px rgba(0, 0, 0, 0.25);  */
}

.chat__window--guest {
    height: 550px;
    width: 400px;
    text-align: center;
}

.chat__window--waiting-message {
    font-size: 18px;
    text-align: center;
    font-style: italic;
    color: #7A7A7A;
    padding-top: 30px;
    max-width: 340px;
    margin: 18px auto;
}

.chat__window--loading {
    display: flex;
    justify-content: center;
    align-items: center;
}

.chat__input {
    /* background-color: white;
    border-radius: 4px;
    border: 1px solid #0078d4;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
    padding: 4px; */
}

.chat__input--form {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
}

.chat__input--message {
    flex: auto;
    border: 1px solid #BCBCBC;
    padding: 10px;
    font-size: 16px;
    border-radius: 8px;
}

.chat__input--message:focus {
    outline: 1px solid #BCBCBC;
}

.chat__container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: "Segoe UI", "Segoe UI Web (West European)", "Segoe UI", -apple-system, BlinkMacSystemFont, Roboto, "Helvetica Neue", sans-serif;
    border-radius: 4px;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
    background-color: white;
    padding: 5px;
}

.chat__container--not-visible {
    background-color: rgba(255, 255, 255, 0);
    border: none;
    box-shadow: none;
    right: 30px;
}

.chat__navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 10px;
}

.chat__navbar p {
    font-size: 20px;
    margin: 0;
}

.chat__button--wrapper {
    display: flex;
    align-items: center;
}

.chat__button {
    color: rgb(255, 255, 255);
    padding: 8px 15px;
    border-radius: 8px;
    width: fit-content;
    display: inline-block;
    margin: 4px 4px 8px;
    background-color: #0078d4;
    transition: all 100ms;
    position: relative;
}

.chat__button--circle, .chat__button--custom-icon--container {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-left: 15px;
    background-color: #0078d4;
    transition: all 100ms;
    position: relative;
}

.chat__button--custom-icon--container {
    margin-left: 10px;
}

.chat__button--custom-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
}

.chat__button--circle i {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    font-weight: 700;
    color: #fff;
}

.chat__button:hover, .chat__button--circle:hover, .chat__button--custom-icon--container:hover {
    cursor: pointer;
    background-color: rgb(28, 44, 78);
}

.chat__button--notification-counter, .chat__button--notification-counter-type-2 {
    position: absolute;
    top: -5px;
    left: -10px;
    min-width: 18px;
    min-height: 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: red;
    color: white;
    padding: 1px;
    border-radius: 50%;
}

.chat__button--notification-counter-type-2 {
    top: -5px;
    right: -10px;
    left: unset;
    background-color: orange;
}

.chat__button--notification-counter > p, .chat__button--notification-counter-type-2 > p {
    margin: 0;
    padding: 0;
    font-size: 11px;
}

/* Closed */
.chat__open {
    width: 60px;
    height: 60px;
    background-color: #0078d4;
    border-radius: 30px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    transition: all 100ms;
}

.chat__open:hover {
    background-color: #0078d4;
    transform: scale(1.1);
}

.chat__icon {
    color: white;
    font-size: 22px;
}

.chat__icon--notification-counter, .chat__icon--notification-counter-type-2, .chat__icon--load-spinner {
    position: absolute;
    top: -10px;
    left: -10px;
    min-width: 25px;
    min-height: 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: red;
    color: white;
    padding: 2px;
    border-radius: 50%;
}

.chat__icon--notification-counter-type-2 {
    left: unset;
    right: -10px;
    background-color: orange;
}

.chat__icon--load-spinner {
    background-color: rgba(0, 0, 0, 0.1);
    padding: 14px;
    top: -14px;
    left: -14px;
}

.chat__icon--notification-counter > p, .chat__icon--notification-counter-type-2 > p {
    margin: 0;
    padding: 0;
}

/* Chat Threads View */
.chat__option {
    position: relative;
    background-color: #f5f5f5;
    box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 50ms;
    cursor: pointer;
    margin: 20px;
    padding: 20px;
    /* margin: 4px;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 50ms; */
}

.chat__option-name {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.chat__option div {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat__option-circle {
    width: 10px;
    min-width: 10px;
    height: 10px;
    min-height: 10px;
    background-color: #0078d4;
    border-radius: 50%;
    margin: 0 10px;
}

.chat__option-date {
    font-size: 14px;
    font-style: italic;
    margin: 0;
    color: #7A7A7A;
    text-align: center;
}

.chat__option-status {
    font-size: 14px;
    font-style: italic;
    margin: 0;
    color: #37af00;
    text-align: center;
}

.chat__option-status--red {
    color: #ff4949;
}

.chat__option-status--orange {
    color: #ff9900;
}

.chat__option:hover {
    opacity: 0.5;
}

.chat__option--user {
    border: 1px solid lightgray;
}

.chat__option--notification {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 40px;
    min-width: 25px;
    min-height: 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: red;
    color: white;
    padding: 2px;
    border-radius: 50%;
}

.chat__option--notification > p {
    margin: 0;
    padding: 0;
}

.chat__option--disabled {
    background-color: rgb(225, 225, 225);
    pointer-events: none;
}

.chat__option-delete-icon--wrapper {
    margin-left: 10px;
    /* position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    min-width: 25px;
    min-height: 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center; */
}

.chat__option-delete-icon {
    color: rgb(170, 170, 170);
    transition: all 100ms;
}

.chat__option-delete-icon:hover {
    color: rgb(0, 0, 0);
    transform: scale(1.1);
}

.chat__waiting-message {
    font-style: italic;
    color: #7A7A7A;
    margin-left: 20px;
}