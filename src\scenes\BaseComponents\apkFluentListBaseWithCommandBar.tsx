import { Icon, SearchBox, ThemeProvider } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import AppConsts from '../../lib/appconst';
import { fluentTableClassNames } from '../../styles/fluentTableStyles';
import { myTheme } from '../../styles/theme';
import { FluentTableBase } from '../Fluent/base/fluentTableBase';
import { ICrudPermissons } from './commandBarBase';
import { ITableColumn } from './ITableColumn';
import { LabelComponent } from './labelComponent';
import { LabelContainerComponent } from './labelContainerComponent';
import { CustomerPanel } from '../CustomerList/components/customerPanel';
import { IGenericPanelProps } from '../Fluent/base/genericPanel';
import { ApkAttachedFilesDto } from '../../services/apkAttachedFiles/apkAttachedFilesDto';
import { dateFormat } from '../../utils/utils';

var _ = require('lodash');

export class ApkFluentListBaseWithCommandBar extends FluentTableBase<ApkAttachedFilesDto> {
  private debouncedOnSearchboxChange: any = _.debounce((e: any, newValue: string | undefined, customPayload?: any) => {
    newValue = typeof newValue === 'undefined' || newValue.length === 0 ? " " : newValue;
    if(this.props.customOnSearchTextChanged) {
      this.props.customOnSearchTextChanged(newValue);
    } else {
      this.overrideFilter(newValue);
    }
  }, AppConsts.defaultSerachBarDelay, []);
  
  getItemDisplayNameOf(item: ApkAttachedFilesDto): string {
    if(!!item.clientId) {
      return item.clientId.toString();
    } else {
      return item.id;
    }
  }

  getColumns(): ITableColumn[] {
    return this.getTableColumns(this.props);
  }

  private getTableColumns(props: any): ITableColumn[] {
    return [          
      {
        name: L('APK number'),
        fieldName: 'displayedFileName',
        minWidth: 180,
        maxWidth: 180,
      },
      // {
      //   name: L('Client name'),
      //   fieldName: 'client.user.fullName',
      //   minWidth: 250,
      //   maxWidth: 250,
      //   onRender: (item: any): any => {
      //     return item && item.client && item.client.user ? item.client.user.fullName : (item && item.client && !!item.client.company ? item.client.company : "");
      //   }
      // },
      {
        name: L('Company name'),
        fieldName: 'client.company',
        minWidth: 250,
        maxWidth: 250,
        onRender: (item: any): any => {
          return item && item.client ? item.client.company : "";
        }
      },
      {
        name: L('Client name'),
        fieldName: 'client.user.fullName',
        onRender: (item: any): any => {
          return item && item.client && item.client.user ? item.client.user.fullName : "";
        }
      },
      {
        name: L('Product'),
        fieldName: 'productName',
        minWidth: 100,
        maxWidth: 100,
        onRender: (item: any): any => {
          return L(`${item.productName}2`);
        }
      },
      {
        name: L('Status'),
        fieldName: 'status',
        minWidth: 80,
        maxWidth: 80,
        onRender: (item: any): any => {
          return <span style={{ color: '#fff', backgroundColor: myTheme.palette.themePrimary, padding: '2px 5px', borderRadius: '2px' }}>
            {L(item.status)}
          </span>;
        }
      },
      {
        minWidth: 95,
        maxWidth: 95,
        name: L('Policy issued?'),
        fieldName: 'policyIssued',
        onRender: (item: any) => {
          return <Icon style={{ color: !!item.policyIssued && item.policyIssued === true ? "green" : "red", margin: '0 auto', display: 'block', width: 'min-content'}} 
                      iconName={ !!item.policyIssued && item.policyIssued === true ? "SkypeCheck" : "StatusCircleErrorX" } />
        }
      },
      {
        name: L('Created by'),
        fieldName: 'creationWay',
        minWidth: 80,
        maxWidth: 80,
        onRender: (item: any): any => {
          return <span style={{ color: '#fff', backgroundColor: myTheme.palette.neutralPrimaryAlt, padding: '2px 5px', borderRadius: '2px' }}>
            {L(item.creationWay)}
          </span>;
        }
      },
      {
        name: L('Creation time'),
        fieldName: 'creationTime',
        minWidth: 130,
        maxWidth: 130,
        onRender: (item: any): any => {
          return dateFormat(item.creationTime, undefined, true);
        }
      },
    ];
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: false,
      update: false,
      delete: false,
      customActions: false,
    };
  }

  getCommandBarBaseProps() {
    let props = super.getCommandBarBaseProps();
    return {
      ...props,
      customStyles: {marginTop: '-30px'},
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <CustomerPanel
        {...props}
      />
    </>;
  }

  renderAll(pageInfo: string, values: any, columns: any) {
    return <>
      <LabelContainerComponent marginTop={'0'}>
        <LabelComponent label={L('Select APK')} />
        <SearchBox
          theme={myTheme}
          styles={{
            root: {
              flex: 1,
              maxWidth: '252px !important',
              height: '32px', 
              backgroundColor: myTheme.palette.white,
              border: `1px solid ${myTheme.palette.black}`,
              boxSizing: 'border-box',
            },
            field: { borderRadius: '2px' },
          }}
          placeholder={ L('Search') }
          onChange={ (e: any, newValue: string | undefined) => {
            this.debouncedOnSearchboxChange(e, newValue);
          }}
        />

        {this.props.customData.selectedApk &&
          <p className={fluentTableClassNames.summaryAttribute}>
              <span className={fluentTableClassNames.fontBold}>{this.props.customData.selectedApk}</span>
              <Icon iconName="Delete" style={{marginRight: '0', marginLeft: '10px', cursor: 'pointer'}} title={L("Delete")} 
                onClick={() => { if(this.props.customOnSelectionChanged) this.props.customOnSelectionChanged('deleteApk') }} />
          </p>
        }
      </LabelContainerComponent>

      <ThemeProvider theme={myTheme}>
        {this.renderAnnounced(pageInfo)}
        {this.renderCommandBarBase()}

        {this.renderListScrollablePane(values, columns)}
      </ThemeProvider>

      {this.renderPanel()}
    </>;
  }
}