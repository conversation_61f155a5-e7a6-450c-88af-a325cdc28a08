import { BaseApiEntityModel } from "../dto/BaseApiEntityModel";

export interface IndividualOfferDto extends BaseApiEntityModel {
    calculationId: number,
    status: string,
    insurerName: string,
    insuranceCoverage: string[],
    installments: string,
    total: number, 
    policyNumber?: string,
    bankAccountNumber?: string,
    paymentDates: string[],
    fees: string[],
    startDate?: string,
    endDate?: string,
    individualOfferId?: number
}