import { BaseApiEntityModel } from "../../dto/BaseApiEntityModel";
import { PrizeDto } from "../../prize/dto/prizeDto";
import { ContestTypeEnum } from "./contestTypeEnum";

export interface ContestDto extends BaseApiEntityModel {
    name: string;
    productType: string;
    prizesAmount: number;
    prizeItems: PrizeDto[],
    infinitePrizes: boolean;
    conditions: string;
    promotionRules: string;
    isEnabled: boolean;
    startDate: string;
    endDate: string;
    marketingAgreementsContent: string;
    contestDescription: string;
    contestPrizeDescription: string;
    contestType: ContestTypeEnum;
    imageUrl: string;
    scale: number;
    section1Name: string;
    section1Description: string;
    section2Name: string;
    section2Description: string;
    description: string;
    descriptionTitle: string;
    receivedPromotionDescription : string;
}