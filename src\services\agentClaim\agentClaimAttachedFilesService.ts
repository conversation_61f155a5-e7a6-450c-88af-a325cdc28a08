import Endpoint from '../endpoint';
import { httpApi } from '../httpService';
import { CrudServiceBase } from '../base/crudServiceBase';
import { isUserLoggedIn } from '../../utils/authUtils';
import { AgentClaimAttachedFilesDto } from './dto/agentClaimAttachedFilesDto';
import { getPartialModel } from '../../utils/modelUtils';

export class AgentClaimAttachedFilesService extends CrudServiceBase<AgentClaimAttachedFilesDto> {
    constructor() {
        super(Endpoint.AgentClaimAttachedFiles);
        this.internalHttp = httpApi;
    }

    public async createNew(createAgentClaimAttachedFileInput: AgentClaimAttachedFilesDto) {
        isUserLoggedIn();

        const newCreateAgentClaimAttachedFileInput = getPartialModel(createAgentClaimAttachedFileInput, [], ['id', 'claim']);

        let result = await httpApi.post(this.endpoint.Create(), newCreateAgentClaimAttachedFileInput);
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getAllFiles() {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.GetAll());
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getOpinionsByClaimId(claimId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetOpinionsByClaimId?claimId=${claimId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getQuotationByClaimId(claimId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetQuotationByClaimId?claimId=${claimId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getFvByClaimId(claimId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetFvByClaimId?claimId=${claimId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getDocumentByClaimId(claimId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetDocumentByClaimId?claimId=${claimId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }

    public async getPhotoByClaimId(claimId: number) {
        isUserLoggedIn();
        let result = await httpApi.get(this.endpoint.Custom(`GetPhotoByClaimId?claimId=${claimId}`, false));
        return !!result.data && !!result.data.result ? result.data.result : result.data;
    }
}

const exportAgentClaimAttachedFilesService: AgentClaimAttachedFilesService = new AgentClaimAttachedFilesService();
export default exportAgentClaimAttachedFilesService;