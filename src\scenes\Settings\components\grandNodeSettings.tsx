import { Default<PERSON><PERSON>on, Dialog, DialogFooter, DialogType, Icon, IStackTokens, mergeStyleSets, MessageBar, MessageBarType, PrimaryButton, Spinner, SpinnerSize, Stack } from '@fluentui/react';
import * as React from 'react';
import { L } from '../../../lib/abpUtility';
import createOrUpdateClassNames from '../../BaseComponents/createOrUpdate';
import productVersionService from '../../../services/productVersion/productVersionService';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { catchErrorMessage, dateFormat } from '../../../utils/utils';
import { ProductVersionDto } from '../../../services/productVersion/dto/productVersionDto';
import { TextFieldBase } from '../../BaseComponents/textFieldBase';
import { uploadFileToAzure } from '../../../services/azureService';

const verticalGapStackTokens: IStackTokens = {
  childrenGap: 10,
  padding: 10,
};

const classNames = mergeStyleSets({
  messageBar: {
    width: 'fit-content',
    marginTop: '10px',
    marginBottom: '10px',
    selectors: {
      '& .ms-MessageBar-innerText': {
        selectors: {
          '& span': {
            whiteSpace: 'pre-line',
          }
        }
      }
    }
  },
  loadSpinnerAbsolute: {
    position: 'absolute',
    top: 10,
    right: 20,
    margin: 0,
    zIndex: 10,
  },
  loadSpinnerAbsoluteBottom: {
    top: 'unset',
    bottom: 5,
  },
  stackContainer: {
    width: '95%',
    maxWidth: '95%',
    height: 'auto',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: '0 0 20px',
    margin: '25px auto 0 0',
    position: 'relative',
  },
  inputsWrapper: {
    background: myTheme.palette.neutralLighterAlt,
    // wdith: '68%',
    // minWidth: '68%',
    wdith: '100%',
    minWidth: '100%',
    // minHeight: '250px',
    // marginRight: '20px',
    padding: '5px 15px 28px',
    overflow: 'hidden',
    position: 'relative',
    boxSizing: 'border-box',
  },
  jsonViewerWrapper: {
    wdith: '28%',
    minWidth: '28%',
    minHeight: '100px',
    selectors: {
      '& > div': {
        background: myTheme.palette.neutralLighterAlt,
      }
    },
  },
  descriptionInput: {
    width: '100%',
    padding: '5px 2px',
    marginBottom: '15px',
    selectors: {
      '& .ms-TextField-field': {
        width: '100%',
      }
    }
  },
  attachedFilesListItem: {
    listStyleType: 'none',
    marginBottom: '15px',
    padding: '25px',
    // background: myTheme.palette.themeLighterAlt,
    border: `1px solid ${myTheme.palette.neutralQuaternary}`,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    selectors: {
      // ':nth-child(even)': {
      //     background: myTheme.palette.neutralLight,
      // }
    }
  },
  actionButton: {
    width: 'fit-content',
    padding: '25px 50px',
    marginTop: '20px',
    whiteSpace: 'nowrap',
  },
  deleteButton: {
    width: 'fit-content',
    padding: '25px 50px',
    marginTop: '20px',
    background: additionalTheme.lighterRed,
    selectors: {
      ':hover': {
        background: 'darkred',
      }
    }
  },
});

export interface IState {
  allProductVersions: ProductVersionDto[];
  asyncActionInProgress: boolean;
  formError: string;
  resultJsonOutput: any;
  resultJsonString: string;
  jsonStringError: boolean;
  fileDescription: string;
  dialogData: any;
}

export class GrandNodeSettings<TProps> extends React.Component<TProps, IState> {
  private fileUploadInputRef: any;

  constructor(props: any){
    super(props);
    
    this.state = {
      allProductVersions: [],
      asyncActionInProgress: false,
      formError: L(''),
      resultJsonOutput: {},
      resultJsonString: '',
      jsonStringError: false,
      fileDescription: '',
      dialogData: {
        label: '',
        type: 'DELETE',
        show: false,
        entityId: 0,
      },
    };

    this.fileUploadInputRef = React.createRef();
  };

  async componentDidMount() {
    this.getAllProductVersions();
  };

  private async getAllProductVersions() {
    this.setState({ asyncActionInProgress: true });

    await productVersionService.getAll().then((response: any) => {
      if(response && response.totalCount > 0) {
        this.setState({ formError: '', asyncActionInProgress: false, allProductVersions: response.items });
      }
      this.setState({ asyncActionInProgress: false });
    }).catch((error: any) => {
      console.error(error);
      this.setState({ formError: catchErrorMessage(error), asyncActionInProgress: false });
    });
  }

  private triggerUpload = () => {
    this.fileUploadInputRef.current.click();
  };

  private async AddCurrentProductsVersion() {
    //TODO dodać asyncActionInProgress
    await productVersionService.AddCurrentProductsVersion().then((response: any) => {
      //this.getAllProductVersions();
    }).catch((error: any) => {
      console.error(error);
    });
  };

  private async onUpload() {
    this.setState((prevState) => ({ ...prevState, asyncActionInProgress: true }));

    const selectedFile = !!this.fileUploadInputRef.current.files && this.fileUploadInputRef.current.files.length ? this.fileUploadInputRef.current.files[0] : null;
    const result = await uploadFileToAzure(selectedFile);

    const payload: any = {
      // productVersionId: 0,
      linkToFile: result.url,
      description: this.state.fileDescription,
      payloadDate: new Date().toISOString(),
    };

    await productVersionService.UploadProductsVersion(payload).then((response: any) => {
      if(response && !!response.id) {
        this.setState((prevState) => ({ ...prevState, formError: '', asyncActionInProgress: false }));
        this.getAllProductVersions();
      } else {
        this.setState((prevState) => ({ ...prevState, formError: L('Something went wrong. Try again later or contact with administrator.'), asyncActionInProgress: false }));
      }
    }).catch((error: any) => {
      console.error(error);
      this.setState((prevState) => ({ ...prevState, formError: catchErrorMessage(error), asyncActionInProgress: false }));
    });
  }

  private async deleteProductVersion(id: number) {
    this.setState((prevState) => ({ ...prevState, asyncActionInProgress: true, dialogData: {...this.state.dialogData, show: false } }));

    await productVersionService.Delete(id).then((response: any) => {
      if(response && response.success) {
        this.setState((prevState) => ({ ...prevState, formError: '', asyncActionInProgress: false }));
        this.getAllProductVersions();
      } else {
        this.setState((prevState) => ({ ...prevState, formError: L('Something went wrong. Try again later or contact with administrator.'), asyncActionInProgress: false }));
      }
    }).catch((error: any) => {
      console.error(error);
      this.setState((prevState) => ({ ...prevState, formError: catchErrorMessage(error), asyncActionInProgress: false }));
    });
  }

  private async setActiveProductVersion(id: number) {
    this.setState((prevState) => ({ ...prevState, asyncActionInProgress: true, dialogData: {...this.state.dialogData, show: false } }));

    await productVersionService.SetActive(id).then((response: any) => {
      if(response && response.success) {
        this.setState((prevState) => ({ ...prevState, formError: '', asyncActionInProgress: false }));
        this.getAllProductVersions();
      } else {
        this.setState((prevState) => ({ ...prevState, formError: L('Something went wrong. Try again later or contact with administrator.'), asyncActionInProgress: false }));
      }
    }).catch((error: any) => {
      console.error(error);
      this.setState((prevState) => ({ ...prevState, formError: catchErrorMessage(error), asyncActionInProgress: false }));
    });
  }

  render() {
    const {asyncActionInProgress, allProductVersions, fileDescription, dialogData} = this.state;

    return <Stack horizontal className={classNames.stackContainer}>
      {this.state.asyncActionInProgress &&
        <Spinner label={L('Please wait...')} className={`${createOrUpdateClassNames.loadSpinner} ${classNames.loadSpinnerAbsolute}`} size={SpinnerSize.large} ariaLive="assertive"
          labelPosition="right" />
      }

      <Stack className={classNames.inputsWrapper} tokens={verticalGapStackTokens}>
        {(this.state.asyncActionInProgress && allProductVersions.length > 0) &&
          <Spinner label={L('Please wait...')} className={`${createOrUpdateClassNames.loadSpinner} ${classNames.loadSpinnerAbsolute} ${classNames.loadSpinnerAbsoluteBottom}`} 
            size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
        }
        <PrimaryButton theme={myTheme} text={L('Add current version')} style={{marginTop: 0, padding: '18px'}} type={'button'} className="form-btn"
                        disabled={false} onClick={this.AddCurrentProductsVersion} />
        <TextFieldBase type="text" theme={myTheme} value={fileDescription} multiline={true} rows={50} customInputStyles={{ width: '100%' }}
          customClassNames={`${classNames.descriptionInput}`} isDataLoaded={true} onChange={(e, value) => { 
            this.setState((prevState) => ({ ...prevState, fileDescription: value ? value : '' }));
          }}
        />

        <input ref={this.fileUploadInputRef} type="file" accept="application/JSON" style={{display: 'none'}} onChange={() => this.onUpload()} />
        <PrimaryButton theme={myTheme} text={L('Upload JSON file')} style={{marginBottom: 10, marginTop: 0, padding: '18px'}} type={'button'} className="form-btn"
                        disabled={asyncActionInProgress || fileDescription.length === 0} onClick={this.triggerUpload} />
        
        {this.state.formError &&
          <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`}
            onDismiss={() => { this.setState({ formError: '' }); }}
          >
            {this.state.formError}
          </MessageBar>
        }

        <ul style={{margin: '10px 0 0 0', padding: '20px 0 0 0', borderTop: '1px solid #c7c7c7'}}>
          {allProductVersions.map((productVersion: ProductVersionDto) => {
            const splittedFileUrl: string[] = !!productVersion.linkToFile ? productVersion.linkToFile.split('/') : [];
            const baseFileName: string = splittedFileUrl.length > 0 ? splittedFileUrl[splittedFileUrl.length - 1] : '';
            const splittedBaseFileName: string[] = baseFileName.split('.json');
            const fileName: string = splittedBaseFileName.length > 1 ? splittedBaseFileName[0] + ".json" : splittedBaseFileName[0];

            return (
              <li key={productVersion.id} className={classNames.attachedFilesListItem} style={{border: productVersion.isActive ? '1px solid green' : `1px solid ${myTheme.palette.neutralQuaternary}`}}>
                <div>
                  <div style={{color: myTheme.palette.neutralTertiaryAlt, display: 'flex', alignItems: 'center', flexDirection: 'row'}}>
                    <div style={{position: 'relative', marginRight: 20, paddingRight: 25, borderRight: '1px solid #000', display: 'inline-block', width: 15, height: 20}}>
                      {productVersion.isActive ?
                        <Icon iconName='CheckMark' title={L("Active")} style={{margin: 0, fontSize: 20, position: 'absolute', left: '0px', cursor: 'default', color: 'green'}} />
                        :
                        <Icon iconName='Cancel' title={L("Not active")} style={{margin: 0, fontSize: 20, position: 'absolute', left: '0px', cursor: 'default', color: 'red'}} />
                      }
                    </div>
                    <div style={{position: 'relative', marginRight: 20, paddingRight: 25, borderRight: '1px solid #000', display: 'inline-block', width: 15, height: 20}}>
                      {productVersion.isUnusable ?
                        <Icon iconName='Dislike' title={L("Unusable")} style={{margin: 0, fontSize: 20, position: 'absolute', left: '0px', cursor: 'default', color: 'red'}} />
                        :
                        <Icon iconName='Like' title={L("Usable")} style={{margin: 0, fontSize: 20, position: 'absolute', left: '0px', cursor: 'default', color: 'green'}} />
                      }
                    </div>
                    {!!productVersion.uploadProcess &&
                      <div style={{position: 'relative', marginRight: 20, paddingRight: 25, borderRight: '1px solid #000', display: 'inline-block', width: 15, height: 20}}>
                        <Icon iconName='Sync' title={L("Uploading")} style={{margin: 0, fontSize: 20, position: 'absolute', left: '0px', cursor: 'default', color: 'red'}} />
                      </div>
                    }

                    {!!productVersion.linkToFile && 
                      <a href={productVersion.linkToFile} title={L("Download file")} style={{margin: '0 35px 0 50px', position: 'relative'}}>
                        <Icon iconName='Download' style={{margin: 0, fontSize: 20, position: 'absolute', left: '-50px', cursor: 'pointer', color: 'green'}} />
                        
                        {fileName}
                      </a>
                    }
                    
                    {!!productVersion.creationTime ? dateFormat(productVersion.creationTime, "DD.MM.YYYY HH:mm", true) : ''}
                  
                  {!!productVersion.description && 
                    <div style={{marginLeft: 20, marginRight: 20}}>
                      {productVersion.description}
                    </div>
                  }
                  </div>
                </div>
                
                <Stack horizontal={true}>
                  <PrimaryButton 
                    key={`setActiveButton${productVersion.id}`}
                    className={classNames.actionButton}
                    theme={myTheme}
                    text={L('Set active')}
                    type={'button'}
                    onClick={() => this.setState({ dialogData: {label: L('Are you sure you want to set this settings as active?'), type: 'SET_ACTIVE', entityId: productVersion.id, show: true} })}
                    disabled={asyncActionInProgress || productVersion.isActive}
                    style={{padding: '18px', marginTop: 0}}
                  />

                  <PrimaryButton 
                    key={`deleteButton${productVersion.id}`}
                    className={productVersion.isActive ? classNames.actionButton : classNames.deleteButton}
                    theme={myTheme}
                    text={L('Delete')}
                    type={'button'}
                    onClick={() => this.setState({ dialogData: {label: L('Are you sure you want to delete this settings?'), type: 'DELETE', entityId: productVersion.id, show: true} })}
                    disabled={asyncActionInProgress || productVersion.isActive}
                    style={{padding: '18px', marginTop: 0, marginLeft: '20px'}}
                  />
                </Stack>
              </li>
            );
          })}
        </ul>

        <Dialog
          hidden={!dialogData.show}
          onDismiss={() => this.setState({ dialogData: {...dialogData, show: false} })}
          dialogContentProps={{
              type: DialogType.normal,
              title: dialogData.label,
              closeButtonAriaLabel: L('Close'),
          }}
          modalProps={{isBlocking: true, styles: {main: { maxWidth: 450 }},}}
          theme={myTheme}
        >
          <DialogFooter theme={myTheme}>
              <DefaultButton theme={myTheme} onClick={() => this.setState({ dialogData: {...dialogData, show: false} })} text={L('No')} />
              <PrimaryButton text={L('Yes')} disabled={asyncActionInProgress}
                  onClick={() => {
                    if(dialogData.type === 'DELETE') {
                      this.deleteProductVersion(dialogData.entityId);
                    } else if(dialogData.type === 'SET_ACTIVE') {
                      this.setActiveProductVersion(dialogData.entityId);
                    }
                  }}
              />
          </DialogFooter>
        </Dialog>
      </Stack>
    </Stack>
  };
}