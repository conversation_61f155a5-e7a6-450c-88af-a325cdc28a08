
import { ComboBox, IComboBoxOption, Icon, mergeStyleSets, MessageBar, MessageBarType, Spinner, SpinnerSize, TextField } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import AppConfig from "../../../lib/appconfig";
import policyDictionaryService from "../../../services/policyDictionary/policyDictionaryService";
import { myTheme } from "../../../styles/theme";
import { catchErrorMessage } from "../../../utils/utils";
import { ICustomInputsBoxGeneric, IEuroTaxEngineCapacity, IEuroTaxEnginePower, IEurotaxVehicleBrands, IEurotaxVehicleConfiguration, IEurotaxVehicleTypes } from "./types";

const classNames = mergeStyleSets({
    hide: {
        display: 'none !important',
    },
    fontBold: {
        fontWeight: '800',
    },
    toolbar: {
        selectors: {
            '& .ms-Pivot': {
                display: 'none',
            }
        }
    },
    messageBar: {
        width: 'fit-content'
    },
    messageBarMargin: {
        marginTop: '15px'
    },
    closeIcon: {
        position: 'absolute',
        top: 20,
        right: 20,
        cursor: 'pointer',
        transition: 'all 150ms',
        selectors: {
            ':hover': {
                transform: 'scale(1.1)',
            },
            ':active': {
                transform: 'scale(0.9) rotate(90deg)',
            },
        }
    },
    customInputsWrapper: {
        position: 'relative',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        width: 'fit-content',
        height: 'auto',
        marginTop: '20px',
        padding: '20px',
        border: `1px solid ${myTheme.palette.themePrimary}`,
        borderRadius: '3px',
        minWidth: '502px',
        boxSizing: 'border-box',
    },
    comboBoxStyles: {
        width: '100%',
        marginTop: '15px',
    },
    loadSpinner: {
        display: 'inline-flex',
        marginTop: '15px',
        marginLeft: 'auto',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinnerTopBar: {
        display: 'inline-flex',
        marginLeft: '15px',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
});

export interface IEurotaxCustomInputsBoxProps {
    showCustomInputsBox: boolean,
    customInputsData: any,
    onInputChange: (id: string, value: any) => void,
    onToggleClose: () => void,
    mapKeyToId: (mapType: string, key: string) => string;
}

type IEurotaxCustomInputsBoxState = ICustomInputsBoxGeneric & {
    vehicleTypes: IEurotaxVehicleTypes;
    vehicleConfiguration: IEurotaxVehicleConfiguration;
    vehicleBrands: IEurotaxVehicleBrands;
    enginePower: IEuroTaxEngineCapacity;
    engineCapacity: IEuroTaxEnginePower;
    customInputsPrevData: {
        vehicleTypeId: number,
        vehicleBrandId: number,
        productionYear: number,
        engineCapacity: number,
        enginePower: number,
        vehicleModelId: number,
        vehicleConfigurationId: number,
    },
    maxErrorsCount: number,
    operationSuccess: boolean,
};

export class EurotaxCustomInputsBox extends React.Component<IEurotaxCustomInputsBoxProps, IEurotaxCustomInputsBoxState> {
    constructor(props: IEurotaxCustomInputsBoxProps) {
        super(props);
    
        this.state = {
            ...this.state,
            date: new Date(),
            customInputsAsyncActionInProgress: false,
            vehicleTypes: { errorsCount: 0, error: "", gettingDataInProgress: false, types: [] },
            vehicleBrands: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, typeId: [...brands] */ },
            vehicleModels: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, brandId: [...models]" */ },
            vehicleConfiguration: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            engineCapacity: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            enginePower: { errorsCount: 0, error: "", gettingDataInProgress: false, /*, modelId: [...configurations]" */ },
            customInputsPrevData: {
                vehicleTypeId: 0,
                vehicleBrandId: 0,
                productionYear: 0,
                engineCapacity: 0,
                enginePower: 0,
                vehicleModelId: 0,
                vehicleConfigurationId: 0,
            },
            maxErrorsCount: AppConfig.eurotaxCollectDataMaxConnectionErrorsCount,
            operationSuccess: false,
        };
    }

    private async getDataForCustomInputs(requestType: string, vehicleTypeId?: number, vehicleBrandId?: number, productionYear?: number, vehicleModelId?: number, engineCapacityInput?: number, enginePowerInput?: number) {
        const { vehicleTypes, vehicleBrands, vehicleModels, vehicleConfiguration, engineCapacity, enginePower, customInputsPrevData } = this.state;

        switch(requestType) {
            case "getTypes":
                if(!vehicleTypes['gettingDataInProgress']) {
                    vehicleTypes['gettingDataInProgress'] = true;

                    await policyDictionaryService.getEurotaxType().then((response: any) => {
                        if(response.data && response.data.result) {
                            vehicleTypes["error"] = "";
                            vehicleTypes["types"] = response.data.result.items;
                        }
                
                        vehicleTypes['errorsCount'] = 0;
                        vehicleTypes['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleTypes: vehicleTypes, customInputsAsyncActionInProgress: false, 
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationId: 0, 
                                vehicleBrandId: 0,
                                productionYear: 0,
                                engineCapacity: 0,
                                enginePower: 0,
                                vehicleModelId: 0} 
                            }
                        ));
                    }).catch((error: any) => {
                        vehicleTypes["errorsCount"]++;
                        vehicleTypes["error"] = catchErrorMessage(error);
                        vehicleTypes['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleTypes: vehicleTypes, customInputsAsyncActionInProgress: false, 
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationId: 0, 
                                vehicleBrandId: 0,
                                productionYear: 0,
                                engineCapacity: 0,
                                enginePower: 0,
                                vehicleModelId: 0} 
                            }
                        ));
                    });
                }
            break;
            
            case "getBrands":
                if(!vehicleBrands['gettingDataInProgress']) {
                    vehicleBrands['gettingDataInProgress'] = true;
                    await policyDictionaryService.getEurotaxBrand(vehicleTypeId!).then((response: any) => {
                        if(response.data && response.data.result) {
                            vehicleBrands["error"] = "";
                            vehicleBrands[vehicleTypeId!] = response.data.result.items;
                        }
                
                        vehicleBrands['errorsCount'] = 0;
                        vehicleBrands['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleBrands: vehicleBrands, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationId: 0,
                                productionYear: 0,
                                engineCapacity: 0,
                                enginePower: 0,
                                vehicleModelId: 0
                            }}
                        ));
                    }).catch((error: any) => {
                        vehicleBrands["errorsCount"]++;
                        vehicleBrands["error"] = catchErrorMessage(error);
                        vehicleBrands['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleBrands: vehicleBrands, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationId: 0,
                                productionYear: 0,
                                engineCapacity: 0,
                                enginePower: 0,
                                vehicleModelId: 0
                            }}
                        ));
                    });
                }
            break;

            case "getModels":
                if(!vehicleModels['gettingDataInProgress']) {
                    vehicleModels['gettingDataInProgress'] = true;
                    await policyDictionaryService.getEurotaxModel(vehicleBrandId!, vehicleTypeId!, productionYear!).then((response: any) => {
                        if(response.data && response.data.result) {
                            vehicleModels["error"] = "";
                            vehicleModels[vehicleTypeId!] = {};
                            vehicleModels[vehicleTypeId!][vehicleBrandId!] = {};
                            vehicleModels[vehicleTypeId!][vehicleBrandId!][productionYear!] = response.data.result.items;
                        }
                
                        vehicleModels['errorsCount'] = 0;
                        vehicleModels['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleModels: vehicleModels, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationId: 0,
                                engineCapacity: 0,
                                enginePower: 0,
                            }    
                        }));
                    }).catch((error: any) => {
                        vehicleModels["errorsCount"]++;
                        vehicleModels["error"] = catchErrorMessage(error);
                        vehicleModels['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleModels: vehicleModels, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationId: 0,
                                engineCapacity: 0,
                                enginePower: 0,
                            }    
                        }));
                    });
                }
            break;

            case "getEngineCapacity":
                if(!engineCapacity['gettingDataInProgress']) {
                    engineCapacity['gettingDataInProgress'] = true;
                    await policyDictionaryService.getEurotaxEngineCapacity(vehicleModelId!).then((response: any) => {
                        if(response.data && response.data.result) {
                            engineCapacity["error"] = "";
                            engineCapacity[vehicleModelId!] = response.data.result.items;
                        }
                        engineCapacity['errorsCount'] = 0;
                        engineCapacity['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, engineCapacity: engineCapacity, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationId: 0,
                                enginePower: 0,
                            } 
                        }));
                    }).catch((error: any) => {
                        engineCapacity["errorsCount"]++;
                        engineCapacity["error"] = catchErrorMessage(error);
                        engineCapacity['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, engineCapacity: engineCapacity, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationId: 0,
                                enginePower: 0,
                            } 
                        }));
                    });
                }
            break;

            case "getEnginePower":
                if(!enginePower['gettingDataInProgress']) {
                    enginePower['gettingDataInProgress'] = true;
                    await policyDictionaryService.getEurotaxEnginePower(vehicleModelId!, engineCapacityInput!).then((response: any) => {
                        if(response.data && response.data.result) {
                            enginePower["error"] = "";
                            enginePower[vehicleModelId!] = {};
                            enginePower[vehicleModelId!][engineCapacityInput!] = response.data.result.items;
                        }
                
                        enginePower['errorsCount'] = 0;
                        enginePower['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, enginePower: enginePower, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationId: 0,
                            } 
                        }));
                    }).catch((error: any) => {
                        enginePower["errorsCount"]++;
                        enginePower["error"] = catchErrorMessage(error);
                        enginePower['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, enginePower: enginePower, customInputsAsyncActionInProgress: false,
                            customInputsPrevData: {...customInputsPrevData, 
                                vehicleConfigurationId: 0,
                            } 
                        }));
                    });
                }
            break;

            case "getConfiguration":
                if(!vehicleConfiguration['gettingDataInProgress']) {
                    vehicleConfiguration['gettingDataInProgress'] = true;
                    await policyDictionaryService.getEurotaxConfiguration(vehicleModelId!,engineCapacityInput!, enginePowerInput!).then((response: any) => {
                        if(response.data && response.data.result) {
                            vehicleConfiguration["error"] = "";
                            vehicleConfiguration[vehicleModelId!] = {};
                            vehicleConfiguration[vehicleModelId!][engineCapacityInput!] = {};
                            vehicleConfiguration[vehicleModelId!][engineCapacityInput!][enginePowerInput!] = response.data.result.items;
                        }
                        
                        vehicleConfiguration['errorsCount'] = 0;
                        this.setState((prevState) => ({...prevState, vehicleConfiguration: vehicleConfiguration, customInputsAsyncActionInProgress: false }));
                        vehicleConfiguration['gettingDataInProgress'] = false;
                    }).catch((error: any) => {
                        vehicleConfiguration["errorsCount"]++;
                        vehicleConfiguration["error"] = catchErrorMessage(error);
                        vehicleConfiguration['gettingDataInProgress'] = false;
                        this.setState((prevState) => ({...prevState, vehicleConfiguration: vehicleConfiguration, customInputsAsyncActionInProgress: false }));
                    });
                }
            break;
        }
    }

    private toggleCustomInputsAsyncActionInProgressFlag(newState: boolean) {
        if(this.state.customInputsAsyncActionInProgress !== newState) {
            this.setState((prevState) => ({...prevState, customInputsAsyncActionInProgress: newState }));
        }
    }

    render() {
        const { operationSuccess, maxErrorsCount, customInputsAsyncActionInProgress, vehicleTypes, vehicleBrands, vehicleModels, vehicleConfiguration, 
                customInputsPrevData,engineCapacity,enginePower } = this.state;

        let typeOptions: IComboBoxOption[] = [];
        let brandOptions: IComboBoxOption[] = [];
        let modelOptions: IComboBoxOption[] = [];
        let engineCapacityOptions: IComboBoxOption[] = [];
        let enginePowerOptions: IComboBoxOption[] = [];
        let brandSelected: boolean = false;
        let configurationOptions: IComboBoxOption[] = [];
        let errors: any = {
            typeError: '',
            brandError: '',
            yearError:'',
            modelError: '',
            engineCapacityError:'',
            enginePowerError:'',
            configurationError: '',
            eurotaxError: '',
        };

        const vehicleTypeId = this.props.customInputsData.vehicleTypeId;
        const vehicleBrandId = this.props.customInputsData.vehicleBrandId;
        const productionYear = this.props.customInputsData.productionYear;
        const engineCapacityInput = this.props.customInputsData.engineCapacity;
        const enginePowerInput = this.props.customInputsData.enginePower;
        const vehicleModelId = this.props.customInputsData.vehicleModelId;
        const vehicleConfigurationId = this.props.customInputsData.vehicleConfigurationId;

        if(vehicleTypes["types"]!.length > 0 && vehicleTypes['error'].length === 0 && vehicleTypes['errorsCount'] < maxErrorsCount) {
            vehicleTypes["types"]!.forEach((type: any) => {
                typeOptions.push({ key: type.id, text: type.name, selected: (vehicleTypeId === type.id ? true : false) });
            });

            if(vehicleTypeId && vehicleTypeId > 0) {
                if(vehicleBrands[vehicleTypeId] && vehicleBrands[vehicleTypeId].length > 0 && vehicleBrands['error'].length === 0 && vehicleBrands['errorsCount'] < maxErrorsCount) {
                    vehicleBrands[vehicleTypeId].forEach((brand: any, index: number) => {
                        brandOptions.push({ key: brand.code, text: brand.name });
                    });
                    if(vehicleBrandId && vehicleBrandId > 0) {
                        brandSelected = true
                        if(productionYear) {
                            if(vehicleModels[vehicleTypeId] && vehicleModels[vehicleTypeId][vehicleBrandId] && vehicleModels[vehicleTypeId][vehicleBrandId][productionYear] && vehicleModels[vehicleTypeId][vehicleBrandId][productionYear].length > 0 && vehicleModels['error'].length === 0 && vehicleModels['errorsCount'] < maxErrorsCount) {
                                vehicleModels[vehicleTypeId][vehicleBrandId][productionYear].forEach((model:any, index:number) =>{
                                    modelOptions.push({key:model.id, text:model.name});
                                });
                                if(vehicleModelId && vehicleModelId > 0) {
                                    if(engineCapacity[vehicleModelId] && engineCapacity['error'].length === 0 && engineCapacity['errorsCount'] < maxErrorsCount){
                                        engineCapacity[vehicleModelId].forEach((model:any,index:number) =>{
                                            engineCapacityOptions.push({key:model, text:model});
                                        })
                                        if(engineCapacityInput && engineCapacityInput > 0){
                                            if(enginePower[vehicleModelId] && enginePower[vehicleModelId][engineCapacityInput] && enginePower['error'].length === 0 && enginePower['errorsCount'] < maxErrorsCount){
                                                enginePower[vehicleModelId][engineCapacityInput].forEach((model:any,index:number)=>{
                                                    enginePowerOptions.push({key:model, text:model});
                                                })
                                                if(enginePowerInput && engineCapacityInput > 0){
                                                    if(vehicleConfiguration[vehicleModelId] && vehicleConfiguration[vehicleModelId][engineCapacityInput] && vehicleConfiguration[vehicleModelId][engineCapacityInput][enginePowerInput] && vehicleConfiguration['error'].length === 0 && vehicleConfiguration['errorsCount'] < maxErrorsCount) {
                                                            vehicleConfiguration[vehicleModelId][engineCapacityInput][enginePowerInput].forEach((configuration: any, index: number) => {
                                                                configurationOptions.push({ key: configuration.id, text: configuration.name });
                                                            });
                                                            if(vehicleConfigurationId && vehicleConfigurationId > 0 && customInputsPrevData['vehicleConfigurationId'] !== vehicleConfigurationId) {
                                                                this.setState((prevState) => ({...prevState, operationSuccess: true }));
                                                                customInputsPrevData['vehicleConfigurationId'] = vehicleConfigurationId;
                                                                this.props.onInputChange(this.props.mapKeyToId("mapAttributeNameToId","eurotaxId"), vehicleConfigurationId.toString());
                                                            }
                                                        } else {
                                                            if((enginePowerInput && enginePowerInput > 0 && customInputsPrevData['enginePower'] !== enginePowerInput) && vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0) {
                                                                this.toggleCustomInputsAsyncActionInProgressFlag(true);
                                                                customInputsPrevData['enginePower'] = enginePowerInput;
                                                                this.getDataForCustomInputs("getConfiguration", vehicleTypeId, vehicleBrandId, productionYear,vehicleModelId,engineCapacityInput, enginePowerInput);
                                                            } else {
                                                                errors.eurotaxError = L(vehicleConfiguration['error']);
                                                            }
                                                        }
                                                }else{
                                                    errors.configurationError = L("Please select engine power. ")
                                                }
                                            } else{
                                                if((engineCapacityInput && engineCapacityInput > 0 && customInputsPrevData['engineCapacity'] !== engineCapacityInput) && vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0){
                                                    this.toggleCustomInputsAsyncActionInProgressFlag(true);
                                                    customInputsPrevData['engineCapacity'] = engineCapacityInput;
                                                    this.getDataForCustomInputs("getEnginePower", vehicleTypeId, vehicleBrandId, productionYear, vehicleModelId, engineCapacityInput);
                                                }else{
                                                    errors.eurotaxError = L(vehicleConfiguration['error']);
                                                }
                                            }
                                        }else{
                                            errors.enginePowerError = L("Please select engine capacity. ")
                                        }
                                    }else{
                                        if((vehicleModelId && vehicleModelId > 0 && customInputsPrevData['vehicleModelId'] !== vehicleModelId)&& vehicleConfiguration['errorsCount'] < maxErrorsCount && vehicleConfiguration['error'].length === 0){
                                            this.toggleCustomInputsAsyncActionInProgressFlag(true);
                                            customInputsPrevData['vehicleModelId'] = vehicleModelId;
                                            this.getDataForCustomInputs("getEngineCapacity", vehicleTypeId, vehicleBrandId, productionYear,vehicleModelId);
                                        }else{
                                            errors.eurotaxError = L(vehicleConfiguration['error']);
                                        }
                                    }
                                } else {
                                    errors.engineCapacityError = L('Please select vehicle model.');
                                }
                            } else {
                                if(productionYear && customInputsPrevData['productionYear'] !== productionYear) {
                                    if(!(productionYear > 1922 && productionYear < new Date().getFullYear())) {
                                        errors.modelError = L("Vehicle production year must be between 1922 and current year.")
                                    } else {
                                        this.toggleCustomInputsAsyncActionInProgressFlag(true);
                                        customInputsPrevData['productionYear'] = productionYear;
                                        this.getDataForCustomInputs("getModels", vehicleTypeId, vehicleBrandId, productionYear);
                                    }
                                } else {
                                    errors.yearError = L('Please select year of production.');
                                }
                            }
                        } else {
                            errors.yearError = L('Please select year of production.');
                        }
                    } else {
                        errors.modelError = L('Please select vehicle brand.');
                    }
                } else {
                    if((vehicleTypeId && vehicleTypeId > 0 && customInputsPrevData['vehicleTypeId'] !== vehicleTypeId) && vehicleBrands['errorsCount'] < maxErrorsCount && vehicleTypes['error'].length === 0) {
                        this.toggleCustomInputsAsyncActionInProgressFlag(true);
                        customInputsPrevData['vehicleTypeId'] = vehicleTypeId;
                        this.getDataForCustomInputs("getBrands", vehicleTypeId);
                    } else {
                        errors.brandError = L(vehicleBrands['error']);
                    }
                }
            } else {
                errors.brandError = L('Please select vehicle type.');
            }
        } else {
            if(vehicleTypes['errorsCount'] < maxErrorsCount && vehicleTypes['error'].length === 0) {
                this.toggleCustomInputsAsyncActionInProgressFlag(true);
                this.getDataForCustomInputs("getTypes");
            } else {
                errors.typeError = L(vehicleTypes['error']);
            }
        }
        return <div className={`${classNames.customInputsWrapper} ${this.props.showCustomInputsBox ? '' : classNames.hide}`}>
            <Icon iconName="ChromeClose" onClick={() => this.props.onToggleClose()} title={L('Close')} className={classNames.closeIcon} />
            
            <MessageBar messageBarType={MessageBarType.info} isMultiline={false} className={classNames.messageBar}>
                {L('Enter vehicle details to find Eurotax ID.')}
            </MessageBar>

            { customInputsAsyncActionInProgress && <Spinner label={L('Collecting data...')} className={classNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" /> }

            <ComboBox
                label={L("Select vehicle type")}
                allowFreeform={false}
                autoComplete={'off'}
                options={typeOptions}
                className={classNames.comboBoxStyles}
                key={`selectType`}
                errorMessage={errors.typeError}
                disabled={typeOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={vehicleTypeId}
                onChange={(e: any, value: any) => this.props.onInputChange('vehicleTypeId', value.key)}
            />

            <ComboBox
                label={L("Select vehicle brand")}
                allowFreeform={false}
                autoComplete={'on'}
                options={brandOptions}
                className={classNames.comboBoxStyles}
                key={`selectBrand-${vehicleTypeId}`}
                errorMessage={errors.brandError}
                disabled={brandOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={vehicleBrandId}
                onChange={(e: any, value: any) => this.props.onInputChange('vehicleBrandId', value.key)}
            />

            <TextField
                type="number"
                autoComplete={'off'}
                label={L("Enter year of production")}
                errorMessage={errors.modelError}
                className={classNames.comboBoxStyles}
                disabled={!brandSelected || customInputsAsyncActionInProgress}
                value={productionYear}
                onChange={(e: any, value: any) => this.props.onInputChange('productionYear', parseInt(value))}
            />

            <ComboBox
                label={L("Select vehicle model")}
                allowFreeform={false}
                autoComplete={'on'}
                options={modelOptions}
                className={classNames.comboBoxStyles}
                key={`selectModel-${vehicleTypeId}-${vehicleBrandId}`}
                errorMessage={errors.yearError}
                disabled={modelOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={vehicleModelId}
                onChange={(e: any, value: any) => this.props.onInputChange('vehicleModelId', value.key)}
            />

            <ComboBox
                label={L("Select engine capacity")}
                allowFreeform={false}
                autoComplete={'on'}
                options={engineCapacityOptions}
                className={classNames.comboBoxStyles}
                errorMessage={errors.engineCapacityError}
                disabled={engineCapacityOptions.length <=0 || customInputsAsyncActionInProgress}
                selectedKey={engineCapacityInput}
                onChange={(e: any, value: any) => this.props.onInputChange('engineCapacity', value.key)}
            />

            <ComboBox
                label={L("Select engine power")}
                allowFreeform={false}
                autoComplete={'on'}
                options={enginePowerOptions}
                className={classNames.comboBoxStyles}
                errorMessage={errors.enginePowerError}
                disabled={enginePowerOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={enginePowerInput}
                onChange={(e: any, value: any) => this.props.onInputChange('enginePower', value.key)}
            />

            <ComboBox
                label={L("Select vehicle configuration")}
                allowFreeform={false}
                autoComplete={'on'}
                options={configurationOptions}
                className={classNames.comboBoxStyles}
                key={`selectConfiguration-${vehicleTypeId}-${vehicleBrandId}-${vehicleModelId}`}
                errorMessage={errors.configurationError}
                disabled={configurationOptions.length <= 0 || customInputsAsyncActionInProgress}
                selectedKey={vehicleConfigurationId}
                onChange={(e: any, value: any) => this.props.onInputChange('vehicleConfigurationId', value.key)}
            />

            { operationSuccess && 
                <MessageBar messageBarType={MessageBarType.success} isMultiline={false} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}
                    onDismiss={() => this.setState((prevState) => ({...prevState, operationSuccess: false })) } >
                    { L('Success! Eurotax ID is now set.') }
                </MessageBar> }
            { errors.eurotaxError.length > 0 && 
                <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar} ${classNames.messageBarMargin}`}>
                    {errors.eurotaxError}
                </MessageBar> }

            { customInputsAsyncActionInProgress && <Spinner label={L('Collecting data...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" /> }
        </div>;
    }
}