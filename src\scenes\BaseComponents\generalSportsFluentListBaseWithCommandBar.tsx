import {FluentTableBase} from "../Fluent/base/fluentTableBase";
import {ITableColumn} from "./ITableColumn";
import {myTheme} from "../../styles/theme";
import {L} from "../../lib/abpUtility";
import {ICrudPermissons} from "./commandBarBase";
import {DefaultButton, Dialog, DialogFooter, PrimaryButton, TextField, ThemeProvider} from "@fluentui/react";
import sportDisciplineService from "../../services/sportDiscipline/sportDisciplineService";
import { SportDisciplineDto } from "../../services/sportDiscipline/dto/sportDisciplineDto";

const dialogStyles = {
    main: {
        selectors: {
            '@media (min-width: 0px)': {
                maxWidth: 500,
                width: 500
            }
        }
    }
};

export class GeneralSportsFluentListBaseWithCommandBar extends FluentTableBase<SportDisciplineDto> {
    private shouldReloadItems: boolean = false;
    private showPopUpDialog: boolean = false;
    private sportDisciplineName: string = "";
    private createToEditFlag: boolean = false;
    
    disableGetAllOnMount = (this.props.customData && this.props.customData.disableGetAllOnMount && this.props.customData.disableGetAllOnMount === true) ? true : false;

    getColumns(): ITableColumn[] {
        return this.getTableColumns(this.props);
    }

    private getTableColumns(props: any): ITableColumn[] {
        return [
            {
                name: L('Sport discipline'),
                fieldName: 'sport',
                onRender: (item: SportDisciplineDto) => {
                    return L(item.name);
                }
            }
        ];
    }

    getCrudPermission(): ICrudPermissons {
        return {
            create: false,
            update: false,
            delete: true,
            customActions: true,
        };
    }

    getCommandBarBaseProps() {
        let props = super.getCommandBarBaseProps();
        return {
            ...props,
            customActionsProps: [
                {
                    displayFor: 'none',
                    buttonText: L("New"),
                    buttonIcon: "Add",
                    buttonColor: myTheme.palette.white,
                    buttonIconColor: myTheme.palette.white,
                    buttonBackground: myTheme.palette.themeTertiary,
                },
                {
                    displayFor: 'single',
                    buttonText: L("Edit"),
                    buttonIcon: "edit",
                    buttonColor: myTheme.palette.black,
                    buttonIconColor: myTheme.palette.black,
                    buttonBackground: myTheme.palette.white,
                }
            ],
            customActions: [
                () => {
                    this.createToEditFlag = true;
                    this.sportDisciplineName = '';
                    this.handleShowPopUpDialog();
                },
                () => {
                    this.createToEditFlag = false;
                    this.getSports();
                }
            ]
        }
    }

    private createSport = async (name: string) => {
        await sportDisciplineService.create({
            name: name,
            id: '0'
        }).then((response: any) => {
            if(response) {
                this.showPopUpDialog = false;
                this.reloadItems();
            }
        }).catch((error: any) => {
            console.error(error);
        })
        this.forceUpdate();
    }

    private getSports = async () => {
        await  sportDisciplineService.get({id: this.props.customData.selectedSport.id}).then((response: any) => {
            if(response) {
                this.sportDisciplineName = response.name
                this.showPopUpDialog = true;
                this.reloadItems();
             }
        })
        this.forceUpdate();
    }

    private editSport = async (name: string) => {
        await sportDisciplineService.update({
            name: name,
            id: this.props.customData.selectedSport.id
        }).then((response: any) => {
            if(response) {
                this.showPopUpDialog = false;
                this.reloadItems();
            }
        }).catch((error: any) => {
            console.error(error);
        })
        this.forceUpdate();
    }

    private handleShowPopUpDialog() {
        this.sportDisciplineName = '';
        this.showPopUpDialog = true;
        this.forceUpdate();
    }

    private reloadListOnDialogClose() {
        this.showPopUpDialog = false;
    
        if(this.shouldReloadItems) {
            this.reloadItems();
        }
    
        this.forceUpdate();
    }

    private handleSaveDialog(name: string) {
        if(this.createToEditFlag) {
            this.createSport(name)
        } else {
            this.editSport(name)
        }
    }

    private async reloadItems() {
        this.selectionSetAllSelected(false);
        if(typeof this.props.refreshItems !== 'undefined') {
            await this.props.refreshItems!();
        }
    }  

    renderAll(pageInfo: string, values: any, columns: any) {
        return <>
            <Dialog
                hidden={!this.showPopUpDialog}
                onDismiss={() => this.reloadListOnDialogClose()}
                modalProps={{
                    isBlocking: true,
                    styles: dialogStyles
                }}
            >
                <TextField 
                    label={L("Enter the sport name")}
                    value={this.sportDisciplineName}
                    onChange={(e: any, newValue?: string) => {
                        this.sportDisciplineName = newValue || '';
                        this.forceUpdate();
                    }}
                />
                <DialogFooter theme={myTheme}>
                    <PrimaryButton
                        onClick={() => {
                            this.handleSaveDialog(this.sportDisciplineName);
                        }}
                        text={L('Save')}
                        theme={myTheme}
                    />
                    <DefaultButton theme={myTheme} onClick={() => this.reloadListOnDialogClose()} text={L('Cancel')} />
                </DialogFooter>
            </Dialog>
            
            <ThemeProvider theme={myTheme}>
                { this.renderAnnounced(pageInfo) }
                { this.renderCommandBarBase() }

                { this.renderListScrollablePane(values, columns) }
            </ThemeProvider>

            {this.renderDialog()}
        </>;
    }
}