import { Spinner, SpinnerSize } from '@fluentui/react';
import React, { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { AzureB2CStorageKey } from '../../scenes/Login/AzureB2C/signInButton';
import { ChatMessageDto } from '../../services/chat/chatMessageDto';
import { ChatUserDto } from '../../services/chat/chatUserDto';
import chatStore from '../../stores/chatStore';
import Message from './Message';
import { isJsonString } from '../../utils/utils';

interface IChatWindow {
    chat: ChatMessageDto[];
    setChat: (chat: ChatMessageDto[]) => void;
    setIsChatEnded: (value: boolean) => void;
    threadId: string;
    prevThreadId: string;
    users: ChatUserDto[];
    isChatEnded: boolean;
    userId: number;
}

const ChatWindow: React.FC<IChatWindow> = (props: IChatWindow) => {
    const [myUserFullName, setMyUserFullName] = useState<string>();
    const [page, setPage] = useState(0)
    const [loader, setLoader] = useState(false)
    const [loading, setLoading] = useState(false)
    const chatWindowRef = useRef<HTMLDivElement>(null);
    const [scrollPosition, setScrollPosition] = useState(0);

    const findUserByIdentity = useCallback((identityId?: string): string => {
        return props.users.find(x => x.communicationIdentityId === identityId)?.name || '';
    }, [props.users])

    const handleGetMoreMessage = () => {
        if (chatStore.moreMessages && page > 0) {
            const newMessages = chatStore.moreMessages
                .sort((a: any, b: any) => a.createdOn - b.createdOn)
                .filter(x => !x.content?.initiator)
                .map(x => ({
                    authToken: '',
                    user: findUserByIdentity(x.sender?.id),
                    message: x.content?.message || ''
                }));
            newMessages.reverse();
            props.setChat([...newMessages, ...props.chat]);

            //After adding new items, set the scroll to the same position
            if (chatWindowRef.current) {
                chatWindowRef.current.scrollTop = scrollPosition;
            }
        }
        // eslint-disable-next-line
    };

    const fetchData = async(page: number) => {
        setLoading(true);
        if(page > 0) {
            await chatStore.getMoreMessages(props.threadId, page * 25);
            handleGetMoreMessage();
        }
        setLoading(false);
        setLoader(true);
    }

    useEffect(() => {
        fetchData(page);
        // eslint-disable-next-line
    }, [page]);

    const loadMore = () => {
        setPage(prevPage => prevPage + 1);
    }

    const pageEnd = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (loader && pageEnd.current) {
            const observer = new IntersectionObserver(entries => {
                if (entries[0].isIntersecting) {
                    loadMore();
                }
            }, { threshold: 1 });
            observer.observe(pageEnd.current);
        }
    }, [loader, pageEnd]);
    
    const chat = props.chat.map(m => {
        return <Message
            key={Date.now() * Math.random()}
            user={m.user}
            message={m.message}
            isMyUser={m.user === myUserFullName || m.user === props.userId.toString()}
            setIsChatEnded={(value: boolean) => props.setIsChatEnded(value)}
        />
    });

    useEffect(() => {
        const getFromLocalStorage: string | null = localStorage.getItem(AzureB2CStorageKey);
        const parsedLocalStorageData: any = JSON.parse(!!getFromLocalStorage ? getFromLocalStorage : '');

        if(parsedLocalStorageData && parsedLocalStorageData.user) {
            setMyUserFullName(`${parsedLocalStorageData.user.name} ${parsedLocalStorageData.user.surname}`);
        }
    }, []);

    //Scroll down when the component first renders
    useLayoutEffect(() => {
        scrollDown();
    }, []);

    useEffect(() => {
        scrollDown(0);

        if(props.isChatEnded) {
            let endChatMessageFound: boolean = false;

            props.chat.some((m: any) => {
                let message = isJsonString(m.message) ? JSON.parse(m.message).value : m.message; 
                if(!!message && message.substring(0, 4) === '#%#%' && message.substring(message.length - 4) === '#%#%') {
                    endChatMessageFound = true;
                    return true;
                }
                return false;
            });

            if(endChatMessageFound === false) {
                props.setIsChatEnded(false);
            }
        }
        // eslint-disable-next-line
    }, [props.chat]);

    const scrollDown = (delay: number = 100) => {
        const chatWindow = chatWindowRef.current;
        if(chatWindow) {
            setTimeout(() => {
                chatWindow.scrollTop = chatWindow.scrollHeight;
            }, delay);
        }
    };

    const handleScroll = () => {
        if (chatWindowRef.current) {
            //Scroll counter from the bottom
            const maxScrollTop = chatWindowRef.current.scrollHeight - chatWindowRef.current.clientHeight;
            const newScrollPosition = maxScrollTop - chatWindowRef.current.scrollTop;
            setScrollPosition(newScrollPosition);
        }
    };

    return (
        <div ref={chatWindowRef} className='chat__window' onScroll={handleScroll}>
            {(chatStore.moreMessagesTotalCount! > page * 25 + 25) && <div ref={pageEnd}></div>}
            {chat}
            {loading && <Spinner className={`chat__load-spinner`} size={SpinnerSize.small} ariaLive="assertive" labelPosition="right" />}
        </div>
    );
};

export default ChatWindow;