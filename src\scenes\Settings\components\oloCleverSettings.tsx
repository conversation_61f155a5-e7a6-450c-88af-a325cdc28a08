import { mergeStyleSets, MessageBar, MessageBarType, Spinner, SpinnerSize, Stack } from '@fluentui/react';
import * as React from 'react';
import { L } from '../../../lib/abpUtility';
import { GlobalConfigurationDto } from '../../../services/globalConfiguration/dto/globalConfigurationDto';
import globalConfigurationService from '../../../services/globalConfiguration/globalConfigurationService';
import createOrUpdateClassNames from '../../BaseComponents/createOrUpdate';
import { ChoiceGroupBase } from '../../BaseComponents/ChoiceGroupBase';

const classNames = mergeStyleSets({
  messageBar: {
      width: 'fit-content',
      marginTop: '10px',
      marginBottom: '10px',
      selectors: {
          '& .ms-MessageBar-innerText': {
              selectors: {
                  '& span': {
                      whiteSpace: 'pre-line',
                  }
              }
          }
      }
  },
  loadSpinnerAbsolute: {
    position: 'absolute',
    top: 0,
    left: 10,
  },
  loadSpinnerAbsoluteBottom: {
    top: 'unset',
    bottom: 50,
  },
  choiceGroup: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '300px',
  },
  columns: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    maxWidth: '1500px',
    marginTop: '30px'
  },
  column: {
    flex: '0 0 50%',
    paddingLeft: '10px',
    paddingRight: '20px',
    boxSizing: 'border-box'
  }
});

export interface IState {
  allConfigurations: GlobalConfigurationDto[][];
  asyncActionInProgress: boolean;
  formError: string;
}

export class OloCleverSettings<TProps> extends React.Component<TProps, IState>{
  constructor(props: any){
    super(props);
    
    this.state = {
      allConfigurations: [[], []],
      asyncActionInProgress: false,
      formError: L(''),
    }
  };

  async componentDidMount() {
    this.setState({ asyncActionInProgress: true });

    await globalConfigurationService.getAll().then((response: any) => {
      if(response && response.totalCount > 0) {
        let halfLength = Math.ceil(response.items.length / 2); 
        let firstHalf = response.items.slice(0, halfLength);
        let secondHalf = response.items.slice(halfLength);
        this.setState({ asyncActionInProgress: false, allConfigurations: [firstHalf, secondHalf] });
      }
    }).catch((error: any) => {
      console.error(error);
      this.setState({ formError: error, asyncActionInProgress: false });
    });

  };

  render() {
    return <Stack style={{paddingBottom: 25, position: 'relative', width: '100%', display: 'flex', flexDirection: 'column', flexWrap: 'wrap'}}>
      {this.state.asyncActionInProgress &&
        <Spinner label={L('Please wait...')} className={`${createOrUpdateClassNames.loadSpinner} ${classNames.loadSpinnerAbsolute}`} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right"
          style={{marginTop: 10}} />
      }
      {(this.state.asyncActionInProgress && this.state.allConfigurations[0].length > 0 && this.state.allConfigurations[1].length > 0) &&
        <Spinner label={L('Please wait...')} className={`${createOrUpdateClassNames.loadSpinner} ${classNames.loadSpinnerAbsolute} ${classNames.loadSpinnerAbsoluteBottom}`} 
          size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" style={{marginTop: 10}} />
      }

        {this.state.formError &&
            <MessageBar messageBarType={MessageBarType.warning} isMultiline={false} className={`${classNames.messageBar}`}
                onDismiss={() => { this.setState({ formError: '' }); }}
            >
                {this.state.formError}
            </MessageBar>
        }

        <div className={classNames.columns}>
          {this.state.allConfigurations.map((configurations: GlobalConfigurationDto[], columnIndex: number) => {
            return <div className={classNames.column}>
              {configurations.map((configuration: GlobalConfigurationDto, configurationIndex: number) => {
                return <ChoiceGroupBase label={L(configuration.configurationName)} disabled={this.state.asyncActionInProgress}
                value={!!configuration.isEnabled ? (configuration.isEnabled ? 'true' : 'false') : 'false'}
                options={[{key: 'true', text: L('On')}, {key: 'false', text: L('Off')}]}
                onChange={async (e: any) => {
                  if(!!configuration.id && !!e.key) {
                      this.setState({ asyncActionInProgress: true });

                      await globalConfigurationService.updateGlobalConfiguration(configuration.id, e.key === 'true' ? true : false).then((response: any) => {
                        if(response && !!response.id) {
                          const cloneAllConfigurations: GlobalConfigurationDto[][] = [...this.state.allConfigurations];
                          cloneAllConfigurations[columnIndex][configurationIndex] = {...cloneAllConfigurations[columnIndex][configurationIndex], isEnabled: e.key === 'true' ? true : false};
                          this.setState({ formError: '', asyncActionInProgress: false, allConfigurations: cloneAllConfigurations });
                        } else {
                          this.setState({ formError: L('Something went wrong, response from server is not valid.'), asyncActionInProgress: false });
                        }
                      }).catch((error: any) => {
                        console.error(error);
                        this.setState({ formError: error, asyncActionInProgress: false });
                      });
                  }
                }}
            />;
              })}
            </div>
          })}
        </div>
    </Stack>
  };
}
