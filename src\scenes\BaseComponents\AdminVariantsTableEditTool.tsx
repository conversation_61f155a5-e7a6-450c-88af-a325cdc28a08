import { IStackTokens, MarqueeSelection, mergeStyleSets, MessageBar, MessageBarType, PrimaryButton, ScrollablePane, ScrollbarVisibility, SelectionMode, ShimmeredDetailsList, Spinner, SpinnerSize, Stack, Text, Selection, IDetailsColumnStyles, DefaultButton } from "@fluentui/react";
import { inject } from "mobx-react";
import React from "react";
import { L } from "../../lib/abpUtility";
import productAttributeService from "../../services/productAttribute/productAttributeService";
import ProductAttributeStore from "../../stores/productAttributeStore";
import Stores from "../../stores/storeIdentifier";
import { myTheme } from "../../styles/theme";
import { isJsonString } from "../../utils/utils";
import { LabeledTextField } from "../../components/LabeledTextField";
import { TextFieldBase } from "./textFieldBase";
import { ITableColumn } from "./ITableColumn";

const classNames = mergeStyleSets({
    stackContainer: {
        width: '100vw',
        maxWidth: '100%',
        height: 'auto',
        minHeight: '60vh',
        maxHeight: '90vh',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        padding: '0 25px 20px',
        margin: '0',
    },
    inputsWrapper: {
        background: myTheme.palette.neutralQuaternaryAlt,
        wdith: '100%',
        minWidth: '400px',
        marginRight: '20px',
        padding: '10px 15px',
        overflow: 'hidden',
    },
    loadSpinner: {
        position: 'absolute',
        top: -10,
        left: 20,
        display: 'inline-flex',
        marginTop: '30px',
        marginRight: 'auto',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    customScrollablePane: {
        width: '100%',
        minHeight: '380px',
        position: 'relative',
        border: '1px solid rgba(0, 0, 0, 0.2)',
        selectors: {
            '& .ms-DetailsList.is-horizontalConstrained': {
                overflow: 'unset',
            }
        }
    },
    dialogDisclaimers: {
        display: 'block',
        fontSize: '12px',
        textAlign: 'left',
        marginLeft: '25px',
        whiteSpace: 'pre-line',
    }
});

const headerStyle: Partial<IDetailsColumnStyles> = {
    cellTitle: {
        textAlign: 'center',
        display: 'block',
        width: '100%',
        maxWidth: '270px',
    }
}

const verticalGapStackTokens: IStackTokens = {
    childrenGap: 10,
    padding: 10,
};

export interface IAdminVariantsTableEditToolProps {
    activeAttribute?: any;
    productAttributeStore?: ProductAttributeStore;
    variantsTableData?: any;
    gnLanguage?: any;
}

export interface IAdminVariantsTableEditToolState {
    asyncActionInProgress: boolean;
    asyncActionMessage: string;
    messageBarType: MessageBarType;
    variantsTableColumns: any[];
    variantsTableItems: any[];
    variantsTableTitle: string;
    variantsTableDisclaimers: string[];
}

@inject(Stores.ProductAttributeStore)
export class AdminVariantsTableEditTool extends React.Component<IAdminVariantsTableEditToolProps, IAdminVariantsTableEditToolState> {
    constructor(props: IAdminVariantsTableEditToolProps) {
        super(props);

        this.state = {
            asyncActionInProgress: false,
            asyncActionMessage: "",
            messageBarType: MessageBarType.success,
            variantsTableColumns: [],
            variantsTableItems: [],
            variantsTableTitle: "",
            variantsTableDisclaimers: [],
        };
    }

    private setVariantsTableData() {
        const currentLanguage: string = this.props.gnLanguage.UniqueSeoCode;
        const {variantsTableData} = this.props;
        let tempVariantsTableDisclaimers: string[] = [];
        let tempVariantsTableTitle: string = "";
        let tempVariantsTableColumns: any[] = [];
        let tempVariantsTableItems: any[] = [];

        if(variantsTableData) {
            if(variantsTableData.Table && variantsTableData.Table.Locales) {
                variantsTableData.Table.Locales.forEach((locale: any) => {
                    if(locale.Language === currentLanguage) {
                        tempVariantsTableTitle = locale.LocaleValue;
                    }
                });
            }
            
            if(variantsTableData.Disclaimers) {
                variantsTableData.Disclaimers.forEach((disclaimer: any) => {
                    if(disclaimer.Language === currentLanguage) {
                        disclaimer.LocaleValue.forEach((disclaimerValue: string) => {
                            tempVariantsTableDisclaimers.push(disclaimerValue);
                        });
                    }
                });
            }
            
            variantsTableData.Rows.forEach((row: any, rowIndex: number) => {
                row.Cells.forEach((cell: any) => {
                    if(cell.Language === currentLanguage) {
                        if(rowIndex === 0) {
                            cell.LocaleValue.forEach((value: string, valueIndex: number) => {
                                tempVariantsTableColumns.push({
                                    key: valueIndex === 0 ? 'nameCol' : `col_${valueIndex}`,
                                    minWidth: valueIndex === 0 ? 250 : 270,
                                    maxWidth: valueIndex === 0 ? 250 : 270,
                                    name: valueIndex === 0 ? '' : L(value),
                                    fieldName: valueIndex === 0 ? 'nameCol' : `col_${valueIndex}`,
                                    styles: headerStyle,
                                    onRender: (item: any, itemIndex: number) => {
                                        if(valueIndex === 0) {
                                            return <TextFieldBase label={undefined} width={250} height={33} value={item['nameCol']} isDataLoaded={true} style={{fontWeight: 'bold'}}
                                                    customShimmerStyles={{maxWidth: '250px'}} rows={1} multiline={true}
                                                    onChange={(e, inputValue) => { 
                                                        if(typeof inputValue === 'string') {
                                                            const cloneVariantsTableItems: any[] = [...this.state.variantsTableItems];
                                                            const colIndex: string = valueIndex === 0 ? 'nameCol' : `col_${valueIndex}`;

                                                            if(inputValue !== cloneVariantsTableItems[itemIndex][colIndex]) {
                                                                cloneVariantsTableItems[itemIndex][colIndex] = inputValue;
                                                                this.setState((prevState) => ({ ...prevState, variantsTableItems: cloneVariantsTableItems }));
                                                            }
                                                        }
                                                    }}
                                                />;
                                        } else {
                                            return <TextFieldBase label={undefined} width={270} height={33} value={item[`col_${valueIndex}`]} customShimmerStyles={{maxWidth: '270px'}} 
                                                    style={{textAlign: 'center', display: 'block', width: '100%', maxWidth: '270px'}} isDataLoaded={true} 
                                                    rows={1} multiline={true}
                                                    onChange={(e, inputValue) => { 
                                                        if(typeof inputValue === 'string') {
                                                            const cloneVariantsTableItems: any[] = [...this.state.variantsTableItems];
                                                            const colIndex: string = valueIndex === 0 ? 'nameCol' : `col_${valueIndex}`;
                                                            
                                                            if(inputValue !== cloneVariantsTableItems[itemIndex][colIndex]) {
                                                                cloneVariantsTableItems[itemIndex][colIndex] = inputValue;
                                                                this.setState((prevState) => ({ ...prevState, variantsTableItems: cloneVariantsTableItems }));
                                                            }
                                                        }
                                                    }}
                                                />;
                                        }
                                    }
                                });
                            });
                        } else {
                            cell.LocaleValue.forEach((value: string, localeValueIndex: number) => {
                                const newRowIndex: number = rowIndex - 1;

                                if(!tempVariantsTableItems[newRowIndex]) {
                                    tempVariantsTableItems[newRowIndex] = {};
                                }

                                if(localeValueIndex === 0) {
                                    tempVariantsTableItems[newRowIndex][`nameCol`] = `${value}`;
                                } else {
                                    tempVariantsTableItems[newRowIndex][`col_${localeValueIndex}`] = `${value}`;
                                }
                            });
                        }
                    }
                });
            });

            this.setState((prevState) => ({ ...prevState, variantsTableColumns: tempVariantsTableColumns, variantsTableItems: tempVariantsTableItems, 
                                            variantsTableDisclaimers: tempVariantsTableDisclaimers, variantsTableTitle: tempVariantsTableTitle }));
        }
    }

    private async saveJsonStringAsUserField(attrProperty: any) {
        if(attrProperty && attrProperty.userFields) {
            this.setState((prevState) => ({ ...prevState, asyncActionInProgress: true }));

            const {variantsTableColumns, variantsTableItems, variantsTableTitle, variantsTableDisclaimers} = this.state;
            const currentLanguage: string = this.props.gnLanguage.UniqueSeoCode.toUpperCase();
            let currentJsonUserFieldKey: string = "";
            let parsedCurrentJsonUserFieldValue: any;
    
            let indexOfJsonProps: number = -1;
            attrProperty.userFields.some((userField: any, index: number) => {
                if(userField.Key === 'olo_clever_variants_table' || userField.Key === 'variants_table') {
                    currentJsonUserFieldKey = userField.Key;
                    parsedCurrentJsonUserFieldValue = isJsonString(userField.Value) ? JSON.parse(userField.Value) : {};
                    indexOfJsonProps = index;
                    return true;
                }
                return false;
            });

            const buildNewJson: any = {
                Table: {
                    Locales: [
                        {Language: currentLanguage, LocaleValue: variantsTableTitle}
                    ],
                },
                Rows: [
                    {Cells: []}
                ],
            };

            // Title
            parsedCurrentJsonUserFieldValue.Table.Locales.forEach((Locale: any) => {
                if(Locale.Language !== currentLanguage) {
                    buildNewJson.Table.Locales.push(Locale);
                }
            });

            // Disclaimers
            if(parsedCurrentJsonUserFieldValue.Disclaimers || variantsTableDisclaimers.length > 0) {
                if(!buildNewJson.Disclaimers) {
                    buildNewJson['Disclaimers'] = [];
                }

                let newDisclaimerLocaleValue: string[] = [];
                variantsTableDisclaimers.forEach((newDisclaimer: string) => {
                    newDisclaimerLocaleValue.push(newDisclaimer);
                });
                if(newDisclaimerLocaleValue.length > 0) {
                    buildNewJson.Disclaimers.push({Language: currentLanguage, LocaleValue: newDisclaimerLocaleValue});
                }

                if(parsedCurrentJsonUserFieldValue.Disclaimers) {
                    parsedCurrentJsonUserFieldValue.Disclaimers.forEach((Disclaimer: any) => {
                        if(Disclaimer.Language !== currentLanguage) {
                            buildNewJson.Disclaimers.push(Disclaimer);
                        }
                    });
                }
            }

            // Columns
            const columnsLocaleValueToPush: string[] = [];
            variantsTableColumns.forEach((column: ITableColumn) => {
                if(column && typeof column.name === 'string') {
                    columnsLocaleValueToPush.push(column.name);
                }
            });

            buildNewJson.Rows[0].Cells.push({
                Language: currentLanguage,
                LocaleValue: columnsLocaleValueToPush,
            });

            if(parsedCurrentJsonUserFieldValue && parsedCurrentJsonUserFieldValue.Rows && parsedCurrentJsonUserFieldValue.Rows[0] && parsedCurrentJsonUserFieldValue.Rows[0].Cells) {
                parsedCurrentJsonUserFieldValue.Rows[0].Cells.forEach((oldColumn: any) => {
                    if(oldColumn.Language !== currentLanguage) {
                        while(oldColumn.LocaleValue.length !== columnsLocaleValueToPush.length) {
                            if(oldColumn.LocaleValue.length > columnsLocaleValueToPush.length) {
                                oldColumn.LocaleValue.pop();
                            } else {
                                oldColumn.LocaleValue.push("");
                            }
                        }
    
                        buildNewJson.Rows[0].Cells.push(oldColumn);
                    }
                });
            }

            // Rows
            variantsTableItems.forEach((item: any, itemIndex: number) => {
                let newCell: any[] = [];
                let newLocaleValue: string[] = [item.nameCol];

                for(let i = 1; i < columnsLocaleValueToPush.length; i++) {
                    newLocaleValue.push(item[`col_${i}`] ? item[`col_${i}`] : '');
                }

                newCell.push({
                    Language: currentLanguage,
                    LocaleValue: newLocaleValue,
                });

                const otherLanguagesToPush: string[] = [];

                if(parsedCurrentJsonUserFieldValue && parsedCurrentJsonUserFieldValue.Rows && parsedCurrentJsonUserFieldValue.Rows[itemIndex + 1] && parsedCurrentJsonUserFieldValue.Rows[itemIndex + 1].Cells) {
                    parsedCurrentJsonUserFieldValue.Rows[itemIndex + 1].Cells.forEach((oldItem: any) => {
                        if(oldItem.Language !== currentLanguage) {
                            if(!otherLanguagesToPush.includes(oldItem.Language)) {
                                otherLanguagesToPush.push(oldItem.Language);
                            }

                            while(oldItem.LocaleValue.length !== newLocaleValue.length) {
                                if(oldItem.LocaleValue.length > newLocaleValue.length) {
                                    oldItem.LocaleValue.pop();
                                } else {
                                    oldItem.LocaleValue.push("");
                                }
                            }
        
                            newCell.push(oldItem);
                        }
                    });
                } else {
                    let emptyStringsLocaleValue: string[] = [];
                    for(let i = 0; i < newLocaleValue.length; i++) {
                        emptyStringsLocaleValue.push("");
                    }

                    otherLanguagesToPush.forEach((otherLanguage: string) => {
                        newCell.push({Language: otherLanguage, LocaleValue: emptyStringsLocaleValue});
                    });
                }

                buildNewJson.Rows.push({
                    Cells: newCell
                });
            });

            let requestBody: any = [
                {
                    "operationType": indexOfJsonProps >= 0 ? "Replace" : "Add",
                    "value": indexOfJsonProps >= 0 ? JSON.stringify(buildNewJson) : {
                        "Key": currentJsonUserFieldKey,
                        "Value": JSON.stringify(buildNewJson)
                    },
                    "path": `/UserFields/${indexOfJsonProps >= 0 ? indexOfJsonProps + '/Value' : '-'}`
                }
            ];
    
            let response = await productAttributeService.saveUserField(attrProperty.ProductAttributeId, requestBody);
            // await this.props.productAttributeStore?.getAll(this.props.productAttributeStore.defaultRequest);
    
            this.setState((prevState) => ({ 
                ...prevState,
                asyncActionInProgress: false, 
                asyncActionMessage: response.status === 200 ? `${L('Success')}. ${L('Refresh page to see your changes.')}` : L('Something went wrong, check response in dev console.'),
                messageBarType: response.status === 200 ? MessageBarType.success : MessageBarType.error,
            }));
        }
    }
    
    private resetEverything() {
        if(window.confirm(L("Are you sure?"))) {
            this.setState((prevState) => ({
                variantsTableColumns: [prevState.variantsTableColumns[0], {...prevState.variantsTableColumns[1], name: ""}],
                variantsTableItems: [{'nameCol': "", 'col_1': ""}],
                variantsTableTitle: "",
                variantsTableDisclaimers: [],
            }));
        }
    }

    render() {
        const { activeAttribute, variantsTableData } = this.props;
        const { asyncActionInProgress, asyncActionMessage, messageBarType, variantsTableColumns, variantsTableDisclaimers, variantsTableItems, variantsTableTitle } = this.state;

        if(variantsTableData && (!variantsTableItems || variantsTableItems.length === 0)) {
            this.setVariantsTableData();
        }

        return <Stack horizontal={false} className={classNames.stackContainer}>
                {asyncActionInProgress && 
                    <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="right" />
                }

                <Stack className={classNames.inputsWrapper} horizontal verticalAlign="center" tokens={verticalGapStackTokens} style={{marginBottom: 25, minHeight: '62px'}}>
                    {activeAttribute && <>
                        <Text variant="mediumPlus" style={{marginRight: 10, textAlign: 'left'}}>
                            {L(`You are working in context of attribute labeled as:`)}
                            <br />
                            <strong>{activeAttribute.attrName}</strong>
                        </Text>

                        <PrimaryButton theme={myTheme} text={L('Save changes')} disabled={asyncActionInProgress || !activeAttribute}
                            type={'button'} className="form-btn" onClick={() => this.saveJsonStringAsUserField(activeAttribute) } />
                    </>}

                    <DefaultButton theme={myTheme} text={L('Reset everything')} style={{background: myTheme.palette.red, color: myTheme.palette.white}}
                                    type={'button'} className="form-btn" onClick={() => this.resetEverything() } />

                    {(asyncActionMessage && asyncActionMessage.length > 0) && 
                        <MessageBar
                            messageBarType={messageBarType}
                            onDismiss={ () => this.setState((prevState) => ({ ...prevState, asyncActionMessage: "" })) }
                            dismissButtonAriaLabel={L("Close")}
                            style={{whiteSpace: 'pre-line'}}
                            styles={{ root: { width: 'fit-content' } }}
                        >
                            {asyncActionMessage}
                        </MessageBar>
                    }
                </Stack>

                <LabeledTextField key={'variantsTableTitle'} label={L('Title')} value={variantsTableTitle} isDataLoaded={true} 
                    labelContainerCustomStyles={{marginBottom: 10, marginTop: 0, maxWidth: 100}} customLabelStyles={{minWidth: 100, width: 100, maxWidth: 100}}
                    onChange={(e, value) => { 
                        if(typeof value === 'string') {
                            this.setState((prevState) => ({ ...prevState, variantsTableTitle: value }));
                        }
                    }} 
                />

                <Stack horizontal horizontalAlign="center" style={{width: '100%', marginTop: 25, marginBottom: 25}}>
                    <PrimaryButton theme={myTheme} text={L('Add column')} type={'button'} className="form-btn" style={{marginRight: 25, minWidth: '220px'}}
                        onClick={() => {
                            const newColumnItem: any = {};
                            let newFieldName: string = "";

                            for(let key in variantsTableColumns[variantsTableColumns.length - 1]) {
                                if(variantsTableColumns[variantsTableColumns.length - 1].hasOwnProperty(key)) {
                                    newColumnItem[key] = variantsTableColumns[variantsTableColumns.length - 1][key];

                                    if(key === 'key' || key === 'fieldName') {
                                        const keySplitted: string[] = variantsTableColumns[variantsTableColumns.length - 1][key].split('_');

                                        if(keySplitted.length > 1) {
                                            newFieldName = `${keySplitted[0]}_${parseInt(keySplitted[1]) + 1}`;
                                            newColumnItem[key] = newFieldName;
                                        }
                                    } else if(key === 'name') {
                                        newColumnItem[key] = "";
                                    }
                                }
                            }

                            for(let key in variantsTableColumns[variantsTableColumns.length - 1]) {
                                if(variantsTableColumns[variantsTableColumns.length - 1].hasOwnProperty(key) && key === 'onRender') {
                                    newColumnItem[key] = (item: any, itemIndex: number) => {
                                        return <TextFieldBase label={undefined} width={270} height={33} value={item[newFieldName]} customShimmerStyles={{maxWidth: '270px'}} 
                                                style={{textAlign: 'center', display: 'block', width: '100%', maxWidth: '270px'}} isDataLoaded={true} disabled={false} 
                                                rows={1} multiline={true}
                                                onChange={(e, inputValue) => { 
                                                    if(typeof inputValue === 'string') {
                                                        const cloneVariantsTableItems: any[] = [...this.state.variantsTableItems];
                                                        
                                                        if(inputValue !== cloneVariantsTableItems[itemIndex][newFieldName]) {
                                                            cloneVariantsTableItems[itemIndex][newFieldName] = inputValue;
                                                            this.setState((prevState) => ({ ...prevState, variantsTableItems: cloneVariantsTableItems }));
                                                        }
                                                    }
                                                }}
                                            />;
                                    }
                                }
                            }

                            this.setState((prevState) => ({ ...prevState, variantsTableColumns: [...variantsTableColumns, newColumnItem] }));
                        }}
                    />

                    <DefaultButton theme={myTheme} text={L('Remove last column')} style={{background: myTheme.palette.red, color: myTheme.palette.white, minWidth: '220px'}} 
                        type={'button'} className="form-btn"
                        disabled={variantsTableColumns.length <= 2}
                        onClick={() => {
                            const cloneVariantsTableColumns: any[] = [...variantsTableColumns];
                            const lastVariantsTableColumn: ITableColumn = cloneVariantsTableColumns[variantsTableColumns.length - 1];

                            const cloneVariantsTableItems: any[] = [...variantsTableItems];
                            cloneVariantsTableItems.forEach((item: any) => {
                                if(lastVariantsTableColumn.fieldName && item[lastVariantsTableColumn.fieldName]) {
                                    delete item[lastVariantsTableColumn.fieldName];
                                }
                            });
                            cloneVariantsTableColumns.pop();

                            this.setState((prevState) => ({ ...prevState, variantsTableColumns: cloneVariantsTableColumns, variantsTableItems: cloneVariantsTableItems }));
                        }}
                    />
                </Stack>

                <ScrollablePane scrollbarVisibility={ScrollbarVisibility.auto} theme={myTheme} className={classNames.customScrollablePane}>
                    <MarqueeSelection isEnabled={false} selection={ new Selection({selectionMode: SelectionMode.none}) }>
                        <Stack horizontal style={{paddingLeft: '12px', position: 'absolute', top: '21px', left: 0, zIndex: 10}}>
                            {variantsTableColumns.map((column: ITableColumn) => {
                                return <TextFieldBase label={undefined} width={270} height={33} value={column.name} isDataLoaded={true} disabled={column.fieldName === 'nameCol'}
                                    style={{textAlign: 'center', display: 'block', width: '100%', maxWidth: '270px', fontWeight: 'bold'}} 
                                    customShimmerStyles={{marginRight: 20, maxWidth: '270px', width: `${column.maxWidth}px`}} 
                                    onChange={(e, inputValue) => { 
                                        if(typeof inputValue === 'string') {
                                            const cloneVariantsTableColumns: any[] = [...this.state.variantsTableColumns];

                                            for(let key in cloneVariantsTableColumns) {
                                                if(cloneVariantsTableColumns.hasOwnProperty(key) && cloneVariantsTableColumns[key].fieldName === column.fieldName &&
                                                    cloneVariantsTableColumns[key].name !== inputValue
                                                ) {
                                                    cloneVariantsTableColumns[key].name = inputValue;
                                                }
                                            }
                                            this.setState((prevState) => ({ ...prevState, variantsTableColumns: cloneVariantsTableColumns }));
                                        }
                                    }}
                                />;
                            })}
                        </Stack>

                        <ShimmeredDetailsList
                            columns={variantsTableColumns}
                            items={variantsTableItems}
                            selectionMode={SelectionMode.none}
                            enableShimmer={false}
                        />
                    </MarqueeSelection>
                </ScrollablePane>

                <Stack horizontal horizontalAlign="center" style={{width: '100%', marginTop: 25}}>
                    <PrimaryButton theme={myTheme} text={L('Add row')} type={'button'} className="form-btn" style={{marginRight: 25, minWidth: '220px'}}
                        onClick={() => {
                            const newTableItem: any = {};
                            for(let key in variantsTableItems[variantsTableItems.length - 1]) {
                                if(variantsTableItems[variantsTableItems.length - 1].hasOwnProperty(key)) {
                                    newTableItem[key] = "";
                                }
                            }

                            this.setState((prevState) => ({ ...prevState, variantsTableItems: [...variantsTableItems, newTableItem] }));
                        }}
                    />

                    <DefaultButton theme={myTheme} text={L('Remove last row')} style={{background: myTheme.palette.red, color: myTheme.palette.white, minWidth: '220px'}} 
                        type={'button'} className="form-btn"
                        disabled={variantsTableItems.length <= 1}
                        onClick={() => {
                            const cloneVariantsTableItems: any[] = [...variantsTableItems];
                            cloneVariantsTableItems.pop();
                            this.setState((prevState) => ({ ...prevState, variantsTableItems: cloneVariantsTableItems }));
                        }}
                    />
                </Stack>

                {variantsTableDisclaimers.map((variantsTableDisclaimer: string, variantsTableDisclaimerIndex: number) => {
                    return variantsTableDisclaimerIndex === 0 ?
                        <LabeledTextField key={`variantsTableDisclaimer${variantsTableDisclaimerIndex}`} label={L('Disclaimers')} value={variantsTableDisclaimer} isDataLoaded={true} 
                            labelContainerCustomStyles={{marginTop: 20, marginBottom: 10, maxWidth: 100}} 
                            customLabelStyles={{minWidth: 100, width: 100, maxWidth: 100}} customInputStyles={{width: '86vw'}}
                            onChange={(e, value) => { 
                                if(typeof value === 'string') {
                                    const cloneVariantsTableDisclaimers: string[] = [...variantsTableDisclaimers];
                                    cloneVariantsTableDisclaimers[variantsTableDisclaimerIndex] = value;
                                    this.setState((prevState) => ({ ...prevState, variantsTableDisclaimers: cloneVariantsTableDisclaimers }));
                                }
                            }} 
                        />
                        :
                        <TextFieldBase key={`variantsTableDisclaimer${variantsTableDisclaimerIndex}`} label={undefined} value={variantsTableDisclaimer} isDataLoaded={true}
                            customShimmerStyles={{marginBottom: 10, marginLeft: '130px', width: '86vw'}} customInputStyles={{width: '86vw'}}
                            onChange={(e, value) => { 
                                if(typeof value === 'string') {
                                    const cloneVariantsTableDisclaimers: string[] = [...variantsTableDisclaimers];
                                    cloneVariantsTableDisclaimers[variantsTableDisclaimerIndex] = value;
                                    this.setState((prevState) => ({ ...prevState, variantsTableDisclaimers: cloneVariantsTableDisclaimers }));
                                }
                            }} 
                        />
                })}

                <Stack horizontal horizontalAlign="center" style={{width: '100%', marginTop: variantsTableDisclaimers.length === 0 ? 25 : 5}}>
                    <PrimaryButton theme={myTheme} text={L('Add disclaimer')} type={'button'} className="form-btn" style={{marginRight: 25, minWidth: '220px'}}
                        onClick={() => {
                            this.setState((prevState) => ({ ...prevState, variantsTableDisclaimers: [...variantsTableDisclaimers, ""] }));
                        }}
                    />

                    <DefaultButton theme={myTheme} text={L('Remove last disclaimer')} style={{background: myTheme.palette.red, color: myTheme.palette.white, minWidth: '220px'}} 
                        type={'button'} className="form-btn"
                        disabled={variantsTableDisclaimers.length === 0}
                        onClick={() => {
                            const cloneVariantsTableDisclaimers: string[] = [...variantsTableDisclaimers];
                            cloneVariantsTableDisclaimers.pop();
                            this.setState((prevState) => ({ ...prevState, variantsTableDisclaimers: cloneVariantsTableDisclaimers }));
                        }}
                    />
                </Stack>
            </Stack>
    }
}