import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { CalculationContentView } from './components/calculationContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import CalculationStore from '../../stores/calculationStore';
import { CalculationDto } from '../../services/calculation/dto/calculationDto';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	calculationStore: CalculationStore;
	match: any;
}

@inject(Stores.CalculationStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private calculationId = this.props.match.params.id;

	async componentDidMount() {
		await this.props.calculationStore.get({ id: this.calculationId } as CalculationDto);
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<CalculationContentView store={this.props.calculationStore} payload={ this.props.calculationStore.model as CalculationDto } renderFooter={{show: true}}
					customData={{calculationId: this.calculationId}}	
				/>
			</FocusZone>
		);
	}
}

export default Index;