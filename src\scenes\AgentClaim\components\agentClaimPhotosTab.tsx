import { Spinner, SpinnerSize, Stack, mergeStyleSets, Text, PrimaryButton, IconButton, Dialog, DialogType, DialogFooter, De<PERSON>ult<PERSON><PERSON><PERSON> } from "@fluentui/react";
import React from "react";
import { L } from "../../../lib/abpUtility";
import { spinnerClassNames } from "../../../styles/spinnerStyles";
import { dateFormat } from "../../../utils/utils";
import { myTheme } from "../../../styles/theme";
import { AgentClaimDto } from "../../../services/agentClaim/dto/agentClaimDto";
import { uploadFileToAzure } from "../../../services/azureService";
import agentClaimAttachedFilesService from "../../../services/agentClaim/agentClaimAttachedFilesService";
import { AgentClaimTypeEnum } from "../../../services/agentClaim/agentClaimTypeEnums";
import { AgentClaimAttachedFilesDto } from "../../../services/agentClaim/dto/agentClaimAttachedFilesDto";

const classNames = mergeStyleSets({
    attachedFilesLabel: {
        fontWeight: 'bold',
        marginTop: '20px',
        marginBottom: '10px',
    },
    attachedFilesListItem: {
        marginBottom: '5px',
    },
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    uploadButton: {
        width: 'fit-content',
        padding: '25px 50px',
        marginTop: '20px',
    },
    uploadedImage: {
        maxHeight: '120px',
        width: 'auto',
        display: 'block',
        border: '1px solid rgba(0, 0, 0, 0.2)',
        margin: '5px 0',
    },
});

export interface IAgentClaimPhotosTabProps {
    agentClaim: AgentClaimDto;
}

export class AgentClaimPhotosTab extends React.Component<IAgentClaimPhotosTabProps> {
    private asyncActionInProgress: boolean = false;
    private fileUploadInputRef: any;
    private showDialog: boolean = false;
    private attachedFiles: any;
    private attachedFileToDelete: AgentClaimAttachedFilesDto | null = null;

    constructor(props: any) {
        super(props);
        this.fileUploadInputRef = React.createRef();
    }

    async componentDidMount() {
        this.asyncActionInProgress = true;
        this.forceUpdate();
        
        await agentClaimAttachedFilesService.getPhotoByClaimId(parseInt(this.props.agentClaim.id)).then((response: any) => {
            if(response && response.items) {
                this.attachedFiles = response.items;
            }
        }).catch((error: any) => {
            console.error(error);
        });
        
        this.asyncActionInProgress = false;
        this.forceUpdate();
    }

    private triggerUpload = () => {
        this.fileUploadInputRef.current.click();
    };

    private async onUpload() {
        this.asyncActionInProgress = true;
        this.forceUpdate();

        const selectedFiles = !!this.fileUploadInputRef.current.files && this.fileUploadInputRef.current.files.length > 0 ? this.fileUploadInputRef.current.files : null;

        [...selectedFiles].map(async (selectedFile: any) => {
            this.asyncActionInProgress = true;
            this.forceUpdate();

            const result = await uploadFileToAzure(selectedFile);
    
            await agentClaimAttachedFilesService.createNew({
                "id": "0",
                "claimId": parseInt(this.props.agentClaim.id),
                "claim": this.props.agentClaim,
                "agentClaimFileType": AgentClaimTypeEnum.Photo,
                "fileUrl": result.url,
                "originalFileName": selectedFile.name,
                "blobFileName": result.name,
                "description": "",
            }).then(async (response: any) => {
                await agentClaimAttachedFilesService.getPhotoByClaimId(parseInt(this.props.agentClaim.id)).then((response: any) => {
                    if(response && response.items) {
                        this.attachedFiles = response.items;
                    }
                }).catch((error: any) => {
                    console.error(error);
                });
                this.asyncActionInProgress = false;
                this.forceUpdate();
            }).catch((error: any) => {
                console.error(error);
    
                this.asyncActionInProgress = false;
                this.forceUpdate();
            });
        });
    }

    async deleteAttachedFile() {
        this.showDialog = false;

        if(this.attachedFileToDelete) {
            this.asyncActionInProgress = true;
            this.forceUpdate();
    
            await agentClaimAttachedFilesService.delete(this.attachedFileToDelete).then(async (deleteResponse: any) => {
                this.attachedFileToDelete = null;

                if(deleteResponse && deleteResponse.success) {
                    await agentClaimAttachedFilesService.getPhotoByClaimId(parseInt(this.props.agentClaim.id)).then((response: any) => {
                        if(response && response.items) {
                            this.attachedFiles = response.items;
                        }

                        this.asyncActionInProgress = false;
                        this.forceUpdate();
                    }).catch((error: any) => {
                        console.error(error);

                        this.asyncActionInProgress = false;
                        this.forceUpdate();
                    });
                }
                this.asyncActionInProgress = false;
                this.forceUpdate();
            }).catch((error: any) => {
                console.error(error);
                this.attachedFileToDelete = null;
                this.asyncActionInProgress = false;
                this.forceUpdate();
            });
        }
    }

    closeModal() {
        this.attachedFileToDelete = null;
        this.showDialog = false;
        this.forceUpdate();
    }

    render() {
        let attachedFilesList: JSX.Element[] = [];
        if(!!this.attachedFiles && this.attachedFiles.length > 0) {
            this.attachedFiles.forEach((file: any) => {
                attachedFilesList.push(
                    <li key={file.id} className={classNames.attachedFilesListItem}>
                        <IconButton
                            styles={{
                                root: {
                                    color: myTheme.palette.red,
                                    marginRight: '2px',
                                },
                                rootHovered: {
                                    color: myTheme.palette.redDark,
                                },
                            }}
                            iconProps={{ iconName: 'Delete' }}
                            ariaLabel={L("Close popup modal")}
                            onClick={() => { 
                                this.attachedFileToDelete = file;
                                this.showDialog = true; 
                                this.forceUpdate();
                            }}
                        />
                        {dateFormat(file.creationTime, "DD.MM.YYYY HH:mm", true)}{` - `} 
                        <a href={file.fileUrl} title={L("Download file")}>{file.originalFileName}</a>
                        <img alt={file.originalFileName} src={file.fileUrl} className={classNames.uploadedImage} />
                    </li>
                );
            });
        }

        const fileUploadButton = <PrimaryButton 
                                    className={classNames.uploadButton}
                                    theme={myTheme}
                                    text={L('Upload file')}
                                    type={'file'}
                                    onClick={this.triggerUpload}
                                    disabled={this.asyncActionInProgress}
                                    iconProps={{ iconName: 'Upload' }}
                                    style={{marginTop: '20px'}}
                                />;

        return <>
            <Dialog
                hidden={!this.showDialog}
                onDismiss={() => this.closeModal()}
                dialogContentProps={{
                    type: DialogType.normal,
                    title: L("Are you sure you want to delete this file?"),
                    closeButtonAriaLabel: L('Close'),
                }}
                modalProps={{isBlocking: true, styles: {main: { maxWidth: 450 }},}}
                theme={myTheme}
            >
                <DialogFooter theme={myTheme}>
                    <DefaultButton theme={myTheme} onClick={() => this.closeModal()} text={L('No')} />
                    <PrimaryButton text={L('Yes')} theme={myTheme} disabled={this.asyncActionInProgress}
                        onClick={() => {
                            this.deleteAttachedFile();
                        }}
                    />
                </DialogFooter>
            </Dialog>

            <Stack horizontal={true}>
                <input ref={this.fileUploadInputRef} type="file" multiple accept="image/png, image/jpeg" style={{display: 'none'}} onChange={() => this.onUpload()} />
                { fileUploadButton }

                {this.asyncActionInProgress && (
                    <Spinner label={L('Please wait...')} className={classNames.loadSpinner} size={SpinnerSize.large} ariaLive="assertive" labelPosition="top" />
                )}
            </Stack>

            { attachedFilesList.length > 0 && <Stack>
                <Text variant="large" className={classNames.attachedFilesLabel}>
                    { L('Attached files:') }
                </Text>

                <ul>
                    { attachedFilesList }
                </ul>
            </Stack> }

            {this.asyncActionInProgress &&
                <Spinner label={L('Please wait...')} className={spinnerClassNames.loadSpinner} size={SpinnerSize.medium} ariaLive="assertive" labelPosition="right" />
            }
        </>;
    }
}