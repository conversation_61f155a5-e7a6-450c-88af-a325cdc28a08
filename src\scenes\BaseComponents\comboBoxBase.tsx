import * as React from 'react';
import { ComboBox, IComboBox, IComboBoxOption, mergeStyleSets, Shimmer } from '@fluentui/react';
import Stores from '../../stores/storeIdentifier';
import { inject } from 'mobx-react';
import EventStore from '../../stores/eventStore';
import { myTheme } from '../../styles/theme';
import { LabelContainerComponent } from './labelContainerComponent';
import { LabelComponent } from './labelComponent';
import { getShimmerStyles } from '../../utils/stylesUtils';
import { L } from '../../lib/abpUtility';

var _ = require('lodash');

const classNames = mergeStyleSets({
    requiredMarker: {
        color: 'rgb(164, 38, 44)',
        marginLeft: '5px',
    }
});

export interface IComboBoxBaseProps {
    id?: string | undefined
    label?: string
    options: IComboBoxOption[]
    value?: string | number | undefined;
    onChange?: (value: string | number | undefined) => void;
    onInputValueChange?: (value: string | undefined) => void;
    required?: boolean
    eventStore?: EventStore
    initialValue?: string | number | undefined
    disabled?: boolean,
    placeholder?: string,
    customClassName?: any;
    isDataLoaded?: boolean;
    customDropdownWidth?: string;
    customLabelStyles?: any;
    allowFreeform?: boolean;
    autoComplete?: string;
    validationData?: any;
    openListOnFocus?: boolean;
}

export interface IComboBoxBaseState {
    value: string | number | undefined,
    selectedKey: string | number | null;
    inputValue: string | undefined;
    isOpen: boolean;
}

@inject(Stores.EventStore)
export class ComboBoxBase extends React.Component<IComboBoxBaseProps, IComboBoxBaseState> {
    private comboBoxRef: IComboBox | null = null;
    private debouncedOnChange: typeof this.props.onChange;
    private debouncedOnInputValueChange: typeof this.props.onInputValueChange;

    constructor(props: IComboBoxBaseProps) {
        super(props);

        this.state = {
            value: "",
            selectedKey: null,
            inputValue: undefined,
            isOpen: false
        }
    }

    static getDerivedStateFromProps(props: IComboBoxBaseProps, state: IComboBoxBaseState) {
        const val = props.initialValue ?? props.value;

        if (val === undefined || val === null || val === '') {
            return {
                selectedKey: null,
                inputValue: '',
                value: ''
            };
        }

        const selectedOption = props.options.find(opt => opt.key === val);

        return {
            selectedKey: selectedOption ? selectedOption.key : null,
            inputValue: selectedOption ? undefined : val.toString(),
            value: selectedOption ? selectedOption.key : val.toString(),
        };
    }

    componentDidMount() {
        try {
            if (this.props.value !== undefined && this.props.value !== null && this.props.value !== '') {
                this.setState({ value: this.props.value });
                this.props.onChange?.(this.props.value);
            }
        } catch(error) {
            console.error(error);
        }
    }

    componentDidUpdate(prevProps: IComboBoxBaseProps) {
        if (this.props.onChange !== prevProps.onChange) {
            this.debouncedOnChange = _.debounce(this.props.onChange, 500);
        }

        if (this.props.onInputValueChange !== prevProps.onInputValueChange) {
            this.debouncedOnInputValueChange = _.debounce(this.props.onInputValueChange, 500);
        }
    }

    private mapValidationData(validationData: any): any {
        let mappedValidationData: any = {};
    
        let notDefaultBehaviourForKeys: string[] = ['multiline', 'errorMessage', 'readOnly', 'disabled'];
        
        for(const key in validationData) {
            if (Object.prototype.hasOwnProperty.call(validationData, key)) {
                if(key === 'multiline') {
                    mappedValidationData['multiline'] = 'true';
                    mappedValidationData['rows'] = validationData[key];
                }
                
                if(key === 'errorMessage') {
                    mappedValidationData[key] = L(validationData[key]);
                }
                if(key === 'readOnly' || key === 'disabled') {
                    mappedValidationData[key] = (validationData[key] === 'true' ? true : false);
                }
        
                if(!notDefaultBehaviourForKeys.includes(key)) {
                    mappedValidationData[key] = validationData[key];
                }
            }
        }
    
        return mappedValidationData;
    }

    private pendingValueChange = (option?: IComboBoxOption, index?: number, value?: string) => {
        if(this.props.onInputValueChange && typeof value === 'string' && this.debouncedOnInputValueChange) {
            this.debouncedOnInputValueChange(value);
        }

        if(this.props.onChange && typeof value === 'string' && this.debouncedOnChange) {
            this.debouncedOnChange(value);
        }
    }

    private handleFocus = () => {
        if (this.props.openListOnFocus) {
            this.setState({ isOpen: true }, () => {
                if (this.comboBoxRef) {
                    this.comboBoxRef.focus(true);
                }
            });
        }
    };

    private handleIconClick = () => {
        this.setState({ isOpen: true }, () => {
            if (this.comboBoxRef) {
                this.comboBoxRef.focus(true);
            }
        });
    };

    private change = (event: React.FormEvent<IComboBox>, option?: IComboBoxOption, index?: number, value?: string) => {
        if (option) {
            this.setState({
                selectedKey: option.key,
                inputValue: undefined,
                isOpen: false
            }, () => {
                this.debouncedOnChange?.(option.key);
            });
        } else if (value !== undefined) {
            this.setState({
                inputValue: value,
                selectedKey: null
            }, () => {
                this.debouncedOnInputValueChange?.(value);
                this.debouncedOnChange?.(value);
            });
        } else {
            this.setState({
                selectedKey: null,
                inputValue: undefined,
            }, () => {
                this.debouncedOnChange?.('');
            });
        }
    };

    render() {
        const { label, options, required, eventStore, id, disabled, placeholder, customClassName,
            isDataLoaded, customDropdownWidth, customLabelStyles, validationData, autoComplete, allowFreeform, openListOnFocus } = this.props;

        let mappedValidationData = this.mapValidationData(validationData);
        
        const combobox = <ComboBox
            componentRef={(ref) => (this.comboBoxRef = ref)}
            className={customClassName && customClassName}
            style={{width: customDropdownWidth ? customDropdownWidth : "250px"}}
            theme={myTheme} 
            errorMessage={eventStore!.getErrorMessageFor(id)} 
            disabled={disabled} 
            required={required} 
            inputValue={this.state.inputValue}
            selectedKey={this.state.selectedKey}
            options={options} 
            onChange={this.change}
            onPendingValueChanged={this.pendingValueChange}
            allowFreeform={typeof allowFreeform === 'boolean' ? allowFreeform : true}
            autoComplete={!!autoComplete ? autoComplete : 'off'} 
            placeholder= {placeholder}
            onFocus={openListOnFocus === true ? this.handleFocus : undefined}
            onMenuOpen={() => this.setState({ isOpen: true })}
            onMenuDismiss={() => this.setState({ isOpen: false })}
            isOpen={this.state.isOpen}
            onClick={this.handleIconClick}
            {...mappedValidationData}
        />;

        return label && label.length > 0 ?
            <LabelContainerComponent>
                <LabelComponent customStyles={customLabelStyles ? customLabelStyles : {}} label={label || ''}/>
                <Shimmer isDataLoaded={isDataLoaded} styles={getShimmerStyles} ariaLabel="Loading content">
                    { combobox }
                </Shimmer>
                {(required && required === true) && <span className={classNames.requiredMarker}>*</span>}
            </LabelContainerComponent>
            : 
            <Shimmer isDataLoaded={isDataLoaded} styles={getShimmerStyles} ariaLabel="Loading content">
                { combobox }
                {(required && required === true) && <span className={classNames.requiredMarker}>*</span>}
            </Shimmer>;
    }
}