import { mergeStyleSets} from '@fluentui/merge-styles'
import { myTheme } from '../../styles/theme';
import { AboutAppText } from '../../styles/aboutApp';

const classNames = mergeStyleSets({
  footer: {
    position: 'sticky',
    left: 0,
    bottom: 0,
    width: '100%',
    textAlign: 'center',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: myTheme.palette.neutralLighterAlt,
    padding: '10px 15px 10px 20px',
    boxSizing: 'border-box'
  },
  footerText: {
    margin: 0,
  },
});

const Footer = () => {
  return (
    <footer className={classNames.footer}>
      <p className={classNames.footerText}>{AboutAppText}</p>
    </footer>
  );
};

export default Footer;
