import { CrudServiceBase } from '../base/crudServiceBase';
import http from '../httpService';
import { EntityStringDto } from '../dto/entityStringDto';
import { isUserLoggedIn } from '../../utils/authUtils';

export class CrudManyServiceBase<TDto extends EntityStringDto> extends CrudServiceBase<TDto> {
    public async createMany(createUserInputs: TDto[]) {
      isUserLoggedIn();
      let result = await http.post(this.endpoint.Custom("CreateMany"), createUserInputs);
      return !!result.data && !!result.data.result ? result.data.result : result.data;
  }
}