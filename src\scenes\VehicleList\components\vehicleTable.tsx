import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import { RouterPath } from "../../../components/Router/router.config";
import { Link } from "@fluentui/react";
import { VehicleDto } from "../../../services/vehicle/vehicleDto";
import { VehiclePanel } from "./vehiclePanel";

export class VehicleTable extends FluentTableBase<VehicleDto> {
  getItemDisplayNameOf(item: VehicleDto): string {
    return `${item.registrationNumber}`;
  }

  getColumns(): ITableColumn[] {
    return VehicleTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('ID'),
        fieldName: 'id',
        minWidth: 30,
        maxWidth: 30,
        onRender: (item: any): any => {
          return <Link onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.Vehicle}/${item.id}`);
                      }} 
                        href={`/${RouterPath.Vehicle}/${item.id}`}>
                  {item.id}
                </Link>
        }
      },
      {
        name: L('Owner'),
        fieldName: 'clientName',
        minWidth: 170,
        maxWidth: 170,
      },
      {
        name: L('VIN number'),
        fieldName: 'vin',
        minWidth: 140,
        maxWidth: 140,
      },
      {
        name: L('Registration number'),
        fieldName: 'registrationNumber',
        minWidth: 140,
        maxWidth: 140,
      },
      {
        name: L('Production year'),
        fieldName: 'productionYear',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Mileage'),
        fieldName: 'mileage',
        minWidth: 70,
        maxWidth: 70,
      },
      {
        name: L('Eurotax ID'),
        fieldName: 'eurotaxCarId',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Infoexpert ID'),
        fieldName: 'infoExpertId',
        minWidth: 120,
        maxWidth: 120,
      },
      {
        name: L('Vehicle type'),
        fieldName: 'vehicleType',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any): any => {
          return L(`${item.vehicleType}`);
        }
      },
      {
        name: L('Fuel type'),
        fieldName: 'fuelType',
        minWidth: 120,
        maxWidth: 120,
        onRender: (item: any): any => {
          return L(`${item.fuelType}`);
        }
      },
      {
        name: L('Vehicle info'),
        fieldName: 'vehicleInfo',
        minWidth: 120,
        maxWidth: 120,
      },
    ];
  }

  getTitle(): string {
    return L('Vehicles');
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: true,
      customActions: false,
    };
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <>
      <VehiclePanel
        {...props}
      />
    </>
  }
}