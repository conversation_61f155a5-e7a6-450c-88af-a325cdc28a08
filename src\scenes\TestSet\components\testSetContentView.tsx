import { Callout, Icon, IStackStyles, IStackTokens, mergeStyleSets, Pivot, PivotItem, Stack, Text } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { Controls } from '../../BaseComponents/controls';
import { TestSetDto } from '../../../services/testSet/dto/testSetDto';
import { defaultTestSet } from '../../../stores/testSetStore';
import { jsonViewer } from '../../../utils/jsonViewerUtils';
import { dateFormat, getNumberWithSpaces, isJsonString } from '../../../utils/utils';
import { LabeledTextField } from '../../../components/LabeledTextField';
import testSetService from '../../../services/testSet/testSetService';

const stackStyles: IStackStyles = {
    root: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
    },
};

const customSpacingStackTokens: IStackTokens = {
    childrenGap: '0',
    padding: '25px 0',
};

const classNames = mergeStyleSets({
    calculationBox: {
        minWidth: '164px',
        boxSizing: 'border-box',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '25px 50px',
        cursor: 'default',
        borderRadius: '3px',
        marginRight: '50px',
        marginBottom: '25px',
        position: 'relative',
        selectors: {
            '& p': {
                color: myTheme.palette.themePrimary,
                fontWeight: 'bold',
                fontSize: '1rem',
            },
            '& h3': {
                color: myTheme.palette.blue,
                fontSize: '1.2rem',
                selectors: {
                    '& span': {
                        color: myTheme.palette.black,
                        fontSize: '0.8rem',
                    }
                }
            },
            '& h5': {
                fontSize: '0.9rem',
            }
        }
    },
    calculationBoxError: {
        border: `2px solid ${myTheme.palette.red} !important`,
        cursor: 'not-allowed',
        selectors: {
            '& p': {
                color: myTheme.palette.red,
            },
            '& h3': {
                color: `${myTheme.palette.red} !important`,
                textDecoration: 'line-through',
            }
        }
    },
    calculationBoxWarning: {
        border: `2px solid ${myTheme.palette.yellow} !important`,
        cursor: 'not-allowed',
        selectors: {
            '& p': {
                color: myTheme.palette.yellowDark,
            },
            '& h3': {
                color: `${myTheme.palette.yellowDark} !important`,
                textDecoration: 'line-through',
            }
        }
    },
    calculationBoxDisabled: {
        border: `2px solid ${myTheme.palette.themeDark} !important`,
        background: `${myTheme.palette.blackTranslucent40} !important`,
        cursor: 'not-allowed',
        selectors: {
            '& p': {
                color: myTheme.palette.themeDark,
            },
            '& h3': {
                color: `${myTheme.palette.themeDark} !important`,
            }
        }
    },
    calculationBoxIcon: {
        position: 'absolute',
        top: '5px',
        right: '5px',
        color: myTheme.palette.themePrimary,
        fontSize: '22px',
        cursor: 'pointer',
    },
    calculationBoxErrorIcon: {
        color: myTheme.palette.red,
        left: '5px',
        right: 'none',
    },
    calculationBoxWarningIcon: {
        color: `${myTheme.palette.yellow} !important`,
    },
    calculationBoxOutputDetailsIcon: {
        bottom: '5px',
        top: 'none',
        left: '5px',
        right: 'none',
        color: myTheme.palette.neutralQuaternary,
    },
    notSelectedCalculationBox: {
        border: `1px solid ${myTheme.palette.themePrimary}`,
        background: myTheme.palette.themeLighterAlt,
        marginTop: '1px',
        marginLeft: '1px',
        marginRight: '51px',
        marginBottom: '26px',
    },
    selectedCalculationBox: {
        border: `2px solid ${myTheme.palette.blue}`,
        background: myTheme.palette.themeLighter,
        selectors: {
            '& p': {
                color: myTheme.palette.blue,
            },
            '& h3': {
                color: `${myTheme.palette.blue} !important`,
            }
        }
    },
    callout: {
        width: 320,
        maxWidth: '90%',
        padding: '20px',
        cursor: 'pointer',
    },
    calloutText: {
        whiteSpace: 'pre-line',
    },
    hide: {
        display: 'none !important',
    },
    messageBar: {
        width: 'fit-content'
    },
    customActionButton: {
        width: 'fit-content',
        padding: '25px 50px',
        marginTop: '0 !important',
        marginRight: '20px',
    },
    fontBold: {
        fontWeight: '800',
    },
    summaryAttributesWrapper: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',
        boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.25)',
        borderRadius: '12px',
        padding: '15px',
        maxWidth: '90%',
        marginBottom: '15px',
        marginTop: '15px',
    },
    summaryAttribute: {
        padding: '5px 10px',
        marginRight: '10px',
        marginBottom: '5px',
        background: myTheme.palette.themeDarker,
        color: `${additionalTheme.white} !important`,
        borderRadius: '10px',
        fontSize: '0.8rem',
        whiteSpace: 'pre-line',
        selectors: {
            '&:last-child': {
                borderRight: 'none',
            }
        }
    },
    historyResultRowWrapper: {
    },
    activeHistoryResultRowWrapper: {
        background: myTheme.palette.themeLighterAlt,
        padding: '5px 25px 5px',
    },
    historyResultDateLabel: {
        cursor: 'pointer',
        fontWeight: 'bold',
        fontSize: '1.2rem',
        marginTop: 5
    },
    historyResultRow: {
        selectors: {
            '& .ms-Stack': {
                paddingTop: 0,
                paddingBottom: 0,
            }
        }
    }
});

const contentStyles = mergeStyleSets({
    container: {
        display: 'flex',
        flexFlow: 'column nowrap',
        alignItems: 'stretch',
    },
    body: {
        flex: '4 4 auto',
        padding: '0 24px 24px 24px',
        overflowY: 'hidden',
        maxWidth: '33%',
        selectors: {
            ':first-child': {
                padding: '0 60px 24px 24px',
            },
            ':last-child': {
                padding: '0 24px 24px 50px',
            },
            'p': { 
                margin: '14px 0' 
            },
            'p:first-child': { 
                marginTop: 0 
            },
            'p:last-child': { 
                marginBottom: 0 
            },
            '.json': {
                fontSize: '16px',
            },
            '.json > .json__item': {
                display: 'block',
            },
            '.json__item': {
                display: 'none',
                marginTop: '10px',
                paddingLeft: '20px',
                userSelect: 'none',
            },
            '.json__item--collapsible': {
                cursor: 'pointer',
                overflow: 'hidden',
                position: 'relative',
                selectors: {
                    '::before': {
                        content: "'+'",
                        position: 'absolute',
                        left: '5px',
                    },
                    '::after': {
                        backgroundColor: 'lightgrey',
                        content: "''",
                        height: '100%',
                        left: '9px',
                        position: 'absolute',
                        top: '26px',
                        width: '1px',
                    }
                }
            },
            '.json__item--collapsible:hover > .json__key, .json__item--collapsible:hover > .json__value': {
                textDecoration: 'underline',
            },
            '.json__toggle': {
                display: 'none',
            },
            '.json__toggle:checked ~ .json__item': {
                display: 'block',
            },
            '.json__key': {
                color: 'darkblue',
                display: 'inline',
            },
            '.json__key::after': {
                content: "': '",
            },
            '.json__value': {
                display: 'inline',
            },
            '.json__value--string': {
                color: 'green',
            },
            '.json__value--number': {
                color: 'blue',
            },
            '.json__value--boolean': {
                color: 'red',
            },
        },
    },
});

export class TestSetContentView extends GenericContentView {
    private testSet: TestSetDto = defaultTestSet;
    private calloutStatus: any = {};
    private customInputsForCalculationIndex: number = -1;
    private jsonResponseHistory: any[] = [];
    private historyResultListShowRow: number = -1;

    async componentDidMount() {
        this.checkIfDataIsLoaded("testSet");

        if(this.props.payload.model && !!this.props.payload.model.id) {
            await testSetService.getHistory(this.props.payload.model).then((response: any) => {
                if(response && response.totalCount > 0) {
                    response.items.forEach((element: any) => {
                        this.jsonResponseHistory.push({date: dateFormat(element.modificationDate, undefined, true), result: element.result});
                    });
                }
    
                this.forceUpdate();
            }).catch((error) => {
                console.error(error);
            });
        }
    }

    private toggleCallout(index: number, newState: boolean) {
        if(typeof index !== 'undefined' && index >= 0) {
            this.calloutStatus[index] = newState;
            this.forceUpdate();
        }
    }

    private getCalculationsFromRawResult(rawResult: any): any {
        let parsedResult: any = null;
        let calculations = {};

        if(typeof rawResult === 'object' || (typeof rawResult === 'string' && isJsonString(rawResult))) {
            parsedResult = JSON.parse(rawResult);

            if(!!parsedResult) {
                calculations['data'] = {};
                calculations['data']['error'] = null;
                calculations['data']['result'] = {};
                calculations['data']['result']['policyCalculations'] = [...parsedResult.policyCalculations];
                calculations['data']['result']['request'] = parsedResult.request;
            }
        }

        let rawRequest: any = null;
        let tempCalculations: any = calculations;
        tempCalculations = tempCalculations && tempCalculations.data ? tempCalculations.data : L('No data');
        if(typeof tempCalculations !== 'string' && tempCalculations.error === null) {
            if(tempCalculations.result && tempCalculations.result.policyCalculations) {
                if(tempCalculations.result.request && rawRequest === null) {
                    rawRequest = tempCalculations.result.request;
                }
                tempCalculations = tempCalculations.result.policyCalculations;
            } else {
                tempCalculations = L('No data');
            }
        } else {
            tempCalculations = tempCalculations.error;
        }

        return tempCalculations;
    }

    renderContent() {
        this.testSet = this.props.payload.model ? this.props.payload.model : this.props.payload;

        let tempCalculations: any = this.getCalculationsFromRawResult(this.testSet.result);
        let parsedPayload: any = null;
        let attributes: any[] = [];

        if(typeof this.testSet.payload === 'object' || (typeof this.testSet.payload === 'string' && isJsonString(this.testSet.payload))) {
            parsedPayload = JSON.parse(this.testSet.payload);
            
            if(parsedPayload && Array.isArray(parsedPayload.data)) {
                attributes = parsedPayload.data.map((data: any, index: number) => {
                    return <p key={index} className={classNames.summaryAttribute}>
                        <span className={classNames.fontBold}>{ data.core_path }:</span> { data.value }
                    </p>;
                });
            }
        }

        const pivotStyles = {
            root: {
                marginLeft: '-8px'
            },
            linkIsSelected: {
                color: myTheme.palette.red,
                selectors: {
                    ':before': {
                        eight: '5px',
                        backgroundColor: additionalTheme.darkerRed
                    }
                }
            }
        };

        const testSetCommentClone: string = this.testSet.comment.split('(').join('\n\n').split(':').join(':\n').split('; ').join('\n\n').split(';)').join('').split(';').join(', ');

        return <Pivot theme={myTheme} styles={pivotStyles}>
            {!!this.testSet.id && 
                <PivotItem headerText={L('Result visualization')}>
                    { typeof this.testSet.result !== 'object' && (typeof this.testSet.result === 'string' && !isJsonString(this.testSet.result)) ? 
                        <p style={{color: 'orange'}}>{L('Result is not a valid JSON.')}</p>
                        :
                        <Stack horizontal styles={stackStyles} tokens={customSpacingStackTokens}>
                            {Array.isArray(tempCalculations) ?
                                tempCalculations.map((calculation: any, index: number) => {
                                    return <div key={index}
                                                className={`${classNames.calculationBox} ${calculation.success ? '' : (calculation.rideryErrors && calculation.rideryErrors.length > 0 ? classNames.calculationBoxWarning : classNames.calculationBoxError)} 
                                                            ${classNames.notSelectedCalculationBox}
                                                            ${this.customInputsForCalculationIndex >= 0 && this.customInputsForCalculationIndex !== index ? classNames.calculationBoxDisabled : ''}`}
                                    >
                                        <p>{calculation.insurerName}</p> 
                                        <h3>{calculation.price} <span>{calculation.currency}</span></h3>
                                        { (calculation.insuranceSum) ?
                                            <h5 style={{marginTop: 0, marginBottom: 10}}>SU: {getNumberWithSpaces(calculation.insuranceSum)} <span>{calculation.currency}</span></h5>
                                            :
                                            <span style={{display: 'block', height: 28}}></span>
                                        }
                                        { (calculation.errors && calculation.errors.length > 0) &&
                                            <Icon onClick={ () => this.toggleCallout(index, true) } iconName={'WarningSolid'} className={`${classNames.calculationBoxIcon} ${classNames.calculationBoxErrorIcon} ${(calculation.rideryErrors && calculation.rideryErrors.length > 0) && classNames.calculationBoxWarningIcon}`}
                                                id={`calculationOffer${index}`} title={L('Errors have occurred')} /> }
                                        
                                        {(this.calloutStatus[index] && this.calloutStatus[index] === true) && (
                                            <Callout
                                                className={classNames.callout}
                                                gapSpace={0}
                                                target={`#calculationOffer${index}`}
                                                onDismiss={ () => this.toggleCallout(index, false) }
                                                setInitialFocus
                                            >
                                                <Text className={classNames.calloutText} block variant="small">
                                                    { calculation.rideryErrors && calculation.rideryErrors.length > 0 ?
                                                        calculation.rideryErrors.map((rideryError: any) => {
                                                            return `${L(rideryError)} \n\r\n\r`
                                                        }) :
                                                        (calculation.errors && calculation.errors.length > 0 ?
                                                            calculation.errors.map((error: any) => {
                                                                return `${L(error)} \n\r\n\r`
                                                            }) : '')
                                                    }
                                                </Text>
                                            </Callout>
                                        )}
                                    </div>;
                                })
                            : <p>{tempCalculations}</p>}
                        </Stack>
                    }
                </PivotItem>
            }

            <PivotItem headerText={L('General')} key={'General'}>
                {this.renderElement(new ContentViewModelProperty('name', L('Name'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, rows: 3}), [], {'name': this.testSet.name})}Add commentMore actions
                {this.renderElement(new ContentViewModelProperty('isSuccess', L('Is success'), Controls.Text, false, [], true, {isDataLoaded: this.isDataLoaded}), [], {'isSuccess': this.testSet.isSuccess ? L('Yes') : L('No')})}
                
                <LabeledTextField key={'comment'} label={L('Comment')} rows={10} multiline={true} disabled={true} isDataLoaded={true} value={testSetCommentClone} Add commentMore actions
                                    customInputStyles={{height: '220px', whiteSpace: 'pre-line'}}
                                    onChange={(event: React.FormEvent<HTMLInputElement | HTMLTextAreaElement>, value?: string) => {}} 
                />
            </PivotItem>
            
            {!!this.testSet.id &&
                <PivotItem headerText={L('JSON payload')}>
                    <div className={classNames.summaryAttributesWrapper}>
                        { attributes.length > 0 ? attributes : L('There are no details for this product') }
                    </div>

                    <div className={contentStyles.container}>
                        { typeof this.testSet.payload === 'object' ? 
                            <div className={contentStyles.body} 
                                dangerouslySetInnerHTML={{__html: jsonViewer(this.testSet.payload, true)}}>
                            </div> 
                            :
                            ( typeof this.testSet.payload === 'string' && isJsonString(this.testSet.payload) ?
                                <div className={contentStyles.body} 
                                    dangerouslySetInnerHTML={{__html: jsonViewer(JSON.parse(this.testSet.payload), true)}}>
                                </div> 
                                :
                                <p style={{color: 'orange'}}>{L('Payload is not a valid JSON.')}</p>
                            )
                        }
                    </div>
                </PivotItem>
            }

            {!!this.testSet.id &&
                <PivotItem headerText={L('JSON response')}>
                    <div className={contentStyles.container}>
                        { typeof this.testSet.result === 'object' ? 
                            <div className={contentStyles.body} 
                                dangerouslySetInnerHTML={{__html: jsonViewer(this.testSet.result, true)}}>
                            </div> 
                            :
                            ( typeof this.testSet.result === 'string' && isJsonString(this.testSet.result) ?
                                <div className={contentStyles.body} 
                                    dangerouslySetInnerHTML={{__html: jsonViewer(JSON.parse(this.testSet.result), true)}}>
                                </div> 
                                :
                                <p style={{color: 'orange'}}>{L('Result is not a valid JSON.')}</p>
                            )
                        }
                    </div>
                </PivotItem>
            }

            {!!this.testSet.id &&
                <PivotItem headerText={L('Error details')}>
                    { this.testSet.errorDetails && this.testSet.errorDetails.length > 0 ? 
                        <p style={{color: 'red', whiteSpace: 'pre'}}>{this.testSet.errorDetails.split(';').join('\n\r\n\r')}</p>
                        :
                        <p style={{color: 'green'}}>{L('There is no error.')}</p>
                    }
                </PivotItem>
            }

            {(!!this.testSet.id && this.jsonResponseHistory.length > 0) &&
                <PivotItem headerText={L('Result visualization history')}>
                    {this.jsonResponseHistory.filter((historyItem) => {
                        if (historyItem.result === '') {
                            return false; // skip
                        }
                        return true;
                    }).map((historyItem: any, i: number) => {
                        let tempCalculations: any = this.getCalculationsFromRawResult(historyItem.result);

                        return <div className={`${classNames.historyResultRowWrapper} ${this.historyResultListShowRow === i && classNames.activeHistoryResultRowWrapper}`}>
                            <p className={classNames.historyResultDateLabel} onClick={() => { 
                                if(this.historyResultListShowRow === i) {
                                    this.historyResultListShowRow = -1; 
                                } else {
                                    this.historyResultListShowRow = i; 
                                }
                                this.forceUpdate();
                            }}>{historyItem.date}</p>

                            <div className={classNames.historyResultRow} style={{display: this.historyResultListShowRow === i ? 'block' : 'none'}}>
                                { historyItem.result === null || (typeof historyItem.result !== 'object' && typeof historyItem.result === 'string' && !isJsonString(historyItem.result)) ? 
                                    <p style={{color: 'orange'}}>{L('Result is not a valid JSON.')}</p>
                                    :
                                    <Stack horizontal styles={stackStyles} tokens={customSpacingStackTokens}>
                                        {Array.isArray(tempCalculations) ?
                                            tempCalculations.map((calculation: any, index: number) => {
                                                let tempCalloutIndex = (tempCalculations.length + (i + 1)) * (index + 10);
                                                let parsedJsonOutput = calculation && calculation.jsonOutput ? JSON.parse(calculation.jsonOutput) : undefined;
                                                
                                                return <div key={index}
                                                            className={`${classNames.calculationBox} ${calculation.success ? '' : (calculation.rideryErrors && calculation.rideryErrors.length > 0 ? classNames.calculationBoxWarning : classNames.calculationBoxError)} 
                                                                        ${classNames.notSelectedCalculationBox}
                                                                        ${this.customInputsForCalculationIndex >= 0 && this.customInputsForCalculationIndex !== index ? classNames.calculationBoxDisabled : ''}`}
                                                >
                                                    <p>{calculation.insurerName}</p> 
                                                    <h3>{calculation.price} <span>{calculation.currency}</span></h3>
                                                    { (parsedJsonOutput && parsedJsonOutput.offer && parsedJsonOutput.offer.insuredObject && parsedJsonOutput.offer.insuredObject[0] && parsedJsonOutput.offer.insuredObject[0].insuranceSum) ?
                                                        <h5 style={{marginTop: 0, marginBottom: 10}}>SU: {getNumberWithSpaces(parsedJsonOutput.offer.insuredObject[0].insuranceSum)} <span>{calculation.currency}</span></h5>
                                                        :
                                                        <span style={{display: 'block', height: 28}}></span>
                                                    }
                                                    { (calculation.errors && calculation.errors.length > 0) &&
                                                        <Icon onClick={ () => this.toggleCallout(tempCalloutIndex, true) } iconName={'WarningSolid'} className={`${classNames.calculationBoxIcon} ${classNames.calculationBoxErrorIcon} ${(calculation.rideryErrors && calculation.rideryErrors.length > 0) && classNames.calculationBoxWarningIcon}`}
                                                            id={`calculationOffer${tempCalloutIndex}`} title={L('Errors have occurred')} /> }
                                                    
                                                    {(this.calloutStatus[tempCalloutIndex] && this.calloutStatus[tempCalloutIndex] === true) && (
                                                        <Callout
                                                            className={classNames.callout}
                                                            gapSpace={0}
                                                            target={`#calculationOffer${tempCalloutIndex}`}
                                                            onDismiss={ () => this.toggleCallout(tempCalloutIndex, false) }
                                                            setInitialFocus
                                                        >
                                                            <Text className={classNames.calloutText} block variant="small">
                                                                { calculation.rideryErrors && calculation.rideryErrors.length > 0 ?
                                                                    calculation.rideryErrors.map((rideryError: any) => {
                                                                        return `${L(rideryError)} \n\r\n\r`
                                                                    }) :
                                                                    (calculation.errors && calculation.errors.length > 0 ?
                                                                        calculation.errors.map((error: any) => {
                                                                            return `${L(error)} \n\r\n\r`
                                                                        }) : '')
                                                                }
                                                            </Text>
                                                        </Callout>
                                                    )}
                                                </div>;
                                            })
                                        : <p>{tempCalculations}</p>}
                                    </Stack>
                                }
                            </div>
                        </div>;
                    })}
                </PivotItem>
            }
        </Pivot>
    }
}