<!DOCTYPE html>
<html lang="en">
    <head>
        <title></title>

        <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta charset="utf-8" />
        <meta name="locale" content="en-US" />
        <meta name="ROBOTS" content="NONE, NOARCHIVE" />
        <meta name="GOOGLEBOT" content="NOARCHIVE" />
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=yes"
        />
        <style>
            @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");
            :root {
                --black: #000;
                --white: #fff;
                --blue: #097ffc;
                --blue-border: #097ffb;
                --gray: #e7eaf0;
                --color-google: #eb4335;
                --padding-top: 25px;
            }
            *,
            *:after,
            *:before {
                box-sizing: border-box;
            }
            html {
                width: 100%;
                height: 100%;
            }
            body {
                margin: 0;
                padding: 0;
                font-family: "Roboto", sans-serif;
                display: flex;
                justify-content: center;
                min-height: 100vh;
            }
            input,
            button {
                font-family: inherit;
            }
            #verifying_blurb {
                display: none;
            }
            #api {
                min-height: 100%;
                width: 100%;
                max-width: 325px;
                padding-bottom: 30px;
                position: relative;
            }
            #cancel {
                position: absolute;
                top: var(--padding-top);
                background: none;
                border: none;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-self: center;
            }
            .backArrow {
                width: 20px;
                height: 15px;
                color: var(--black);
            }
        </style>
        <style>
            ul {
                list-style: none;
                padding: 0;
            }
            .helpLink {
                display: none;
            }
            .intro {
                margin-top: 15%;
                width: 85%;
            }
            .intro p {
                font-size: 28px;
                font-weight: 700;
                text-align: left;
                color: var(--blue);
                margin: 0;
            }
            .attrEntry {
                display: flex;
                flex-direction: column;
                margin: 15px 0;
            }
            label {
                color: var(--blue);
                font-size: 15px;
                font-weight: 500;
            }
            input {
                width: 100%;
                border: none;
                font-size: 17px;
                font-weight: 500;
                border-bottom: 2px solid var(--gray);
                outline: none;
                padding: 5px 0;
                transition: 0.2s linear;
                border-radius: 0;
                -webkit-appearance: none;
                -webkit-border-radius: 0;
            }
            input:focus {
                border-bottom: 3px solid var(--blue-border);
            }
            #emailVerificationControl_but_send_code {
                cursor: pointer;
                border: none;
                outline: none;
                background: var(--blue);
                padding: 7px 0;
                border-radius: 50px;
                color: var(--white);
                font-size: 15px;
                font-weight: normal;
                letter-spacing: 1px;
                margin: 15px 42px;
            }
            #emailVerificationControl_but_verify_code {
                cursor: pointer;
                border: none;
                outline: none;
                background: var(--blue);
                padding: 9px 0;
                border-radius: 50px;
                color: var(--white);
                font-size: 15px;
                font-weight: normal;
                letter-spacing: 1px;
                margin: 6px 90px;
            }
            #emailVerificationControl_but_send_new_code {
                cursor: pointer;
                border: none;
                outline: none;
                background: var(--white);
                padding: 9px 0;
                border-radius: 50px;
                color: var(--blue);
                font-size: 14px;
                font-weight: normal;
                letter-spacing: 1px;
                margin: 6px 90px;
                text-decoration: underline;
            }
            #emailVerificationControl_but_change_claims {
                cursor: pointer;
                width: 1px;
                height: 1px;
                border: none;
                outline: none;
                background: var(--white);
                padding: 0;
                color: var(--white);
                font-size: 1px;
                margin: 0;
            }


            #continue {
                cursor: pointer;
                width: 100%;
                border: none;
                outline: none;
                background: var(--blue);
                padding: 15px 0;
                border-radius: 50px;
                color: var(--white);
                font-size: 19px;
                font-weight: 500;
                letter-spacing: 1px;
                margin: 5px 0;
                position: absolute;
            }
            .buttons {
                display: flex;
                flex-direction: column;
            }
            .verificationControlContent {
                margin-top: 30px;
            }
        </style>
    </head>
    <body>
        <div id="api" data-name="SelfAsserted" role="main"></div>

        <script>
            "use strict";
            $(document).ready(function () {
                if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
                    let t = document.createElement("style");
                    t.appendChild(
                        document.createTextNode(
                            "@-ms-viewport{width:auto!important}"
                        )
                    ),
                        t.appendChild(
                            document.createTextNode(
                                "@-ms-viewport{height:auto!important}"
                            )
                        ),
                        document.getElementsByTagName("head")[0].appendChild(t);
                }
                if (navigator.userAgent.match(/MSIE 10/i)) {
                    let e = $("#footer_links_container");
                    $(e).css("padding-top", "100px");
                }
                let o,
                    i = $("#background_background_image"),
                    n = function () {
                        (document.body.style.overflow = "hidden"),
                            ($(window).width() - 500) / $(window).height() < o
                                ? (i.height($(window).height()),
                                    i.width("auto"))
                                : (i.width($(window).width() - 500),
                                    i.height("auto")),
                            (document.body.style.overflow = "");
                    };
                $("<img>")
                    .attr("src", i.attr("src"))
                    .on("load", function () {
                        (o = this.width / this.height), n();
                    }),
                    $(window).resize(function () {
                        n();
                    }),
                    "undefined" != typeof $("#MicrosoftAccountExchange") &&
                        $("#MicrosoftAccountExchange").text("Microsoft"),
                    $("*").removeAttr("placeholder");
            });

            const cancelButton = document.querySelector("#cancel");

            if (cancelButton) {
                cancelButton.innerHTML = "";

                const cancelButtonSVG = `<svg xmlns="http://www.w3.org/2000/svg" width="11.121"     height="19.414" viewBox="0 0 11.121 19.414">
                <path id="Path_229" data-name="Path 229" d="M0,9,9,0l9,9" transform="translate(1.414 18.707) rotate(-90)" fill="none" stroke="#000" stroke-width="2"/></svg>`;

                cancelButton.innerHTML = cancelButtonSVG;
            }
        </script>
    </body>
</html>
