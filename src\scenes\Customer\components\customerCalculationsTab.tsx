import React from "react";
import { mergeStyleSets, MessageBar, MessageBarType, Spinner, SpinnerSize, Stack } from "@fluentui/react";
import { L } from "../../../lib/abpUtility";
import { myTheme } from "../../../styles/theme";

const classNames = mergeStyleSets({
    smallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '10px !important',
        marginTop: '22px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    loadSpinner: {
        display: 'inline-flex',
        marginLeft: '45px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
            }
        }
    },
    fileLabelSmallLoadSpinner: {
        display: 'inline-flex',
        marginLeft: '30px !important',
        marginTop: '0',
        selectors: {
            '& .ms-Spinner-label': {
                color: myTheme.palette.themePrimary,
                textDecoration: 'none',
            }
        },
    },
    messageBar: {
        width: 'fit-content',
        marginLeft: '25px !important',
        marginTop: '20px',
        selectors: {
            '& .ms-MessageBar-innerText': {
                selectors: {
                    '& span': {
                        whiteSpace: 'pre-line',
                    }
                }
            }
        }
    },
    fileListLabel: {
        fontSize: 16,
        fontWeight: 'bold',
        textDecoration: 'underline',
    },
});

export interface ICustomerCalculationsTabProps {
    asyncActionInProgress: boolean;
    clientFilePasswordError: string;
    policyCalculationAttachedFiles: JSX.Element[];
    onMessageBarDismiss: () => void;
}

export class CustomerCalculationsTab extends React.Component<ICustomerCalculationsTabProps> {
    render() {
        const {policyCalculationAttachedFiles, clientFilePasswordError, asyncActionInProgress} = this.props;

        return <Stack>
            {!!clientFilePasswordError &&
                <MessageBar messageBarType={MessageBarType.error} isMultiline={false} className={`${classNames.messageBar}`}
                    onDismiss={() => { this.props.onMessageBarDismiss(); }}
                >
                    {clientFilePasswordError}
                </MessageBar>
            }

            <ul style={{padding: 0}}>
                <p className={classNames.fileListLabel}>
                    {L('Files sent to the client')}:
                    {asyncActionInProgress && (
                        <Spinner className={`${classNames.fileLabelSmallLoadSpinner}`} size={SpinnerSize.small} ariaLive="assertive" labelPosition="right" />
                    )}
                </p>
                {
                    policyCalculationAttachedFiles.length > 0 ?
                        policyCalculationAttachedFiles.map((element: any) => (
                            element.element
                        ))
                        : 
                        L('There are no files to display')
                }
            </ul>
        </Stack>;
    }
}