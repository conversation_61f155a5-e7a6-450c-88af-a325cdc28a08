import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import ProductStore from '../../stores/productStore';
import { ProductTable } from './components/productTable';
import {mergeStyleSets} from "@fluentui/react";
import {L} from "../../lib/abpUtility";
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  },
});

export interface IProps {
  searchStore: SearchStore;
  productStore: ProductStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.ProductStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  public render() {
    let items = this.props.productStore.dataSet ? this.props.productStore.dataSet.items : [];

    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Product List')}</h2>
        </div>
        <ProductTable
          searchText={this.props.searchStore.searchText}
          items={items}
          store={this.props.productStore}
          history={this.props.history}
        />
      </>
    );
  }
}

export default Index;