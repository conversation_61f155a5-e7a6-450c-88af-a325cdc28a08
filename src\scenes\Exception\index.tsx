import * as React from 'react';

import error401 from '../../images/401.png';
import error404 from '../../images/404.png';
import error500 from '../../images/500.png';
import { mergeStyleSets, PrimaryButton } from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import {additionalTheme, myTheme} from '../../styles/theme';

const classNames = mergeStyleSets({
  errorAvatar: {
    height: '360px',
    width: '430px',
  },

  errorTitle: {
    color: additionalTheme.grey,
    fontSize: '72px',
    fontWeight: 600,
    lineHeight: '72px',
    marginBottom: '24px',
  },

  errorDescription: {
    color: 'rgba(0, 0, 0, 0.45)',
    fontSize: '20px',
    lineHeight: '28px',
    marginBottom: '16px',
  }
})
class Exception extends React.Component<any, any> {
  public render() {
    const exception = [
      { errorCode: '404', errorImg: error404, errorDescription: 'Sorry, the page you visited does not exist' },
      {
        errorCode: '401',
        errorImg: error401,
        errorDescription: 'Sorry, you dont have access to this page',
      },
      { errorCode: '500', errorImg: error500, errorDescription: 'Sorry, the server is reporting an error' },
    ];

    let params = new URLSearchParams(this.props.match.params.type);
    const type = params.get('type');
    let error = exception.find(x => x.errorCode === type);

    if (error == null) {
      error = exception[0];
    }

    return (<div style={{ marginTop: 150 }}>
      <div>
        <img alt="ErrorImage" className={classNames.errorAvatar} src={error!.errorImg} />
      </div>
      <div
        style={{ marginTop: 75 }}
      >
        <div
        >
          <h1 className={classNames.errorTitle}>{error!.errorCode}</h1>
        </div>
        <div
        >
          <h5 className={classNames.errorDescription}> {error!.errorDescription}</h5>
        </div>
        <div
        >
          <PrimaryButton theme={myTheme} href='/' text={L('Back to Home')} />
        </div>
      </div>
      <div />
    </div>);

  }
}

export default Exception;
