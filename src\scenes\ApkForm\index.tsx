import { observer } from 'mobx-react';

import AppComponentBase from '../../components/AppComponentBase';
import { IStateBase } from '../BaseComponents/IStateBase';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import { ApkContentView } from './components/apkContentView';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '0 0 30px 0',
		margin: '0 0 0 5px'
	},
});

export interface IProps {
	match: any,
	location: any,
}

@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private query = new URLSearchParams(this.props.location.search);
	private entityId = this.query.get('entityId');
	private entityType = this.query.get('entityType');

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<ApkContentView renderFooter={{show: true}} policyCalculation={{ payloadType: this.entityType, payloadId: this.entityId }} />
			</FocusZone>
		);
	}
}

export default Index;