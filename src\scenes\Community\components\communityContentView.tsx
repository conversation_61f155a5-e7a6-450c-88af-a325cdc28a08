import { Pivot, PivotItem } from '@fluentui/react';
import { L } from '../../../lib/abpUtility';
import {additionalTheme, myTheme} from '../../../styles/theme';
import { GenericContentView } from '../../BaseComponents/genericContentView';
import { ContentViewModelProperty } from '../../BaseComponents/contentViewBase';
import { Controls } from '../../BaseComponents/controls';
import { CommunityDto } from '../../../services/community/dto/communityDto';
import { defaultCommunity } from '../../../stores/communityStore';

export class CommunityContentView extends GenericContentView {
    private community: CommunityDto = defaultCommunity;

    componentDidMount() {
        this.checkIfDataIsLoaded("community");
    }

    renderContent() {
        this.community = this.props.payload.model ? this.props.payload.model : this.props.payload;

        const pivotStyles = {
            root: {
              marginLeft: '-8px'
            },
            linkIsSelected: {
              color: myTheme.palette.red,
              selectors: {
                ':before': {
                  height: '5px',
                  backgroundColor: additionalTheme.darkerRed
                }
              }
            }
          };

        return <Pivot theme={myTheme} styles={pivotStyles}>
            <PivotItem headerText={L('General')} key={'General'}>
                {this.renderElement(new ContentViewModelProperty('numberOfUsers', L('Number of users'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: 'number'}), [], {'numberOfUsers': this.community.numberOfUsers})}
                {this.renderElement(new ContentViewModelProperty('numberOfPurchasedPolicies', L('Number of purchased policies'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: 'number'}), [], {'numberOfPurchasedPolicies': this.community.numberOfPurchasedPolicies})}
                {this.renderElement(new ContentViewModelProperty('numberOfClaims', L('Number of claims'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: 'number'}), [], {'numberOfClaims': this.community.numberOfClaims})}
                {this.renderElement(new ContentViewModelProperty('averageTimeOfConcludingPolicyAgreement', L('Average time of concluding policy agreement'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: 'number'}), [], {'averageTimeOfConcludingPolicyAgreement': this.community.averageTimeOfConcludingPolicyAgreement})}
                {this.renderElement(new ContentViewModelProperty('appRank', L('App rank'), Controls.Text, false, [], false, {isDataLoaded: this.isDataLoaded, textType: 'number'}), [], {'appRank': this.community.appRank})}
            </PivotItem>
        </Pivot>
    }
}