import { GenericPanel } from '../../Fluent/base/genericPanel';
import { L } from '../../../lib/abpUtility';
import { ClaimDto } from '../../../services/claim/dto/claimDto';
import { AgentClaimHistoryContentView } from '../../AgentClaim/components/agentClaimHistoryContentView';

export class AgentClaimHistoryPanel extends GenericPanel {
    getPanelTitle(): string {
        return L("Claim History");
    }

    renderContent() {
        return <AgentClaimHistoryContentView store={this.props.store} createOrUpdate={this.props.createOrUpdate} payload={ this.props.payload as ClaimDto } customData={this.props.customData} />;
    }
}