import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import { CustomerTable } from './components/customerTable';
import ClientStore from '../../stores/clientStore';
import {mergeStyleSets, Pivot, PivotItem, SelectionMode} from '@fluentui/react';
import {additionalTheme, myTheme} from '../../styles/theme';
import { L } from '../../lib/abpUtility';
import { ArchivedCustomerTable } from './components/archivedCustomerTable';
import clientService from '../../services/client/clientService';
import { CrudConsts } from '../../stores/crudStoreBase';

const classNames = mergeStyleSets( {
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  },
  customers: {
    root: {
      '& ms-FocusZone': {
        marginBottom: '30px',
      },

      '& span': {
        fontWeight: 600,
        transition: 'color .2s ease-out',
      },

      '& span ms-button': {
        color:myTheme.palette.white
      },

      '& span .ms-Pivot-text:hover': {
        color: myTheme.palette.red,
      },

      '& .ms-DetailsHeader-cellTitle .cellTitle:hover': {
        color: myTheme.palette.black,
      }
    }
  }
});

export interface IProps {
  searchStore: SearchStore;
  clientStore: ClientStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.ClientStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;
  private archivedItems: any[] = [];
  private getItemsProceded: boolean = false;

  async componentDidMount() {    
    if(!this.props.clientStore!.dataSet || this.props.clientStore!.dataSet.totalCount <= 0) {
      await this.props.clientStore.getAll({...this.props.clientStore.defaultRequest, maxResultCount: CrudConsts.LAZY_LOAD_PAGE_SIZE});
    }

    clientService.getAllDeletedInLast30Days().then((response: any) => {
      if(response && response.items) {
        this.archivedItems = response.items;
      }
      this.getItemsProceded = true;
      this.forceUpdate();
    });
  }

  private filterCustomers(value: any) {
    return !value.Deleted;
  }

  // private sortItems(a: any, b: any): number {
  //   if (a.name < b.name)
  //     return -1;
  //   if (a.name > b.name)
  //     return 1;
  //   return 0;
  // }

  private async refreshItems() {
    await this.props.clientStore.getAllLazy().then(() => {
      this.forceUpdate();
    });
  }

  private async refreshArchivedItems() {
    await clientService.getAllDeletedInLast30Days().then((response: any) => {
      if(response && response.items) {
        this.archivedItems = response.items;
      }
      this.getItemsProceded = true;
      this.forceUpdate();
    });
  }

  public render() {
    let items: any[] = [];

    if(this.props.clientStore.dataSet) {
      items = [...this.props.clientStore.dataSet.items].filter(this.filterCustomers);
      
      // let concatColumn: any[] = [];
      // items.forEach((item: any, index: number) => {
      //   if(item.clientType === ClientTypeEnum.Individual) {
      //     concatColumn.push({index: index, name: `${item.user.name} ${item.user.surname}`});
      //   } else {
      //     concatColumn.push({index: index, name: `${item.company}`});
      //   }
      // });
      // concatColumn.sort(this.sortItems);

      // let sortedItems: any[] = [];
      // concatColumn.forEach((col: any) => {
      //   sortedItems.push(items[col.index]);
      // });
      // items = sortedItems;
    }
    const pivotStyles = {
      root: {
        marginLeft: '-8px'
      },
      linkIsSelected: {
        color: myTheme.palette.red,
        selectors: {
          ':before': {
            height: '5px',
            backgroundColor: additionalTheme.darkerRed,
          }
        }
      }
    };

    return (
      <>
        <div className={classNames.titleContainer}>
          <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Customer List')}</h2>
        </div>
        
        <Pivot className={classNames.customers} styles={pivotStyles} theme={myTheme}>
          <PivotItem headerText={L('Customers')}>
            <CustomerTable
              searchText={this.props.searchStore.searchText}
              items={items}
              store={this.props.clientStore}
              history={this.props.history}
              refreshItems={() => this.refreshItems()}
              scrollablePanelMarginTop={210}
            />
          </PivotItem>

          <PivotItem headerText={L('Archived customers')}>
            <ArchivedCustomerTable
              searchText={this.props.searchStore.searchText}
              items={this.archivedItems}
              store={this.props.clientStore}
              history={this.props.history}
              refreshItems={() => this.refreshArchivedItems()}
              scrollablePanelMarginTop={210}
              selectionMode={SelectionMode.single}
              customData={{
                useOnlyRefreshItems: true,
                disableShimmer: this.getItemsProceded,
              }}
            />
          </PivotItem>
        </Pivot>
      </>
    );
  }
}

export default Index;