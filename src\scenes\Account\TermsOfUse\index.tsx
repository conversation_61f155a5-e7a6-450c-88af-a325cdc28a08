import * as React from 'react';
import { PatternTypeEnum } from '../../../services/pattern/patternTypeEnums';
import { getPatternText } from '../../../utils/utils';
import TextPage, { TextType } from '../textPage';

export interface IProps {
}
export interface IState {
  text: string
}

export class TermsOfUse extends React.Component<IProps, IState>  {

  constructor(props: any){
    super(props);
    this.state = {
      text : ""
    }
  }

  async componentDidMount() {
    let textFromPattern = await getPatternText(PatternTypeEnum.TermsAndConditions);
    this.setState({ text: textFromPattern })
  }

  render() {
    return <TextPage type={TextType.TermsOfUse} text={this.state.text} />
  }
}

export default TermsOfUse;
