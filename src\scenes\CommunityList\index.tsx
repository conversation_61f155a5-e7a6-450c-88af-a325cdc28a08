import { inject, observer } from 'mobx-react';
import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import '../BaseComponents/index.less';
import { IStateBase } from '../BaseComponents/IStateBase';
import SearchStore from '../../stores/searchStore';
import { CommunityTable } from './components/communityTable';
import CommunityStore from '../../stores/communityStore';
import {mergeStyleSets, SelectionMode} from '@fluentui/react';
import { L } from '../../lib/abpUtility';
import { isConfigForAG, isConfigForProduction } from '../../utils/authUtils';
import {additionalTheme} from "../../styles/theme";

const classNames = mergeStyleSets({
  titleContainer: {
    textTransform: "uppercase",
    display: "flex",
    alignItems: "center",
  }
})

export interface IProps {
  searchStore: SearchStore;
  communityStore: CommunityStore;
  history: any;
}

@inject(Stores.SearchStore)
@inject(Stores.CommunityStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
  formRef: any;

  componentDidMount() {
		if(isConfigForAG() || isConfigForProduction()) {
			window.location.href = '/exception';
		}
	}

  public render() {
    let items = this.props.communityStore.dataSet ? this.props.communityStore.dataSet.items : [];
    
    if(isConfigForAG() || isConfigForProduction()) {
      return <>
        <p style={{marginTop: 50, fontWeight: 'bold', fontSize: '1.5rem'}}>{L('You do not have access to this page.')}</p>
      </>;
    } else {
      return (
        <>
          <div className={classNames.titleContainer}>
            <h2 style={{fontSize: '26px', color: additionalTheme.grey}}>{L('Community List')}</h2>
          </div>
          <CommunityTable
            searchText={this.props.searchStore.searchText}
            items={items}
            store={this.props.communityStore}
            history={this.props.history}
            selectionMode={SelectionMode.none}
          />
        </>
      );
    }
  }
}

export default Index;