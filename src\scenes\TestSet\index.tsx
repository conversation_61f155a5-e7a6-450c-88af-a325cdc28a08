import { inject, observer } from 'mobx-react';

import AppComponentBase from '../../components/AppComponentBase';
import Stores from '../../stores/storeIdentifier';
import { IStateBase } from '../BaseComponents/IStateBase';
import { TestSetDto } from '../../services/testSet/dto/testSetDto';
import { TestSetContentView } from './components/testSetContentView';
import { mergeStyleSets, FocusZone } from '@fluentui/react';
import TestSetStore from '../../stores/testSetStore';
import testSetService from '../../services/testSet/testSetService';

const classNames = mergeStyleSets({
	subpageContentWrapper: {
		display: 'block',
		padding: '30px 0'
	},
});

export interface IProps {
	testSetStore: TestSetStore;
	match: any
}

@inject(Stores.TestSetStore)
@observer
class Index extends AppComponentBase<IProps, IStateBase> {
	private testSetId: string = this.props.match.params.id;

	async componentDidMount() {
		this.props.testSetStore.model = await testSetService.get({ id: this.testSetId } as TestSetDto);;
		this.forceUpdate();
	}

	public render() {
		return (
			<FocusZone as="div" className={classNames.subpageContentWrapper}>
				<TestSetContentView store={this.props.testSetStore} payload={ this.props.testSetStore.model as TestSetDto } renderFooter={{show: true}} />
			</FocusZone>
		);
	}
}

export default Index;