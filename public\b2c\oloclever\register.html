<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=yes"
    />
    <title></title>
    <style>
        @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");

        :root {
            --black: #000;
            --white: #fff;
            --blue: #097ffc;
            --blue-border: #097ffb;
            --gray: #e7eaf0;
            --color-google: #eb4335;
            --padding-top: 25px;
        }

        *,
        *:after,
        *:before {
            box-sizing: border-box;
        }

        html {
            width: 100%;
            height: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: "Roboto", sans-serif;
            display: flex;
            justify-content: center;
            min-height: 100vh;
        }

        input[type=text], input[type=email], input[type=password],
        button {
            font-family: inherit;
        }

        #verifying_blurb {
            display: none;
        }

        #api {
            min-height: 100%;
            width: 100%;
            max-width: 325px;
            padding-bottom: 30px;
            position: relative;
        }

        #cancel {
            position: absolute;
            top: var(--padding-top);
            background: none;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-self: center;
        }

        .backArrow {
            width: 20px;
            height: 15px;
            color: var(--black);
        }
    </style>
    <style>
        #api {
            display: flex;
            flex-direction: column;
        }

        #attributeVerification {
            margin-top: 15%;
            display: flex;
            flex-direction: column;
            flex: 1;
        }

        #attributeList {
            flex: 1;
        }

        .intro {
            position: absolute;
            top: var(--padding-top);
            left: 50%;
            margin-right: 50%;
            transform: translateX(-50%);
        }

        .intro p {
            margin: 0;
            font-size: 17px;
            text-align: center;
            font-weight: 500;
        }

        ul {
            list-style: none;
            padding: 0;
        }
        #emailVerificationControl_but_send_code {
            cursor: pointer;
            border: none;
            outline: none;
            background: var(--blue);
            padding: 9px 0;
            border-radius: 50px;
            color: var(--white);
            font-size: 15px;
            font-weight: normal;
            letter-spacing: 1px;
            margin: 15px 42px;
        }
        #emailVerificationControl_but_verify_code {
            cursor: pointer;
            border: none;
            outline: none;
            background: var(--blue);
            padding: 9px 0;
            border-radius: 50px;
            color: var(--white);
            font-size: 15px;
            font-weight: normal;
            letter-spacing: 1px;
            margin: 6px 90px;
        }
        #emailVerificationControl_but_send_new_code {
            cursor: pointer;
            border: none;
            outline: none;
            background: var(--white);
            padding: 9px 0;
            border-radius: 50px;
            color: var(--blue);
            font-size: 14px;
            font-weight: normal;
            letter-spacing: 1px;
            margin: 6px 90px;
            text-decoration: underline;
        }
        #emailVerificationControl_but_change_claims {
            cursor: pointer;
            width: 1px;
            height: 1px;
            border: none;
            outline: none;
            background: var(--white);
            padding: 0;
            color: var(--white);
            font-size: 1px;
            margin: 0;
        }


        #continue {
            cursor: pointer;
            width: 100%;
            border: none;
            outline: none;
            background: var(--blue);
            padding: 15px 0;
            border-radius: 50px;
            color: var(--white);
            font-size: 19px;
            font-weight: 500;
            letter-spacing: 1px;
            margin-bottom: 700px;
        }

        .verifyCode {
            font-size: 15px;
        }

        .helpLink {
            display: none;
        }

        .buttons {
            display: flex;
            flex-direction: column;
        }

        .attrEntry {
            display: flex;
            flex-direction: column;
            margin: 10px 0;
        }

        ul {
            display: flex;
            flex-direction: column;
        }

        .extension_AcceptPolicy_li, .extension_Regulamin_li {
            order: 10;
        }

        .extension_PersonalDataProcessing_li {
            order: 12;
        }

        .extension_AcceptPolicy_li > .attrEntry, .extension_Regulamin_li > .attrEntry {
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
            float: left;
        }

        .extension_PersonalDataProcessing_li > .attrEntry {
            display: flex;
            flex-direction: row-reverse;
            justify-content: flex-start;
            align-items: flex-start;
            float: left;
        }

        .extension_PersonalDataProcessing_li > .attrEntry > input {
            float: left;
        }

        .extension_AcceptPolicy_li > .attrEntry > .error, .extension_Regulamin_li > .attrEntry > .error, .extension_PersonalDataProcessing_li > .attrEntry > .error {
            order: 12;
        }

        label {
            color: var(--blue);
            font-size: 15px;
            font-weight: 500;
            transition: transform 200ms, color 200ms;
        }

        input[type=text], input[type=email], input[type=password] {
            width: 100%;
            border: none;
            font-size: 17px;
            font-weight: 500;
            outline: none;
            padding: 5px 0;
            border-bottom: 2px solid var(--gray);
            transition: 0.2s linear;
            border-radius: 0;
            -webkit-appearance: none;
            -webkit-border-radius: 0;
        }

        input[type=text]:focus, input[type=email]:focus, input[type=password]:focus {
            border-bottom: 3px solid var(--blue-border);
        }
        .input:focus ~ label,  /* Input has focus */
        .input:not(:placeholder-shown) ~ label  /* Input has a value */  {
        transform: translateY(-30px) translateX(10px) scale(0.75);
        }

        a {
            color: var(--blue);
            text-decoration: underline;
        }

        input[type=checkbox] {
            margin-right: 10px;
        }

        p {
            margin: 5px 0;
        }
    </style>
</head>
<body>
<div id="api" data-name="SelfAsserted" role="main"></div>

<script>
    "use strict";
    $(document).ready(function () {
        if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
            var t = document.createElement("style");
            t.appendChild(
                document.createTextNode(
                    "@-ms-viewport{width:auto!important}"
                )
            ),
                t.appendChild(
                    document.createTextNode(
                        "@-ms-viewport{height:auto!important}"
                    )
                ),
                document.getElementsByTagName("head")[0].appendChild(t);
        }
        if (navigator.userAgent.match(/MSIE 10/i)) {
            var e = $("#footer_links_container");
            $(e).css("padding-top", "100px");
        }
        var o,
            i = $("#background_background_image"),
            n = function () {
                (document.body.style.overflow = "hidden"),
                    ($(window).width() - 500) / $(window).height() < o
                        ? (i.height($(window).height()),
                            i.width("auto"))
                        : (i.width($(window).width() - 500),
                            i.height("auto")),
                    (document.body.style.overflow = "");
            };
        $("<img>")
            .attr("src", i.attr("src"))
            .on("load", function () {
                (o = this.width / this.height), n();
            }),
            $(window).resize(function () {
                n();
            }),
        "undefined" != typeof $("#MicrosoftAccountExchange") &&
        $("#MicrosoftAccountExchange").text("Microsoft"),
            $("*").removeAttr("placeholder");

        document.getElementById("extension_AcceptPolicy_label").innerHTML = "Zapoznałem się i akceptuję <a href='https://top.a-soft.pl/account/privacy-policy' target='_blank'>Politykę prywatności</a> korzystania z aplikacji OLO Clever oraz usług świadczonych przez przez Grupę Top Ubezpieczenia sp. z o.o."
        document.getElementById("extension_Regulamin_label").innerHTML = "Zapoznałem się i akceptuję <a href='https://top.a-soft.pl/account/terms-of-use' target='_blank'>Regulamin</a>"
        document.getElementById("extension_PersonalDataProcessing_label").innerHTML = "Wyrażam zgodę na przetwarzanie przez Grupę TOP Ubezpieczenia sp.z o.o. będą właścicielem marki OLO Clever z siedzibą w Zielonej Górze moich danych osobowych </br><p> a) przesyłania przez GRUPĘ TOP Ubezpieczenia sp. z o.o. informacji handlowych,</p></br><p>b) kontakt w celu prowadzenia marketingu bezpośredniego,</p></br><p>c) prezentowania ofert z użyciem przetwarzania zautomatyzowanego</p>"
    });

    const cancelButton = document.querySelector("#cancel");

    if (cancelButton) {
        cancelButton.innerHTML = "";

        const cancelButtonSVG = `<svg xmlns="http://www.w3.org/2000/svg" width="11.121"     height="19.414" viewBox="0 0 11.121 19.414">
                <path id="Path_229" data-name="Path 229" d="M0,9,9,0l9,9" transform="translate(1.414 18.707) rotate(-90)" fill="none" stroke="#000" stroke-width="2"/></svg>`;

        cancelButton.innerHTML = cancelButtonSVG;
    }
</script>
<script>
    // change order in form list
    const formList = document.querySelector("#attributeList > ul");
    const passwordItem = formList.querySelector(".newPassword_li");
    const reenterPasswordItem = formList.querySelector(
        ".reenterPassword_li"
    );
    const mailItem = formList.querySelector(
        ".emailVerificationControl_li"
    );
    const usernameItem = formList.querySelector(".displayName_li");
    const givenNameItem = formList.querySelector(".givenName_li");
    const surnameItem = formList.querySelector(".surname_li");

    formList.appendChild(passwordItem);
    formList.appendChild(reenterPasswordItem);

    formList.insertBefore(givenNameItem, surnameItem);
    formList.insertBefore(mailItem, passwordItem);
    formList.insertBefore(usernameItem, mailItem);
</script>
</body>
</html>
