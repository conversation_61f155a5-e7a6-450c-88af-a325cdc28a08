import { ITableColumn } from "../../BaseComponents/ITableColumn";
import { L } from '../../../lib/abpUtility';
import { CustomerGroupDto } from '../../../services/customerGroup/customerGroupDto';
import { FluentTableBase } from '../../Fluent/base/fluentTableBase';
import { CustomerGroupPanel } from './customerGroupPanel';
import { IGenericPanelProps } from '../../Fluent/base/genericPanel';
import { ICrudPermissons } from "../../BaseComponents/commandBarBase";
import {additionalTheme, myTheme} from "../../../styles/theme";
import { Link } from "@fluentui/react";
import { RouterPath } from "../../../components/Router/router.config";

export class CustomerGroupTable extends FluentTableBase<CustomerGroupDto> {
  
  getColumns(): ITableColumn[] {
    return CustomerGroupTable.getTableColumns(this.props);
  }

  static getTableColumns(props: any): ITableColumn[] {
    return [
      {
        name: L('Name'),
        fieldName: 'Name',
        onRender: (item: any): any => {
          return <Link style={{color:myTheme.palette.neutralDark}} onClick={(e) => {
                        e.preventDefault();
                        props.history.push(`/${RouterPath.CustomerGroup}/${item.id}`);
                      }} 
                        href={`/${RouterPath.CustomerGroup}/${item.id}`}>
                  {item.Name}
                </Link>
        }
      },
      {
        name: L('System name'),
        fieldName: 'SystemName',
      },
      {
        name: L('Active'),
        fieldName: 'Active',
        onRender: (item: CustomerGroupDto) => {
          return item.Active ? (
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.green, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
          ) : (
            <span style={{color: additionalTheme.white, backgroundColor: additionalTheme.lighterRed, padding: '2px 5px', borderRadius: '2px'}}>{L('No')}</span>
          );
        }
      },
      {
        name: L('Is system'),
        fieldName: 'IsSystem',
        onRender: (item: CustomerGroupDto) => {
          return item.IsSystem ? (
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.green, padding: '2px 5px', borderRadius: '2px' }}>{L('Yes')}</span>
          ) : (
            <span style={{color: additionalTheme.white, backgroundColor: additionalTheme.lighterRed, padding: '2px 5px', borderRadius: '2px'}}>{L('No')}</span>
          );
        }
      },
      {
        name: L('Enable password lifetime'),
        fieldName: 'EnablePasswordLifetime',
        onRender: (item: CustomerGroupDto) => {
          return item.EnablePasswordLifetime ? (
            <span style={{ color: additionalTheme.white, backgroundColor: myTheme.palette.green, padding: '2px 5px', borderRadius: '2px'  }}>{L('Yes')}</span>
          ) : (
            <span style={{color: additionalTheme.white, backgroundColor: additionalTheme.lighterRed, padding: '2px 5px', borderRadius: '2px'}}>{L('No')}</span>
          );
        }
      },
    ];
  }

  getCrudPermission(): ICrudPermissons {
    return {
      create: true,
      update: true,
      delete: true,
      customActions: false,
    };
  }

  getTitle(): string {
    return L('Customer group list');
  }

  renderPanelView(props: IGenericPanelProps): JSX.Element {
    return <CustomerGroupPanel
      {...props}
    />
  }
}