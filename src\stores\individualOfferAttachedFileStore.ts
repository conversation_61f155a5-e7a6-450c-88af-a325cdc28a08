import { CrudStoreBase } from "./crudStoreBase";
import { IndividualOfferAttachedFileDto } from "../services/individualOfferAttachedFile/individualOfferAttachedFileDto";
import individualOfferAttachedFileService from "../services/individualOfferAttachedFile/individualOfferAttachedFileService";

class IndividualOfferAttachedFileStore extends CrudStoreBase<IndividualOfferAttachedFileDto> {
    constructor() {
        super(individualOfferAttachedFileService, defaultIndividualOfferAttachedFile)
    }
}

export const defaultIndividualOfferAttachedFile = {
    individualOfferId: 0,
    fileUrl: "",
    originalFileName: "",
    blobFileName: "",
    description: "",
    id: "",
    type: "",
}

export default IndividualOfferAttachedFileStore;
