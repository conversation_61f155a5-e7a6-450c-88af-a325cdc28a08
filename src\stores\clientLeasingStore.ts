import { CrudStoreBase } from './crudStoreBase';
import { ClientDto } from '../services/client/dto/clientDto';
import ClientService from '../services/client/clientService';
import { ClientTypeEnum } from '../services/client/clientTypeEnums';
import { defaultUser } from './userCrudStore';
import { ClientMaritalStatusEnum } from '../services/client/clientMaritalStatusEnums';
import { PagedResultRequestDto } from '../services/base/pagedResultRequestDto';

class ClientLeasingStore extends CrudStoreBase<ClientDto>{
	constructor() {
		super(ClientService, defaultClient)
	}

    public async getAll(pagedFilterAndSortedRequest: PagedResultRequestDto) {
        console.info("getAllLeasing." + this.crudService.endpoint.dataSet);
        if (this.dataSet && pagedFilterAndSortedRequest.skipCount === 0) {
            this.dataSet.totalCount = -1;
            this.dataSet.items = [];
        }
        
        if (this.dataSet && this.dataSet.totalCount === this.dataSet.items.length) {
            console.info("Ignoring GetAll requests.");
            return;
        }
        
        let result = await await ClientService.getAllLeasing(pagedFilterAndSortedRequest).catch((error: any) => {
			console.error(error);
		});
        
        if (this.dataSet && this.dataSet.items) {
            this.dataSet.totalCount = result.totalCount;
            this.dataSet.items = this.dataSet.items.concat(result.items);
            return;
        }
        
        this.dataSet = {...result};
	}
}

const defaultClient: ClientDto = {
	id: '',
    agreementsPayload: '',
    agreementsStatus: null,
    emailAdditional: '',
    customerId: '',
    dateOfBirth: '',
    company: '',
    streetAddress: '',
    streetAddress2: '',
    zipPostalCode: '',
    city: '',
    county: '',
    country: '',
    countryId: '60f0391b217c7a758d6a3a29',
    stateProvinceId: '',
    phone: '',
    pesel: '',
    nip: '',
    regon: '',
    pkDs: [{}],
	clientType: ClientTypeEnum.Individual,
    note: '',
    nationality: 'PL',
    userId: 0,
    user: defaultUser,
    superAgentId: null,
    maritalStatus: ClientMaritalStatusEnum.Single,
}

export default ClientLeasingStore;