export enum AgentClaimDocumentType {
    DOWÓDREJESTRACYJNYPOJAZDU = 'Dowód rejestracyjny pojazdu',
    OŚWIADCZENIEKIEROWCY = 'Oświadczenie kierowcy',
    WSPÓLNEOŚWIADCZENIE = 'Wspólne oświadczenie',
    OŚWIADCZENIESPRAWCY = 'Oświadczenie sprawcy',
    DYSPOZYCJAWYPŁATYODSZKODOWANIA = 'Dyspozycja wypłaty odszkodowania',
    KOSZTORYSTU = 'Kosztorys TU',
    KOSZTORYSWARSZTAT = 'Kosztorys warsztat',
    KOSZTORYSFIRMAKLIENT = 'Kosztorys firma/klient',
    KOSZTORYSRZECZOZNAWCADE = 'Kosztorys Rzeczoznawca DE',
    DOWÓDOSOBISTY = 'Dowód osobisty',
    DOKUMENTACJAMEDYCZNA = 'Dokumentacja medyczna',
    KORESPONDENCJATU = 'Korespondencja TU',
    DECYZJATUWYPŁATA = 'Decyzja TU - WYPŁATA',
    DECYZJATUODMOWA = 'Decyzja TU - ODMOWA',
    REKLAMACJA = 'Reklamacja',
    ZGŁOSZENIESZKODY = 'Zgłoszenie szkody',
    PEŁNOMOCNICTWO = 'Pełnomocnictwo',
    UMOWACESJI = 'Umowa Cesji',
    UMOWACESJIZAŁĄCZNIK = 'Umowa Cesji - załącznik',
    PRAWOJAZDY = 'Prawo jazdy',
    AKTWŁASNOŚCI = 'Akt własności',
    FVKLIENT = 'FV klient',
    FVKLIENTPROWIZJA = 'FV klient - prowizja',
    NOTATKAPOLICJI = 'Notatka policji',
    CRM = 'CRM',
    ZLECENIETRANSPORTOWE = 'Zlecenie transportowe',
    FVZAPRZEŁADUNEK = 'FV za przeładunek',
    FVHOLOWANIEDŹWIG = 'FV holowanie/dźwig',
    FVPOJAZDZASTĘPCZY = 'FV pojazd zastępczy',
    LICENCJATRANSPORTOWA = 'Licencja transportowa',
    DOKUMENTACJAAME = 'Dokumentacja AME',
    INNYDOKUMENTKLIENT = 'Inny dokument klient',
    INNYDOKUMENTTU = 'Inny dokument TU',
    WYCENATUSZKODACAŁKOWITA = 'Wycena TU szkoda całkowita'
}